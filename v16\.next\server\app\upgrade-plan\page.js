(()=>{var e={};e.id=2723,e.ids=[2723],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27199:(e,s,r)=>{Promise.resolve().then(r.bind(r,60391))},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},60391:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\upgrade-plan\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\upgrade-plan\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64151:(e,s,r)=>{Promise.resolve().then(r.bind(r,77165))},74075:e=>{"use strict";e.exports=require("zlib")},77165:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>o});var a=r(60687),t=r(43210),n=r(16189),i=r(85814),l=r.n(i),d=r(17019);function o(){(0,n.useRouter)();let[e,s]=(0,t.useState)(null),[r,i]=(0,t.useState)(!0),[o,c]=(0,t.useState)(null),m=[{id:"usuario",name:"Plan Usuario",price:"€10.00",period:"/mes",description:"Uso de la plataforma durante el mes (una vez finalizado no podr\xe1 volver a acceder hasta renovaci\xf3n)",icon:(0,a.jsx)(d.cfS,{className:"w-8 h-8"}),recommended:!0,features:[{name:"Subida de documentos",included:!0},{name:"Habla con tu preparador IA *",included:!0},{name:"Generador de test *",included:!0},{name:"Generador de flashcards *",included:!0},{name:"Generador de mapas mentales *",included:!0},{name:"* Para las tareas en las que se haga uso de IA, el l\xedmite mensual ser\xe1 de 1,000,000 de tokens, podr\xe1n ampliarse durante el mes mediante pago",included:!0},{name:"Planificaci\xf3n de estudios",included:!1},{name:"Res\xfamenes A2 y A1",included:!1}],stripePriceId:process.env.NEXT_PUBLIC_STRIPE_PRICE_ID_USUARIO},{id:"pro",name:"Plan Pro",price:"€15.00",period:"/mes",description:"Uso de la plataforma durante el mes (una vez finalizado no podr\xe1 volver a acceder hasta renovaci\xf3n)",icon:(0,a.jsx)(d.usP,{className:"w-8 h-8"}),features:[{name:"Subida de documentos",included:!0},{name:"Planificaci\xf3n de estudios mediante IA*",included:!0},{name:"Habla con tu preparador IA *",included:!0},{name:"Generador de test *",included:!0},{name:"Generador de flashcards *",included:!0},{name:"Generador de mapas mentales *",included:!0},{name:"Generaci\xf3n de Res\xfamenes para A2 y A1",included:!0},{name:"* Para las tareas en las que se haga uso de IA, el l\xedmite mensual ser\xe1 de 1,000,000 de tokens, podr\xe1n ampliarse durante el mes mediante pago",included:!0}],stripePriceId:process.env.NEXT_PUBLIC_STRIPE_PRICE_ID_PRO}],u=async e=>{if(!e.stripePriceId)return void console.error("No Stripe price ID configured for plan:",e.id);c(e.id);try{let s=await fetch("/api/stripe/create-checkout-session",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({priceId:e.stripePriceId,successUrl:`${window.location.origin}/app?upgrade=success`,cancelUrl:`${window.location.origin}/upgrade-plan?upgrade=cancelled`})});if(!s.ok)throw Error("Error creating checkout session");let{url:r}=await s.json();window.location.href=r}catch(e){console.error("Error creating checkout session:",e),c(null)}};return r?(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsx)("div",{className:"bg-white shadow-sm",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,a.jsxs)(l(),{href:"/app",className:"flex items-center text-gray-600 hover:text-gray-900 transition-colors",children:[(0,a.jsx)(d.kRp,{className:"w-5 h-5 mr-2"}),"Volver al Dashboard"]}),e&&(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:["Plan actual: ",(0,a.jsx)("span",{className:"font-medium capitalize",children:e.subscription_plan})]})]})})}),(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,a.jsxs)("div",{className:"text-center mb-16",children:[(0,a.jsx)("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:"Actualiza tu Plan"}),(0,a.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"Desbloquea todo el potencial de OposiAI con nuestros planes premium. M\xe1s recursos, funciones avanzadas y soporte prioritario."})]}),(0,a.jsx)("div",{className:"grid md:grid-cols-2 gap-8 max-w-5xl mx-auto",children:m.map(s=>(0,a.jsxs)("div",{className:`relative bg-white rounded-2xl shadow-lg overflow-hidden ${s.recommended?"ring-2 ring-blue-500 transform scale-105":""}`,children:[s.recommended&&(0,a.jsx)("div",{className:"absolute top-0 left-0 right-0 bg-gradient-to-r from-blue-500 to-purple-600 text-white text-center py-2 text-sm font-medium",children:"⭐ M\xe1s Popular"}),(0,a.jsxs)("div",{className:"p-8",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)("div",{className:"flex justify-center mb-4 text-blue-600",children:s.icon}),(0,a.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-2",children:s.name}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:s.description}),(0,a.jsxs)("div",{className:"flex items-baseline justify-center",children:[(0,a.jsx)("span",{className:"text-4xl font-bold text-gray-900",children:s.price}),(0,a.jsx)("span",{className:"text-gray-600 ml-1",children:s.period})]})]}),(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsxs)("div",{className:"space-y-3 mb-6",children:[(0,a.jsx)("h4",{className:"text-sm font-semibold text-gray-900",children:"Incluye:"}),(0,a.jsx)("ul",{className:"space-y-3",children:s.features.filter(e=>e.included).map((e,s)=>(0,a.jsxs)("li",{className:"flex items-start",children:[(0,a.jsx)(d.YrT,{className:"w-5 h-5 mr-3 mt-0.5 flex-shrink-0 text-green-500"}),(0,a.jsx)("span",{className:"text-sm text-gray-700",children:e.name})]},s))})]}),s.features.filter(e=>!e.included).length>0&&(0,a.jsxs)("div",{className:"space-y-3 pt-4 border-t border-gray-200",children:[(0,a.jsx)("h4",{className:"text-sm font-semibold text-gray-900",children:"No incluye:"}),(0,a.jsx)("ul",{className:"space-y-3",children:s.features.filter(e=>!e.included).map((e,s)=>(0,a.jsxs)("li",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"w-5 h-5 mr-3 mt-0.5 flex-shrink-0 flex items-center justify-center",children:(0,a.jsx)("div",{className:"w-3 h-0.5 bg-red-400 rounded"})}),(0,a.jsx)("span",{className:"text-sm text-gray-500",children:e.name})]},s))})]})]}),(0,a.jsx)("button",{onClick:()=>u(s),disabled:o===s.id||e?.subscription_plan===s.id,className:`w-full py-4 px-6 rounded-xl font-semibold transition-all duration-200 ${e?.subscription_plan===s.id?"bg-gray-100 text-gray-500 cursor-not-allowed":s.recommended?"bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5":"bg-gray-900 text-white hover:bg-gray-800"} ${o===s.id?"opacity-50 cursor-not-allowed":""}`,children:o===s.id?(0,a.jsxs)("div",{className:"flex items-center justify-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"}),"Procesando..."]}):e?.subscription_plan===s.id?"Plan Actual":`Seleccionar ${s.name}`})]})]},s.id))}),(0,a.jsx)("div",{className:"mt-16 text-center",children:(0,a.jsxs)("div",{className:"bg-white rounded-xl p-8 shadow-lg max-w-4xl mx-auto",children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-4",children:"\xbfPor qu\xe9 actualizar?"}),(0,a.jsxs)("div",{className:"grid md:grid-cols-3 gap-8 mt-8",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(d.FrA,{className:"w-12 h-12 text-blue-600 mx-auto mb-4"}),(0,a.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:"M\xe1s Potencia"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Acceso a m\xe1s tokens y recursos para estudiar sin l\xedmites"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(d.pcC,{className:"w-12 h-12 text-blue-600 mx-auto mb-4"}),(0,a.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:"Funciones Avanzadas"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Planificaci\xf3n inteligente, res\xfamenes A1/A2 y an\xe1lisis detallado"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(d.cfS,{className:"w-12 h-12 text-blue-600 mx-auto mb-4"}),(0,a.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:"Soporte Prioritario"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Atenci\xf3n personalizada y acceso anticipado a nuevas funciones"})]})]})]})})]})]})}r(79481)},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},85091:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>o});var a=r(65239),t=r(48088),n=r(88170),i=r.n(n),l=r(30893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);r.d(s,d);let o={children:["",{children:["upgrade-plan",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,60391)),"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\upgrade-plan\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\upgrade-plan\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:t.RouteKind.APP_PAGE,page:"/upgrade-plan/page",pathname:"/upgrade-plan",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),a=s.X(0,[4447,6126,7019,5814,1191],()=>r(85091));module.exports=a})();