(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8997],{763:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>o});var t=a(5155),r=a(2115),n=a(5695);function l(){let e=(0,n.useRouter)(),s=(0,n.useSearchParams)(),[a,l]=(0,r.useState)(!1),[o,i]=(0,r.useState)(null),[c,d]=(0,r.useState)("pending"),u=s.get("session_id"),m=s.get("email"),x=s.get("plan");(0,r.useEffect)(()=>{m&&i(m);let s=async()=>{if(u)try{let s=await fetch("/api/payment/status?session_id=".concat(u)),a=await s.json();a.success&&"completed"===a.status?(d("completed"),setTimeout(()=>{let s="/thank-you?session_id=".concat(u).concat(x?"&plan=".concat(x):"","&payment_successful=true");e.push(s)},2e3)):a.success&&"failed"===a.status&&d("failed")}catch(e){console.error("Error verificando estado del pago:",e)}};s();let a=setInterval(s,5e3),t=setTimeout(()=>{clearInterval(a)},3e5);return()=>{clearInterval(a),clearTimeout(t)}},[u,x,e]);let h=async()=>{l(!0);try{if(u){let s=await fetch("/api/payment/status?session_id=".concat(u)),a=await s.json();if(a.success&&"completed"===a.status){d("completed");let s="/thank-you?session_id=".concat(u).concat(x?"&plan=".concat(x):"","&payment_successful=true");e.push(s)}else a.success&&"failed"===a.status&&d("failed")}}catch(e){console.error("Error verificando estado:",e)}finally{l(!1)}},p=async()=>{if(o)try{let e=await fetch("/api/auth/resend-confirmation",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:o})}),s=await e.json();s.success?alert("Email de confirmaci\xf3n reenviado exitosamente"):alert("Error reenviando email: "+s.error)}catch(e){console.error("Error reenviando email:",e),alert("Error reenviando email")}};return(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,t.jsx)("div",{className:"max-w-md mx-auto",children:(0,t.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:["pending"===c&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"text-center mb-6",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Procesando tu Pago"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Tu pago est\xe1 siendo procesado. Una vez completado, tu cuenta ser\xe1 activada autom\xe1ticamente."})]}),(0,t.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-md p-4 mb-6",children:(0,t.jsxs)("div",{className:"flex",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("svg",{className:"h-5 w-5 text-blue-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"})})}),(0,t.jsxs)("div",{className:"ml-3",children:[(0,t.jsx)("h3",{className:"text-sm font-medium text-blue-800",children:"\xbfQu\xe9 est\xe1 pasando?"}),(0,t.jsx)("div",{className:"mt-2 text-sm text-blue-700",children:(0,t.jsxs)("ul",{className:"list-disc list-inside space-y-1",children:[(0,t.jsx)("li",{children:"Tu cuenta ha sido creada"}),(0,t.jsx)("li",{children:"Estamos verificando tu pago"}),(0,t.jsx)("li",{children:"Tu cuenta ser\xe1 activada autom\xe1ticamente una vez completado"})]})})]})]})}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("button",{onClick:h,disabled:a,className:"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",children:a?"Verificando...":"Verificar Estado"}),o&&(0,t.jsx)("button",{onClick:p,className:"w-full bg-gray-600 text-white py-2 px-4 rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2",children:"Reenviar Email de Confirmaci\xf3n"})]})]}),"completed"===c&&(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4",children:(0,t.jsx)("svg",{className:"h-6 w-6 text-green-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"\xa1Pago Completado!"}),(0,t.jsx)("p",{className:"text-gray-600 mb-4",children:"Tu pago ha sido procesado exitosamente. Redirigiendo..."}),(0,t.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-green-600 mx-auto"})]}),"failed"===c&&(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4",children:(0,t.jsx)("svg",{className:"h-6 w-6 text-red-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})}),(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Error en el Pago"}),(0,t.jsx)("p",{className:"text-gray-600 mb-4",children:"Hubo un problema procesando tu pago. Por favor, intenta de nuevo."}),(0,t.jsx)("button",{onClick:()=>e.push("/upgrade-plan"),className:"w-full bg-red-600 text-white py-2 px-4 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2",children:"Intentar de Nuevo"})]}),(0,t.jsx)("div",{className:"mt-6 text-center",children:(0,t.jsxs)("p",{className:"text-sm text-gray-500",children:["\xbfNecesitas ayuda? ",(0,t.jsx)("a",{href:"mailto:<EMAIL>",className:"text-blue-600 hover:text-blue-500",children:"Cont\xe1ctanos"})]})})]})})})}function o(){return(0,t.jsx)(r.Suspense,{fallback:(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,t.jsx)("div",{className:"max-w-md mx-auto",children:(0,t.jsx)("div",{className:"bg-white shadow rounded-lg p-6",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Cargando..."}),(0,t.jsx)("p",{className:"text-gray-600",children:"Preparando la p\xe1gina de pago"})]})})})}),children:(0,t.jsx)(l,{})})}},5695:(e,s,a)=>{"use strict";var t=a(8999);a.o(t,"usePathname")&&a.d(s,{usePathname:function(){return t.usePathname}}),a.o(t,"useRouter")&&a.d(s,{useRouter:function(){return t.useRouter}}),a.o(t,"useSearchParams")&&a.d(s,{useSearchParams:function(){return t.useSearchParams}})},7333:(e,s,a)=>{Promise.resolve().then(a.bind(a,763))}},e=>{var s=s=>e(e.s=s);e.O(0,[8441,1684,7358],()=>s(7333)),_N_E=e.O()}]);