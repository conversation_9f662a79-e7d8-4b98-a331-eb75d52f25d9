"use strict";(()=>{var e={};e.id=8606,e.ids=[8606],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{e.exports=require("punycode")},27910:e=>{e.exports=require("stream")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{e.exports=require("tls")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},86676:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>f,routeModule:()=>d,serverHooks:()=>m,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>x});var s={};r.r(s),r.d(s,{GET:()=>c});var o=r(96559),i=r(48088),a=r(37719),n=r(32190),p=r(34386),u=r(31571);async function c(e){try{let t=(0,p.createServerClient)("https://fxnhpxjijinfuxxxplzj.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4",{cookies:{getAll:()=>e.cookies.getAll(),setAll(){}}}),{data:{user:r},error:s}=await t.auth.getUser();if(s||!r)return n.NextResponse.json({error:"No autorizado"},{status:401});let{searchParams:o}=new URL(e.url),i=parseInt(o.get("limit")||"20"),a=o.get("type")||void 0,c=await u.X.getUserNotifications(r.id,i,a),d=c.notifications.map(e=>({id:e.id,type:e.type,subject:e.subject,sentAt:e.sent_at,status:e.status,metadata:e.metadata}));return n.NextResponse.json({success:!0,data:{notifications:d,total:c.total,hasMore:c.total>i},timestamp:new Date().toISOString()})}catch(e){return console.error("❌ Error obteniendo notificaciones del usuario:",e),n.NextResponse.json({success:!1,error:"Error interno del servidor",details:e instanceof Error?e.message:"Error desconocido"},{status:500})}}let d=new o.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/user/notifications/route",pathname:"/api/user/notifications",filename:"route",bundlePath:"app/api/user/notifications/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\user\\notifications\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:x,serverHooks:m}=d;function f(){return(0,a.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:x})}},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,6345,4386,8295],()=>r(86676));module.exports=s})();