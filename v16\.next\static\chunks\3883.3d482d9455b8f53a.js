"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3883],{3883:(e,r,a)=>{a.d(r,{SupabaseAdminService:()=>i});var t=a(851),o=a(9509);let s=(0,t.UU)("https://fxnhpxjijinfuxxxplzj.supabase.co",o.env.SUPABASE_SERVICE_ROLE_KEY,{auth:{autoRefreshToken:!1,persistSession:!1}});class i{static async createStripeTransaction(e){let{data:r,error:a}=await s.from("stripe_transactions").insert([e]).select().single();if(a)throw console.error("Error creating stripe transaction:",a),Error("Failed to create transaction: ".concat(a.message));return r}static async getTransactionBySessionId(e){let{data:r,error:a}=await s.from("stripe_transactions").select("*").eq("stripe_session_id",e).single();if(a&&"PGRST116"!==a.code)throw console.error("Error fetching transaction:",a),Error("Failed to fetch transaction: ".concat(a.message));return r}static async createUserWithInvitation(e,r){var a,t,o,i,n,l,c;console.log("\uD83D\uDD04 [SUPABASE_ADMIN] Creating user invitation:",{email:e,userData:r,redirectTo:"".concat("http://localhost:3000","/auth/callback"),timestamp:new Date().toISOString()});let{data:u,error:d}=await s.auth.admin.inviteUserByEmail(e,{data:r,redirectTo:"".concat("http://localhost:3000","/auth/callback")});if(console.log("\uD83D\uDCCA [SUPABASE_ADMIN] Invitation result:",{hasData:!!u,hasUser:!!(null==u?void 0:u.user),userId:null==u||null==(a=u.user)?void 0:a.id,userEmail:null==u||null==(t=u.user)?void 0:t.email,userAud:null==u||null==(o=u.user)?void 0:o.aud,userRole:null==u||null==(i=u.user)?void 0:i.role,emailConfirmed:null==u||null==(n=u.user)?void 0:n.email_confirmed_at,userMetadata:null==u||null==(l=u.user)?void 0:l.user_metadata,appMetadata:null==u||null==(c=u.user)?void 0:c.app_metadata,error:null==d?void 0:d.message,errorCode:null==d?void 0:d.status,fullError:d}),d)throw console.error("❌ [SUPABASE_ADMIN] Error creating user invitation:",{message:d.message,status:d.status,details:d}),Error("Failed to create user invitation: ".concat(d.message));return u}static async createUserWithPassword(e,r,a){var t,o,i,n;let l=!(arguments.length>3)||void 0===arguments[3]||arguments[3];console.log("\uD83D\uDD04 [SUPABASE_ADMIN] Creating user with password:",{email:e,userData:a,sendConfirmationEmail:l,timestamp:new Date().toISOString()});let{data:c,error:u}=await s.auth.admin.createUser({email:e,password:r,user_metadata:a,email_confirm:!1});if(console.log("\uD83D\uDCCA [SUPABASE_ADMIN] User creation result:",{hasData:!!c,hasUser:!!(null==c?void 0:c.user),userId:null==c||null==(t=c.user)?void 0:t.id,userEmail:null==c||null==(o=c.user)?void 0:o.email,emailConfirmed:null==c||null==(i=c.user)?void 0:i.email_confirmed_at,userMetadata:null==c||null==(n=c.user)?void 0:n.user_metadata,error:null==u?void 0:u.message,errorCode:null==u?void 0:u.status}),u)return console.error("❌ [SUPABASE_ADMIN] Error creating user with password:",{message:u.message,status:u.status,details:u}),{data:null,error:u};if((null==c?void 0:c.user)&&l){console.log("\uD83D\uDCE7 Enviando email de confirmaci\xf3n...");let{error:a}=await s.auth.admin.generateLink({type:"signup",email:e,password:r,options:{redirectTo:"".concat("http://localhost:3000","/auth/confirmed")}});a?console.error("⚠️ Error enviando email de confirmaci\xf3n:",a):console.log("✅ Email de confirmaci\xf3n enviado exitosamente")}else(null==c?void 0:c.user)&&!l&&console.log("\uD83D\uDCE7 Email de confirmaci\xf3n omitido (se enviar\xe1 despu\xe9s del pago)");return{data:c,error:null}}static async sendConfirmationEmailForUser(e){console.log("\uD83D\uDCE7 [SUPABASE_ADMIN] Enviando email de confirmaci\xf3n para usuario:",e);try{let{data:r,error:a}=await s.auth.admin.getUserById(e);if(a||!(null==r?void 0:r.user))return console.error("Error obteniendo datos del usuario:",a),{success:!1,error:"Usuario no encontrado"};let t=r.user,{error:o}=await s.auth.admin.updateUserById(t.id,{email_confirm:!0,user_metadata:{...t.user_metadata,payment_verified:!0,email_confirmed_via_payment:!0,confirmed_at:new Date().toISOString()}});if(o)return console.error("⚠️ Error confirmando email del usuario:",o),{success:!1,error:o.message};return console.log("✅ Usuario confirmado autom\xe1ticamente despu\xe9s del pago exitoso"),{success:!0}}catch(e){return console.error("Error en sendConfirmationEmailForUser:",e),{success:!1,error:e instanceof Error?e.message:"Error desconocido"}}}static async sendConfirmationEmail(e,r){console.log("\uD83D\uDCE7 [SUPABASE_ADMIN] Enviando email de confirmaci\xf3n para:",e);let{error:a}=await s.auth.admin.generateLink({type:"signup",email:e,password:r,options:{redirectTo:"".concat("http://localhost:3000","/auth/confirmed")}});return a?(console.error("⚠️ Error enviando email de confirmaci\xf3n:",a),{success:!1,error:a.message}):(console.log("✅ Email de confirmaci\xf3n enviado exitosamente"),{success:!0})}static async createUserProfile(e){let{data:r,error:a}=await s.from("user_profiles").insert([e]).select().single();if(a)throw console.error("Error creating user profile:",a),Error("Failed to create user profile: ".concat(a.message));return r}static async upsertUserProfile(e){let{data:r,error:a}=await s.from("user_profiles").upsert([e],{onConflict:"user_id"}).select().single();if(a)throw console.error("Error upserting user profile:",a),Error("Failed to upsert user profile: ".concat(a.message));return r}static async logPlanChange(e){let{data:r,error:a}=await s.from("user_plan_history").insert([e]).select().single();if(a)throw console.error("Error logging plan change:",a),Error("Failed to log plan change: ".concat(a.message));return r}static async logFeatureAccess(e){let{data:r,error:a}=await s.from("feature_access_log").insert([e]).select().single();if(a)throw console.error("Error logging feature access:",a),Error("Failed to log feature access: ".concat(a.message));return r}static async getUserProfile(e){let{data:r,error:a}=await s.from("user_profiles").select("*").eq("user_id",e).single();if(a&&"PGRST116"!==a.code)throw console.error("Error fetching user profile:",a),Error("Failed to fetch user profile: ".concat(a.message));return r}static async updateTransactionWithUser(e,r){let{error:a}=await s.from("stripe_transactions").update({user_id:r,updated_at:new Date().toISOString()}).eq("id",e);if(a)throw console.error("Error updating transaction with user_id:",a),Error("Failed to update transaction: ".concat(a.message))}static async activateTransaction(e){let{error:r}=await s.from("stripe_transactions").update({activated_at:new Date().toISOString()}).eq("id",e);if(r)throw console.error("Error activating transaction:",r),Error("Failed to activate transaction: ".concat(r.message))}static async getDocumentsCount(e){let{count:r,error:a}=await s.from("documentos").select("*",{count:"exact",head:!0}).eq("user_id",e);return a?(console.error("Error getting documents count:",a),0):r||0}static async getUserByEmail(e){try{let{data:{users:r},error:a}=await s.auth.admin.listUsers();if(a)throw console.error("Error getting user by email:",a),Error("Failed to get user by email: ".concat(a.message));if(!r||0===r.length)return null;let t=r.find(r=>r.email===e);if(!t)return null;return{id:t.id,email:t.email,email_confirmed_at:t.email_confirmed_at}}catch(e){throw console.error("Error in getUserByEmail:",e),e}}}}}]);