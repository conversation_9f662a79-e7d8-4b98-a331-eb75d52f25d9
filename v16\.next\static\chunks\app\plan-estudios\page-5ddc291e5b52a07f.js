(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[525],{2477:(e,a,n)=>{Promise.resolve().then(n.bind(n,3187))},3187:(e,a,n)=>{"use strict";n.r(a),n.d(a,{default:()=>h});var t=n(5155),s=n(2115),r=n(351),o=n(7634),i=n(3792),l=n(2646),c=n(572),d=n(3737),m=n(8260),u=n(4092),x=n(1544),g=n(5967);let h=()=>{let[e,a]=(0,s.useState)(null),[n,h]=(0,s.useState)(null),[p,b]=(0,s.useState)(!0),[f,j]=(0,s.useState)(!1),[y,w]=(0,s.useState)(!1),[N,v]=(0,s.useState)([]),E=(0,s.useRef)(null),{user:P}=(0,u.A)(),[R,k]=(0,s.useState)(null);(0,s.useEffect)(()=>{S()},[]),(0,s.useEffect)(()=>{(async()=>{P&&k(await (0,x.qk)("study_planning")?"paid":"free")})()},[P]);let S=async()=>{b(!0);try{let e=await (0,o.jg)();if(!e){m.oR.error("No se encontr\xf3 un temario configurado"),a(null),w(!1),h(null);return}a(e);let n=await (0,i.vD)(e.id);if(w(n),!n){m.oR.error("Necesitas configurar tu planificaci\xf3n antes de generar el plan de estudios"),h(null);return}let t=await (0,l.fF)(e.id);t&&t.plan_data?(console.log("✅ Plan de estudios existente encontrado"),h(t.plan_data),m.oR.success("Plan de estudios cargado desde la base de datos")):h(null)}catch(e){console.error("Error al cargar datos:",e),m.oR.error("Error al cargar los datos"),a(null),w(!1),h(null)}finally{b(!1)}},C=async()=>{let a;if(e){if(!y)return void m.oR.error("Necesitas configurar tu planificaci\xf3n antes de generar el plan de estudios");j(!0);try{a=m.oR.loading("La generaci\xf3n del plan de estudios puede tardar unos minutos. Si encuentra alg\xfan fallo una vez finalizado, vuelve a generar. OposiAI puede cometer errores de configuraci\xf3n.",{duration:0});let n=await fetch("/api/ai",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"generarPlanEstudios",peticion:e.id,contextos:[]})});if(!n.ok){let e=(await n.json().catch(()=>({}))).error||"Error en la API: ".concat(n.status);throw Error(e)}let{result:t}=await n.json();h(t),m.oR.success("\xa1Plan de estudios generado exitosamente!",{id:a})}catch(e){console.error("Error al generar plan:",e),(e instanceof Error?e.message:"Error desconocido").includes("planificaci\xf3n configurada")?m.oR.error('Necesitas completar la configuraci\xf3n de planificaci\xf3n en "Mi Temario"',{id:a}):m.oR.error("Error al generar el plan de estudios. Int\xe9ntalo de nuevo.",{id:a})}finally{j(!1)}}},_=a=>{let n="# Plan de Estudios - ".concat(null==e?void 0:e.titulo,"\n\n");return n+="".concat(a.introduccion,"\n\n"),n+="## Resumen del Plan\n\n",n+="- **Tiempo total de estudio:** ".concat(a.resumen.tiempoTotalEstudio,"\n"),n+="- **N\xfamero de temas:** ".concat(a.resumen.numeroTemas,"\n"),n+="- **Duraci\xf3n estudio nuevo:** ".concat(a.resumen.duracionEstudioNuevo,"\n"),n+="- **Duraci\xf3n repaso final:** ".concat(a.resumen.duracionRepasoFinal,"\n\n"),n+="## Cronograma Semanal\n\n",a.semanas.forEach(e=>{n+="### Semana ".concat(e.numero," (").concat(e.fechaInicio," - ").concat(e.fechaFin,")\n\n"),n+="**Objetivo:** ".concat(e.objetivoPrincipal,"\n\n"),e.dias.forEach(e=>{n+="**".concat(e.dia," (").concat(e.horas,"h):**\n"),e.tareas.forEach(e=>{n+="- ".concat(e.titulo," (").concat(e.duracionEstimada,")\n"),e.descripcion&&(n+="  ".concat(e.descripcion,"\n"))}),n+="\n"})}),n+="## Estrategia de Repasos\n\n".concat(a.estrategiaRepasos,"\n\n"),n+="## Pr\xf3ximos Pasos\n\n".concat(a.proximosPasos,"\n")};return p?(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Cargando datos..."})]})}):"free"===R?(0,t.jsx)(g.A,{feature:"study_planning",benefits:["Planes de estudio personalizados con IA","Cronogramas adaptativos a tu ritmo","Seguimiento autom\xe1tico de progreso","Recomendaciones inteligentes de repaso"]}):e&&y?(0,t.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,t.jsxs)("div",{className:"max-w-6xl mx-auto px-4 py-6",children:[(0,t.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-1",children:"Mi Plan de Estudios"}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:["Plan personalizado generado con IA para: ",(0,t.jsx)("strong",{children:e.titulo})]})]}),(0,t.jsx)("div",{className:"flex items-center space-x-3",children:n&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("button",{onClick:()=>{h(null),C()},disabled:f,className:"flex items-center px-3 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50 text-sm",children:[(0,t.jsx)(r.jTZ,{className:"w-4 h-4 mr-2"}),"Regenerar"]}),(0,t.jsxs)("button",{onClick:()=>{if(!n)return;let a=new Blob([_(n)],{type:"text/markdown"}),t=URL.createObjectURL(a),s=document.createElement("a");s.href=t,s.download="plan-estudios-".concat((null==e?void 0:e.titulo)||"temario",".md"),document.body.appendChild(s),s.click(),document.body.removeChild(s),URL.revokeObjectURL(t),m.oR.success("Plan descargado exitosamente")},className:"flex items-center px-3 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors text-sm",children:[(0,t.jsx)(r.a4x,{className:"w-4 h-4 mr-2"}),"Descargar"]}),(0,t.jsxs)("button",{onClick:()=>{if(!n)return;let a=_(n),t=window.open("","_blank");t&&(t.document.write("\n        <html>\n          <head>\n            <title>Plan de Estudios - ".concat(null==e?void 0:e.titulo,'</title>\n            <style>\n              body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }\n              h1, h2, h3 { color: #333; }\n              h1 { border-bottom: 2px solid #333; padding-bottom: 10px; }\n              h2 { border-bottom: 1px solid #666; padding-bottom: 5px; }\n              ul, ol { margin-left: 20px; }\n              strong { color: #2563eb; }\n              @media print { body { margin: 0; } }\n            </style>\n          </head>\n          <body>\n            <div id="content"></div>\n            <script>\n              // Convertir markdown a HTML b\xe1sico para impresi\xf3n\n              const markdown = ').concat(JSON.stringify(a),";\n              const content = markdown\n                .replace(/^# (.*$)/gim, '<h1>$1</h1>')\n                .replace(/^## (.*$)/gim, '<h2>$1</h2>')\n                .replace(/^### (.*$)/gim, '<h3>$1</h3>')\n                .replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>')\n                .replace(/\\*(.*?)\\*/g, '<em>$1</em>')\n                .replace(/^- (.*$)/gim, '<li>$1</li>')\n                .replace(/(<li>.*<\\/li>)/s, '<ul>$1</ul>')\n                .replace(/\\n/g, '<br>');\n              document.getElementById('content').innerHTML = content;\n              window.print();\n            <\/script>\n          </body>\n        </html>\n      ")),t.document.close())},className:"flex items-center px-3 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors text-sm",children:[(0,t.jsx)(r.Mvz,{className:"w-4 h-4 mr-2"}),"Imprimir"]})]})})]})}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6",children:[(0,t.jsxs)("div",{className:"flex items-center mb-3",children:[(0,t.jsx)(r.jH2,{className:"w-5 h-5 text-blue-600 mr-2"}),(0,t.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Documentos Seleccionados"})]}),(0,t.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"Selecciona los documentos que quieres consultar durante el estudio de tu plan personalizado."}),(0,t.jsx)(d.A,{ref:E,onSelectionChange:v}),N.length>0&&(0,t.jsx)("div",{className:"mt-3 p-3 bg-blue-50 rounded-lg",children:(0,t.jsxs)("p",{className:"text-sm text-blue-800",children:[(0,t.jsx)("strong",{children:N.length})," documento",1!==N.length?"s":""," seleccionado",1!==N.length?"s":""," para consulta."]})})]}),n?(0,t.jsx)(c.A,{plan:n,temarioId:e.id}):(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center",children:[(0,t.jsx)("div",{className:"w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,t.jsx)(r.wIk,{className:"w-10 h-10 text-blue-600"})}),(0,t.jsx)("h2",{className:"text-2xl font-semibold text-gray-900 mb-4",children:"Genera tu Plan de Estudios Personalizado"}),(0,t.jsx)("p",{className:"text-gray-600 mb-8 max-w-2xl mx-auto",children:"Nuestro asistente de IA analizar\xe1 tu planificaci\xf3n, disponibilidad de tiempo, y las caracter\xedsticas de cada tema para crear un plan de estudios completamente personalizado y realista."}),(0,t.jsx)("button",{onClick:C,disabled:f,className:"flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 mx-auto",children:f?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"}),"Generando plan con IA..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(r.x_j,{className:"w-5 h-5 mr-3"}),"Generar Plan de Estudios"]})})]})]})}):(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center p-4",children:(0,t.jsxs)("div",{className:"max-w-md w-full bg-white rounded-lg shadow-lg p-6 text-center",children:[(0,t.jsx)("div",{className:"w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,t.jsx)(r.x_j,{className:"w-8 h-8 text-yellow-600"})}),(0,t.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Configuraci\xf3n Requerida"}),(0,t.jsx)("p",{className:"text-gray-600 mb-4",children:"Para generar tu plan de estudios personalizado, necesitas:"}),(0,t.jsxs)("ul",{className:"text-left text-sm text-gray-600 mb-6 space-y-2",children:[(0,t.jsxs)("li",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-blue-400 rounded-full mr-3"}),"Tener un temario configurado"]}),(0,t.jsxs)("li",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-blue-400 rounded-full mr-3"}),"Completar la planificaci\xf3n inteligente"]})]}),(0,t.jsxs)("a",{href:"/temario",className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,t.jsx)(r.x_j,{className:"w-4 h-4 mr-2"}),"Ir a Mi Temario"]})]})})}}},e=>{var a=a=>e(e.s=a);e.O(0,[844,7361,8260,6874,3898,4092,1869,8441,1684,7358],()=>a(2477)),_N_E=e.O()}]);