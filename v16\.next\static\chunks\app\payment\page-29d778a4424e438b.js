(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8407],{1544:(e,a,r)=>{"use strict";r.d(a,{IE:()=>t,qk:()=>o,qo:()=>s});let s={free:{id:"free",name:"Plan Gratis",price:0,limits:{documents:1,mindMapsForTrial:2,testsForTrial:10,flashcardsForTrial:10,tokensForTrial:5e4,features:["document_upload","test_generation","flashcard_generation","mind_map_generation"]},features:["document_upload","test_generation","flashcard_generation","mind_map_generation"],restrictedFeatures:["study_planning","ai_tutor_chat","summary_a1_a2"]},usuario:{id:"usuario",name:"Plan Usuario",price:1e3,limits:{documents:-1,mindMapsPerWeek:-1,testsPerWeek:-1,flashcardsPerWeek:-1,monthlyTokens:1e6,features:["document_upload","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation"]},features:["document_upload","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation"],restrictedFeatures:["study_planning","summary_a1_a2"]},pro:{id:"pro",name:"Plan Pro",price:1500,limits:{documents:-1,mindMapsPerWeek:-1,testsPerWeek:-1,flashcardsPerWeek:-1,monthlyTokens:1e6,features:["document_upload","study_planning","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation","summary_a1_a2"]},features:["document_upload","study_planning","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation","summary_a1_a2"],restrictedFeatures:[]}};function t(e){return s[e]||null}function n(e,a){let r=t(e);return!(!r||r.restrictedFeatures.includes(a))&&r.features.includes(a)}async function o(e){try{let a=await fetch("/api/user/plan");if(!a.ok)return console.error("Error obteniendo plan del usuario"),n("free",e);let{plan:r}=await a.json();return n(r||"free",e)}catch(a){return console.error("Error verificando acceso a caracter\xedstica:",a),n("free",e)}}},1547:(e,a,r)=>{"use strict";r.d(a,{Md:()=>n,NB:()=>t});var s=r(1544);let t={free:{id:"free",name:"Plan Gratis",price:0,stripeProductId:null,stripePriceId:null,features:["Incluye:","• Uso de la plataforma solo durante 5 d\xedas","• Subida de documentos: m\xe1ximo 1 documento","• Generador de test: m\xe1ximo 10 preguntas test","• Generador de flashcards: m\xe1ximo 10 tarjetas flashcard","• Generador de mapas mentales: m\xe1ximo 2 mapas mentales","No incluye:","• Planificaci\xf3n de estudios","• Habla con tu preparador IA","• Res\xfamenes A2 y A1"],limits:s.qo.free.limits,planConfig:s.qo.free},usuario:{id:"usuario",name:"Plan Usuario",price:1e3,stripeProductId:"prod_SR65BdKdek1OXd",stripePriceId:"price_1Rae5807kFn3sIXhRf3adX1n",features:["Incluye:","• Uso de la plataforma durante el mes (una vez finalizado no podr\xe1 volver a acceder hasta renovaci\xf3n)","• Subida de documentos","• Habla con tu preparador IA *","• Generador de test *","• Generador de flashcards *","• Generador de mapas mentales *","• * Para las tareas en las que se haga uso de IA, el l\xedmite mensual ser\xe1 de 1.000.000 de tokens, podr\xe1n ampliarse durante el mes mediante pago","No incluye:","• Planificaci\xf3n de estudios","• Res\xfamenes A2 y A1"],limits:s.qo.usuario.limits,planConfig:s.qo.usuario},pro:{id:"pro",name:"Plan Pro",price:1500,stripeProductId:"prod_SR66U2G7bVJqu3",stripePriceId:"price_1Rae3U07kFn3sIXhkvSuJco1",features:["Incluye:","• Uso de la plataforma durante el mes (una vez finalizado no podr\xe1 volver a acceder hasta renovaci\xf3n)","• Subida de documentos","• Planificaci\xf3n de estudios mediante IA*","• Habla con tu preparador IA *","• Generador de test *","• Generador de flashcards *","• Generador de mapas mentales *","• Generaci\xf3n de Res\xfamenes para A2 y A1","• * Para las tareas en las que se haga uso de IA, el l\xedmite mensual ser\xe1 de 1.000.000 de tokens, podr\xe1n ampliarse durante el mes mediante pago"],limits:s.qo.pro.limits,planConfig:s.qo.pro}};function n(e){return t[e]||null}},2427:(e,a,r)=>{Promise.resolve().then(r.bind(r,9789))},5695:(e,a,r)=>{"use strict";var s=r(8999);r.o(s,"usePathname")&&r.d(a,{usePathname:function(){return s.usePathname}}),r.o(s,"useRouter")&&r.d(a,{useRouter:function(){return s.useRouter}}),r.o(s,"useSearchParams")&&r.d(a,{useSearchParams:function(){return s.useSearchParams}})},9789:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>d});var s=r(5155),t=r(2115),n=r(5695),o=r(1547),i=r(8260);function l(){let e=(0,n.useRouter)(),a=(0,n.useSearchParams)().get("plan")||"free",[r,l]=(0,t.useState)(""),[d,u]=(0,t.useState)(""),[c,m]=(0,t.useState)(""),[p,f]=(0,t.useState)(""),[h,g]=(0,t.useState)(!1),x=(0,o.Md)(a);(0,t.useEffect)(()=>{x||e.push("/")},[x,e]);let b=async s=>{if(s.preventDefault(),!r.trim())return void i.Ay.error("Por favor, ingresa tu email");if(!d.trim())return void i.Ay.error("Por favor, ingresa una contrase\xf1a");if(d.length<6)return void i.Ay.error("La contrase\xf1a debe tener al menos 6 caracteres");if(d!==c)return void i.Ay.error("Las contrase\xf1as no coinciden");g(!0);try{if("free"===a){let s=await fetch("/api/auth/register-free",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:r,password:d,customerName:p})}),t=await s.json();s.ok&&t.success?(i.Ay.success("Registro exitoso. Revisa tu email para confirmar tu cuenta."),e.push("/thank-you?plan=".concat(a,"&email_sent=true"))):429===s.status?i.Ay.error("Demasiados intentos. Int\xe9ntalo en 15 minutos."):i.Ay.error(t.error||"Error al crear la cuenta gratuita")}else{console.log("\uD83D\uDD04 Iniciando nuevo flujo de pre-registro para plan de pago");let e=await fetch("/api/auth/pre-register-paid",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:r,password:d,customerName:p||r.split("@")[0],planId:a})}),s=await e.json();if(!e.ok)return void i.Ay.error(s.error||"Error al crear la cuenta");console.log("✅ Usuario pre-registrado exitosamente:",s.userId);let t=await fetch("/api/stripe/create-checkout-session",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({planId:a,email:r,customerName:p,userId:s.userId})}),n=await t.json();t.ok&&n.url?(console.log("\uD83D\uDD04 Redirigiendo a Stripe Checkout..."),window.location.href=n.url):i.Ay.error(n.error||"Error al crear la sesi\xf3n de pago")}}catch(e){console.error("Error en handleSubmit:",e),i.Ay.error("Error al procesar la solicitud. Por favor, intenta de nuevo.")}finally{g(!1)}};return x?(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"max-w-md mx-auto",children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:x.name}),(0,s.jsxs)("p",{className:"text-2xl font-semibold text-blue-600 mt-2",children:[0===x.price?"Gratis":"€".concat((x.price/100).toFixed(2)),("pro"===a||"usuario"===a)&&x.price>0&&(0,s.jsx)("span",{className:"text-sm text-gray-500",children:"/mes"})]})]}),(0,s.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-3",children:"Caracter\xedsticas incluidas:"}),(0,s.jsx)("ul",{className:"space-y-2",children:x.features.map((e,a)=>(0,s.jsxs)("li",{className:"flex items-center text-sm text-gray-600",children:[(0,s.jsx)("svg",{className:"h-4 w-4 text-green-500 mr-2",fill:"currentColor",viewBox:"0 0 20 20",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})}),e]},a))})]}),(0,s.jsxs)("form",{onSubmit:b,className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email *"}),(0,s.jsx)("input",{type:"email",id:"email",required:!0,value:r,onChange:e=>l(e.target.value),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"<EMAIL>",disabled:h})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Contrase\xf1a *"}),(0,s.jsx)("input",{type:"password",id:"password",required:!0,value:d,onChange:e=>u(e.target.value),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"M\xednimo 6 caracteres",disabled:h,minLength:6})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700",children:"Confirmar Contrase\xf1a *"}),(0,s.jsx)("input",{type:"password",id:"confirmPassword",required:!0,value:c,onChange:e=>m(e.target.value),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"Repite tu contrase\xf1a",disabled:h,minLength:6})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"customerName",className:"block text-sm font-medium text-gray-700",children:"Nombre (opcional)"}),(0,s.jsx)("input",{type:"text",id:"customerName",value:p,onChange:e=>f(e.target.value),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"Tu nombre",disabled:h})]}),(0,s.jsx)("button",{type:"submit",disabled:h,className:"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",children:h?"Procesando...":"free"===a?"Solicitar Acceso Gratuito":"Proceder al Pago"})]})]})]})}):(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:"Cargando detalles del plan o redirigiendo..."})}function d(){return(0,s.jsx)(t.Suspense,{fallback:(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsx)("div",{className:"max-w-md mx-auto",children:(0,s.jsx)("div",{className:"bg-white shadow rounded-lg p-6",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"}),(0,s.jsx)("p",{className:"mt-4 text-gray-600",children:"Cargando..."})]})})})}),children:(0,s.jsx)(l,{})})}}},e=>{var a=a=>e(e.s=a);e.O(0,[8260,8441,1684,7358],()=>a(2427)),_N_E=e.O()}]);