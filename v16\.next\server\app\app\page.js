(()=>{var e={};e.id=4466,e.ids=[4466],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},18307:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var a=s(65239),r=s(48088),i=s(88170),n=s.n(i),o=s(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let d={children:["",{children:["app",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,25380)),"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\app\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\app\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/app/page",pathname:"/app",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25380:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\app\\page.tsx","default")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37922:(e,t,s)=>{"use strict";let a;s.r(t),s.d(t,{default:()=>nH});var r,i,n=s(60687),o=s(43210),l=s(16189),d=s(17019),c=s(84567),u=s(19),m=s(37590);function h({onSelectConversation:e,conversacionActualId:t,onConversationDeleted:s}){let[a,r]=(0,o.useState)([]),[i,l]=(0,o.useState)(!0),[c,h]=(0,o.useState)(""),[x,p]=(0,o.useState)(null),[g,f]=(0,o.useState)(null),[b,y]=(0,o.useState)(""),[v,j]=(0,o.useState)(null),N=e=>{let t=new Date(e),s=Math.floor((new Date().getTime()-t.getTime())/864e5);return 0===s?t.toLocaleTimeString("es-ES",{hour:"2-digit",minute:"2-digit"}):1===s?"Ayer":s<7?`Hace ${s} d\xedas`:t.toLocaleDateString("es-ES",{day:"2-digit",month:"2-digit",year:"2-digit"})},w=e=>e.titulo?e.titulo:`Conversaci\xf3n del ${N(e.creado_en)}`,E=async e=>{let a;if(!x){p(e);try{a=m.oR.loading("Eliminando conversaci\xf3n..."),await (0,u.sq)(e)?(m.oR.success("Conversaci\xf3n eliminada exitosamente",{id:a}),r(t=>t.filter(t=>t.id!==e)),e===t&&s?.()):m.oR.error("Error al eliminar la conversaci\xf3n",{id:a})}catch(e){console.error("Error al eliminar conversaci\xf3n:",e),m.oR.error("Error al eliminar la conversaci\xf3n",{id:a})}finally{p(null),j(null)}}},C=e=>{f(e.id),y(w(e))},S=()=>{f(null),y("")},k=async e=>{let t;if(!b.trim())return void m.oR.error("El t\xedtulo no puede estar vac\xedo");try{t=m.oR.loading("Actualizando t\xedtulo..."),await (0,u.fW)(e,b.trim())?(m.oR.success("T\xedtulo actualizado exitosamente",{id:t}),r(t=>t.map(t=>t.id===e?{...t,titulo:b.trim()}:t)),f(null),y("")):m.oR.error("Error al actualizar el t\xedtulo",{id:t})}catch(e){console.error("Error al actualizar t\xedtulo:",e),m.oR.error("Error al actualizar el t\xedtulo",{id:t})}};return(0,n.jsxs)("div",{className:"w-64 bg-gray-50 border border-gray-200 rounded-lg flex flex-col h-full",children:[(0,n.jsxs)("div",{className:"p-4 border-b border-gray-200",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Conversaciones"}),(0,n.jsxs)("p",{className:"text-sm text-gray-500 mt-1",children:[a.length," conversaci\xf3n",1!==a.length?"es":""]})]}),(0,n.jsx)("div",{className:"flex-1 overflow-y-auto",children:i?(0,n.jsxs)("div",{className:"flex flex-col items-center justify-center py-8",children:[(0,n.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"}),(0,n.jsx)("p",{className:"text-sm text-gray-500 mt-2",children:"Cargando conversaciones..."})]}):c?(0,n.jsx)("div",{className:"p-4",children:(0,n.jsx)("div",{className:"text-red-500 text-sm",children:c})}):0===a.length?(0,n.jsx)("div",{className:"p-4",children:(0,n.jsx)("div",{className:"text-gray-500 text-sm text-center",children:"No hay conversaciones guardadas"})}):(0,n.jsx)("div",{className:"divide-y divide-gray-200",children:a.map(s=>(0,n.jsxs)("div",{className:`p-4 hover:bg-gray-100 transition-colors ${t===s.id?"bg-blue-50 border-r-2 border-blue-500":""}`,children:[(0,n.jsx)("div",{className:"flex items-start justify-between mb-2",children:(0,n.jsx)("div",{className:"flex-1 min-w-0",children:g===s.id?(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)("input",{type:"text",value:b,onChange:e=>y(e.target.value),className:"flex-1 text-sm font-medium bg-white border border-gray-300 rounded px-2 py-1",onKeyDown:e=>{"Enter"===e.key?k(s.id):"Escape"===e.key&&S()},autoFocus:!0}),(0,n.jsx)("button",{onClick:()=>k(s.id),className:"text-green-600 hover:text-green-800",children:(0,n.jsx)(d.YrT,{size:16})}),(0,n.jsx)("button",{onClick:S,className:"text-gray-600 hover:text-gray-800",children:(0,n.jsx)(d.yGN,{size:16})})]}):(0,n.jsx)("h4",{className:"font-medium text-gray-800 truncate cursor-pointer hover:text-blue-600",onClick:()=>e(s.id),title:w(s),children:w(s)})})}),(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsx)("div",{className:"text-xs text-gray-500",children:N(s.actualizado_en)}),g!==s.id&&(0,n.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,n.jsx)("button",{onClick:()=>C(s),className:"p-1 text-gray-400 hover:text-blue-600 transition-colors",title:"Renombrar conversaci\xf3n",children:(0,n.jsx)(d.Pj4,{size:14})}),v===s.id?(0,n.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,n.jsx)("button",{onClick:()=>E(s.id),disabled:x===s.id,className:"p-1 text-red-600 hover:text-red-800 transition-colors",title:"Confirmar eliminaci\xf3n",children:(0,n.jsx)(d.YrT,{size:14})}),(0,n.jsx)("button",{onClick:()=>j(null),className:"p-1 text-gray-400 hover:text-gray-600 transition-colors",title:"Cancelar",children:(0,n.jsx)(d.yGN,{size:14})})]}):(0,n.jsx)("button",{onClick:()=>j(s.id),disabled:x===s.id,className:"p-1 text-gray-400 hover:text-red-600 transition-colors",title:"Eliminar conversaci\xf3n",children:(0,n.jsx)(d.IXo,{size:14})})]})]}),t===s.id&&(0,n.jsx)("div",{className:"text-xs text-blue-600 mt-1 font-medium",children:"Conversaci\xf3n actual"})]},s.id))})})]})}var x=s(27605),p=s(63442),g=s(41835),f=s(65660),b=s(78956),y=s(87373);function v({documentosSeleccionados:e}){let[t,s]=(0,o.useState)([]),[a,r]=(0,o.useState)(!1),[i,l]=(0,o.useState)(""),[d,c]=(0,o.useState)(null),[m,b]=(0,o.useState)(!1),v=(0,o.useRef)(null),{user:j}=(0,f.A)(),[N,w]=(0,o.useState)(null),{register:E,handleSubmit:C,formState:{errors:S},reset:k,setValue:T}=(0,x.mN)({resolver:(0,p.u)(g.oS),defaultValues:{pregunta:"",documentos:e}}),A=async e=>{try{r(!0),await (0,u.vW)(e);let t=(await (0,u.C9)(e)).map(e=>({id:e.id,tipo:e.tipo,contenido:e.contenido,timestamp:new Date(e.timestamp)}));s(t),c(e),l("")}catch(e){console.error("Error al cargar la conversaci\xf3n:",e),l("No se pudo cargar la conversaci\xf3n")}finally{r(!1)}},P=async(e,t)=>{try{b(!0);let s=t||d;if(s){let t=await (0,u.Sl)();return t&&t.id===s||await (0,u.vW)(s),await (0,u.QE)({conversacion_id:s,tipo:e.tipo,contenido:e.contenido}),s}if("usuario"===e.tipo){let t=`Conversaci\xf3n: ${e.contenido.substring(0,50)}${e.contenido.length>50?"...":""}`,s=await (0,u.Yp)(t,!0);if(!s)throw Error("No se pudo crear la conversaci\xf3n");return c(s),await (0,u.QE)({conversacion_id:s,tipo:e.tipo,contenido:e.contenido}),s}throw console.error("❌ ERROR CR\xcdTICO: Intentando guardar mensaje de IA sin conversaci\xf3n activa"),Error("No se puede guardar un mensaje de IA sin una conversaci\xf3n activa")}catch(e){return console.error("Error al guardar el mensaje:",e),null}finally{b(!1)}},R=async()=>{try{r(!0),await (0,u.CM)(),s([]),c(null),l("")}catch(e){console.error("Error al iniciar nueva conversaci\xf3n:",e)}finally{r(!1)}},D=async a=>{r(!0),l("");let i={tipo:"usuario",contenido:a.pregunta,timestamp:new Date};s(e=>[...e,i]),r(!0),l(""),k({pregunta:"",documentos:e});let n=null;try{n=await P(i);let e=await fetch("/api/ai",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({pregunta:i.contenido,documentos:a.documentos})}),r=await e.json(),o="";if(o=r.result?"string"==typeof r.result?r.result:JSON.stringify(r.result):r.error?"string"==typeof r.error?r.error:JSON.stringify(r.error):"Error desconocido al obtener respuesta de la IA.",!n)throw console.error("❌ ERROR: No se pudo obtener el ID de conversaci\xf3n para guardar la respuesta de la IA"),Error("No se pudo guardar la respuesta: conversaci\xf3n no encontrada");d!==n&&c(n);let l={tipo:"ia",contenido:o,timestamp:new Date};if(s(e=>[...e,l]),await P(l,n),0===t.length&&n){let e=`Conversaci\xf3n: ${a.pregunta.substring(0,50)}${a.pregunta.length>50?"...":""}`;await (0,u.fW)(n,e)}}catch(a){console.error("Error al obtener respuesta:",a);let e="Lo siento, ha ocurrido un error al procesar tu pregunta. Por favor, int\xe9ntalo de nuevo.";a instanceof Error&&(e=a.message.includes("API key")?"Error de configuraci\xf3n: La clave de API de Gemini no est\xe1 configurada correctamente.":a.message.includes("network")||a.message.includes("fetch")?"Error de conexi\xf3n: No se pudo conectar con el servicio de IA. Verifica tu conexi\xf3n a internet.":a.message.includes("quota")||a.message.includes("limit")?"Se ha alcanzado el l\xedmite de uso del servicio de IA. Int\xe9ntalo m\xe1s tarde.":`Error: ${a.message}`),l(e);let t={tipo:"ia",contenido:e,timestamp:new Date};s(e=>[...e,t]);try{let e=n||d;e&&await P(t,e)}catch(e){console.error("Error al guardar mensaje de error en DB:",e)}}finally{r(!1)}},M=e=>e.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"});return"free"===N?(0,n.jsx)("div",{className:"mt-6",children:(0,n.jsx)(y.A,{feature:"ai_tutor_chat",benefits:["Chat ilimitado con IA especializada","Respuestas personalizadas a tus documentos","Historial completo de conversaciones","Explicaciones detalladas y ejemplos"],className:"h-[600px]"})}):(0,n.jsxs)("div",{className:"mt-6 flex h-[600px] gap-6",children:[(0,n.jsxs)("div",{className:"flex-1 flex flex-col",children:[(0,n.jsx)("div",{className:"flex justify-start mb-4",children:(0,n.jsxs)("button",{type:"button",onClick:R,className:"bg-green-500 hover:bg-green-600 text-white font-semibold py-2 px-4 rounded inline-flex items-center",disabled:a,children:[(0,n.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-2",viewBox:"0 0 20 20",fill:"currentColor",children:(0,n.jsx)("path",{fillRule:"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z",clipRule:"evenodd"})}),"Nueva conversaci\xf3n"]})}),(0,n.jsx)("div",{ref:v,className:"flex-grow overflow-y-auto mb-4 p-4 border rounded-lg bg-gray-50",style:{height:"calc(100% - 180px)"},children:0===t.length?(0,n.jsx)("div",{className:"flex items-center justify-center h-full text-gray-500",children:(0,n.jsx)("p",{children:"Selecciona documentos y haz una pregunta para comenzar la conversaci\xf3n."})}):(0,n.jsxs)("div",{className:"space-y-4",children:[t.map((e,t)=>(0,n.jsx)("div",{className:`flex ${"usuario"===e.tipo?"justify-end":"justify-start"}`,children:(0,n.jsxs)("div",{className:`max-w-[80%] p-3 rounded-lg ${"usuario"===e.tipo?"bg-blue-500 text-white rounded-br-none":"bg-white border border-gray-300 rounded-bl-none"}`,children:[(0,n.jsx)("div",{className:"whitespace-pre-wrap",children:e.contenido}),(0,n.jsx)("div",{className:`text-xs mt-1 text-right ${"usuario"===e.tipo?"text-blue-100":"text-gray-500"}`,children:M(e.timestamp)})]})},e.id||t)),a&&(0,n.jsx)("div",{className:"flex justify-start",children:(0,n.jsx)("div",{className:"bg-white p-3 rounded-lg border border-gray-300 rounded-bl-none",children:(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)("div",{className:"animate-bounce h-2 w-2 bg-gray-500 rounded-full"}),(0,n.jsx)("div",{className:"animate-bounce h-2 w-2 bg-gray-500 rounded-full",style:{animationDelay:"0.2s"}}),(0,n.jsx)("div",{className:"animate-bounce h-2 w-2 bg-gray-500 rounded-full",style:{animationDelay:"0.4s"}})]})})}),m&&(0,n.jsx)("div",{className:"text-xs text-gray-500 text-center py-1",children:"Guardando conversaci\xf3n..."})]})}),(0,n.jsxs)("form",{onSubmit:C(D),className:"mt-auto",children:[i&&(0,n.jsx)("div",{className:"text-red-500 text-sm mb-2",children:i}),(0,n.jsx)("div",{className:"text-xs text-gray-600 mb-2",children:e.length>0?(0,n.jsxs)("span",{className:"text-green-600",children:["✓ ",e.length," documento",1!==e.length?"s":""," seleccionado",1!==e.length?"s":""]}):(0,n.jsx)("span",{className:"text-red-600",children:"⚠ No hay documentos seleccionados. Selecciona al menos uno para hacer preguntas."})}),(0,n.jsxs)("div",{className:"flex items-end space-x-2",children:[(0,n.jsxs)("div",{className:"flex-grow",children:[(0,n.jsx)("textarea",{id:"pregunta",className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",rows:2,...E("pregunta"),placeholder:"Escribe tu pregunta sobre los documentos seleccionados...",disabled:a,onKeyDown:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),C(D)())}}),S.pregunta&&(0,n.jsx)("p",{className:"text-red-500 text-xs mt-1",children:S.pregunta.message}),(0,n.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Presiona Enter para enviar, Shift+Enter para nueva l\xednea"})]}),(0,n.jsx)("button",{type:"submit",className:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-full h-10 w-10 flex items-center justify-center focus:outline-none focus:shadow-outline disabled:opacity-50 disabled:cursor-not-allowed",disabled:a||0===e.length,title:0===e.length?"Selecciona al menos un documento para hacer una pregunta":"Enviar pregunta",children:a?(0,n.jsxs)("svg",{className:"animate-spin h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,n.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,n.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 008-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}):(0,n.jsx)("svg",{className:"h-5 w-5 text-white",fill:"currentColor",viewBox:"0 0 20 20",children:(0,n.jsx)("path",{d:"M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z"})})})]})]})]}),(0,n.jsx)(h,{onSelectConversation:A,conversacionActualId:d,onConversationDeleted:()=>{s([]),c(null),l("")}})]})}var j=s(85814),N=s.n(j);function w({className:e=""}){let[t,s]=(0,o.useState)(null),[a,r]=(0,o.useState)(!0);if(a)return(0,n.jsxs)("div",{className:`flex items-center space-x-2 text-sm text-gray-500 ${e}`,children:[(0,n.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-gray-400"}),(0,n.jsx)("span",{children:"Cargando l\xedmites..."})]});if(!t)return null;if("paid"===t.plan||-1===t.limit)return(0,n.jsxs)("div",{className:`flex items-center space-x-2 text-sm text-green-600 ${e}`,children:[(0,n.jsx)(d.jH2,{className:"w-4 h-4"}),(0,n.jsx)("span",{children:"Documentos ilimitados"})]});let i=t.current/t.limit*100;return(0,n.jsxs)("div",{className:`space-y-2 ${e}`,children:[(0,n.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)(d.jH2,{className:"w-4 h-4 text-gray-500"}),(0,n.jsxs)("span",{className:"text-gray-700",children:["Documentos: ",t.current,"/",t.limit]})]}),t.isAtLimit&&(0,n.jsxs)("div",{className:"flex items-center space-x-1 text-red-600",children:[(0,n.jsx)(d.y3G,{className:"w-4 h-4"}),(0,n.jsx)("span",{className:"text-xs font-medium",children:"L\xedmite alcanzado"})]})]}),(0,n.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,n.jsx)("div",{className:`h-2 rounded-full transition-all duration-300 ${t.isAtLimit?"bg-red-500":i>80?"bg-yellow-500":"bg-green-500"}`,style:{width:`${Math.min(i,100)}%`}})}),t.isAtLimit?(0,n.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3",children:(0,n.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,n.jsx)(d.y3G,{className:"w-5 h-5 text-red-600 mt-0.5 flex-shrink-0"}),(0,n.jsxs)("div",{className:"flex-1",children:[(0,n.jsx)("p",{className:"text-sm text-red-800 font-medium",children:"L\xedmite de documentos alcanzado"}),(0,n.jsxs)("p",{className:"text-xs text-red-700 mt-1",children:["Has alcanzado el l\xedmite de ",t.limit," documento(s) para el plan gratuito."]}),(0,n.jsxs)(N(),{href:"/upgrade-plan",className:"inline-flex items-center mt-2 px-3 py-1 bg-red-600 text-white text-xs font-medium rounded hover:bg-red-700 transition-colors",children:[(0,n.jsx)(d.ei4,{className:"w-3 h-3 mr-1"}),"Actualizar Plan"]})]})]})}):0===t.remaining?(0,n.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-3",children:(0,n.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,n.jsx)(d.y3G,{className:"w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0"}),(0,n.jsxs)("div",{className:"flex-1",children:[(0,n.jsx)("p",{className:"text-sm text-yellow-800 font-medium",children:"\xdaltimo documento disponible"}),(0,n.jsx)("p",{className:"text-xs text-yellow-700 mt-1",children:"Este es tu \xfaltimo documento disponible en el plan gratuito."})]})]})}):(0,n.jsxs)("div",{className:"text-xs text-gray-600",children:["Te quedan ",t.remaining," documento(s) disponible(s)"]})]})}function E({onSuccess:e}){let[t,s]=(0,o.useState)(""),[a,r]=(0,o.useState)(""),[i,l]=(0,o.useState)(""),[d,c]=(0,o.useState)(""),[h,x]=(0,o.useState)(null),p=(0,o.useRef)(null),[g,f]=(0,o.useState)(!1),[b,y]=(0,o.useState)({texto:"",tipo:""}),v=async n=>{let o;if(n.preventDefault(),f(!0),y({texto:"",tipo:""}),h){o=m.Ay.loading(`Subiendo ${h.name}...`);let a=new FormData;a.append("file",h),a.append("titulo",t),i&&a.append("categoria",i),d&&a.append("numero_tema",d);try{let t=await fetch("/api/document/upload",{method:"POST",body:a});if(t.ok){let a=await t.json();m.Ay.success(`Documento "${h.name}" subido y procesado con ID: ${a.documentId}.`,{id:o}),s(""),r(""),l(""),c(""),x(null),p.current&&(p.current.value=""),e&&e()}else{let e=await t.json();403===t.status&&e.needsUpgrade?m.Ay.error(`${e.error}: ${e.reason}`,{id:o,duration:6e3}):m.Ay.error(`Error al subir archivo: ${e.error||t.statusText}`,{id:o})}}catch(e){console.error("Error en la subida del archivo:",e),m.Ay.error("Error de conexi\xf3n o inesperado al subir el archivo.",{id:o})}finally{f(!1)}}else{if(!t.trim()||!a.trim()){y({texto:"El t\xedtulo y el contenido son obligatorios si no se selecciona un archivo.",tipo:"error"}),f(!1);return}o=m.Ay.loading("Guardando documento manualmente...");try{let n={titulo:t,contenido:a,categoria:i||void 0,numero_tema:d?parseInt(d):void 0};await (0,u.hE)(n)?(m.Ay.success("Documento guardado manualmente correctamente.",{id:o}),s(""),r(""),l(""),c(""),e&&e()):m.Ay.error("Error al guardar el documento manualmente.",{id:o})}catch(e){console.error("Error al guardar documento manualmente:",e),m.Ay.error("Ha ocurrido un error al guardar el documento manualmente.",{id:o})}finally{f(!1)}}};return(0,n.jsxs)("div",{className:"bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4",children:[(0,n.jsx)("h2",{className:"text-xl font-bold mb-4",children:"Subir nuevo documento"}),(0,n.jsx)(w,{className:"mb-6"}),(0,n.jsxs)("form",{onSubmit:v,className:"space-y-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"titulo",className:"block text-gray-700 text-sm font-bold mb-2",children:"T\xedtulo:"}),(0,n.jsx)("input",{type:"text",id:"titulo",className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",value:t,onChange:e=>s(e.target.value),placeholder:"T\xedtulo del documento (se autocompleta con el nombre del archivo)",disabled:g,required:!0})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"categoria",className:"block text-gray-700 text-sm font-bold mb-2",children:"Categor\xeda:"}),(0,n.jsxs)("select",{id:"categoria",className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",value:i,onChange:e=>l(e.target.value),disabled:g,children:[(0,n.jsx)("option",{value:"",children:"Seleccionar categor\xeda"}),(0,n.jsx)("option",{value:"tema",children:"Tema"}),(0,n.jsx)("option",{value:"anexo",children:"Anexo"}),(0,n.jsx)("option",{value:"resumen",children:"Resumen"})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"numeroTema",className:"block text-gray-700 text-sm font-bold mb-2",children:"N\xfamero de tema:"}),(0,n.jsx)("input",{type:"number",id:"numeroTema",className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",value:d,onChange:e=>c(e.target.value),placeholder:"Opcional",min:"1",disabled:g})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"contenido",className:"block text-gray-700 text-sm font-bold mb-2",children:"Contenido (manual o previsualizaci\xf3n de .txt):"}),(0,n.jsx)("textarea",{id:"contenido",className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",rows:10,value:a,onChange:e=>r(e.target.value),placeholder:"Escribe o pega el contenido aqu\xed, o selecciona un archivo .txt para previsualizarlo. Para PDFs, el contenido se extraer\xe1 autom\xe1ticamente.",disabled:g})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"archivo",className:"block text-gray-700 text-sm font-bold mb-2",children:"O sube un archivo (.txt o .pdf):"}),(0,n.jsx)("input",{type:"file",id:"archivo",ref:p,className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",onChange:e=>{let t=e.target.files?.[0];if(y({texto:"",tipo:""}),!t)return void x(null);if(t.size>5242880){m.Ay.error(`El archivo es demasiado grande. El tama\xf1o m\xe1ximo es 5MB.`),p.current&&(p.current.value=""),x(null);return}if(x(t),s(t.name),"text/plain"===t.type){let e=new FileReader;e.onload=e=>{e.target?.result?(r(e.target.result),m.Ay.success("Archivo TXT le\xeddo y listo para vista previa.")):(m.Ay.error("Error al leer el archivo TXT."),r(""))},e.onerror=()=>{m.Ay.error("Error al leer el archivo TXT."),r("")},e.readAsText(t)}else"application/pdf"===t.type?(r("El contenido se extraer\xe1 del PDF al guardar. Puedes editar el t\xedtulo si es necesario."),m.Ay.success("Archivo PDF seleccionado. El contenido se procesar\xe1 en el servidor.")):(r("Este tipo de archivo no tiene previsualizaci\xf3n. El contenido se procesar\xe1 en el servidor si es compatible."),(0,m.Ay)(`Archivo ${t.name} seleccionado. El tipo no es previsualizable.`))},accept:".txt,.pdf",disabled:g}),(0,n.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:["Solo archivos .txt o .pdf. M\xe1ximo ",5,"MB."]})]}),b.texto&&(0,n.jsx)("div",{className:`p-3 rounded ${"error"===b.tipo?"bg-red-100 text-red-700":"bg-green-100 text-green-700"}`,children:b.texto}),(0,n.jsx)("div",{children:(0,n.jsx)("button",{type:"submit",className:"bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",disabled:g,children:g?"Guardando...":"Guardar documento"})})]})]})}var C=s(93667);let S=()=>{let{addTask:e,updateTask:t,getTasksByType:s}=(0,C.M)(),a=(0,o.useCallback)(async(s,a,r)=>{let{peticion:i,contextos:n,cantidad:o,onComplete:l,onError:d}=r,c=e({type:s,title:i.length>50?`${i.substring(0,50)}...`:i});try{t(c,{status:"processing"}),console.log(`🚀 Iniciando generaci\xf3n de ${s}:`,{action:a,peticion:i,contextos:n?.length,cantidad:o});let e=await fetch("/api/ai",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:a,peticion:i,contextos:n,cantidad:o})});if(console.log(`📡 Respuesta recibida para ${s}:`,e.status,e.ok),!e.ok){let t=await e.text();throw console.error(`❌ Error en la API para ${s}:`,e.status,t),Error(`Error en la API: ${e.status} - ${t}`)}let r=await e.json();console.log(`✅ Resultado obtenido para ${s}:`,r);let{result:d}=r;return t(c,{status:"completed",result:d,progress:100}),l&&setTimeout(()=>l(d),0),c}catch(s){let e=s instanceof Error?s.message:"Error desconocido";throw t(c,{status:"error",error:e}),d&&setTimeout(()=>d(e),0),s}},[e,t]),r=(0,o.useCallback)(async e=>a("mapa-mental","generarMapaMental",e),[a]),i=(0,o.useCallback)(async e=>a("test","generarTest",e),[a]),n=(0,o.useCallback)(async e=>a("flashcards","generarFlashcards",e),[a]),l=(0,o.useCallback)(async e=>{let{documento:t,instrucciones:s,onComplete:r,onError:i}=e,n={action:"generarResumen",peticion:`${t.titulo}|${t.categoria||""}|${t.numero_tema||""}|${s}`,contextos:[t.contenido]};return a("resumen","generarResumen",{peticion:n.peticion,contextos:n.contextos,onComplete:r,onError:i})},[a]),d=(0,o.useCallback)(async s=>{let{temarioId:a,onComplete:r,onError:i}=s,n=e({type:"plan-estudios",title:"Generando plan de estudios personalizado"});try{t(n,{status:"processing"});let e=await fetch("/api/ai",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"generarPlanEstudios",peticion:a,contextos:[]})});if(!e.ok)throw Error(`Error en la API: ${e.status}`);let{result:s}=await e.json();return t(n,{status:"completed",result:s,progress:100}),r&&setTimeout(()=>r(s),0),n}catch(s){let e=s instanceof Error?s.message:"Error desconocido";throw t(n,{status:"error",error:e}),i&&setTimeout(()=>i(e),0),s}},[e,t]);return{generateMapaMental:r,generateTest:i,generateFlashcards:n,generatePlanEstudios:d,generateResumen:l,isGenerating:(0,o.useCallback)(e=>s(e).some(e=>"pending"===e.status||"processing"===e.status),[s]),getActiveTask:(0,o.useCallback)(e=>s(e).find(e=>"pending"===e.status||"processing"===e.status),[s])}},k=({taskType:e,onResult:t,onError:s})=>{let{tasks:a}=(0,C.M)(),[r,i]=(0,o.useState)(new Set),n=(0,o.useRef)(t),l=(0,o.useRef)(s);return n.current=t,l.current=s,(0,o.useEffect)(()=>{let t=a.filter(t=>t.type===e&&"completed"===t.status&&!r.has(t.id)&&t.result),s=a.filter(t=>t.type===e&&"error"===t.status&&!r.has(t.id)&&t.error);if(t.length>0){let e=t[t.length-1];i(t=>{let s=new Set(t);return s.add(e.id),s}),n.current&&setTimeout(()=>{n.current?.(e.result)},0)}if(s.length>0){let e=s[s.length-1];i(t=>{let s=new Set(t);return s.add(e.id),s}),l.current&&setTimeout(()=>{l.current?.(e.error)},0)}},[a,e,r]),{resetProcessed:(0,o.useCallback)(()=>{i(new Set)},[])}};function T({className:e=""}){let[t,s]=(0,o.useState)(!1);return(0,n.jsxs)("div",{className:`relative ${e}`,children:[(0,n.jsxs)("button",{onClick:()=>s(!t),className:"inline-flex items-center px-3 py-1 text-sm text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-md transition-colors",title:"Ayuda sobre mapas mentales",children:[(0,n.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-1",viewBox:"0 0 20 20",fill:"currentColor",children:(0,n.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z",clipRule:"evenodd"})}),"\xbfC\xf3mo usar?"]}),t&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-40",onClick:()=>s(!1)}),(0,n.jsx)("div",{className:"fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 sm:w-96 bg-white border border-gray-200 rounded-lg shadow-xl z-50 p-4 max-h-80 sm:max-h-96 overflow-y-auto",children:(0,n.jsxs)("div",{className:"space-y-3",children:[(0,n.jsxs)("div",{className:"flex justify-between items-center",children:[(0,n.jsx)("h3",{className:"font-semibold text-gray-800",children:"Gu\xeda de Mapas Mentales"}),(0,n.jsx)("button",{onClick:()=>s(!1),className:"text-gray-400 hover:text-gray-600",children:(0,n.jsx)("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,n.jsxs)("div",{className:"space-y-2 text-sm text-gray-600",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h4",{className:"font-medium text-gray-700 mb-1",children:"\uD83D\uDCDD Generar"}),(0,n.jsx)("p",{className:"text-xs",children:"Describe el mapa mental basado en tus documentos."})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h4",{className:"font-medium text-gray-700 mb-1",children:"\uD83D\uDD0D Vista Previa"}),(0,n.jsx)("p",{className:"text-xs",children:"Revisa el resultado antes de expandir."})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h4",{className:"font-medium text-gray-700 mb-1",children:"\uD83D\uDDA5️ Pantalla Completa"}),(0,n.jsx)("p",{className:"text-xs",children:"Bot\xf3n azul para mejor visualizaci\xf3n."})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h4",{className:"font-medium text-gray-700 mb-1",children:"⌨️ Controles"}),(0,n.jsxs)("ul",{className:"list-disc list-inside space-y-0.5 ml-2 text-xs",children:[(0,n.jsxs)("li",{children:[(0,n.jsx)("kbd",{className:"px-1 py-0.5 bg-gray-100 rounded text-xs",children:"ESC"})," para salir"]}),(0,n.jsx)("li",{children:"Clic fuera para cerrar"}),(0,n.jsx)("li",{children:"Zoom y pan en el mapa"}),(0,n.jsx)("li",{children:"Clic en nodos para expandir"})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h4",{className:"font-medium text-gray-700 mb-1",children:"\uD83D\uDCBE Descargar"}),(0,n.jsx)("p",{className:"text-xs",children:"Guarda como archivo HTML interactivo."})]})]}),(0,n.jsx)("div",{className:"pt-2 border-t border-gray-100",children:(0,n.jsxs)("p",{className:"text-xs text-gray-500",children:["\uD83D\uDCA1 ",(0,n.jsx)("strong",{children:"Consejo:"})," Los mapas son interactivos con zoom y navegaci\xf3n."]})})]})})]})]})}function A(){let{validateAndExecute:e}=function(){let{isFreeAccount:e,status:t,canPerformAction:s}=function(){let{user:e,isLoading:t}=(0,f.A)(),[s,a]=(0,o.useState)({isFreeAccount:!1,status:null,alerts:[],usageWarnings:[],recommendations:[],upgradeUrl:"/upgrade-plan",loading:!0,error:null}),r=(0,o.useCallback)(async()=>{if(!e||t)return void a(e=>({...e,loading:!1}));try{a(e=>({...e,loading:!0,error:null}));let e=await fetch("/api/auth/free-account-status"),t=await e.json();if(!e.ok){if(404===e.status)return void a({isFreeAccount:!1,status:null,alerts:[],usageWarnings:[],recommendations:[],upgradeUrl:"/upgrade-plan",loading:!1,error:null});throw Error(t.error||"Error obteniendo estado")}a({isFreeAccount:t.isFreeAccount,status:t.status,alerts:t.alerts||[],usageWarnings:t.usageWarnings||[],recommendations:t.recommendations||[],upgradeUrl:t.upgradeUrl||"/upgrade-plan",loading:!1,error:null})}catch(e){console.error("Error obteniendo estado de cuenta gratuita:",e),a(t=>({...t,loading:!1,error:e instanceof Error?e.message:"Error desconocido"}))}},[e,t]),i=(0,o.useCallback)((e,t=1)=>!s.isFreeAccount||!s.status||!!s.status.isActive&&(s.status.usage[e]||0)+t<=(s.status.limits[e]||0),[s.isFreeAccount,s.status]),n=(0,o.useCallback)(async()=>{await r()},[r]);return{...s,refresh:n,canPerformAction:i}}(),a=(0,o.useCallback)(async(a,r=1,i)=>{if(!e)try{let e=await i();return{success:!0,result:e}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Error desconocido"}}if(!s(a,r)){let e=t?.usage[a]||0,s=t?.limits[a]||0;return t?.isActive?{success:!1,error:`Has alcanzado el l\xedmite de ${a} (${e}/${s}). Actualiza tu plan para continuar.`}:{success:!1,error:"Tu cuenta gratuita ha expirado. Actualiza tu plan para continuar."}}try{let e=await i();return{success:!0,result:e}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Error desconocido"}}},[e,t,s]);return{isFreeAccount:e,status:t,validateAndExecute:a,canPerformAction:s}}();return{executeWithGuard:async(t,s,a=1)=>e(t,a,s)}}function P(){let[e,t]=(0,o.useState)("free"),[s,a]=(0,o.useState)(!0),[r,i]=(0,o.useState)(null),{user:n}=(0,f.A)();return{plan:e,isLoading:s,error:r}}function R({documentosSeleccionados:e}){let[t,s]=(0,o.useState)(null),[a,r]=(0,o.useState)(!1),[i,l]=(0,o.useState)(!1),{generateMapaMental:c,isGenerating:u,getActiveTask:h}=S(),{getTask:y}=(0,C.M)(),{executeWithGuard:v}=A(),{user:j}=(0,f.A)(),{plan:w,isLoading:E}=P(),R=h("mapa-mental"),D=u("mapa-mental");k({taskType:"mapa-mental",onResult:e=>{s(e),m.oR.success("\xa1Mapa mental generado exitosamente!")},onError:e=>{m.oR.error(`Error al generar mapa mental: ${e}`)}});let{register:M,handleSubmit:$,formState:{errors:L}}=(0,x.mN)({resolver:(0,p.u)(g.MZ),defaultValues:{peticion:""}}),F=async t=>{let s=e.map(e=>e.contenido);console.log("\uD83D\uDDFA️ Iniciando generaci\xf3n de mapa mental:",{peticion:t.peticion,documentos:e.length,contextosLength:s.length});let a=await v("mindMaps",async()=>{let e=await c({peticion:t.peticion,contextos:s});return console.log("✅ Tarea de mapa mental creada:",e),e},1);a.success?m.oR.success("Generaci\xf3n iniciada en segundo plano. Puedes continuar usando la aplicaci\xf3n.",{duration:4e3}):(console.error("❌ Error al generar mapa mental:",a.error),m.oR.error(a.error||"Error al iniciar la generaci\xf3n del mapa mental"))},V=()=>{if(!t)return;let e=new Blob([t],{type:"text/html"}),s=URL.createObjectURL(e),a=document.createElement("a");a.href=s,a.download="mapa-mental.html",document.body.appendChild(a),a.click(),setTimeout(()=>{document.body.removeChild(a),URL.revokeObjectURL(s)},0)},_=()=>{r(!1)};return(0,n.jsxs)("div",{className:"mt-8 border-t pt-8",children:[(0,n.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,n.jsx)("h2",{className:"text-xl font-bold",children:"Generador de Mapas Mentales"}),(0,n.jsx)(T,{})]}),!E&&"free"===w&&(0,n.jsx)("div",{className:"mb-6 p-4 bg-purple-50 border border-purple-200 rounded-lg",children:(0,n.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,n.jsx)(d.F5$,{className:"w-5 h-5 text-purple-600 mt-0.5"}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"font-medium text-purple-900",children:"L\xedmites del Plan Gratuito"}),(0,n.jsxs)("p",{className:"text-sm text-purple-700 mt-1",children:["M\xe1ximo ",b.qo.free.limits.mindMapsForTrial," mapas mentales durante el per\xedodo de prueba. Para generar mapas mentales ilimitados,",(0,n.jsx)(N(),{href:"/upgrade-plan",className:"font-medium underline hover:text-purple-800 ml-1",children:"actualiza tu plan"}),"."]})]})]})}),(0,n.jsxs)("form",{onSubmit:$(F),className:"space-y-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"peticion",className:"block text-gray-700 text-sm font-bold mb-2",children:"Describe el mapa mental que deseas generar:"}),(0,n.jsx)("input",{id:"peticion",type:"text",...M("peticion"),disabled:D,className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",placeholder:"Ej: Genera un mapa mental sobre los conceptos principales del tema 1"}),L.peticion&&(0,n.jsx)("span",{className:"text-red-500 text-sm",children:L.peticion.message}),(0,n.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"La IA generar\xe1 un mapa mental basado en los documentos seleccionados y tu petici\xf3n."})]}),(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsx)("button",{type:"submit",className:"bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline disabled:opacity-50",disabled:D||0===e.length,children:D?"Generando en segundo plano...":"Generar Mapa Mental"}),R&&(0,n.jsxs)("div",{className:"text-sm text-blue-600 bg-blue-50 px-3 py-2 rounded-lg",children:[(0,n.jsx)("span",{className:"font-medium",children:"Generando:"})," ",R.title]})]})]}),D&&(0,n.jsx)("div",{className:"mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg",children:(0,n.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,n.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"}),(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-blue-800 font-medium",children:"Generando mapa mental en segundo plano"}),(0,n.jsx)("p",{className:"text-blue-600 text-sm",children:"Puedes continuar usando otras funciones de la aplicaci\xf3n"})]})]})}),t&&(0,n.jsxs)("div",{className:"mt-6",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Vista previa:"}),(0,n.jsx)("div",{className:"bg-gray-100 p-4 rounded-lg border overflow-hidden",style:{maxHeight:"500px"},children:(0,n.jsx)("iframe",{srcDoc:t,title:"Vista previa del mapa mental",className:"w-full h-96 border-0",sandbox:"allow-scripts allow-same-origin"})}),(0,n.jsxs)("div",{className:"flex justify-between items-center mt-2",children:[(0,n.jsx)("p",{className:"text-sm text-gray-500",children:"Vista previa limitada. Usa pantalla completa o descarga para mejor experiencia."}),(0,n.jsxs)("div",{className:"flex gap-2",children:[(0,n.jsxs)("button",{type:"button",onClick:()=>{r(!0)},className:"bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline flex items-center",children:[(0,n.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-2",viewBox:"0 0 20 20",fill:"currentColor",children:(0,n.jsx)("path",{fillRule:"evenodd",d:"M3 4a1 1 0 011-1h4a1 1 0 010 2H6.414l2.293 2.293a1 1 0 11-1.414 1.414L5 6.414V8a1 1 0 01-2 0V4zm9 1a1 1 0 010-2h4a1 1 0 011 1v4a1 1 0 01-2 0V6.414l-2.293 2.293a1 1 0 11-1.414-1.414L13.586 5H12zm-9 7a1 1 0 012 0v1.586l2.293-2.293a1 1 0 111.414 1.414L6.414 15H8a1 1 0 010 2H4a1 1 0 01-1-1v-4zm13-1a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 010-2h1.586l-2.293-2.293a1 1 0 111.414-1.414L15 13.586V12a1 1 0 011-1z",clipRule:"evenodd"})}),"Pantalla Completa"]}),(0,n.jsxs)("button",{type:"button",onClick:V,className:"bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline flex items-center",children:[(0,n.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-2",viewBox:"0 0 20 20",fill:"currentColor",children:(0,n.jsx)("path",{fillRule:"evenodd",d:"M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z",clipRule:"evenodd"})}),"Descargar Mapa Mental"]})]})]})]}),a&&t&&(0,n.jsxs)("div",{className:"fixed inset-0 z-50 bg-black bg-opacity-90 flex items-center justify-center animate-fadeIn",children:[(0,n.jsxs)("div",{className:"relative w-full h-full max-w-none max-h-none p-4 animate-scaleIn",children:[(0,n.jsxs)("div",{className:"absolute top-4 left-4 right-4 z-10 flex justify-between items-center bg-white bg-opacity-90 backdrop-blur-sm rounded-lg px-4 py-2 shadow-lg",children:[(0,n.jsx)("div",{className:"flex items-center space-x-4",children:(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-800",children:"Mapa Mental - Vista Completa"})}),(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsxs)("button",{onClick:V,className:"bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm flex items-center",title:"Descargar mapa mental",children:[(0,n.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-1",viewBox:"0 0 20 20",fill:"currentColor",children:(0,n.jsx)("path",{fillRule:"evenodd",d:"M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z",clipRule:"evenodd"})}),"Descargar"]}),(0,n.jsxs)("button",{onClick:_,className:"bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm flex items-center",title:"Cerrar pantalla completa",children:[(0,n.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-1",viewBox:"0 0 20 20",fill:"currentColor",children:(0,n.jsx)("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})}),"Cerrar"]})]})]}),(0,n.jsx)("div",{className:"w-full h-full pt-16 pb-4",children:(0,n.jsx)("iframe",{srcDoc:t,title:"Mapa mental en pantalla completa",className:"w-full h-full border-0 rounded-lg shadow-2xl bg-white",sandbox:"allow-scripts allow-same-origin"})})]}),(0,n.jsx)("div",{className:"absolute inset-0 -z-10",onClick:_,"aria-label":"Cerrar pantalla completa"})]})]})}function D({documentosSeleccionados:e}){let[t,s]=(0,o.useState)(""),[a,r]=(0,o.useState)(""),[i,l]=(0,o.useState)([]),[c,h]=(0,o.useState)(!1),[y,v]=(0,o.useState)(0),[j,w]=(0,o.useState)(!1),[E,T]=(0,o.useState)([]),[R,D]=(0,o.useState)("nueva"),[M,$]=(0,o.useState)(!1),[L,F]=(0,o.useState)(""),{generateFlashcards:V,isGenerating:_,getActiveTask:z}=S(),{getTask:I}=(0,C.M)(),{executeWithGuard:O}=A(),{user:B}=(0,f.A)(),{plan:U,isLoading:G}=P();z("flashcards");let q=_("flashcards");k({taskType:"flashcards",onResult:e=>{l(e),m.oR.success("\xa1Flashcards generadas exitosamente!")},onError:e=>{m.oR.error(`Error al generar flashcards: ${e}`)}});let{register:H,handleSubmit:W,formState:{errors:Y}}=(0,x.mN)({resolver:(0,p.u)(g.oO),defaultValues:{peticion:"",cantidad:10}}),X=async()=>{$(!0);try{let e=await (0,u.oE)();T(e)}catch(e){console.error("Error al cargar colecciones:",e),m.oR.error("No se pudieron cargar las colecciones existentes.")}finally{$(!1)}},K=async a=>{let r=e.map(e=>e.contenido);l([]),h(!1);let i=await O("flashcards",async()=>(await V({peticion:a.peticion,contextos:r,cantidad:a.cantidad}),t||s(`Flashcards: ${a.peticion.substring(0,50)}${a.peticion.length>50?"...":""}`),!0),a.cantidad);i.success?m.oR.success("Generaci\xf3n iniciada en segundo plano. Puedes continuar usando la aplicaci\xf3n.",{duration:4e3}):m.oR.error(i.error||"Error al iniciar la generaci\xf3n de flashcards")},Z=async()=>{if(0===i.length)return void F("No hay flashcards para guardar");if("nueva"===R&&!t.trim())return void F("Por favor, proporciona un t\xedtulo para la nueva colecci\xf3n");if("nueva"!==R&&""===R)return void F("Por favor, selecciona una colecci\xf3n existente");F("");try{let e;if("nueva"===R){if(!(e=await (0,u.qJ)(t,a)))throw Error("No se pudo crear la colecci\xf3n")}else e=R;let s=i.map(t=>({coleccion_id:e,pregunta:t.pregunta,respuesta:t.respuesta}));if(!await (0,u.yK)(s))throw Error("No se pudieron guardar las flashcards");h(!0),"nueva"===R&&await X()}catch(e){console.error("Error al guardar las flashcards:",e),F("Ha ocurrido un error al guardar las flashcards. Por favor, int\xe9ntalo de nuevo.")}},J=()=>{w(!j)};return(0,n.jsxs)("div",{className:"mt-8 border-t pt-8",children:[(0,n.jsx)("h2",{className:"text-xl font-bold mb-4",children:"Generador de Flashcards"}),!G&&"free"===U&&(0,n.jsx)("div",{className:"mb-6 p-4 bg-orange-50 border border-orange-200 rounded-lg",children:(0,n.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,n.jsx)(d.F5$,{className:"w-5 h-5 text-orange-600 mt-0.5"}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"font-medium text-orange-900",children:"L\xedmites del Plan Gratuito"}),(0,n.jsxs)("p",{className:"text-sm text-orange-700 mt-1",children:["M\xe1ximo ",b.qo.free.limits.flashcardsForTrial," flashcards durante el per\xedodo de prueba. Para generar flashcards ilimitadas,",(0,n.jsx)(N(),{href:"/upgrade-plan",className:"font-medium underline hover:text-orange-800 ml-1",children:"actualiza tu plan"}),"."]})]})]})}),(0,n.jsxs)("form",{onSubmit:W(K),className:"space-y-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"peticion",className:"block text-gray-700 text-sm font-bold mb-2",children:"Describe las flashcards que deseas generar:"}),(0,n.jsx)("textarea",{id:"peticion",className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",rows:3,...H("peticion"),placeholder:"Ej: Genera flashcards sobre los conceptos principales del tema 1",disabled:q}),Y.peticion&&(0,n.jsx)("span",{className:"text-red-500 text-sm",children:Y.peticion.message}),(0,n.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"La IA generar\xe1 flashcards basadas en los documentos seleccionados y tu petici\xf3n."})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"cantidad",className:"block text-gray-700 text-sm font-bold mb-2",children:"N\xfamero de flashcards:"}),(0,n.jsx)("input",{id:"cantidad",type:"number",min:"1",max:"30",className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",...H("cantidad",{valueAsNumber:!0}),disabled:q}),Y.cantidad&&(0,n.jsx)("span",{className:"text-red-500 text-sm",children:Y.cantidad.message}),(0,n.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"Especifica cu\xe1ntas flashcards quieres generar (entre 1 y 30)."})]}),L&&(0,n.jsx)("div",{className:"text-red-500 text-sm",children:L}),(0,n.jsx)("div",{children:(0,n.jsx)("button",{type:"submit",className:"bg-orange-500 hover:bg-orange-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",disabled:q||0===e.length,children:q?"Generando...":"Generar Flashcards"})})]}),q&&(0,n.jsxs)("div",{className:"mt-4 text-center",children:[(0,n.jsx)("p",{className:"text-gray-600",children:"Generando flashcards, por favor espera..."}),(0,n.jsx)("div",{className:"mt-2 flex justify-center",children:(0,n.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"})})]}),i.length>0&&(0,n.jsxs)("div",{className:"mt-6",children:[(0,n.jsxs)("h3",{className:"text-lg font-semibold mb-4",children:["Flashcards generadas (",i.length,")"]}),!c&&(0,n.jsxs)("div",{className:"bg-gray-100 p-4 rounded-lg mb-6",children:[(0,n.jsx)("h4",{className:"font-medium mb-2",children:"Guardar flashcards"}),(0,n.jsxs)("div",{className:"mb-4",children:[(0,n.jsx)("label",{htmlFor:"tipoColeccion",className:"block text-sm font-medium text-gray-700 mb-1",children:"\xbfD\xf3nde quieres guardar estas flashcards?"}),(0,n.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)("input",{type:"radio",id:"nuevaColeccion",name:"tipoColeccion",value:"nueva",checked:"nueva"===R,onChange:()=>D("nueva"),className:"mr-2",disabled:q}),(0,n.jsx)("label",{htmlFor:"nuevaColeccion",className:"text-sm text-gray-700",children:"Crear nueva colecci\xf3n"})]}),(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)("input",{type:"radio",id:"coleccionExistente",name:"tipoColeccion",value:"existente",checked:"nueva"!==R,onChange:()=>{E.length>0?D(E[0].id):D("")},className:"mr-2",disabled:q||0===E.length}),(0,n.jsxs)("label",{htmlFor:"coleccionExistente",className:"text-sm text-gray-700",children:["A\xf1adir a una colecci\xf3n existente",0===E.length&&(0,n.jsx)("span",{className:"text-gray-500 ml-2",children:"(No hay colecciones disponibles)"})]})]})]})]}),"nueva"===R&&(0,n.jsxs)("div",{className:"space-y-3",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"tituloColeccion",className:"block text-sm font-medium text-gray-700 mb-1",children:"T\xedtulo de la nueva colecci\xf3n:"}),(0,n.jsx)("input",{type:"text",id:"tituloColeccion",className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",value:t,onChange:e=>s(e.target.value),disabled:q})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"descripcionColeccion",className:"block text-sm font-medium text-gray-700 mb-1",children:"Descripci\xf3n (opcional):"}),(0,n.jsx)("textarea",{id:"descripcionColeccion",className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",rows:2,value:a,onChange:e=>r(e.target.value),disabled:q})]})]}),"nueva"!==R&&E.length>0&&(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"coleccionExistenteSelect",className:"block text-sm font-medium text-gray-700 mb-1",children:"Selecciona una colecci\xf3n:"}),(0,n.jsx)("select",{id:"coleccionExistenteSelect",className:"shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",value:R,onChange:e=>D(e.target.value),disabled:q,children:E.map(e=>(0,n.jsx)("option",{value:e.id,children:e.titulo},e.id))})]}),(0,n.jsx)("div",{className:"mt-4",children:(0,n.jsx)("button",{type:"button",onClick:Z,className:"bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",disabled:!1,children:"Guardar flashcards"})})]}),c&&(0,n.jsxs)("div",{className:"bg-green-100 text-green-800 p-4 rounded-lg mb-6",children:[(0,n.jsx)("p",{className:"font-medium",children:"nueva"===R?"\xa1Nueva colecci\xf3n creada correctamente!":"\xa1Flashcards a\xf1adidas a la colecci\xf3n correctamente!"}),(0,n.jsxs)("p",{className:"text-sm mt-1",children:["Puedes acceder a ","nueva"===R?"ella":"las flashcards",' desde la secci\xf3n de "Mis Flashcards".']})]}),(0,n.jsxs)("div",{className:"bg-white border rounded-lg shadow-md p-6 mb-4",children:[(0,n.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,n.jsx)("button",{onClick:()=>{y>0&&(v(y-1),w(!1))},disabled:0===y,className:`p-2 rounded-full ${0===y?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-200"}`,children:(0,n.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})})}),(0,n.jsxs)("span",{className:"text-gray-600",children:[y+1," de ",i.length]}),(0,n.jsx)("button",{onClick:()=>{y<i.length-1&&(v(y+1),w(!1))},disabled:y===i.length-1,className:`p-2 rounded-full ${y===i.length-1?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-200"}`,children:(0,n.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})})]}),(0,n.jsx)("div",{className:"min-h-[200px] flex items-center justify-center cursor-pointer",onClick:J,children:(0,n.jsx)("div",{className:"text-center p-4 w-full",children:j?(0,n.jsxs)("div",{children:[(0,n.jsx)("div",{className:"font-semibold text-lg mb-2",children:i[y].pregunta}),(0,n.jsx)("div",{className:"border-t pt-4 text-left whitespace-pre-wrap",children:i[y].respuesta})]}):(0,n.jsx)("div",{className:"font-semibold text-lg",children:i[y].pregunta})})}),(0,n.jsx)("div",{className:"mt-4 text-center",children:(0,n.jsx)("button",{onClick:J,className:"bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:j?"Ocultar respuesta":"Mostrar respuesta"})})]}),(0,n.jsxs)("div",{className:"mt-6",children:[(0,n.jsx)("h4",{className:"font-medium mb-2",children:"Todas las flashcards:"}),(0,n.jsx)("div",{className:"space-y-2",children:i.map((e,t)=>(0,n.jsx)("div",{className:`p-3 border rounded-lg cursor-pointer hover:bg-gray-50 ${t===y?"border-blue-500 bg-blue-50":""}`,onClick:()=>{v(t),w(!1)},children:(0,n.jsx)("p",{className:"font-medium",children:e.pregunta})},t))})]})]})]})}var M=s(9275),$=s(48363),L=s(34705);async function F(){try{let{user:e}=await (0,L.iF)();if(!e)return console.error("No hay usuario autenticado"),[];let{data:t,error:s}=await $.N.from("resumenes").select("*").eq("user_id",e.id).order("creado_en",{ascending:!1});if(s)return console.error("Error al obtener res\xfamenes:",s),[];return t||[]}catch(e){return console.error("Error al obtener res\xfamenes:",e),[]}}async function V(e){try{let{user:t}=await (0,L.iF)();if(!t)return console.log("No hay usuario autenticado para verificar resumen"),!1;console.log(`Verificando si existe resumen para documento ${e} y usuario ${t.id}`);let{data:s,error:a}=await $.N.from("resumenes").select("id").eq("user_id",t.id).eq("documento_id",e).maybeSingle();if(a)return console.error("Error al verificar resumen existente:",a),!1;let r=!!s;return console.log(`Resultado verificaci\xf3n resumen: ${r?"existe":"no existe"}`),r}catch(e){return console.error("Error al verificar resumen existente:",e),!1}}async function _(e,t,s,a){try{let{user:r}=await (0,L.iF)();if(!r)return console.error("No hay usuario autenticado para guardar resumen"),null;if(console.log(`Intentando guardar resumen para documento ${e} y usuario ${r.id}`),await V(e))return console.error("Ya existe un resumen para este documento"),null;let i={user_id:r.id,documento_id:e,titulo:t.trim(),contenido:s.trim(),instrucciones:a?.trim()||null};console.log("Datos del resumen a insertar:",{...i,contenido:`${i.contenido.substring(0,100)}...`});let{data:n,error:o}=await $.N.from("resumenes").insert([i]).select().single();if(o)return console.error("Error al guardar resumen en Supabase:",o),null;if(!n?.id)return console.error("No se recibi\xf3 ID del resumen guardado"),null;return console.log(`Resumen guardado exitosamente con ID: ${n.id}`),n.id}catch(e){return console.error("Error al guardar resumen:",e),null}}async function z(e){try{let{user:t}=await (0,L.iF)();if(!t)return console.error("No hay usuario autenticado"),!1;let{error:s}=await $.N.from("resumenes").delete().eq("id",e).eq("user_id",t.id);if(s)return console.error("Error al eliminar resumen:",s),!1;return!0}catch(e){return console.error("Error al eliminar resumen:",e),!1}}async function I(e,t){try{let{user:s}=await (0,L.iF)();if(!s)return console.error("No hay usuario autenticado para guardar resumen editado"),!1;console.log(`Guardando versi\xf3n editada del resumen ${e}`);let{error:a}=await $.N.from("resumenes").update({contenido_editado:t.trim(),editado:!0,fecha_edicion:new Date().toISOString(),actualizado_en:new Date().toISOString()}).eq("id",e).eq("user_id",s.id);if(a)return console.error("Error al guardar resumen editado en Supabase:",a),!1;return console.log("✅ Resumen editado guardado exitosamente"),!0}catch(e){return console.error("Error al guardar resumen editado:",e),!1}}let O=M.z.object({instrucciones:M.z.string().min(10,"Las instrucciones deben tener al menos 10 caracteres")});function B({documentosSeleccionados:e,onSummaryGenerated:t}){let[s,a]=(0,o.useState)(null),[r,i]=(0,o.useState)(0),{generateResumen:l,isGenerating:d,getActiveTask:c}=S(),{register:u,handleSubmit:h,formState:{errors:g},reset:f}=(0,x.mN)({resolver:(0,p.u)(O),defaultValues:{instrucciones:"Crea un resumen completo y estructurado del tema, organizando los conceptos principales de manera clara y did\xe1ctica."}}),b=e=>e&&e.contenido?Math.max(15,Math.min(120,Math.ceil(e.contenido.split(/\s+/).length/100))):30,y=1===e.length,v=e[0],j=v?v?v.titulo&&0!==v.titulo.trim().length?v.contenido&&0!==v.contenido.trim().length?v.contenido.trim().length<50?{valido:!1,error:"El contenido del documento es demasiado corto para generar un resumen \xfatil"}:{valido:!0}:{valido:!1,error:"El documento debe tener contenido"}:{valido:!1,error:"El documento debe tener un t\xedtulo"}:{valido:!1,error:"No se ha proporcionado ning\xfan documento"}:{valido:!1,error:"No hay documento seleccionado"};c("resumen");let N=d("resumen"),w=async e=>{if(!y||!v||!j.valido)return void m.oR.error("No se puede generar el resumen. Verifica que tengas exactamente un documento seleccionado.");try{if(console.log("\uD83D\uDE80 Iniciando generaci\xf3n de resumen..."),i(b(v)),console.log("\uD83D\uDD0D Verificando si ya existe resumen..."),await V(v.id)){console.log("⚠️ Ya existe un resumen para este documento"),m.oR.error("Ya existe un resumen para este documento. Solo se permite un resumen por tema.");return}console.log("✅ No existe resumen previo, continuando..."),await l({documento:v,instrucciones:e.instrucciones,onComplete:async s=>{console.log("✅ Resumen generado, guardando en Supabase...");let a=await _(v.id,`Resumen: ${v.titulo}`,s,e.instrucciones);a?(console.log("✅ Resumen guardado exitosamente con ID:",a),t?.(a),f()):(console.error("❌ Error al guardar el resumen - no se recibi\xf3 ID"),m.oR.error("Error al guardar el resumen en la base de datos"))},onError:e=>{console.error("❌ Error en generaci\xf3n de resumen:",e),i(0)}})}catch(t){console.error("Error al iniciar generaci\xf3n de resumen:",t);let e=t instanceof Error?t.message:"Error al generar el resumen";m.oR.error(e),i(0)}};return(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,n.jsx)("h3",{className:"font-semibold text-blue-900 mb-2",children:"\uD83D\uDCC4 Generaci\xf3n de Res\xfamenes"}),y?j.valido?(0,n.jsxs)("div",{className:"text-blue-700",children:[(0,n.jsx)("p",{className:"font-medium",children:"✅ Documento seleccionado:"}),(0,n.jsxs)("p",{className:"text-sm mt-1",children:[(0,n.jsx)("strong",{children:v.titulo}),v.numero_tema&&` (Tema ${v.numero_tema})`]}),(0,n.jsxs)("p",{className:"text-xs mt-1 text-blue-600",children:["Contenido: ~",v.contenido.split(/\s+/).length," palabras"]})]}):(0,n.jsxs)("div",{className:"text-red-700",children:[(0,n.jsx)("p",{className:"font-medium",children:"⚠️ Documento no v\xe1lido"}),(0,n.jsx)("p",{className:"text-sm mt-1",children:j.error})]}):(0,n.jsxs)("div",{className:"text-red-700",children:[(0,n.jsx)("p",{className:"font-medium",children:"⚠️ Selecci\xf3n incorrecta"}),(0,n.jsx)("p",{className:"text-sm mt-1",children:0===e.length?"Debes seleccionar exactamente un documento para generar un resumen.":`Tienes ${e.length} documentos seleccionados. Solo se permite generar un resumen por tema.`})]})]}),y&&j.valido&&(0,n.jsxs)("form",{onSubmit:h(w),className:"space-y-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"instrucciones",className:"block text-gray-700 text-sm font-bold mb-2",children:"Instrucciones para el resumen:"}),(0,n.jsx)("textarea",{id:"instrucciones",...u("instrucciones"),disabled:N,rows:4,className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline resize-vertical",placeholder:"Describe c\xf3mo quieres que sea el resumen..."}),g.instrucciones&&(0,n.jsx)("span",{className:"text-red-500 text-sm",children:g.instrucciones.message}),(0,n.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"Especifica el enfoque, nivel de detalle, o aspectos particulares que quieres que incluya el resumen."})]}),(0,n.jsx)("button",{type:"submit",disabled:N,className:"bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline flex items-center",children:N?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,n.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,n.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Generando resumen...",r>0&&` (~${r}s)`]}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-2",viewBox:"0 0 20 20",fill:"currentColor",children:[(0,n.jsx)("path",{fillRule:"evenodd",d:"M4 4a2 2 0 012-2h8a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 0v12h8V4H6z",clipRule:"evenodd"}),(0,n.jsx)("path",{fillRule:"evenodd",d:"M8 6a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1zM8 9a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1zM8 12a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z",clipRule:"evenodd"})]}),"Generar Resumen"]})}),N&&(0,n.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-3",children:(0,n.jsxs)("p",{className:"text-yellow-800 text-sm",children:[(0,n.jsx)("strong",{children:"⏳ Generando resumen..."}),(0,n.jsx)("br",{}),"La IA est\xe1 analizando el contenido y creando un resumen estructurado. Este proceso puede tardar ",r>0?`aproximadamente ${r} segundos`:"unos momentos",".",(0,n.jsx)("br",{}),(0,n.jsx)("em",{children:"Puedes navegar a otras pesta\xf1as mientras se genera."})]})})]}),s&&(0,n.jsxs)("div",{className:"mt-6",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold mb-3",children:"\uD83D\uDCCB Resumen Generado"}),(0,n.jsx)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4 max-h-96 overflow-y-auto",children:(0,n.jsx)("div",{className:"prose prose-sm max-w-none",dangerouslySetInnerHTML:{__html:s.replace(/\n/g,"<br />")}})}),(0,n.jsx)("p",{className:"text-sm text-gray-500 mt-2",children:"✅ Resumen guardado exitosamente. Puedes acceder a \xe9l desde la secci\xf3n de res\xfamenes."})]})]})}function U({refreshTrigger:e}){let[t,s]=(0,o.useState)([]),[a,r]=(0,o.useState)(!0),[i,l]=(0,o.useState)(null),[c,u]=(0,o.useState)(!1),[h,x]=(0,o.useState)(null),[p,g]=(0,o.useState)(!1),[f,b]=(0,o.useState)("editada"),y=async()=>{try{r(!0);let e=await F();s(e)}catch(e){console.error("Error al cargar res\xfamenes:",e),m.oR.error("Error al cargar los res\xfamenes")}finally{r(!1)}},v=async(e,t)=>{if(confirm(`\xbfEst\xe1s seguro de que quieres eliminar el resumen "${t}"?`))try{await z(e)?(m.oR.success("Resumen eliminado exitosamente"),await y()):m.oR.error("Error al eliminar el resumen")}catch(e){console.error("Error al eliminar resumen:",e),m.oR.error("Error al eliminar el resumen")}},j=e=>{l(e),e.editado&&e.contenido_editado?b("editada"):b("original"),u(!0)},N=async e=>{if(confirm(`\xbfEst\xe1s seguro de que quieres editar el resumen "${e.titulo}"? Esta acci\xf3n crear\xe1 una versi\xf3n condensada del resumen original.`))try{x(e.id),m.oR.loading("Editando resumen con IA...",{id:"editing-summary"});let t=e.contenido_editado||e.contenido,s=await fetch("/api/ai",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"editarResumen",contextos:[t]})});if(!s.ok)throw Error("Error en la respuesta del servidor");let a=await s.json();if(!a.result)throw Error("No se recibi\xf3 resultado de la edici\xf3n");if(await I(e.id,a.result))m.oR.success("Resumen editado exitosamente",{id:"editing-summary"}),await y();else throw Error("Error al guardar el resumen editado")}catch(e){console.error("Error al editar resumen:",e),m.oR.error("Error al editar el resumen",{id:"editing-summary"})}finally{x(null)}},w=e=>{try{let t,s="";c&&i&&i.id===e.id?(t="editada"===f&&i.contenido_editado?i.contenido_editado:i.contenido,s="editada"===f&&i.contenido_editado?" (Versi\xf3n Editada)":""):(t=e.contenido_editado||e.contenido,s=e.contenido_editado?" (Versi\xf3n Editada)":"");let a=function(e,t,s){let a=(e=e.replace(/\r\n/g,"\n").replace(/\r/g,"\n")).split(/\n\s*\n/),r="";for(let e of a){if(!(e=e.trim()))continue;let t=e.replace(/^### (.*$)/gim,"<h3>$1</h3>").replace(/^## (.*$)/gim,"<h2>$1</h2>").replace(/^# (.*$)/gim,"<h1>$1</h1>").replace(/\*\*(.*?)\*\*/g,"<strong>$1</strong>").replace(/\*(.*?)\*/g,"<em>$1</em>").replace(/^> (.*$)/gim,"<blockquote>$1</blockquote>").replace(/^\- (.*$)/gim,"<li>$1</li>").replace(/^\* (.*$)/gim,"<li>$1</li>").replace(/^\+ (.*$)/gim,"<li>$1</li>").replace(/\n/g," ");t.includes("<li>")?r+=t=t.replace(/(<li>.*?<\/li>)/g,e=>`<ul>${e}</ul>`):t.startsWith("<h1>")||t.startsWith("<h2>")||t.startsWith("<h3>")||t.startsWith("<blockquote>")?r+=t:r+=`<p>${t}</p>`}let i="";if(t&&(i+=`<h1>${t}</h1>`),s){if(i+='<div class="metadata">',s.createdAt){let e=new Date(s.createdAt).toLocaleDateString("es-ES",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"});i+=`<p><strong>Fecha de creaci\xf3n:</strong> ${e}</p>`}s.author&&(i+=`<p><strong>Autor:</strong> ${s.author}</p>`),s.instructions&&(i+=`<p><strong>Instrucciones utilizadas:</strong> ${s.instructions}</p>`),i+="</div>"}return i+=r}(t,e.titulo+s,{createdAt:e.creado_en,instructions:e.instrucciones||void 0,author:"OposiAI"}),r=window.open("","_blank");r&&(r.document.write(`
          <html>
            <head>
              <title>${e.titulo}</title>
              <style>
                * { box-sizing: border-box; }
                body {
                  font-family: Arial, sans-serif;
                  margin: 20mm;
                  line-height: 1.6;
                  color: #333;
                  font-size: 12px;
                }
                h1 {
                  color: #2563eb;
                  border-bottom: 2px solid #2563eb;
                  padding-bottom: 10px;
                  margin: 0 0 20px 0;
                  font-size: 24px;
                  text-align: left;
                }
                h2 {
                  color: #1e40af;
                  border-bottom: 1px solid #cbd5e1;
                  padding-bottom: 5px;
                  margin: 25px 0 15px 0;
                  font-size: 20px;
                  text-align: left;
                }
                h3 {
                  color: #1e40af;
                  margin: 20px 0 10px 0;
                  font-size: 16px;
                  text-align: left;
                }
                p {
                  margin: 12px 0;
                  text-align: justify;
                  text-justify: inter-word;
                  hyphens: auto;
                  word-wrap: break-word;
                }
                strong {
                  color: #1e40af;
                  font-weight: bold;
                }
                em {
                  font-style: italic;
                  color: #64748b;
                }
                ul, ol {
                  margin: 12px 0;
                  padding-left: 20px;
                }
                li {
                  margin: 6px 0;
                  text-align: justify;
                }
                blockquote {
                  margin: 15px 0;
                  padding: 12px 15px;
                  background-color: #f8fafc;
                  border-left: 4px solid #2563eb;
                  font-style: italic;
                  text-align: justify;
                }
                .metadata {
                  font-size: 11px;
                  color: #64748b;
                  margin-bottom: 25px;
                  padding: 12px;
                  background-color: #f8fafc;
                  border-radius: 5px;
                  border: 1px solid #e2e8f0;
                }
                .metadata p {
                  margin: 4px 0;
                  text-align: left;
                }
                @media print {
                  body {
                    margin: 20mm;
                    font-size: 12px;
                  }
                  .metadata {
                    background-color: #f9f9f9;
                    border: 1px solid #ddd;
                  }
                  blockquote {
                    background-color: #f9f9f9;
                  }
                }
              </style>
            </head>
            <body>
              ${a}
            </body>
          </html>
        `),r.document.close(),r.focus(),r.print())}catch(e){console.error("Error al imprimir:",e),m.oR.error("Error al preparar la impresi\xf3n")}},E=e=>new Date(e).toLocaleDateString("es-ES",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"});return a?(0,n.jsxs)("div",{className:"flex justify-center items-center py-8",children:[(0,n.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),(0,n.jsx)("span",{className:"ml-2 text-gray-600",children:"Cargando res\xfamenes..."})]}):(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{className:"flex justify-between items-center",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-800",children:"\uD83D\uDCDA Mis Res\xfamenes"}),(0,n.jsxs)("button",{onClick:y,className:"text-blue-600 hover:text-blue-800 text-sm flex items-center",children:[(0,n.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-1",viewBox:"0 0 20 20",fill:"currentColor",children:(0,n.jsx)("path",{fillRule:"evenodd",d:"M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z",clipRule:"evenodd"})}),"Actualizar"]})]}),(0,n.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,n.jsxs)("div",{className:"flex items-start",children:[(0,n.jsx)("div",{className:"flex-shrink-0",children:(0,n.jsx)(d.Pj4,{className:"h-5 w-5 text-blue-600 mt-0.5"})}),(0,n.jsxs)("div",{className:"ml-3",children:[(0,n.jsx)("h4",{className:"text-sm font-medium text-blue-800",children:"✨ Funci\xf3n de Edici\xf3n con IA"}),(0,n.jsx)("p",{className:"text-sm text-blue-700 mt-1",children:'Puedes usar la IA para condensar y refinar tus res\xfamenes. La edici\xf3n mantiene toda la informaci\xf3n esencial mientras elimina redundancias, creando un texto m\xe1s conciso y estructurado (3.200-3.800 palabras). El resumen original se conserva siempre. Para acceder a ambos, haz clic en "Ver" en el resumen.'})]})]})}),0===t.length?(0,n.jsxs)("div",{className:"text-center py-8 bg-gray-50 rounded-lg border border-gray-200",children:[(0,n.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-12 w-12 text-gray-400 mx-auto mb-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),(0,n.jsx)("p",{className:"text-gray-600 mb-2",children:"No tienes res\xfamenes creados"}),(0,n.jsx)("p",{className:"text-sm text-gray-500",children:"Selecciona un documento y genera tu primer resumen para empezar a estudiar de manera m\xe1s eficiente."})]}):(0,n.jsx)("div",{className:"grid gap-4",children:t.map(e=>(0,n.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow",children:(0,n.jsxs)("div",{className:"flex justify-between items-start",children:[(0,n.jsxs)("div",{className:"flex-1",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,n.jsx)("h4",{className:"font-medium text-gray-900",children:e.titulo}),e.editado&&(0,n.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800",children:"✨ Editado"})]}),(0,n.jsxs)("p",{className:"text-sm text-gray-600 mb-2",children:["Creado: ",E(e.creado_en),e.editado&&e.fecha_edicion&&(0,n.jsxs)("span",{className:"ml-2 text-green-600",children:["• Editado: ",E(e.fecha_edicion)]})]}),e.instrucciones&&(0,n.jsxs)("p",{className:"text-xs text-gray-500 mb-2 italic",children:['"',e.instrucciones.substring(0,100),e.instrucciones.length>100?"...":"",'"']}),(0,n.jsxs)("p",{className:"text-xs text-gray-400",children:["Contenido original: ~",e.contenido.split(/\s+/).length," palabras",e.contenido_editado&&(0,n.jsxs)("span",{className:"ml-2 text-green-600",children:["• Versi\xf3n editada: ~",e.contenido_editado.split(/\s+/).length," palabras"]})]})]}),(0,n.jsxs)("div",{className:"flex flex-wrap gap-2 ml-4",children:[(0,n.jsxs)("button",{onClick:()=>j(e),className:"flex items-center gap-1 bg-blue-600 hover:bg-blue-700 text-white text-xs px-3 py-1 rounded focus:outline-none focus:shadow-outline transition-colors",title:"Ver resumen completo",children:[(0,n.jsx)(d.Vap,{className:"w-3 h-3"}),"Ver"]}),(0,n.jsxs)("button",{onClick:()=>N(e),disabled:h===e.id,className:"flex items-center gap-1 bg-green-600 hover:bg-green-700 disabled:bg-green-400 text-white text-xs px-3 py-1 rounded focus:outline-none focus:shadow-outline transition-colors",title:"Editar resumen con IA",children:[(0,n.jsx)(d.Pj4,{className:"w-3 h-3"}),h===e.id?"Editando...":"Editar"]}),(0,n.jsxs)("button",{onClick:()=>w(e),className:"flex items-center gap-1 bg-gray-600 hover:bg-gray-700 text-white text-xs px-3 py-1 rounded focus:outline-none focus:shadow-outline transition-colors",title:"Imprimir resumen",children:[(0,n.jsx)(d.Mvz,{className:"w-3 h-3"}),"Imprimir"]}),(0,n.jsxs)("button",{onClick:()=>v(e.id,e.titulo),className:"flex items-center gap-1 bg-red-600 hover:bg-red-700 text-white text-xs px-3 py-1 rounded focus:outline-none focus:shadow-outline transition-colors",title:"Eliminar resumen",children:[(0,n.jsx)(d.IXo,{className:"w-3 h-3"}),"Eliminar"]})]})]})},e.id))}),c&&i&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-40",onClick:()=>u(!1)}),(0,n.jsxs)("div",{className:"fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-11/12 max-w-4xl max-h-5/6 bg-white rounded-lg shadow-xl z-50 overflow-hidden",children:[(0,n.jsxs)("div",{className:"flex justify-between items-center p-4 border-b border-gray-200",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:i.titulo}),(0,n.jsx)("button",{onClick:()=>u(!1),className:"text-gray-400 hover:text-gray-600",children:(0,n.jsx)("svg",{className:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),i.editado&&i.contenido_editado&&(0,n.jsxs)("div",{className:"p-4 border-b border-gray-200 flex space-x-2",children:[(0,n.jsx)("button",{onClick:()=>b("editada"),className:`px-3 py-1 rounded text-xs font-medium transition-colors
                    ${"editada"===f?"bg-green-600 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"}`,title:"Versi\xf3n concisa refinada por IA",children:"✨ Versi\xf3n Editada"}),(0,n.jsx)("button",{onClick:()=>b("original"),className:`px-3 py-1 rounded text-xs font-medium transition-colors
                    ${"original"===f?"bg-blue-600 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"}`,title:"Primer borrador generado por IA",children:"\uD83D\uDCDC Versi\xf3n Original"})]}),(0,n.jsxs)("div",{className:"p-4 overflow-y-auto max-h-96",children:[(0,n.jsxs)("div",{className:"mb-4 text-sm text-gray-600",children:[(0,n.jsxs)("p",{children:[(0,n.jsx)("strong",{children:"Creado:"})," ",E(i.creado_en)]}),i.editado&&i.fecha_edicion&&(0,n.jsxs)("p",{children:[(0,n.jsx)("strong",{children:"Editado:"})," ",E(i.fecha_edicion)]}),i.instrucciones&&(0,n.jsxs)("p",{children:[(0,n.jsx)("strong",{children:"Instrucciones:"})," ",i.instrucciones]}),i.editado&&i.contenido_editado&&(0,n.jsx)("div",{className:"mt-2 p-2 bg-gray-100 border border-gray-200 rounded text-xs",children:"editada"===f?(0,n.jsx)("p",{className:"text-green-800 font-medium",children:"✨ Mostrando versi\xf3n editada por IA (condensada y refinada)."}):(0,n.jsx)("p",{className:"text-blue-800 font-medium",children:"\uD83D\uDCDC Mostrando versi\xf3n original."})})]}),(0,n.jsx)("div",{className:"prose prose-sm max-w-none",dangerouslySetInnerHTML:{__html:("editada"===f&&i.contenido_editado?i.contenido_editado:i.contenido).replace(/\*\*(.*?)\*\*/g,"<strong>$1</strong>").replace(/\*(.*?)\*/g,"<em>$1</em>").replace(/^### (.*$)/gim,"<h3>$1</h3>").replace(/^## (.*$)/gim,"<h2>$1</h2>").replace(/^# (.*$)/gim,"<h1>$1</h1>").replace(/^> (.*$)/gim,"<blockquote>$1</blockquote>").replace(/^\- (.*$)/gim,"<li>$1</li>").replace(/\n/g,"<br />")}})]}),(0,n.jsxs)("div",{className:"p-4 border-t border-gray-200 flex justify-between items-center",children:[(0,n.jsx)("div",{className:"flex gap-2",children:(0,n.jsxs)("button",{onClick:()=>w(i),className:"flex items-center gap-2 bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded focus:outline-none focus:shadow-outline transition-colors",children:[(0,n.jsx)(d.Mvz,{className:"w-4 h-4"}),"Imprimir Versi\xf3n Actual"]})}),(0,n.jsx)("button",{onClick:()=>u(!1),className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded focus:outline-none focus:shadow-outline transition-colors",children:"Cerrar"})]})]})]})]})}function G({onDocumentDeleted:e}){let[t,s]=(0,o.useState)([]),[a,r]=(0,o.useState)(!0),[i,l]=(0,o.useState)(null),[c,h]=(0,o.useState)(null),x=async t=>{let a;l(t);try{a=m.Ay.loading("Eliminando documento..."),await (0,u.Q3)(t)?(m.Ay.success("Documento eliminado exitosamente",{id:a}),s(e=>e.filter(e=>e.id!==t)),e?.()):m.Ay.error("Error al eliminar el documento",{id:a})}catch(e){console.error("Error al eliminar documento:",e),m.Ay.error("Error al eliminar el documento",{id:a})}finally{l(null),h(null)}},p=e=>new Date(e).toLocaleDateString("es-ES",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"});return a?(0,n.jsxs)("div",{className:"flex items-center justify-center p-8",children:[(0,n.jsx)(d.jTZ,{className:"animate-spin text-blue-500 mr-2"}),(0,n.jsx)("span",{children:"Cargando documentos..."})]}):0===t.length?(0,n.jsxs)("div",{className:"text-center p-8 text-gray-500",children:[(0,n.jsx)(d.jH2,{className:"mx-auto text-4xl mb-4"}),(0,n.jsx)("p",{children:"No hay documentos subidos a\xfan."}),(0,n.jsx)("p",{className:"text-sm",children:"Sube tu primer documento para comenzar."})]}):(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:["Gestionar Documentos (",t.length,")"]}),(0,n.jsx)("div",{className:"space-y-3",children:t.map(e=>(0,n.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow",children:(0,n.jsxs)("div",{className:"flex items-start justify-between",children:[(0,n.jsxs)("div",{className:"flex-1",children:[(0,n.jsxs)("div",{className:"flex items-center mb-2",children:[(0,n.jsx)(d.jH2,{className:"text-blue-500 mr-2 flex-shrink-0"}),(0,n.jsx)("h4",{className:"font-medium text-gray-900 truncate",children:e.titulo}),e.numero_tema&&(0,n.jsxs)("span",{className:"ml-2 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full",children:["Tema ",e.numero_tema]}),e.categoria&&(0,n.jsx)("span",{className:"ml-2 px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full",children:e.categoria})]}),(0,n.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,n.jsxs)("p",{children:["Subido: ",p(e.creado_en)]}),(0,n.jsxs)("p",{children:["Caracteres: ",e.contenido.length.toLocaleString()]}),e.tipo_original&&(0,n.jsxs)("p",{children:["Tipo: ",e.tipo_original.toUpperCase()]})]})]}),(0,n.jsx)("button",{onClick:()=>h(e.id),disabled:i===e.id,className:"ml-4 p-2 text-red-500 hover:bg-red-50 rounded-lg transition-colors disabled:opacity-50",title:"Eliminar documento",children:i===e.id?(0,n.jsx)(d.jTZ,{className:"animate-spin"}):(0,n.jsx)(d.IXo,{})})]})},e.id))}),c&&(0,n.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,n.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-md w-full mx-4",children:[(0,n.jsxs)("div",{className:"flex items-center mb-4",children:[(0,n.jsx)(d.eHT,{className:"text-red-500 mr-3"}),(0,n.jsx)("h3",{className:"text-lg font-semibold",children:"Confirmar eliminaci\xf3n"})]}),(0,n.jsx)("p",{className:"text-gray-600 mb-6",children:"\xbfEst\xe1s seguro de que quieres eliminar este documento? Esta acci\xf3n no se puede deshacer."}),(0,n.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,n.jsx)("button",{onClick:()=>h(null),className:"px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors",children:"Cancelar"}),(0,n.jsx)("button",{onClick:()=>x(c),className:"px-4 py-2 bg-red-500 text-white hover:bg-red-600 rounded-lg transition-colors",children:"Eliminar"})]})]})})]})}var q=s(24932),H=s(49318);let W=({colecciones:e,coleccionSeleccionada:t,onSeleccionarColeccion:s,onEliminarColeccion:a,isLoading:r,deletingId:i})=>{if(r)return(0,n.jsx)("div",{className:"flex justify-center items-center h-40",children:(0,n.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"})});if(0===e.length)return(0,n.jsxs)("div",{className:"text-center p-8 border-2 border-dashed border-gray-300 rounded-lg",children:[(0,n.jsx)(d._Y7,{className:"mx-auto text-6xl text-gray-400 mb-4"}),(0,n.jsx)("p",{className:"text-gray-500 text-lg",children:"No hay colecciones de flashcards disponibles."}),(0,n.jsx)("p",{className:"text-sm text-gray-400 mt-1",children:"Crea una nueva colecci\xf3n para empezar a estudiar."})]});let o=(e,t)=>{e.stopPropagation(),a(t)};return(0,n.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6",children:e.map(e=>(0,n.jsxs)("div",{className:`border rounded-lg p-4 cursor-pointer transition-colors flex flex-col justify-between ${t?.id===e.id?"border-orange-500 bg-orange-50 shadow-lg":"border-gray-200 hover:bg-gray-50"}`,onClick:()=>s(e),children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"font-semibold text-lg mb-2",children:e.titulo}),e.descripcion&&(0,n.jsx)("p",{className:"text-sm text-gray-600 mb-2 break-words",children:e.descripcion}),(0,n.jsxs)("div",{className:"flex justify-between items-center mb-1",children:[(0,n.jsxs)("p",{className:"text-sm text-gray-500",children:["Flashcards: ","number"==typeof e.numero_flashcards?e.numero_flashcards:"N/A"]}),"number"==typeof e.pendientes_hoy&&e.pendientes_hoy>0&&(0,n.jsxs)("span",{className:"text-xs px-2 py-1 bg-orange-100 text-orange-800 rounded-full",children:[e.pendientes_hoy," para hoy"]})]}),(0,n.jsxs)("p",{className:"text-xs text-gray-400",children:["Creada: ",new Date(e.creado_en).toLocaleDateString("es-ES")]})]}),(0,n.jsxs)("div",{className:"flex justify-between items-center mt-4",children:[(0,n.jsx)("button",{onClick:t=>{t.stopPropagation(),s(e)},className:"bg-orange-500 hover:bg-orange-600 text-white font-semibold py-2 px-4 rounded text-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-opacity-50",children:"Estudiar"}),(0,n.jsxs)("button",{onClick:t=>o(t,e.id),disabled:i===e.id,className:"bg-red-500 hover:bg-red-600 text-white font-semibold py-2 px-4 rounded text-sm flex items-center focus:outline-none focus:ring-2 focus:ring-red-400 focus:ring-opacity-50 disabled:opacity-50",title:"Eliminar colecci\xf3n",children:[i===e.id?(0,n.jsx)(d.jTZ,{size:14,className:"animate-spin mr-2"}):(0,n.jsx)(d.IXo,{size:14,className:"mr-2"}),"Eliminar"]})]})]},e.id))})},Y=({coleccion:e,flashcards:t,estadisticas:s,isLoading:a,onStartStudy:r,onShowStudyOptions:i,onShowStatistics:o,onEditFlashcard:l,onDeleteFlashcard:c,deletingFlashcardId:u})=>(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"text-xl font-semibold mb-4",children:e.titulo}),s&&(0,n.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg mb-6",children:[(0,n.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Estad\xedsticas"}),(0,n.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-2 text-sm",children:[(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("div",{className:"font-semibold text-blue-600",children:s.total}),(0,n.jsx)("div",{className:"text-gray-500",children:"Total"})]}),(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("div",{className:"font-semibold text-orange-600",children:s.paraHoy}),(0,n.jsx)("div",{className:"text-gray-500",children:"Para hoy"})]}),(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("div",{className:"font-semibold text-gray-600",children:s.nuevas}),(0,n.jsx)("div",{className:"text-gray-500",children:"Nuevas"})]}),(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("div",{className:"font-semibold text-yellow-600",children:s.aprendiendo}),(0,n.jsx)("div",{className:"text-gray-500",children:"Aprendiendo"})]}),(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("div",{className:"font-semibold text-green-600",children:s.aprendidas}),(0,n.jsx)("div",{className:"text-gray-500",children:"Aprendidas"})]})]})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 mb-6",children:[(0,n.jsx)("button",{onClick:r,className:"bg-orange-500 hover:bg-orange-600 text-white font-bold py-3 px-4 rounded-lg focus:outline-none focus:shadow-outline transition-colors",children:`Estudiar (${s?s.paraHoy:0} para hoy)`}),(0,n.jsx)("button",{onClick:i,className:"bg-purple-500 hover:bg-purple-600 text-white font-bold py-3 px-4 rounded-lg focus:outline-none focus:shadow-outline transition-colors",children:"Opciones de estudio"}),(0,n.jsx)("button",{onClick:o,className:"bg-blue-500 hover:bg-blue-600 text-white font-bold py-3 px-4 rounded-lg focus:outline-none focus:shadow-outline transition-colors",children:"Ver estad\xedsticas"})]}),a?(0,n.jsx)("div",{className:"flex justify-center items-center h-40",children:(0,n.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"})}):0===t.length?(0,n.jsx)("div",{className:"text-center p-4",children:(0,n.jsx)("p",{className:"text-gray-500",children:"No hay flashcards en esta colecci\xf3n."})}):(0,n.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:t.map((e,t)=>(0,n.jsxs)("div",{className:`p-3 border rounded-lg ${e.debeEstudiar?"border-orange-300 bg-orange-50":"border-gray-200"}`,children:[(0,n.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,n.jsx)("p",{className:"font-medium flex-1 pr-2",children:e.pregunta}),(0,n.jsxs)("div",{className:"flex items-center space-x-1 flex-shrink-0",children:[l&&(0,n.jsx)("button",{onClick:()=>l(e),className:"p-1 text-blue-600 hover:text-blue-800 hover:bg-blue-100 rounded",title:"Editar flashcard",children:(0,n.jsx)(d.WXf,{size:14})}),c&&(0,n.jsx)("button",{onClick:()=>c(e.id),disabled:u===e.id,className:"p-1 text-red-600 hover:text-red-800 hover:bg-red-100 rounded disabled:opacity-50",title:"Eliminar flashcard",children:u===e.id?(0,n.jsx)("div",{className:"animate-spin rounded-full h-3.5 w-3.5 border-b-2 border-red-600"}):(0,n.jsx)(d.IXo,{size:14})})]})]}),(0,n.jsxs)("div",{className:"flex justify-between items-center",children:[(0,n.jsxs)("p",{className:"text-xs text-gray-500",children:["Tarjeta ",t+1]}),e.progreso&&(0,n.jsx)("span",{className:`text-xs px-2 py-1 rounded-full ${"nuevo"===e.progreso.estado?"bg-blue-100 text-blue-800":"aprendiendo"===e.progreso.estado?"bg-yellow-100 text-yellow-800":"repasando"===e.progreso.estado?"bg-orange-100 text-orange-800":"bg-green-100 text-green-800"}`,children:e.progreso.estado})]})]},e.id))})]}),X=({isOpen:e,onClose:t,onSelectStudyType:s,estadisticas:a,isLoading:r=!1})=>{let i=[{tipo:"dificiles",label:"M\xe1s dif\xedciles",descripcion:"Tarjetas que has marcado como dif\xedciles m\xe1s frecuentemente",icon:(0,n.jsx)(d.lrG,{className:"text-red-600"}),color:"red-600",bgColor:"bg-red-100",hoverBgColor:"hover:bg-red-200"},{tipo:"aleatorias",label:"Aleatorias",descripcion:"Selecci\xf3n aleatoria de tarjetas de la colecci\xf3n",icon:(0,n.jsx)(d.jTZ,{className:"text-purple-600"}),color:"purple-600",bgColor:"bg-purple-100",hoverBgColor:"hover:bg-purple-200"},{tipo:"no-recientes",label:"No estudiadas recientemente",descripcion:"Tarjetas que no has revisado en mucho tiempo",icon:(0,n.jsx)(d.Ohp,{className:"text-orange-600"}),color:"orange-600",bgColor:"bg-orange-100",hoverBgColor:"hover:bg-orange-200"},{tipo:"nuevas",label:"Nuevas",descripcion:"Tarjetas que nunca has estudiado",icon:(0,n.jsx)(d.D1A,{className:"text-green-600"}),color:"green-600",bgColor:"bg-green-100",hoverBgColor:"hover:bg-green-200"},{tipo:"aprendiendo",label:"En aprendizaje",descripcion:"Tarjetas que est\xe1s aprendiendo actualmente",icon:(0,n.jsx)(d.TwU,{className:"text-yellow-600"}),color:"yellow-600",bgColor:"bg-yellow-100",hoverBgColor:"hover:bg-yellow-200"}];return e?(0,n.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,n.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto",children:[(0,n.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold",children:"Opciones de Estudio"}),(0,n.jsx)("button",{onClick:t,className:"text-gray-500 hover:text-gray-700",children:(0,n.jsx)(d.yGN,{size:24})})]}),(0,n.jsxs)("div",{className:"mb-6",children:[(0,n.jsx)("p",{className:"text-gray-600 mb-3",children:"Elige el tipo de estudio que prefieras. Cada opci\xf3n te permitir\xe1 enfocar tu aprendizaje de manera diferente:"}),(0,n.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-3",children:(0,n.jsxs)("p",{className:"text-sm text-blue-800 font-medium",children:["ℹ️ Importante: Estos estudios adicionales son complementarios y ",(0,n.jsx)("strong",{children:"no afectan al algoritmo de repetici\xf3n espaciada"}),'. Para el estudio oficial que cuenta para tu progreso, usa el bot\xf3n "Estudiar" principal.']})})]}),(0,n.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:i.map(e=>(0,n.jsx)("button",{onClick:()=>s(e.tipo),className:`p-4 border rounded-lg text-left transition-all duration-200 ${e.hoverBgColor} ${e.bgColor} border-gray-200 hover:border-gray-300`,disabled:r,children:(0,n.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,n.jsx)("div",{className:"flex-shrink-0 mt-1",children:e.icon}),(0,n.jsxs)("div",{className:"flex-1",children:[(0,n.jsx)("h4",{className:`font-medium text-${e.color} mb-1`,children:e.label}),(0,n.jsx)("p",{className:"text-sm text-gray-600",children:e.descripcion})]})]})},e.tipo))}),(0,n.jsx)("div",{className:"mt-6 flex justify-end space-x-3",children:(0,n.jsx)("button",{onClick:t,className:"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",children:"Cancelar"})})]})}):null};var K=s(90296);function Z(e){return(0,K.k5)({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{fill:"none",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"48",d:"M244 400 100 256l144-144M120 256h292"},child:[]}]})(e)}function J(e){return(0,K.k5)({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{fill:"none",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"48",d:"m268 112 144 144-144 144m124-144H100"},child:[]}]})(e)}function Q(e){return null!==e&&"object"==typeof e&&"function"==typeof e.start}function ee(e){let t=[{},{}];return e?.values.forEach((e,s)=>{t[0][s]=e.get(),t[1][s]=e.getVelocity()}),t}function et(e,t,s,a){if("function"==typeof t){let[r,i]=ee(a);t=t(void 0!==s?s:e.custom,r,i)}if("string"==typeof t&&(t=e.variants&&e.variants[t]),"function"==typeof t){let[r,i]=ee(a);t=t(void 0!==s?s:e.custom,r,i)}return t}function es(e,t,s){let a=e.getProps();return et(a,t,void 0!==s?s:a.custom,e)}function ea(e,t){return e?.[t]??e?.default??e}let er=e=>e,ei={},en=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],eo={value:null,addProjectionMetrics:null};function el(e,t){let s=!1,a=!0,r={delta:0,timestamp:0,isProcessing:!1},i=()=>s=!0,n=en.reduce((e,s)=>(e[s]=function(e,t){let s=new Set,a=new Set,r=!1,i=!1,n=new WeakSet,o={delta:0,timestamp:0,isProcessing:!1},l=0;function d(t){n.has(t)&&(c.schedule(t),e()),l++,t(o)}let c={schedule:(e,t=!1,i=!1)=>{let o=i&&r?s:a;return t&&n.add(e),o.has(e)||o.add(e),e},cancel:e=>{a.delete(e),n.delete(e)},process:e=>{if(o=e,r){i=!0;return}r=!0,[s,a]=[a,s],s.forEach(d),t&&eo.value&&eo.value.frameloop[t].push(l),l=0,s.clear(),r=!1,i&&(i=!1,c.process(e))}};return c}(i,t?s:void 0),e),{}),{setup:o,read:l,resolveKeyframes:d,preUpdate:c,update:u,preRender:m,render:h,postRender:x}=n,p=()=>{let i=ei.useManualTiming?r.timestamp:performance.now();s=!1,ei.useManualTiming||(r.delta=a?1e3/60:Math.max(Math.min(i-r.timestamp,40),1)),r.timestamp=i,r.isProcessing=!0,o.process(r),l.process(r),d.process(r),c.process(r),u.process(r),m.process(r),h.process(r),x.process(r),r.isProcessing=!1,s&&t&&(a=!1,e(p))},g=()=>{s=!0,a=!0,r.isProcessing||e(p)};return{schedule:en.reduce((e,t)=>{let a=n[t];return e[t]=(e,t=!1,r=!1)=>(s||g(),a.schedule(e,t,r)),e},{}),cancel:e=>{for(let t=0;t<en.length;t++)n[en[t]].cancel(e)},state:r,steps:n}}let{schedule:ed,cancel:ec,state:eu,steps:em}=el("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:er,!0),eh=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],ex=new Set(eh),ep=new Set(["width","height","top","left","right","bottom",...eh]);function eg(e,t){-1===e.indexOf(t)&&e.push(t)}function ef(e,t){let s=e.indexOf(t);s>-1&&e.splice(s,1)}class eb{constructor(){this.subscriptions=[]}add(e){return eg(this.subscriptions,e),()=>ef(this.subscriptions,e)}notify(e,t,s){let a=this.subscriptions.length;if(a)if(1===a)this.subscriptions[0](e,t,s);else for(let r=0;r<a;r++){let a=this.subscriptions[r];a&&a(e,t,s)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function ey(){a=void 0}let ev={now:()=>(void 0===a&&ev.set(eu.isProcessing||ei.useManualTiming?eu.timestamp:performance.now()),a),set:e=>{a=e,queueMicrotask(ey)}},ej=e=>!isNaN(parseFloat(e)),eN={current:void 0};class ew{constructor(e,t={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(e,t=!0)=>{let s=ev.now();if(this.updatedAt!==s&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(e),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let e of this.dependents)e.dirty();t&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(e),this.owner=t.owner}setCurrent(e){this.current=e,this.updatedAt=ev.now(),null===this.canTrackVelocity&&void 0!==e&&(this.canTrackVelocity=ej(this.current))}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,t){this.events[e]||(this.events[e]=new eb);let s=this.events[e].add(t);return"change"===e?()=>{s(),ed.read(()=>{this.events.change.getSize()||this.stop()})}:s}clearListeners(){for(let e in this.events)this.events[e].clear()}attach(e,t){this.passiveEffect=e,this.stopPassiveEffect=t}set(e,t=!0){t&&this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e,t)}setWithVelocity(e,t,s){this.set(t),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-s}jump(e,t=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,t&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(e){this.dependents||(this.dependents=new Set),this.dependents.add(e)}removeDependent(e){this.dependents&&this.dependents.delete(e)}get(){return eN.current&&eN.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var e;let t=ev.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;let s=Math.min(this.updatedAt-this.prevUpdatedAt,30);return e=parseFloat(this.current)-parseFloat(this.prevFrameValue),s?1e3/s*e:0}start(e){return this.stop(),new Promise(t=>{this.hasAnimated=!0,this.animation=e(t),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function eE(e,t){return new ew(e,t)}let eC=e=>Array.isArray(e),eS=e=>!!(e&&e.getVelocity);function ek(e,t){let s=e.getValue("willChange");if(eS(s)&&s.add)return s.add(t);if(!s&&ei.WillChange){let s=new ei.WillChange("auto");e.addValue("willChange",s),s.add(t)}}let eT=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),eA="data-"+eT("framerAppearId"),eP=(e,t)=>s=>t(e(s)),eR=(...e)=>e.reduce(eP),eD=(e,t,s)=>s>t?t:s<e?e:s,eM=e=>1e3*e,e$=e=>e/1e3,eL={layout:0,mainThread:0,waapi:0},eF=()=>{},eV=()=>{},e_=e=>t=>"string"==typeof t&&t.startsWith(e),ez=e_("--"),eI=e_("var(--"),eO=e=>!!eI(e)&&eB.test(e.split("/*")[0].trim()),eB=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,eU={test:e=>"number"==typeof e,parse:parseFloat,transform:e=>e},eG={...eU,transform:e=>eD(0,1,e)},eq={...eU,default:1},eH=e=>Math.round(1e5*e)/1e5,eW=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,eY=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,eX=(e,t)=>s=>!!("string"==typeof s&&eY.test(s)&&s.startsWith(e)||t&&null!=s&&Object.prototype.hasOwnProperty.call(s,t)),eK=(e,t,s)=>a=>{if("string"!=typeof a)return a;let[r,i,n,o]=a.match(eW);return{[e]:parseFloat(r),[t]:parseFloat(i),[s]:parseFloat(n),alpha:void 0!==o?parseFloat(o):1}},eZ=e=>eD(0,255,e),eJ={...eU,transform:e=>Math.round(eZ(e))},eQ={test:eX("rgb","red"),parse:eK("red","green","blue"),transform:({red:e,green:t,blue:s,alpha:a=1})=>"rgba("+eJ.transform(e)+", "+eJ.transform(t)+", "+eJ.transform(s)+", "+eH(eG.transform(a))+")"},e0={test:eX("#"),parse:function(e){let t="",s="",a="",r="";return e.length>5?(t=e.substring(1,3),s=e.substring(3,5),a=e.substring(5,7),r=e.substring(7,9)):(t=e.substring(1,2),s=e.substring(2,3),a=e.substring(3,4),r=e.substring(4,5),t+=t,s+=s,a+=a,r+=r),{red:parseInt(t,16),green:parseInt(s,16),blue:parseInt(a,16),alpha:r?parseInt(r,16)/255:1}},transform:eQ.transform},e1=e=>({test:t=>"string"==typeof t&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>`${t}${e}`}),e2=e1("deg"),e4=e1("%"),e5=e1("px"),e6=e1("vh"),e3=e1("vw"),e8={...e4,parse:e=>e4.parse(e)/100,transform:e=>e4.transform(100*e)},e7={test:eX("hsl","hue"),parse:eK("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:s,alpha:a=1})=>"hsla("+Math.round(e)+", "+e4.transform(eH(t))+", "+e4.transform(eH(s))+", "+eH(eG.transform(a))+")"},e9={test:e=>eQ.test(e)||e0.test(e)||e7.test(e),parse:e=>eQ.test(e)?eQ.parse(e):e7.test(e)?e7.parse(e):e0.parse(e),transform:e=>"string"==typeof e?e:e.hasOwnProperty("red")?eQ.transform(e):e7.transform(e)},te=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,tt="number",ts="color",ta=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function tr(e){let t=e.toString(),s=[],a={color:[],number:[],var:[]},r=[],i=0,n=t.replace(ta,e=>(e9.test(e)?(a.color.push(i),r.push(ts),s.push(e9.parse(e))):e.startsWith("var(")?(a.var.push(i),r.push("var"),s.push(e)):(a.number.push(i),r.push(tt),s.push(parseFloat(e))),++i,"${}")).split("${}");return{values:s,split:n,indexes:a,types:r}}function ti(e){return tr(e).values}function tn(e){let{split:t,types:s}=tr(e),a=t.length;return e=>{let r="";for(let i=0;i<a;i++)if(r+=t[i],void 0!==e[i]){let t=s[i];t===tt?r+=eH(e[i]):t===ts?r+=e9.transform(e[i]):r+=e[i]}return r}}let to=e=>"number"==typeof e?0:e,tl={test:function(e){return isNaN(e)&&"string"==typeof e&&(e.match(eW)?.length||0)+(e.match(te)?.length||0)>0},parse:ti,createTransformer:tn,getAnimatableNone:function(e){let t=ti(e);return tn(e)(t.map(to))}};function td(e,t,s){return(s<0&&(s+=1),s>1&&(s-=1),s<1/6)?e+(t-e)*6*s:s<.5?t:s<2/3?e+(t-e)*(2/3-s)*6:e}function tc(e,t){return s=>s>0?t:e}let tu=(e,t,s)=>e+(t-e)*s,tm=(e,t,s)=>{let a=e*e,r=s*(t*t-a)+a;return r<0?0:Math.sqrt(r)},th=[e0,eQ,e7],tx=e=>th.find(t=>t.test(e));function tp(e){let t=tx(e);if(eF(!!t,`'${e}' is not an animatable color. Use the equivalent color code instead.`),!t)return!1;let s=t.parse(e);return t===e7&&(s=function({hue:e,saturation:t,lightness:s,alpha:a}){e/=360,s/=100;let r=0,i=0,n=0;if(t/=100){let a=s<.5?s*(1+t):s+t-s*t,o=2*s-a;r=td(o,a,e+1/3),i=td(o,a,e),n=td(o,a,e-1/3)}else r=i=n=s;return{red:Math.round(255*r),green:Math.round(255*i),blue:Math.round(255*n),alpha:a}}(s)),s}let tg=(e,t)=>{let s=tp(e),a=tp(t);if(!s||!a)return tc(e,t);let r={...s};return e=>(r.red=tm(s.red,a.red,e),r.green=tm(s.green,a.green,e),r.blue=tm(s.blue,a.blue,e),r.alpha=tu(s.alpha,a.alpha,e),eQ.transform(r))},tf=new Set(["none","hidden"]);function tb(e,t){return s=>tu(e,t,s)}function ty(e){return"number"==typeof e?tb:"string"==typeof e?eO(e)?tc:e9.test(e)?tg:tN:Array.isArray(e)?tv:"object"==typeof e?e9.test(e)?tg:tj:tc}function tv(e,t){let s=[...e],a=s.length,r=e.map((e,s)=>ty(e)(e,t[s]));return e=>{for(let t=0;t<a;t++)s[t]=r[t](e);return s}}function tj(e,t){let s={...e,...t},a={};for(let r in s)void 0!==e[r]&&void 0!==t[r]&&(a[r]=ty(e[r])(e[r],t[r]));return e=>{for(let t in a)s[t]=a[t](e);return s}}let tN=(e,t)=>{let s=tl.createTransformer(t),a=tr(e),r=tr(t);return a.indexes.var.length===r.indexes.var.length&&a.indexes.color.length===r.indexes.color.length&&a.indexes.number.length>=r.indexes.number.length?tf.has(e)&&!r.values.length||tf.has(t)&&!a.values.length?function(e,t){return tf.has(e)?s=>s<=0?e:t:s=>s>=1?t:e}(e,t):eR(tv(function(e,t){let s=[],a={color:0,var:0,number:0};for(let r=0;r<t.values.length;r++){let i=t.types[r],n=e.indexes[i][a[i]],o=e.values[n]??0;s[r]=o,a[i]++}return s}(a,r),r.values),s):(eF(!0,`Complex values '${e}' and '${t}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),tc(e,t))};function tw(e,t,s){return"number"==typeof e&&"number"==typeof t&&"number"==typeof s?tu(e,t,s):ty(e)(e,t)}let tE=e=>{let t=({timestamp:t})=>e(t);return{start:(e=!0)=>ed.update(t,e),stop:()=>ec(t),now:()=>eu.isProcessing?eu.timestamp:ev.now()}},tC=(e,t,s=10)=>{let a="",r=Math.max(Math.round(t/s),2);for(let t=0;t<r;t++)a+=e(t/(r-1))+", ";return`linear(${a.substring(0,a.length-2)})`};function tS(e){let t=0,s=e.next(t);for(;!s.done&&t<2e4;)t+=50,s=e.next(t);return t>=2e4?1/0:t}function tk(e,t,s){var a,r;let i=Math.max(t-5,0);return a=s-e(i),(r=t-i)?1e3/r*a:0}let tT={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function tA(e,t){return e*Math.sqrt(1-t*t)}let tP=["duration","bounce"],tR=["stiffness","damping","mass"];function tD(e,t){return t.some(t=>void 0!==e[t])}function tM(e=tT.visualDuration,t=tT.bounce){let s,a="object"!=typeof e?{visualDuration:e,keyframes:[0,1],bounce:t}:e,{restSpeed:r,restDelta:i}=a,n=a.keyframes[0],o=a.keyframes[a.keyframes.length-1],l={done:!1,value:n},{stiffness:d,damping:c,mass:u,duration:m,velocity:h,isResolvedFromDuration:x}=function(e){let t={velocity:tT.velocity,stiffness:tT.stiffness,damping:tT.damping,mass:tT.mass,isResolvedFromDuration:!1,...e};if(!tD(e,tR)&&tD(e,tP))if(e.visualDuration){let s=2*Math.PI/(1.2*e.visualDuration),a=s*s,r=2*eD(.05,1,1-(e.bounce||0))*Math.sqrt(a);t={...t,mass:tT.mass,stiffness:a,damping:r}}else{let s=function({duration:e=tT.duration,bounce:t=tT.bounce,velocity:s=tT.velocity,mass:a=tT.mass}){let r,i;eF(e<=eM(tT.maxDuration),"Spring duration must be 10 seconds or less");let n=1-t;n=eD(tT.minDamping,tT.maxDamping,n),e=eD(tT.minDuration,tT.maxDuration,e$(e)),n<1?(r=t=>{let a=t*n,r=a*e;return .001-(a-s)/tA(t,n)*Math.exp(-r)},i=t=>{let a=t*n*e,i=Math.pow(n,2)*Math.pow(t,2)*e,o=Math.exp(-a),l=tA(Math.pow(t,2),n);return(a*s+s-i)*o*(-r(t)+.001>0?-1:1)/l}):(r=t=>-.001+Math.exp(-t*e)*((t-s)*e+1),i=t=>e*e*(s-t)*Math.exp(-t*e));let o=function(e,t,s){let a=s;for(let s=1;s<12;s++)a-=e(a)/t(a);return a}(r,i,5/e);if(e=eM(e),isNaN(o))return{stiffness:tT.stiffness,damping:tT.damping,duration:e};{let t=Math.pow(o,2)*a;return{stiffness:t,damping:2*n*Math.sqrt(a*t),duration:e}}}(e);(t={...t,...s,mass:tT.mass}).isResolvedFromDuration=!0}return t}({...a,velocity:-e$(a.velocity||0)}),p=h||0,g=c/(2*Math.sqrt(d*u)),f=o-n,b=e$(Math.sqrt(d/u)),y=5>Math.abs(f);if(r||(r=y?tT.restSpeed.granular:tT.restSpeed.default),i||(i=y?tT.restDelta.granular:tT.restDelta.default),g<1){let e=tA(b,g);s=t=>o-Math.exp(-g*b*t)*((p+g*b*f)/e*Math.sin(e*t)+f*Math.cos(e*t))}else if(1===g)s=e=>o-Math.exp(-b*e)*(f+(p+b*f)*e);else{let e=b*Math.sqrt(g*g-1);s=t=>{let s=Math.exp(-g*b*t),a=Math.min(e*t,300);return o-s*((p+g*b*f)*Math.sinh(a)+e*f*Math.cosh(a))/e}}let v={calculatedDuration:x&&m||null,next:e=>{let t=s(e);if(x)l.done=e>=m;else{let a=0===e?p:0;g<1&&(a=0===e?eM(p):tk(s,e,t));let n=Math.abs(o-t)<=i;l.done=Math.abs(a)<=r&&n}return l.value=l.done?o:t,l},toString:()=>{let e=Math.min(tS(v),2e4),t=tC(t=>v.next(e*t).value,e,30);return e+"ms "+t},toTransition:()=>{}};return v}function t$({keyframes:e,velocity:t=0,power:s=.8,timeConstant:a=325,bounceDamping:r=10,bounceStiffness:i=500,modifyTarget:n,min:o,max:l,restDelta:d=.5,restSpeed:c}){let u,m,h=e[0],x={done:!1,value:h},p=e=>void 0!==o&&e<o||void 0!==l&&e>l,g=e=>void 0===o?l:void 0===l||Math.abs(o-e)<Math.abs(l-e)?o:l,f=s*t,b=h+f,y=void 0===n?b:n(b);y!==b&&(f=y-h);let v=e=>-f*Math.exp(-e/a),j=e=>y+v(e),N=e=>{let t=v(e),s=j(e);x.done=Math.abs(t)<=d,x.value=x.done?y:s},w=e=>{p(x.value)&&(u=e,m=tM({keyframes:[x.value,g(x.value)],velocity:tk(j,e,x.value),damping:r,stiffness:i,restDelta:d,restSpeed:c}))};return w(0),{calculatedDuration:null,next:e=>{let t=!1;return(m||void 0!==u||(t=!0,N(e),w(e)),void 0!==u&&e>=u)?m.next(e-u):(t||N(e),x)}}}tM.applyToOptions=e=>{let t=function(e,t=100,s){let a=s({...e,keyframes:[0,t]}),r=Math.min(tS(a),2e4);return{type:"keyframes",ease:e=>a.next(r*e).value/t,duration:e$(r)}}(e,100,tM);return e.ease=t.ease,e.duration=eM(t.duration),e.type="keyframes",e};let tL=(e,t,s)=>(((1-3*s+3*t)*e+(3*s-6*t))*e+3*t)*e;function tF(e,t,s,a){if(e===t&&s===a)return er;let r=t=>(function(e,t,s,a,r){let i,n,o=0;do(i=tL(n=t+(s-t)/2,a,r)-e)>0?s=n:t=n;while(Math.abs(i)>1e-7&&++o<12);return n})(t,0,1,e,s);return e=>0===e||1===e?e:tL(r(e),t,a)}let tV=tF(.42,0,1,1),t_=tF(0,0,.58,1),tz=tF(.42,0,.58,1),tI=e=>Array.isArray(e)&&"number"!=typeof e[0],tO=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,tB=e=>t=>1-e(1-t),tU=tF(.33,1.53,.69,.99),tG=tB(tU),tq=tO(tG),tH=e=>(e*=2)<1?.5*tG(e):.5*(2-Math.pow(2,-10*(e-1))),tW=e=>1-Math.sin(Math.acos(e)),tY=tB(tW),tX=tO(tW),tK=e=>Array.isArray(e)&&"number"==typeof e[0],tZ={linear:er,easeIn:tV,easeInOut:tz,easeOut:t_,circIn:tW,circInOut:tX,circOut:tY,backIn:tG,backInOut:tq,backOut:tU,anticipate:tH},tJ=e=>"string"==typeof e,tQ=e=>{if(tK(e)){eV(4===e.length,"Cubic bezier arrays must contain four numerical values.");let[t,s,a,r]=e;return tF(t,s,a,r)}return tJ(e)?(eV(void 0!==tZ[e],`Invalid easing type '${e}'`),tZ[e]):e},t0=(e,t,s)=>{let a=t-e;return 0===a?1:(s-e)/a};function t1({duration:e=300,keyframes:t,times:s,ease:a="easeInOut"}){var r;let i=tI(a)?a.map(tQ):tQ(a),n={done:!1,value:t[0]},o=function(e,t,{clamp:s=!0,ease:a,mixer:r}={}){let i=e.length;if(eV(i===t.length,"Both input and output ranges must be the same length"),1===i)return()=>t[0];if(2===i&&t[0]===t[1])return()=>t[1];let n=e[0]===e[1];e[0]>e[i-1]&&(e=[...e].reverse(),t=[...t].reverse());let o=function(e,t,s){let a=[],r=s||ei.mix||tw,i=e.length-1;for(let s=0;s<i;s++){let i=r(e[s],e[s+1]);t&&(i=eR(Array.isArray(t)?t[s]||er:t,i)),a.push(i)}return a}(t,a,r),l=o.length,d=s=>{if(n&&s<e[0])return t[0];let a=0;if(l>1)for(;a<e.length-2&&!(s<e[a+1]);a++);let r=t0(e[a],e[a+1],s);return o[a](r)};return s?t=>d(eD(e[0],e[i-1],t)):d}((r=s&&s.length===t.length?s:function(e){let t=[0];return!function(e,t){let s=e[e.length-1];for(let a=1;a<=t;a++){let r=t0(0,t,a);e.push(tu(s,1,r))}}(t,e.length-1),t}(t),r.map(t=>t*e)),t,{ease:Array.isArray(i)?i:t.map(()=>i||tz).splice(0,t.length-1)});return{calculatedDuration:e,next:t=>(n.value=o(t),n.done=t>=e,n)}}let t2=e=>null!==e;function t4(e,{repeat:t,repeatType:s="loop"},a,r=1){let i=e.filter(t2),n=r<0||t&&"loop"!==s&&t%2==1?0:i.length-1;return n&&void 0!==a?a:i[n]}let t5={decay:t$,inertia:t$,tween:t1,keyframes:t1,spring:tM};function t6(e){"string"==typeof e.type&&(e.type=t5[e.type])}class t3{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(e=>{this.resolve=e})}notifyFinished(){this.resolve()}then(e,t){return this.finished.then(e,t)}}let t8=e=>e/100;class t7 extends t3{constructor(e){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=(e=!0)=>{if(e){let{motionValue:e}=this.options;e&&e.updatedAt!==ev.now()&&this.tick(ev.now())}this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},eL.mainThread++,this.options=e,this.initAnimation(),this.play(),!1===e.autoplay&&this.pause()}initAnimation(){let{options:e}=this;t6(e);let{type:t=t1,repeat:s=0,repeatDelay:a=0,repeatType:r,velocity:i=0}=e,{keyframes:n}=e,o=t||t1;o!==t1&&"number"!=typeof n[0]&&(this.mixKeyframes=eR(t8,tw(n[0],n[1])),n=[0,100]);let l=o({...e,keyframes:n});"mirror"===r&&(this.mirroredGenerator=o({...e,keyframes:[...n].reverse(),velocity:-i})),null===l.calculatedDuration&&(l.calculatedDuration=tS(l));let{calculatedDuration:d}=l;this.calculatedDuration=d,this.resolvedDuration=d+a,this.totalDuration=this.resolvedDuration*(s+1)-a,this.generator=l}updateTime(e){let t=Math.round(e-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=t}tick(e,t=!1){let{generator:s,totalDuration:a,mixKeyframes:r,mirroredGenerator:i,resolvedDuration:n,calculatedDuration:o}=this;if(null===this.startTime)return s.next(0);let{delay:l=0,keyframes:d,repeat:c,repeatType:u,repeatDelay:m,type:h,onUpdate:x,finalKeyframe:p}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-a/this.speed,this.startTime)),t?this.currentTime=e:this.updateTime(e);let g=this.currentTime-l*(this.playbackSpeed>=0?1:-1),f=this.playbackSpeed>=0?g<0:g>a;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=a);let b=this.currentTime,y=s;if(c){let e=Math.min(this.currentTime,a)/n,t=Math.floor(e),s=e%1;!s&&e>=1&&(s=1),1===s&&t--,(t=Math.min(t,c+1))%2&&("reverse"===u?(s=1-s,m&&(s-=m/n)):"mirror"===u&&(y=i)),b=eD(0,1,s)*n}let v=f?{done:!1,value:d[0]}:y.next(b);r&&(v.value=r(v.value));let{done:j}=v;f||null===o||(j=this.playbackSpeed>=0?this.currentTime>=a:this.currentTime<=0);let N=null===this.holdTime&&("finished"===this.state||"running"===this.state&&j);return N&&h!==t$&&(v.value=t4(d,this.options,p,this.speed)),x&&x(v.value),N&&this.finish(),v}then(e,t){return this.finished.then(e,t)}get duration(){return e$(this.calculatedDuration)}get time(){return e$(this.currentTime)}set time(e){e=eM(e),this.currentTime=e,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(e){this.updateTime(ev.now());let t=this.playbackSpeed!==e;this.playbackSpeed=e,t&&(this.time=e$(this.currentTime))}play(){if(this.isStopped)return;let{driver:e=tE,startTime:t}=this.options;this.driver||(this.driver=e(e=>this.tick(e))),this.options.onPlay?.();let s=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=s):null!==this.holdTime?this.startTime=s-this.holdTime:this.startTime||(this.startTime=t??s),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(ev.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,eL.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}attachTimeline(e){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),e.observe(this)}}let t9=e=>180*e/Math.PI,se=e=>ss(t9(Math.atan2(e[1],e[0]))),st={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:e=>(Math.abs(e[0])+Math.abs(e[3]))/2,rotate:se,rotateZ:se,skewX:e=>t9(Math.atan(e[1])),skewY:e=>t9(Math.atan(e[2])),skew:e=>(Math.abs(e[1])+Math.abs(e[2]))/2},ss=e=>((e%=360)<0&&(e+=360),e),sa=e=>Math.sqrt(e[0]*e[0]+e[1]*e[1]),sr=e=>Math.sqrt(e[4]*e[4]+e[5]*e[5]),si={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:sa,scaleY:sr,scale:e=>(sa(e)+sr(e))/2,rotateX:e=>ss(t9(Math.atan2(e[6],e[5]))),rotateY:e=>ss(t9(Math.atan2(-e[2],e[0]))),rotateZ:se,rotate:se,skewX:e=>t9(Math.atan(e[4])),skewY:e=>t9(Math.atan(e[1])),skew:e=>(Math.abs(e[1])+Math.abs(e[4]))/2};function sn(e){return+!!e.includes("scale")}function so(e,t){let s,a;if(!e||"none"===e)return sn(t);let r=e.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(r)s=si,a=r;else{let t=e.match(/^matrix\(([-\d.e\s,]+)\)$/u);s=st,a=t}if(!a)return sn(t);let i=s[t],n=a[1].split(",").map(sd);return"function"==typeof i?i(n):n[i]}let sl=(e,t)=>{let{transform:s="none"}=getComputedStyle(e);return so(s,t)};function sd(e){return parseFloat(e.trim())}let sc=e=>e===eU||e===e5,su=new Set(["x","y","z"]),sm=eh.filter(e=>!su.has(e)),sh={width:({x:e},{paddingLeft:t="0",paddingRight:s="0"})=>e.max-e.min-parseFloat(t)-parseFloat(s),height:({y:e},{paddingTop:t="0",paddingBottom:s="0"})=>e.max-e.min-parseFloat(t)-parseFloat(s),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:(e,{transform:t})=>so(t,"x"),y:(e,{transform:t})=>so(t,"y")};sh.translateX=sh.x,sh.translateY=sh.y;let sx=new Set,sp=!1,sg=!1,sf=!1;function sb(){if(sg){let e=Array.from(sx).filter(e=>e.needsMeasurement),t=new Set(e.map(e=>e.element)),s=new Map;t.forEach(e=>{let t=function(e){let t=[];return sm.forEach(s=>{let a=e.getValue(s);void 0!==a&&(t.push([s,a.get()]),a.set(+!!s.startsWith("scale")))}),t}(e);t.length&&(s.set(e,t),e.render())}),e.forEach(e=>e.measureInitialState()),t.forEach(e=>{e.render();let t=s.get(e);t&&t.forEach(([t,s])=>{e.getValue(t)?.set(s)})}),e.forEach(e=>e.measureEndState()),e.forEach(e=>{void 0!==e.suspendedScrollY&&window.scrollTo(0,e.suspendedScrollY)})}sg=!1,sp=!1,sx.forEach(e=>e.complete(sf)),sx.clear()}function sy(){sx.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(sg=!0)})}class sv{constructor(e,t,s,a,r,i=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...e],this.onComplete=t,this.name=s,this.motionValue=a,this.element=r,this.isAsync=i}scheduleResolve(){this.state="scheduled",this.isAsync?(sx.add(this),sp||(sp=!0,ed.read(sy),ed.resolveKeyframes(sb))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:e,name:t,element:s,motionValue:a}=this;if(null===e[0]){let r=a?.get(),i=e[e.length-1];if(void 0!==r)e[0]=r;else if(s&&t){let a=s.readValue(t,i);null!=a&&(e[0]=a)}void 0===e[0]&&(e[0]=i),a&&void 0===r&&a.set(e[0])}for(let t=1;t<e.length;t++)e[t]??(e[t]=e[t-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(e=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,e),sx.delete(this)}cancel(){"scheduled"===this.state&&(sx.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let sj=e=>e.startsWith("--");function sN(e){let t;return()=>(void 0===t&&(t=e()),t)}let sw=sN(()=>void 0!==window.ScrollTimeline),sE={},sC=function(e,t){let s=sN(e);return()=>sE[t]??s()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(e){return!1}return!0},"linearEasing"),sS=([e,t,s,a])=>`cubic-bezier(${e}, ${t}, ${s}, ${a})`,sk={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:sS([0,.65,.55,1]),circOut:sS([.55,0,1,.45]),backIn:sS([.31,.01,.66,-.59]),backOut:sS([.33,1.53,.69,.99])};function sT(e){return"function"==typeof e&&"applyToOptions"in e}class sA extends t3{constructor(e){if(super(),this.finishedTime=null,this.isStopped=!1,!e)return;let{element:t,name:s,keyframes:a,pseudoElement:r,allowFlatten:i=!1,finalKeyframe:n,onComplete:o}=e;this.isPseudoElement=!!r,this.allowFlatten=i,this.options=e,eV("string"!=typeof e.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "motion"?');let l=function({type:e,...t}){return sT(e)&&sC()?e.applyToOptions(t):(t.duration??(t.duration=300),t.ease??(t.ease="easeOut"),t)}(e);this.animation=function(e,t,s,{delay:a=0,duration:r=300,repeat:i=0,repeatType:n="loop",ease:o="easeOut",times:l}={},d){let c={[t]:s};l&&(c.offset=l);let u=function e(t,s){if(t)return"function"==typeof t?sC()?tC(t,s):"ease-out":tK(t)?sS(t):Array.isArray(t)?t.map(t=>e(t,s)||sk.easeOut):sk[t]}(o,r);Array.isArray(u)&&(c.easing=u),eo.value&&eL.waapi++;let m={delay:a,duration:r,easing:Array.isArray(u)?"linear":u,fill:"both",iterations:i+1,direction:"reverse"===n?"alternate":"normal"};d&&(m.pseudoElement=d);let h=e.animate(c,m);return eo.value&&h.finished.finally(()=>{eL.waapi--}),h}(t,s,a,l,r),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!r){let e=t4(a,this.options,n,this.speed);this.updateMotionValue?this.updateMotionValue(e):function(e,t,s){sj(t)?e.style.setProperty(t,s):e.style[t]=s}(t,s,e),this.animation.cancel()}o?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(e){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:e}=this;"idle"!==e&&"finished"!==e&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return e$(Number(this.animation.effect?.getComputedTiming?.().duration||0))}get time(){return e$(Number(this.animation.currentTime)||0)}set time(e){this.finishedTime=null,this.animation.currentTime=eM(e)}get speed(){return this.animation.playbackRate}set speed(e){e<0&&(this.finishedTime=null),this.animation.playbackRate=e}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(e){this.animation.startTime=e}attachTimeline({timeline:e,observe:t}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,e&&sw())?(this.animation.timeline=e,er):t(this)}}let sP={anticipate:tH,backInOut:tq,circInOut:tX};class sR extends sA{constructor(e){!function(e){"string"==typeof e.ease&&e.ease in sP&&(e.ease=sP[e.ease])}(e),t6(e),super(e),e.startTime&&(this.startTime=e.startTime),this.options=e}updateMotionValue(e){let{motionValue:t,onUpdate:s,onComplete:a,element:r,...i}=this.options;if(!t)return;if(void 0!==e)return void t.set(e);let n=new t7({...i,autoplay:!1}),o=eM(this.finishedTime??this.time);t.setWithVelocity(n.sample(o-10).value,n.sample(o).value,10),n.stop()}}let sD=(e,t)=>"zIndex"!==t&&!!("number"==typeof e||Array.isArray(e)||"string"==typeof e&&(tl.test(e)||"0"===e)&&!e.startsWith("url("));function sM(e){return"object"==typeof e&&null!==e}function s$(e){return sM(e)&&"offsetHeight"in e}let sL=new Set(["opacity","clipPath","filter","transform"]),sF=sN(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class sV extends t3{constructor({autoplay:e=!0,delay:t=0,type:s="keyframes",repeat:a=0,repeatDelay:r=0,repeatType:i="loop",keyframes:n,name:o,motionValue:l,element:d,...c}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=ev.now();let u={autoplay:e,delay:t,type:s,repeat:a,repeatDelay:r,repeatType:i,name:o,motionValue:l,element:d,...c},m=d?.KeyframeResolver||sv;this.keyframeResolver=new m(n,(e,t,s)=>this.onKeyframesResolved(e,t,u,!s),o,l,d),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(e,t,s,a){this.keyframeResolver=void 0;let{name:r,type:i,velocity:n,delay:o,isHandoff:l,onUpdate:d}=s;this.resolvedAt=ev.now(),!function(e,t,s,a){let r=e[0];if(null===r)return!1;if("display"===t||"visibility"===t)return!0;let i=e[e.length-1],n=sD(r,t),o=sD(i,t);return eF(n===o,`You are trying to animate ${t} from "${r}" to "${i}". ${r} is not an animatable value - to enable this animation set ${r} to a value animatable to ${i} via the \`style\` property.`),!!n&&!!o&&(function(e){let t=e[0];if(1===e.length)return!0;for(let s=0;s<e.length;s++)if(e[s]!==t)return!0}(e)||("spring"===s||sT(s))&&a)}(e,r,i,n)&&((ei.instantAnimations||!o)&&d?.(t4(e,s,t)),e[0]=e[e.length-1],s.duration=0,s.repeat=0);let c={startTime:a?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:t,...s,keyframes:e},u=!l&&function(e){let{motionValue:t,name:s,repeatDelay:a,repeatType:r,damping:i,type:n}=e;if(!s$(t?.owner?.current))return!1;let{onUpdate:o,transformTemplate:l}=t.owner.getProps();return sF()&&s&&sL.has(s)&&("transform"!==s||!l)&&!o&&!a&&"mirror"!==r&&0!==i&&"inertia"!==n}(c)?new sR({...c,element:c.motionValue.owner.current}):new t7(c);u.finished.then(()=>this.notifyFinished()).catch(er),this.pendingTimeline&&(this.stopTimeline=u.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=u}get finished(){return this._animation?this.animation.finished:this._finished}then(e,t){return this.finished.finally(e).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),sf=!0,sy(),sb(),sf=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(e){this.animation.time=e}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(e){this.animation.speed=e}get startTime(){return this.animation.startTime}attachTimeline(e){return this._animation?this.stopTimeline=this.animation.attachTimeline(e):this.pendingTimeline=e,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let s_=e=>null!==e,sz={type:"spring",stiffness:500,damping:25,restSpeed:10},sI=e=>({type:"spring",stiffness:550,damping:0===e?2*Math.sqrt(550):30,restSpeed:10}),sO={type:"keyframes",duration:.8},sB={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},sU=(e,{keyframes:t})=>t.length>2?sO:ex.has(e)?e.startsWith("scale")?sI(t[1]):sz:sB,sG=(e,t,s,a={},r,i)=>n=>{let o=ea(a,e)||{},l=o.delay||a.delay||0,{elapsed:d=0}=a;d-=eM(l);let c={keyframes:Array.isArray(s)?s:[null,s],ease:"easeOut",velocity:t.getVelocity(),...o,delay:-d,onUpdate:e=>{t.set(e),o.onUpdate&&o.onUpdate(e)},onComplete:()=>{n(),o.onComplete&&o.onComplete()},name:e,motionValue:t,element:i?void 0:r};!function({when:e,delay:t,delayChildren:s,staggerChildren:a,staggerDirection:r,repeat:i,repeatType:n,repeatDelay:o,from:l,elapsed:d,...c}){return!!Object.keys(c).length}(o)&&Object.assign(c,sU(e,c)),c.duration&&(c.duration=eM(c.duration)),c.repeatDelay&&(c.repeatDelay=eM(c.repeatDelay)),void 0!==c.from&&(c.keyframes[0]=c.from);let u=!1;if(!1!==c.type&&(0!==c.duration||c.repeatDelay)||(c.duration=0,0===c.delay&&(u=!0)),(ei.instantAnimations||ei.skipAnimations)&&(u=!0,c.duration=0,c.delay=0),c.allowFlatten=!o.type&&!o.ease,u&&!i&&void 0!==t.get()){let e=function(e,{repeat:t,repeatType:s="loop"},a){let r=e.filter(s_),i=t&&"loop"!==s&&t%2==1?0:r.length-1;return r[i]}(c.keyframes,o);if(void 0!==e)return void ed.update(()=>{c.onUpdate(e),c.onComplete()})}return o.isSync?new t7(c):new sV(c)};function sq(e,t,{delay:s=0,transitionOverride:a,type:r}={}){let{transition:i=e.getDefaultTransition(),transitionEnd:n,...o}=t;a&&(i=a);let l=[],d=r&&e.animationState&&e.animationState.getState()[r];for(let t in o){let a=e.getValue(t,e.latestValues[t]??null),r=o[t];if(void 0===r||d&&function({protectedKeys:e,needsAnimating:t},s){let a=e.hasOwnProperty(s)&&!0!==t[s];return t[s]=!1,a}(d,t))continue;let n={delay:s,...ea(i||{},t)},c=a.get();if(void 0!==c&&!a.isAnimating&&!Array.isArray(r)&&r===c&&!n.velocity)continue;let u=!1;if(window.MotionHandoffAnimation){let s=e.props[eA];if(s){let e=window.MotionHandoffAnimation(s,t,ed);null!==e&&(n.startTime=e,u=!0)}}ek(e,t),a.start(sG(t,a,r,e.shouldReduceMotion&&ep.has(t)?{type:!1}:n,e,u));let m=a.animation;m&&l.push(m)}return n&&Promise.all(l).then(()=>{ed.update(()=>{n&&function(e,t){let{transitionEnd:s={},transition:a={},...r}=es(e,t)||{};for(let t in r={...r,...s}){var i;let s=eC(i=r[t])?i[i.length-1]||0:i;e.hasValue(t)?e.getValue(t).set(s):e.addValue(t,eE(s))}}(e,n)})}),l}function sH(e,t,s={}){let a=es(e,t,"exit"===s.type?e.presenceContext?.custom:void 0),{transition:r=e.getDefaultTransition()||{}}=a||{};s.transitionOverride&&(r=s.transitionOverride);let i=a?()=>Promise.all(sq(e,a,s)):()=>Promise.resolve(),n=e.variantChildren&&e.variantChildren.size?(a=0)=>{let{delayChildren:i=0,staggerChildren:n,staggerDirection:o}=r;return function(e,t,s=0,a=0,r=1,i){let n=[],o=(e.variantChildren.size-1)*a,l=1===r?(e=0)=>e*a:(e=0)=>o-e*a;return Array.from(e.variantChildren).sort(sW).forEach((e,a)=>{e.notify("AnimationStart",t),n.push(sH(e,t,{...i,delay:s+l(a)}).then(()=>e.notify("AnimationComplete",t)))}),Promise.all(n)}(e,t,i+a,n,o,s)}:()=>Promise.resolve(),{when:o}=r;if(!o)return Promise.all([i(),n(s.delay)]);{let[e,t]="beforeChildren"===o?[i,n]:[n,i];return e().then(()=>t())}}function sW(e,t){return e.sortNodePosition(t)}function sY(e,t){if(!Array.isArray(t))return!1;let s=t.length;if(s!==e.length)return!1;for(let a=0;a<s;a++)if(t[a]!==e[a])return!1;return!0}function sX(e){return"string"==typeof e||Array.isArray(e)}let sK=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],sZ=["initial",...sK],sJ=sZ.length,sQ=[...sK].reverse(),s0=sK.length;function s1(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function s2(){return{animate:s1(!0),whileInView:s1(),whileHover:s1(),whileTap:s1(),whileDrag:s1(),whileFocus:s1(),exit:s1()}}class s4{constructor(e){this.isMounted=!1,this.node=e}update(){}}class s5 extends s4{constructor(e){super(e),e.animationState||(e.animationState=function(e){let t=t=>Promise.all(t.map(({animation:t,options:s})=>(function(e,t,s={}){let a;if(e.notify("AnimationStart",t),Array.isArray(t))a=Promise.all(t.map(t=>sH(e,t,s)));else if("string"==typeof t)a=sH(e,t,s);else{let r="function"==typeof t?es(e,t,s.custom):t;a=Promise.all(sq(e,r,s))}return a.then(()=>{e.notify("AnimationComplete",t)})})(e,t,s))),s=s2(),a=!0,r=t=>(s,a)=>{let r=es(e,a,"exit"===t?e.presenceContext?.custom:void 0);if(r){let{transition:e,transitionEnd:t,...a}=r;s={...s,...a,...t}}return s};function i(i){let{props:n}=e,o=function e(t){if(!t)return;if(!t.isControllingVariants){let s=t.parent&&e(t.parent)||{};return void 0!==t.props.initial&&(s.initial=t.props.initial),s}let s={};for(let e=0;e<sJ;e++){let a=sZ[e],r=t.props[a];(sX(r)||!1===r)&&(s[a]=r)}return s}(e.parent)||{},l=[],d=new Set,c={},u=1/0;for(let t=0;t<s0;t++){var m,h;let x=sQ[t],p=s[x],g=void 0!==n[x]?n[x]:o[x],f=sX(g),b=x===i?p.isActive:null;!1===b&&(u=t);let y=g===o[x]&&g!==n[x]&&f;if(y&&a&&e.manuallyAnimateOnMount&&(y=!1),p.protectedKeys={...c},!p.isActive&&null===b||!g&&!p.prevProp||Q(g)||"boolean"==typeof g)continue;let v=(m=p.prevProp,"string"==typeof(h=g)?h!==m:!!Array.isArray(h)&&!sY(h,m)),j=v||x===i&&p.isActive&&!y&&f||t>u&&f,N=!1,w=Array.isArray(g)?g:[g],E=w.reduce(r(x),{});!1===b&&(E={});let{prevResolvedValues:C={}}=p,S={...C,...E},k=t=>{j=!0,d.has(t)&&(N=!0,d.delete(t)),p.needsAnimating[t]=!0;let s=e.getValue(t);s&&(s.liveStyle=!1)};for(let e in S){let t=E[e],s=C[e];if(c.hasOwnProperty(e))continue;let a=!1;(eC(t)&&eC(s)?sY(t,s):t===s)?void 0!==t&&d.has(e)?k(e):p.protectedKeys[e]=!0:null!=t?k(e):d.add(e)}p.prevProp=g,p.prevResolvedValues=E,p.isActive&&(c={...c,...E}),a&&e.blockInitialAnimation&&(j=!1);let T=!(y&&v)||N;j&&T&&l.push(...w.map(e=>({animation:e,options:{type:x}})))}if(d.size){let t={};if("boolean"!=typeof n.initial){let s=es(e,Array.isArray(n.initial)?n.initial[0]:n.initial);s&&s.transition&&(t.transition=s.transition)}d.forEach(s=>{let a=e.getBaseTarget(s),r=e.getValue(s);r&&(r.liveStyle=!0),t[s]=a??null}),l.push({animation:t})}let x=!!l.length;return a&&(!1===n.initial||n.initial===n.animate)&&!e.manuallyAnimateOnMount&&(x=!1),a=!1,x?t(l):Promise.resolve()}return{animateChanges:i,setActive:function(t,a){if(s[t].isActive===a)return Promise.resolve();e.variantChildren?.forEach(e=>e.animationState?.setActive(t,a)),s[t].isActive=a;let r=i(t);for(let e in s)s[e].protectedKeys={};return r},setAnimateFunction:function(s){t=s(e)},getState:()=>s,reset:()=>{s=s2(),a=!0}}}(e))}updateAnimationControlsSubscription(){let{animate:e}=this.node.getProps();Q(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:e}=this.node.getProps(),{animate:t}=this.node.prevProps||{};e!==t&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let s6=0;class s3 extends s4{constructor(){super(...arguments),this.id=s6++}update(){if(!this.node.presenceContext)return;let{isPresent:e,onExitComplete:t}=this.node.presenceContext,{isPresent:s}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===s)return;let a=this.node.animationState.setActive("exit",!e);t&&!e&&a.then(()=>{t(this.id)})}mount(){let{register:e,onExitComplete:t}=this.node.presenceContext||{};t&&t(this.id),e&&(this.unmount=e(this.id))}unmount(){}}let s8={x:!1,y:!1};function s7(e,t,s,a={passive:!0}){return e.addEventListener(t,s,a),()=>e.removeEventListener(t,s)}let s9=e=>"mouse"===e.pointerType?"number"!=typeof e.button||e.button<=0:!1!==e.isPrimary;function ae(e){return{point:{x:e.pageX,y:e.pageY}}}let at=e=>t=>s9(t)&&e(t,ae(t));function as(e,t,s,a){return s7(e,t,at(s),a)}function aa({top:e,left:t,right:s,bottom:a}){return{x:{min:t,max:s},y:{min:e,max:a}}}function ar(e){return e.max-e.min}function ai(e,t,s,a=.5){e.origin=a,e.originPoint=tu(t.min,t.max,e.origin),e.scale=ar(s)/ar(t),e.translate=tu(s.min,s.max,e.origin)-e.originPoint,(e.scale>=.9999&&e.scale<=1.0001||isNaN(e.scale))&&(e.scale=1),(e.translate>=-.01&&e.translate<=.01||isNaN(e.translate))&&(e.translate=0)}function an(e,t,s,a){ai(e.x,t.x,s.x,a?a.originX:void 0),ai(e.y,t.y,s.y,a?a.originY:void 0)}function ao(e,t,s){e.min=s.min+t.min,e.max=e.min+ar(t)}function al(e,t,s){e.min=t.min-s.min,e.max=e.min+ar(t)}function ad(e,t,s){al(e.x,t.x,s.x),al(e.y,t.y,s.y)}let ac=()=>({translate:0,scale:1,origin:0,originPoint:0}),au=()=>({x:ac(),y:ac()}),am=()=>({min:0,max:0}),ah=()=>({x:am(),y:am()});function ax(e){return[e("x"),e("y")]}function ap(e){return void 0===e||1===e}function ag({scale:e,scaleX:t,scaleY:s}){return!ap(e)||!ap(t)||!ap(s)}function af(e){return ag(e)||ab(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function ab(e){var t,s;return(t=e.x)&&"0%"!==t||(s=e.y)&&"0%"!==s}function ay(e,t,s,a,r){return void 0!==r&&(e=a+r*(e-a)),a+s*(e-a)+t}function av(e,t=0,s=1,a,r){e.min=ay(e.min,t,s,a,r),e.max=ay(e.max,t,s,a,r)}function aj(e,{x:t,y:s}){av(e.x,t.translate,t.scale,t.originPoint),av(e.y,s.translate,s.scale,s.originPoint)}function aN(e,t){e.min=e.min+t,e.max=e.max+t}function aw(e,t,s,a,r=.5){let i=tu(e.min,e.max,r);av(e,t,s,i,a)}function aE(e,t){aw(e.x,t.x,t.scaleX,t.scale,t.originX),aw(e.y,t.y,t.scaleY,t.scale,t.originY)}function aC(e,t){return aa(function(e,t){if(!t)return e;let s=t({x:e.left,y:e.top}),a=t({x:e.right,y:e.bottom});return{top:s.y,left:s.x,bottom:a.y,right:a.x}}(e.getBoundingClientRect(),t))}let aS=({current:e})=>e?e.ownerDocument.defaultView:null;function ak(e){return e&&"object"==typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}let aT=(e,t)=>Math.abs(e-t);class aA{constructor(e,t,{transformPagePoint:s,contextWindow:a,dragSnapToOrigin:r=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let e=aD(this.lastMoveEventInfo,this.history),t=null!==this.startEvent,s=function(e,t){return Math.sqrt(aT(e.x,t.x)**2+aT(e.y,t.y)**2)}(e.offset,{x:0,y:0})>=3;if(!t&&!s)return;let{point:a}=e,{timestamp:r}=eu;this.history.push({...a,timestamp:r});let{onStart:i,onMove:n}=this.handlers;t||(i&&i(this.lastMoveEvent,e),this.startEvent=this.lastMoveEvent),n&&n(this.lastMoveEvent,e)},this.handlePointerMove=(e,t)=>{this.lastMoveEvent=e,this.lastMoveEventInfo=aP(t,this.transformPagePoint),ed.update(this.updatePoint,!0)},this.handlePointerUp=(e,t)=>{this.end();let{onEnd:s,onSessionEnd:a,resumeAnimation:r}=this.handlers;if(this.dragSnapToOrigin&&r&&r(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let i=aD("pointercancel"===e.type?this.lastMoveEventInfo:aP(t,this.transformPagePoint),this.history);this.startEvent&&s&&s(e,i),a&&a(e,i)},!s9(e))return;this.dragSnapToOrigin=r,this.handlers=t,this.transformPagePoint=s,this.contextWindow=a||window;let i=aP(ae(e),this.transformPagePoint),{point:n}=i,{timestamp:o}=eu;this.history=[{...n,timestamp:o}];let{onSessionStart:l}=t;l&&l(e,aD(i,this.history)),this.removeListeners=eR(as(this.contextWindow,"pointermove",this.handlePointerMove),as(this.contextWindow,"pointerup",this.handlePointerUp),as(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),ec(this.updatePoint)}}function aP(e,t){return t?{point:t(e.point)}:e}function aR(e,t){return{x:e.x-t.x,y:e.y-t.y}}function aD({point:e},t){return{point:e,delta:aR(e,aM(t)),offset:aR(e,t[0]),velocity:function(e,t){if(e.length<2)return{x:0,y:0};let s=e.length-1,a=null,r=aM(e);for(;s>=0&&(a=e[s],!(r.timestamp-a.timestamp>eM(.1)));)s--;if(!a)return{x:0,y:0};let i=e$(r.timestamp-a.timestamp);if(0===i)return{x:0,y:0};let n={x:(r.x-a.x)/i,y:(r.y-a.y)/i};return n.x===1/0&&(n.x=0),n.y===1/0&&(n.y=0),n}(t,.1)}}function aM(e){return e[e.length-1]}function a$(e,t,s){return{min:void 0!==t?e.min+t:void 0,max:void 0!==s?e.max+s-(e.max-e.min):void 0}}function aL(e,t){let s=t.min-e.min,a=t.max-e.max;return t.max-t.min<e.max-e.min&&([s,a]=[a,s]),{min:s,max:a}}function aF(e,t,s){return{min:aV(e,t),max:aV(e,s)}}function aV(e,t){return"number"==typeof e?e:e[t]||0}let a_=new WeakMap;class az{constructor(e){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=ah(),this.visualElement=e}start(e,{snapToCursor:t=!1}={}){let{presenceContext:s}=this.visualElement;if(s&&!1===s.isPresent)return;let{dragSnapToOrigin:a}=this.getProps();this.panSession=new aA(e,{onSessionStart:e=>{let{dragSnapToOrigin:s}=this.getProps();s?this.pauseAnimation():this.stopAnimation(),t&&this.snapToCursor(ae(e).point)},onStart:(e,t)=>{let{drag:s,dragPropagation:a,onDragStart:r}=this.getProps();if(s&&!a&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(e){if("x"===e||"y"===e)if(s8[e])return null;else return s8[e]=!0,()=>{s8[e]=!1};return s8.x||s8.y?null:(s8.x=s8.y=!0,()=>{s8.x=s8.y=!1})}(s),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),ax(e=>{let t=this.getAxisMotionValue(e).get()||0;if(e4.test(t)){let{projection:s}=this.visualElement;if(s&&s.layout){let a=s.layout.layoutBox[e];a&&(t=ar(a)*(parseFloat(t)/100))}}this.originPoint[e]=t}),r&&ed.postRender(()=>r(e,t)),ek(this.visualElement,"transform");let{animationState:i}=this.visualElement;i&&i.setActive("whileDrag",!0)},onMove:(e,t)=>{let{dragPropagation:s,dragDirectionLock:a,onDirectionLock:r,onDrag:i}=this.getProps();if(!s&&!this.openDragLock)return;let{offset:n}=t;if(a&&null===this.currentDirection){this.currentDirection=function(e,t=10){let s=null;return Math.abs(e.y)>t?s="y":Math.abs(e.x)>t&&(s="x"),s}(n),null!==this.currentDirection&&r&&r(this.currentDirection);return}this.updateAxis("x",t.point,n),this.updateAxis("y",t.point,n),this.visualElement.render(),i&&i(e,t)},onSessionEnd:(e,t)=>this.stop(e,t),resumeAnimation:()=>ax(e=>"paused"===this.getAnimationState(e)&&this.getAxisMotionValue(e).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:a,contextWindow:aS(this.visualElement)})}stop(e,t){let s=this.isDragging;if(this.cancel(),!s)return;let{velocity:a}=t;this.startAnimation(a);let{onDragEnd:r}=this.getProps();r&&ed.postRender(()=>r(e,t))}cancel(){this.isDragging=!1;let{projection:e,animationState:t}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:s}=this.getProps();!s&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),t&&t.setActive("whileDrag",!1)}updateAxis(e,t,s){let{drag:a}=this.getProps();if(!s||!aI(e,a,this.currentDirection))return;let r=this.getAxisMotionValue(e),i=this.originPoint[e]+s[e];this.constraints&&this.constraints[e]&&(i=function(e,{min:t,max:s},a){return void 0!==t&&e<t?e=a?tu(t,e,a.min):Math.max(e,t):void 0!==s&&e>s&&(e=a?tu(s,e,a.max):Math.min(e,s)),e}(i,this.constraints[e],this.elastic[e])),r.set(i)}resolveConstraints(){let{dragConstraints:e,dragElastic:t}=this.getProps(),s=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,a=this.constraints;e&&ak(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&s?this.constraints=function(e,{top:t,left:s,bottom:a,right:r}){return{x:a$(e.x,s,r),y:a$(e.y,t,a)}}(s.layoutBox,e):this.constraints=!1,this.elastic=function(e=.35){return!1===e?e=0:!0===e&&(e=.35),{x:aF(e,"left","right"),y:aF(e,"top","bottom")}}(t),a!==this.constraints&&s&&this.constraints&&!this.hasMutatedConstraints&&ax(e=>{!1!==this.constraints&&this.getAxisMotionValue(e)&&(this.constraints[e]=function(e,t){let s={};return void 0!==t.min&&(s.min=t.min-e.min),void 0!==t.max&&(s.max=t.max-e.min),s}(s.layoutBox[e],this.constraints[e]))})}resolveRefConstraints(){var e;let{dragConstraints:t,onMeasureDragConstraints:s}=this.getProps();if(!t||!ak(t))return!1;let a=t.current;eV(null!==a,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:r}=this.visualElement;if(!r||!r.layout)return!1;let i=function(e,t,s){let a=aC(e,s),{scroll:r}=t;return r&&(aN(a.x,r.offset.x),aN(a.y,r.offset.y)),a}(a,r.root,this.visualElement.getTransformPagePoint()),n=(e=r.layout.layoutBox,{x:aL(e.x,i.x),y:aL(e.y,i.y)});if(s){let e=s(function({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}(n));this.hasMutatedConstraints=!!e,e&&(n=aa(e))}return n}startAnimation(e){let{drag:t,dragMomentum:s,dragElastic:a,dragTransition:r,dragSnapToOrigin:i,onDragTransitionEnd:n}=this.getProps(),o=this.constraints||{};return Promise.all(ax(n=>{if(!aI(n,t,this.currentDirection))return;let l=o&&o[n]||{};i&&(l={min:0,max:0});let d={type:"inertia",velocity:s?e[n]:0,bounceStiffness:a?200:1e6,bounceDamping:a?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...r,...l};return this.startAxisValueAnimation(n,d)})).then(n)}startAxisValueAnimation(e,t){let s=this.getAxisMotionValue(e);return ek(this.visualElement,e),s.start(sG(e,s,0,t,this.visualElement,!1))}stopAnimation(){ax(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){ax(e=>this.getAxisMotionValue(e).animation?.pause())}getAnimationState(e){return this.getAxisMotionValue(e).animation?.state}getAxisMotionValue(e){let t=`_drag${e.toUpperCase()}`,s=this.visualElement.getProps();return s[t]||this.visualElement.getValue(e,(s.initial?s.initial[e]:void 0)||0)}snapToCursor(e){ax(t=>{let{drag:s}=this.getProps();if(!aI(t,s,this.currentDirection))return;let{projection:a}=this.visualElement,r=this.getAxisMotionValue(t);if(a&&a.layout){let{min:s,max:i}=a.layout.layoutBox[t];r.set(e[t]-tu(s,i,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:e,dragConstraints:t}=this.getProps(),{projection:s}=this.visualElement;if(!ak(t)||!s||!this.constraints)return;this.stopAnimation();let a={x:0,y:0};ax(e=>{let t=this.getAxisMotionValue(e);if(t&&!1!==this.constraints){let s=t.get();a[e]=function(e,t){let s=.5,a=ar(e),r=ar(t);return r>a?s=t0(t.min,t.max-a,e.min):a>r&&(s=t0(e.min,e.max-r,t.min)),eD(0,1,s)}({min:s,max:s},this.constraints[e])}});let{transformTemplate:r}=this.visualElement.getProps();this.visualElement.current.style.transform=r?r({},""):"none",s.root&&s.root.updateScroll(),s.updateLayout(),this.resolveConstraints(),ax(t=>{if(!aI(t,e,null))return;let s=this.getAxisMotionValue(t),{min:r,max:i}=this.constraints[t];s.set(tu(r,i,a[t]))})}addListeners(){if(!this.visualElement.current)return;a_.set(this.visualElement,this);let e=as(this.visualElement.current,"pointerdown",e=>{let{drag:t,dragListener:s=!0}=this.getProps();t&&s&&this.start(e)}),t=()=>{let{dragConstraints:e}=this.getProps();ak(e)&&e.current&&(this.constraints=this.resolveRefConstraints())},{projection:s}=this.visualElement,a=s.addEventListener("measure",t);s&&!s.layout&&(s.root&&s.root.updateScroll(),s.updateLayout()),ed.read(t);let r=s7(window,"resize",()=>this.scalePositionWithinConstraints()),i=s.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t})=>{this.isDragging&&t&&(ax(t=>{let s=this.getAxisMotionValue(t);s&&(this.originPoint[t]+=e[t].translate,s.set(s.get()+e[t].translate))}),this.visualElement.render())});return()=>{r(),e(),a(),i&&i()}}getProps(){let e=this.visualElement.getProps(),{drag:t=!1,dragDirectionLock:s=!1,dragPropagation:a=!1,dragConstraints:r=!1,dragElastic:i=.35,dragMomentum:n=!0}=e;return{...e,drag:t,dragDirectionLock:s,dragPropagation:a,dragConstraints:r,dragElastic:i,dragMomentum:n}}}function aI(e,t,s){return(!0===t||t===e)&&(null===s||s===e)}class aO extends s4{constructor(e){super(e),this.removeGroupControls=er,this.removeListeners=er,this.controls=new az(e)}mount(){let{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||er}unmount(){this.removeGroupControls(),this.removeListeners()}}let aB=e=>(t,s)=>{e&&ed.postRender(()=>e(t,s))};class aU extends s4{constructor(){super(...arguments),this.removePointerDownListener=er}onPointerDown(e){this.session=new aA(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:aS(this.node)})}createPanHandlers(){let{onPanSessionStart:e,onPanStart:t,onPan:s,onPanEnd:a}=this.node.getProps();return{onSessionStart:aB(e),onStart:aB(t),onMove:s,onEnd:(e,t)=>{delete this.session,a&&ed.postRender(()=>a(e,t))}}}mount(){this.removePointerDownListener=as(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}let{schedule:aG}=el(queueMicrotask,!1),aq=(0,o.createContext)(null),aH=(0,o.createContext)({}),aW=(0,o.createContext)({}),aY={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function aX(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}let aK={correct:(e,t)=>{if(!t.target)return e;if("string"==typeof e)if(!e5.test(e))return e;else e=parseFloat(e);let s=aX(e,t.target.x),a=aX(e,t.target.y);return`${s}% ${a}%`}},aZ={};class aJ extends o.Component{componentDidMount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:s,layoutId:a}=this.props,{projection:r}=e;for(let e in a0)aZ[e]=a0[e],ez(e)&&(aZ[e].isCSSVariable=!0);r&&(t.group&&t.group.add(r),s&&s.register&&a&&s.register(r),r.root.didUpdate(),r.addEventListener("animationComplete",()=>{this.safeToRemove()}),r.setOptions({...r.options,onExitComplete:()=>this.safeToRemove()})),aY.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){let{layoutDependency:t,visualElement:s,drag:a,isPresent:r}=this.props,{projection:i}=s;return i&&(i.isPresent=r,a||e.layoutDependency!==t||void 0===t||e.isPresent!==r?i.willUpdate():this.safeToRemove(),e.isPresent!==r&&(r?i.promote():i.relegate()||ed.postRender(()=>{let e=i.getStack();e&&e.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),aG.postRender(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:s}=this.props,{projection:a}=e;a&&(a.scheduleCheckAfterUnmount(),t&&t.group&&t.group.remove(a),s&&s.deregister&&s.deregister(a))}safeToRemove(){let{safeToRemove:e}=this.props;e&&e()}render(){return null}}function aQ(e){let[t,s]=function(e=!0){let t=(0,o.useContext)(aq);if(null===t)return[!0,null];let{isPresent:s,onExitComplete:a,register:r}=t,i=(0,o.useId)();(0,o.useEffect)(()=>{if(e)return r(i)},[e]);let n=(0,o.useCallback)(()=>e&&a&&a(i),[i,a,e]);return!s&&a?[!1,n]:[!0]}(),a=(0,o.useContext)(aH);return(0,n.jsx)(aJ,{...e,layoutGroup:a,switchLayoutGroup:(0,o.useContext)(aW),isPresent:t,safeToRemove:s})}let a0={borderRadius:{...aK,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:aK,borderTopRightRadius:aK,borderBottomLeftRadius:aK,borderBottomRightRadius:aK,boxShadow:{correct:(e,{treeScale:t,projectionDelta:s})=>{let a=tl.parse(e);if(a.length>5)return e;let r=tl.createTransformer(e),i=+("number"!=typeof a[0]),n=s.x.scale*t.x,o=s.y.scale*t.y;a[0+i]/=n,a[1+i]/=o;let l=tu(n,o,.5);return"number"==typeof a[2+i]&&(a[2+i]/=l),"number"==typeof a[3+i]&&(a[3+i]/=l),r(a)}}};function a1(e){return sM(e)&&"ownerSVGElement"in e}let a2=(e,t)=>e.depth-t.depth;class a4{constructor(){this.children=[],this.isDirty=!1}add(e){eg(this.children,e),this.isDirty=!0}remove(e){ef(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(a2),this.isDirty=!1,this.children.forEach(e)}}function a5(e){return eS(e)?e.get():e}let a6=["TopLeft","TopRight","BottomLeft","BottomRight"],a3=a6.length,a8=e=>"string"==typeof e?parseFloat(e):e,a7=e=>"number"==typeof e||e5.test(e);function a9(e,t){return void 0!==e[t]?e[t]:e.borderRadius}let re=rs(0,.5,tY),rt=rs(.5,.95,er);function rs(e,t,s){return a=>a<e?0:a>t?1:s(t0(e,t,a))}function ra(e,t){e.min=t.min,e.max=t.max}function rr(e,t){ra(e.x,t.x),ra(e.y,t.y)}function ri(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function rn(e,t,s,a,r){return e-=t,e=a+1/s*(e-a),void 0!==r&&(e=a+1/r*(e-a)),e}function ro(e,t,[s,a,r],i,n){!function(e,t=0,s=1,a=.5,r,i=e,n=e){if(e4.test(t)&&(t=parseFloat(t),t=tu(n.min,n.max,t/100)-n.min),"number"!=typeof t)return;let o=tu(i.min,i.max,a);e===i&&(o-=t),e.min=rn(e.min,t,s,o,r),e.max=rn(e.max,t,s,o,r)}(e,t[s],t[a],t[r],t.scale,i,n)}let rl=["x","scaleX","originX"],rd=["y","scaleY","originY"];function rc(e,t,s,a){ro(e.x,t,rl,s?s.x:void 0,a?a.x:void 0),ro(e.y,t,rd,s?s.y:void 0,a?a.y:void 0)}function ru(e){return 0===e.translate&&1===e.scale}function rm(e){return ru(e.x)&&ru(e.y)}function rh(e,t){return e.min===t.min&&e.max===t.max}function rx(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function rp(e,t){return rx(e.x,t.x)&&rx(e.y,t.y)}function rg(e){return ar(e.x)/ar(e.y)}function rf(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class rb{constructor(){this.members=[]}add(e){eg(this.members,e),e.scheduleRender()}remove(e){if(ef(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){let e=this.members[this.members.length-1];e&&this.promote(e)}}relegate(e){let t,s=this.members.findIndex(t=>e===t);if(0===s)return!1;for(let e=s;e>=0;e--){let s=this.members[e];if(!1!==s.isPresent){t=s;break}}return!!t&&(this.promote(t),!0)}promote(e,t){let s=this.lead;if(e!==s&&(this.prevLead=s,this.lead=e,e.show(),s)){s.instance&&s.scheduleRender(),e.scheduleRender(),e.resumeFrom=s,t&&(e.resumeFrom.preserveOpacity=!0),s.snapshot&&(e.snapshot=s.snapshot,e.snapshot.latestValues=s.animationValues||s.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);let{crossfade:a}=e.options;!1===a&&s.hide()}}exitAnimationComplete(){this.members.forEach(e=>{let{options:t,resumingFrom:s}=e;t.onExitComplete&&t.onExitComplete(),s&&s.options.onExitComplete&&s.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let ry={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},rv=["","X","Y","Z"],rj={visibility:"hidden"},rN=0;function rw(e,t,s,a){let{latestValues:r}=t;r[e]&&(s[e]=r[e],t.setStaticValue(e,0),a&&(a[e]=0))}function rE({attachResizeListener:e,defaultParent:t,measureScroll:s,checkIsScrollRoot:a,resetTransform:r}){return class{constructor(e={},s=t?.()){this.id=rN++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,eo.value&&(ry.nodes=ry.calculatedTargetDeltas=ry.calculatedProjections=0),this.nodes.forEach(rk),this.nodes.forEach(r$),this.nodes.forEach(rL),this.nodes.forEach(rT),eo.addProjectionMetrics&&eo.addProjectionMetrics(ry)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=e,this.root=s?s.root||s:this,this.path=s?[...s.path,s]:[],this.parent=s,this.depth=s?s.depth+1:0;for(let e=0;e<this.path.length;e++)this.path[e].shouldResetTransform=!0;this.root===this&&(this.nodes=new a4)}addEventListener(e,t){return this.eventHandlers.has(e)||this.eventHandlers.set(e,new eb),this.eventHandlers.get(e).add(t)}notifyListeners(e,...t){let s=this.eventHandlers.get(e);s&&s.notify(...t)}hasListeners(e){return this.eventHandlers.has(e)}mount(t){if(this.instance)return;this.isSVG=a1(t)&&!(a1(t)&&"svg"===t.tagName),this.instance=t;let{layoutId:s,layout:a,visualElement:r}=this.options;if(r&&!r.current&&r.mount(t),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(a||s)&&(this.isLayoutDirty=!0),e){let s,a=()=>this.root.updateBlockedByResize=!1;e(t,()=>{this.root.updateBlockedByResize=!0,s&&s(),s=function(e,t){let s=ev.now(),a=({timestamp:r})=>{let i=r-s;i>=250&&(ec(a),e(i-t))};return ed.setup(a,!0),()=>ec(a)}(a,250),aY.hasAnimatedSinceResize&&(aY.hasAnimatedSinceResize=!1,this.nodes.forEach(rM))})}s&&this.root.registerSharedNode(s,this),!1!==this.options.animate&&r&&(s||a)&&this.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t,hasRelativeLayoutChanged:s,layout:a})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let i=this.options.transition||r.getDefaultTransition()||rO,{onLayoutAnimationStart:n,onLayoutAnimationComplete:o}=r.getProps(),l=!this.targetLayout||!rp(this.targetLayout,a),d=!t&&s;if(this.options.layoutRoot||this.resumeFrom||d||t&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(e,d);let t={...ea(i,"layout"),onPlay:n,onComplete:o};(r.shouldReduceMotion||this.options.layoutRoot)&&(t.delay=0,t.type=!1),this.startAnimation(t)}else t||rM(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=a})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let e=this.getStack();e&&e.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),ec(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(rF),this.animationId++)}getTransformTemplate(){let{visualElement:e}=this.options;return e&&e.getProps().transformTemplate}willUpdate(e=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function e(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;let{visualElement:s}=t.options;if(!s)return;let a=s.props[eA];if(window.MotionHasOptimisedAnimation(a,"transform")){let{layout:e,layoutId:s}=t.options;window.MotionCancelOptimisedAnimation(a,"transform",ed,!(e||s))}let{parent:r}=t;r&&!r.hasCheckedOptimisedAppear&&e(r)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let e=0;e<this.path.length;e++){let t=this.path[e];t.shouldResetTransform=!0,t.updateScroll("snapshot"),t.options.layoutRoot&&t.willUpdate(!1)}let{layoutId:t,layout:s}=this.options;if(void 0===t&&!s)return;let a=this.getTransformTemplate();this.prevTransformTemplateValue=a?a(this.latestValues,""):void 0,this.updateSnapshot(),e&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(rP);return}this.isUpdating||this.nodes.forEach(rR),this.isUpdating=!1,this.nodes.forEach(rD),this.nodes.forEach(rC),this.nodes.forEach(rS),this.clearAllSnapshots();let e=ev.now();eu.delta=eD(0,1e3/60,e-eu.timestamp),eu.timestamp=e,eu.isProcessing=!0,em.update.process(eu),em.preRender.process(eu),em.render.process(eu),eu.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,aG.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(rA),this.sharedNodes.forEach(rV)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,ed.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){ed.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||ar(this.snapshot.measuredBox.x)||ar(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let e=0;e<this.path.length;e++)this.path[e].updateScroll();let e=this.layout;this.layout=this.measure(!1),this.layoutCorrected=ah(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:t}=this.options;t&&t.notify("LayoutMeasure",this.layout.layoutBox,e?e.layoutBox:void 0)}updateScroll(e="measure"){let t=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===e&&(t=!1),t&&this.instance){let t=a(this.instance);this.scroll={animationId:this.root.animationId,phase:e,isRoot:t,offset:s(this.instance),wasRoot:this.scroll?this.scroll.isRoot:t}}}resetTransform(){if(!r)return;let e=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,t=this.projectionDelta&&!rm(this.projectionDelta),s=this.getTransformTemplate(),a=s?s(this.latestValues,""):void 0,i=a!==this.prevTransformTemplateValue;e&&this.instance&&(t||af(this.latestValues)||i)&&(r(this.instance,a),this.shouldResetTransform=!1,this.scheduleRender())}measure(e=!0){var t;let s=this.measurePageBox(),a=this.removeElementScroll(s);return e&&(a=this.removeTransform(a)),rG((t=a).x),rG(t.y),{animationId:this.root.animationId,measuredBox:s,layoutBox:a,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:e}=this.options;if(!e)return ah();let t=e.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(rH))){let{scroll:e}=this.root;e&&(aN(t.x,e.offset.x),aN(t.y,e.offset.y))}return t}removeElementScroll(e){let t=ah();if(rr(t,e),this.scroll?.wasRoot)return t;for(let s=0;s<this.path.length;s++){let a=this.path[s],{scroll:r,options:i}=a;a!==this.root&&r&&i.layoutScroll&&(r.wasRoot&&rr(t,e),aN(t.x,r.offset.x),aN(t.y,r.offset.y))}return t}applyTransform(e,t=!1){let s=ah();rr(s,e);for(let e=0;e<this.path.length;e++){let a=this.path[e];!t&&a.options.layoutScroll&&a.scroll&&a!==a.root&&aE(s,{x:-a.scroll.offset.x,y:-a.scroll.offset.y}),af(a.latestValues)&&aE(s,a.latestValues)}return af(this.latestValues)&&aE(s,this.latestValues),s}removeTransform(e){let t=ah();rr(t,e);for(let e=0;e<this.path.length;e++){let s=this.path[e];if(!s.instance||!af(s.latestValues))continue;ag(s.latestValues)&&s.updateSnapshot();let a=ah();rr(a,s.measurePageBox()),rc(t,s.latestValues,s.snapshot?s.snapshot.layoutBox:void 0,a)}return af(this.latestValues)&&rc(t,this.latestValues),t}setTargetDelta(e){this.targetDelta=e,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(e){this.options={...this.options,...e,crossfade:void 0===e.crossfade||e.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==eu.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(e=!1){let t=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=t.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=t.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=t.isSharedProjectionDirty);let s=!!this.resumingFrom||this!==t;if(!(e||s&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:a,layoutId:r}=this.options;if(this.layout&&(a||r)){if(this.resolvedRelativeTargetAt=eu.timestamp,!this.targetDelta&&!this.relativeTarget){let e=this.getClosestProjectingParent();e&&e.layout&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ah(),this.relativeTargetOrigin=ah(),ad(this.relativeTargetOrigin,this.layout.layoutBox,e.layout.layoutBox),rr(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=ah(),this.targetWithTransforms=ah()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var i,n,o;this.forceRelativeParentToResolveTarget(),i=this.target,n=this.relativeTarget,o=this.relativeParent.target,ao(i.x,n.x,o.x),ao(i.y,n.y,o.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):rr(this.target,this.layout.layoutBox),aj(this.target,this.targetDelta)):rr(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let e=this.getClosestProjectingParent();e&&!!e.resumingFrom==!!this.resumingFrom&&!e.options.layoutScroll&&e.target&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ah(),this.relativeTargetOrigin=ah(),ad(this.relativeTargetOrigin,this.target,e.target),rr(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}eo.value&&ry.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||ag(this.parent.latestValues)||ab(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let e=this.getLead(),t=!!this.resumingFrom||this!==e,s=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(s=!1),t&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(s=!1),this.resolvedRelativeTargetAt===eu.timestamp&&(s=!1),s)return;let{layout:a,layoutId:r}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(a||r))return;rr(this.layoutCorrected,this.layout.layoutBox);let i=this.treeScale.x,n=this.treeScale.y;!function(e,t,s,a=!1){let r,i,n=s.length;if(n){t.x=t.y=1;for(let o=0;o<n;o++){i=(r=s[o]).projectionDelta;let{visualElement:n}=r.options;(!n||!n.props.style||"contents"!==n.props.style.display)&&(a&&r.options.layoutScroll&&r.scroll&&r!==r.root&&aE(e,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),i&&(t.x*=i.x.scale,t.y*=i.y.scale,aj(e,i)),a&&af(r.latestValues)&&aE(e,r.latestValues))}t.x<1.0000000000001&&t.x>.999999999999&&(t.x=1),t.y<1.0000000000001&&t.y>.999999999999&&(t.y=1)}}(this.layoutCorrected,this.treeScale,this.path,t),e.layout&&!e.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(e.target=e.layout.layoutBox,e.targetWithTransforms=ah());let{target:o}=e;if(!o){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(ri(this.prevProjectionDelta.x,this.projectionDelta.x),ri(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),an(this.projectionDelta,this.layoutCorrected,o,this.latestValues),this.treeScale.x===i&&this.treeScale.y===n&&rf(this.projectionDelta.x,this.prevProjectionDelta.x)&&rf(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",o)),eo.value&&ry.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(e=!0){if(this.options.visualElement?.scheduleRender(),e){let e=this.getStack();e&&e.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=au(),this.projectionDelta=au(),this.projectionDeltaWithTransform=au()}setAnimationOrigin(e,t=!1){let s,a=this.snapshot,r=a?a.latestValues:{},i={...this.latestValues},n=au();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!t;let o=ah(),l=(a?a.source:void 0)!==(this.layout?this.layout.source:void 0),d=this.getStack(),c=!d||d.members.length<=1,u=!!(l&&!c&&!0===this.options.crossfade&&!this.path.some(rI));this.animationProgress=0,this.mixTargetDelta=t=>{let a=t/1e3;if(r_(n.x,e.x,a),r_(n.y,e.y,a),this.setTargetDelta(n),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var d,m,h,x,p,g;ad(o,this.layout.layoutBox,this.relativeParent.layout.layoutBox),h=this.relativeTarget,x=this.relativeTargetOrigin,p=o,g=a,rz(h.x,x.x,p.x,g),rz(h.y,x.y,p.y,g),s&&(d=this.relativeTarget,m=s,rh(d.x,m.x)&&rh(d.y,m.y))&&(this.isProjectionDirty=!1),s||(s=ah()),rr(s,this.relativeTarget)}l&&(this.animationValues=i,function(e,t,s,a,r,i){r?(e.opacity=tu(0,s.opacity??1,re(a)),e.opacityExit=tu(t.opacity??1,0,rt(a))):i&&(e.opacity=tu(t.opacity??1,s.opacity??1,a));for(let r=0;r<a3;r++){let i=`border${a6[r]}Radius`,n=a9(t,i),o=a9(s,i);(void 0!==n||void 0!==o)&&(n||(n=0),o||(o=0),0===n||0===o||a7(n)===a7(o)?(e[i]=Math.max(tu(a8(n),a8(o),a),0),(e4.test(o)||e4.test(n))&&(e[i]+="%")):e[i]=o)}(t.rotate||s.rotate)&&(e.rotate=tu(t.rotate||0,s.rotate||0,a))}(i,r,this.latestValues,a,u,c)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=a},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(e){this.notifyListeners("animationStart"),this.currentAnimation?.stop(!1),this.resumingFrom?.currentAnimation?.stop(!1),this.pendingAnimation&&(ec(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=ed.update(()=>{aY.hasAnimatedSinceResize=!0,eL.layout++,this.motionValue||(this.motionValue=eE(0)),this.currentAnimation=function(e,t,s){let a=eS(e)?e:eE(e);return a.start(sG("",a,t,s)),a.animation}(this.motionValue,[0,1e3],{...e,isSync:!0,onUpdate:t=>{this.mixTargetDelta(t),e.onUpdate&&e.onUpdate(t)},onStop:()=>{eL.layout--},onComplete:()=>{eL.layout--,e.onComplete&&e.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let e=this.getStack();e&&e.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop(!1)),this.completeAnimation()}applyTransformsToTarget(){let e=this.getLead(),{targetWithTransforms:t,target:s,layout:a,latestValues:r}=e;if(t&&s&&a){if(this!==e&&this.layout&&a&&rq(this.options.animationType,this.layout.layoutBox,a.layoutBox)){s=this.target||ah();let t=ar(this.layout.layoutBox.x);s.x.min=e.target.x.min,s.x.max=s.x.min+t;let a=ar(this.layout.layoutBox.y);s.y.min=e.target.y.min,s.y.max=s.y.min+a}rr(t,s),aE(t,r),an(this.projectionDeltaWithTransform,this.layoutCorrected,t,r)}}registerSharedNode(e,t){this.sharedNodes.has(e)||this.sharedNodes.set(e,new rb),this.sharedNodes.get(e).add(t);let s=t.options.initialPromotionConfig;t.promote({transition:s?s.transition:void 0,preserveFollowOpacity:s&&s.shouldPreserveFollowOpacity?s.shouldPreserveFollowOpacity(t):void 0})}isLead(){let e=this.getStack();return!e||e.lead===this}getLead(){let{layoutId:e}=this.options;return e&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:e}=this.options;return e?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:e}=this.options;if(e)return this.root.sharedNodes.get(e)}promote({needsReset:e,transition:t,preserveFollowOpacity:s}={}){let a=this.getStack();a&&a.promote(this,s),e&&(this.projectionDelta=void 0,this.needsReset=!0),t&&this.setOptions({transition:t})}relegate(){let e=this.getStack();return!!e&&e.relegate(this)}resetSkewAndRotation(){let{visualElement:e}=this.options;if(!e)return;let t=!1,{latestValues:s}=e;if((s.z||s.rotate||s.rotateX||s.rotateY||s.rotateZ||s.skewX||s.skewY)&&(t=!0),!t)return;let a={};s.z&&rw("z",e,a,this.animationValues);for(let t=0;t<rv.length;t++)rw(`rotate${rv[t]}`,e,a,this.animationValues),rw(`skew${rv[t]}`,e,a,this.animationValues);for(let t in e.render(),a)e.setStaticValue(t,a[t]),this.animationValues&&(this.animationValues[t]=a[t]);e.scheduleRender()}getProjectionStyles(e){if(!this.instance||this.isSVG)return;if(!this.isVisible)return rj;let t={visibility:""},s=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,t.opacity="",t.pointerEvents=a5(e?.pointerEvents)||"",t.transform=s?s(this.latestValues,""):"none",t;let a=this.getLead();if(!this.projectionDelta||!this.layout||!a.target){let t={};return this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=a5(e?.pointerEvents)||""),this.hasProjected&&!af(this.latestValues)&&(t.transform=s?s({},""):"none",this.hasProjected=!1),t}let r=a.animationValues||a.latestValues;this.applyTransformsToTarget(),t.transform=function(e,t,s){let a="",r=e.x.translate/t.x,i=e.y.translate/t.y,n=s?.z||0;if((r||i||n)&&(a=`translate3d(${r}px, ${i}px, ${n}px) `),(1!==t.x||1!==t.y)&&(a+=`scale(${1/t.x}, ${1/t.y}) `),s){let{transformPerspective:e,rotate:t,rotateX:r,rotateY:i,skewX:n,skewY:o}=s;e&&(a=`perspective(${e}px) ${a}`),t&&(a+=`rotate(${t}deg) `),r&&(a+=`rotateX(${r}deg) `),i&&(a+=`rotateY(${i}deg) `),n&&(a+=`skewX(${n}deg) `),o&&(a+=`skewY(${o}deg) `)}let o=e.x.scale*t.x,l=e.y.scale*t.y;return(1!==o||1!==l)&&(a+=`scale(${o}, ${l})`),a||"none"}(this.projectionDeltaWithTransform,this.treeScale,r),s&&(t.transform=s(r,t.transform));let{x:i,y:n}=this.projectionDelta;for(let e in t.transformOrigin=`${100*i.origin}% ${100*n.origin}% 0`,a.animationValues?t.opacity=a===this?r.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:r.opacityExit:t.opacity=a===this?void 0!==r.opacity?r.opacity:"":void 0!==r.opacityExit?r.opacityExit:0,aZ){if(void 0===r[e])continue;let{correct:s,applyTo:i,isCSSVariable:n}=aZ[e],o="none"===t.transform?r[e]:s(r[e],a);if(i){let e=i.length;for(let s=0;s<e;s++)t[i[s]]=o}else n?this.options.visualElement.renderState.vars[e]=o:t[e]=o}return this.options.layoutId&&(t.pointerEvents=a===this?a5(e?.pointerEvents)||"":"none"),t}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(e=>e.currentAnimation?.stop(!1)),this.root.nodes.forEach(rP),this.root.sharedNodes.clear()}}}function rC(e){e.updateLayout()}function rS(e){let t=e.resumeFrom?.snapshot||e.snapshot;if(e.isLead()&&e.layout&&t&&e.hasListeners("didUpdate")){let{layoutBox:s,measuredBox:a}=e.layout,{animationType:r}=e.options,i=t.source!==e.layout.source;"size"===r?ax(e=>{let a=i?t.measuredBox[e]:t.layoutBox[e],r=ar(a);a.min=s[e].min,a.max=a.min+r}):rq(r,t.layoutBox,s)&&ax(a=>{let r=i?t.measuredBox[a]:t.layoutBox[a],n=ar(s[a]);r.max=r.min+n,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[a].max=e.relativeTarget[a].min+n)});let n=au();an(n,s,t.layoutBox);let o=au();i?an(o,e.applyTransform(a,!0),t.measuredBox):an(o,s,t.layoutBox);let l=!rm(n),d=!1;if(!e.resumeFrom){let a=e.getClosestProjectingParent();if(a&&!a.resumeFrom){let{snapshot:r,layout:i}=a;if(r&&i){let n=ah();ad(n,t.layoutBox,r.layoutBox);let o=ah();ad(o,s,i.layoutBox),rp(n,o)||(d=!0),a.options.layoutRoot&&(e.relativeTarget=o,e.relativeTargetOrigin=n,e.relativeParent=a)}}}e.notifyListeners("didUpdate",{layout:s,snapshot:t,delta:o,layoutDelta:n,hasLayoutChanged:l,hasRelativeLayoutChanged:d})}else if(e.isLead()){let{onExitComplete:t}=e.options;t&&t()}e.options.transition=void 0}function rk(e){eo.value&&ry.nodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function rT(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function rA(e){e.clearSnapshot()}function rP(e){e.clearMeasurements()}function rR(e){e.isLayoutDirty=!1}function rD(e){let{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function rM(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function r$(e){e.resolveTargetDelta()}function rL(e){e.calcProjection()}function rF(e){e.resetSkewAndRotation()}function rV(e){e.removeLeadSnapshot()}function r_(e,t,s){e.translate=tu(t.translate,0,s),e.scale=tu(t.scale,1,s),e.origin=t.origin,e.originPoint=t.originPoint}function rz(e,t,s,a){e.min=tu(t.min,s.min,a),e.max=tu(t.max,s.max,a)}function rI(e){return e.animationValues&&void 0!==e.animationValues.opacityExit}let rO={duration:.45,ease:[.4,0,.1,1]},rB=e=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),rU=rB("applewebkit/")&&!rB("chrome/")?Math.round:er;function rG(e){e.min=rU(e.min),e.max=rU(e.max)}function rq(e,t,s){return"position"===e||"preserve-aspect"===e&&!(.2>=Math.abs(rg(t)-rg(s)))}function rH(e){return e!==e.root&&e.scroll?.wasRoot}let rW=rE({attachResizeListener:(e,t)=>s7(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),rY={current:void 0},rX=rE({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!rY.current){let e=new rW({});e.mount(window),e.setOptions({layoutScroll:!0}),rY.current=e}return rY.current},resetTransform:(e,t)=>{e.style.transform=void 0!==t?t:"none"},checkIsScrollRoot:e=>"fixed"===window.getComputedStyle(e).position});function rK(e,t){let s=function(e,t,s){if(e instanceof EventTarget)return[e];if("string"==typeof e){let t=document,s=(void 0)??t.querySelectorAll(e);return s?Array.from(s):[]}return Array.from(e)}(e),a=new AbortController;return[s,{passive:!0,...t,signal:a.signal},()=>a.abort()]}function rZ(e){return!("touch"===e.pointerType||s8.x||s8.y)}function rJ(e,t,s){let{props:a}=e;e.animationState&&a.whileHover&&e.animationState.setActive("whileHover","Start"===s);let r=a["onHover"+s];r&&ed.postRender(()=>r(t,ae(t)))}class rQ extends s4{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,s={}){let[a,r,i]=rK(e,s),n=e=>{if(!rZ(e))return;let{target:s}=e,a=t(s,e);if("function"!=typeof a||!s)return;let i=e=>{rZ(e)&&(a(e),s.removeEventListener("pointerleave",i))};s.addEventListener("pointerleave",i,r)};return a.forEach(e=>{e.addEventListener("pointerenter",n,r)}),i}(e,(e,t)=>(rJ(this.node,t,"Start"),e=>rJ(this.node,e,"End"))))}unmount(){}}class r0 extends s4{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch(t){e=!0}e&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=eR(s7(this.node.current,"focus",()=>this.onFocus()),s7(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let r1=(e,t)=>!!t&&(e===t||r1(e,t.parentElement)),r2=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),r4=new WeakSet;function r5(e){return t=>{"Enter"===t.key&&e(t)}}function r6(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}let r3=(e,t)=>{let s=e.currentTarget;if(!s)return;let a=r5(()=>{if(r4.has(s))return;r6(s,"down");let e=r5(()=>{r6(s,"up")});s.addEventListener("keyup",e,t),s.addEventListener("blur",()=>r6(s,"cancel"),t)});s.addEventListener("keydown",a,t),s.addEventListener("blur",()=>s.removeEventListener("keydown",a),t)};function r8(e){return s9(e)&&!(s8.x||s8.y)}function r7(e,t,s){let{props:a}=e;if(e.current instanceof HTMLButtonElement&&e.current.disabled)return;e.animationState&&a.whileTap&&e.animationState.setActive("whileTap","Start"===s);let r=a["onTap"+("End"===s?"":s)];r&&ed.postRender(()=>r(t,ae(t)))}class r9 extends s4{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,s={}){let[a,r,i]=rK(e,s),n=e=>{let a=e.currentTarget;if(!r8(e))return;r4.add(a);let i=t(a,e),n=(e,t)=>{window.removeEventListener("pointerup",o),window.removeEventListener("pointercancel",l),r4.has(a)&&r4.delete(a),r8(e)&&"function"==typeof i&&i(e,{success:t})},o=e=>{n(e,a===window||a===document||s.useGlobalTarget||r1(a,e.target))},l=e=>{n(e,!1)};window.addEventListener("pointerup",o,r),window.addEventListener("pointercancel",l,r)};return a.forEach(e=>{((s.useGlobalTarget?window:e).addEventListener("pointerdown",n,r),s$(e))&&(e.addEventListener("focus",e=>r3(e,r)),r2.has(e.tagName)||-1!==e.tabIndex||e.hasAttribute("tabindex")||(e.tabIndex=0))}),i}(e,(e,t)=>(r7(this.node,t,"Start"),(e,{success:t})=>r7(this.node,e,t?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let ie=new WeakMap,it=new WeakMap,is=e=>{let t=ie.get(e.target);t&&t(e)},ia=e=>{e.forEach(is)},ir={some:0,all:1};class ii extends s4{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:e={}}=this.node.getProps(),{root:t,margin:s,amount:a="some",once:r}=e,i={root:t?t.current:void 0,rootMargin:s,threshold:"number"==typeof a?a:ir[a]};return function(e,t,s){let a=function({root:e,...t}){let s=e||document;it.has(s)||it.set(s,{});let a=it.get(s),r=JSON.stringify(t);return a[r]||(a[r]=new IntersectionObserver(ia,{root:e,...t})),a[r]}(t);return ie.set(e,s),a.observe(e),()=>{ie.delete(e),a.unobserve(e)}}(this.node.current,i,e=>{let{isIntersecting:t}=e;if(this.isInView===t||(this.isInView=t,r&&!t&&this.hasEnteredView))return;t&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",t);let{onViewportEnter:s,onViewportLeave:a}=this.node.getProps(),i=t?s:a;i&&i(e)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:e,prevProps:t}=this.node;["amount","margin","root"].some(function({viewport:e={}},{viewport:t={}}={}){return s=>e[s]!==t[s]}(e,t))&&this.startObserver()}unmount(){}}let io=(0,o.createContext)({strict:!1}),il=(0,o.createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"}),id=(0,o.createContext)({});function ic(e){return Q(e.animate)||sZ.some(t=>sX(e[t]))}function iu(e){return!!(ic(e)||e.variants)}function im(e){return Array.isArray(e)?e.join(" "):e}let ih="undefined"!=typeof window,ix={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},ip={};for(let e in ix)ip[e]={isEnabled:t=>ix[e].some(e=>!!t[e])};let ig=Symbol.for("motionComponentSymbol"),ib=ih?o.useLayoutEffect:o.useEffect;function iy(e,{layout:t,layoutId:s}){return ex.has(e)||e.startsWith("origin")||(t||void 0!==s)&&(!!aZ[e]||"opacity"===e)}let iv=(e,t)=>t&&"number"==typeof e?t.transform(e):e,ij={...eU,transform:Math.round},iN={borderWidth:e5,borderTopWidth:e5,borderRightWidth:e5,borderBottomWidth:e5,borderLeftWidth:e5,borderRadius:e5,radius:e5,borderTopLeftRadius:e5,borderTopRightRadius:e5,borderBottomRightRadius:e5,borderBottomLeftRadius:e5,width:e5,maxWidth:e5,height:e5,maxHeight:e5,top:e5,right:e5,bottom:e5,left:e5,padding:e5,paddingTop:e5,paddingRight:e5,paddingBottom:e5,paddingLeft:e5,margin:e5,marginTop:e5,marginRight:e5,marginBottom:e5,marginLeft:e5,backgroundPositionX:e5,backgroundPositionY:e5,rotate:e2,rotateX:e2,rotateY:e2,rotateZ:e2,scale:eq,scaleX:eq,scaleY:eq,scaleZ:eq,skew:e2,skewX:e2,skewY:e2,distance:e5,translateX:e5,translateY:e5,translateZ:e5,x:e5,y:e5,z:e5,perspective:e5,transformPerspective:e5,opacity:eG,originX:e8,originY:e8,originZ:e5,zIndex:ij,fillOpacity:eG,strokeOpacity:eG,numOctaves:ij},iw={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},iE=eh.length;function iC(e,t,s){let{style:a,vars:r,transformOrigin:i}=e,n=!1,o=!1;for(let e in t){let s=t[e];if(ex.has(e)){n=!0;continue}if(ez(e)){r[e]=s;continue}{let t=iv(s,iN[e]);e.startsWith("origin")?(o=!0,i[e]=t):a[e]=t}}if(!t.transform&&(n||s?a.transform=function(e,t,s){let a="",r=!0;for(let i=0;i<iE;i++){let n=eh[i],o=e[n];if(void 0===o)continue;let l=!0;if(!(l="number"==typeof o?o===+!!n.startsWith("scale"):0===parseFloat(o))||s){let e=iv(o,iN[n]);if(!l){r=!1;let t=iw[n]||n;a+=`${t}(${e}) `}s&&(t[n]=e)}}return a=a.trim(),s?a=s(t,r?"":a):r&&(a="none"),a}(t,e.transform,s):a.transform&&(a.transform="none")),o){let{originX:e="50%",originY:t="50%",originZ:s=0}=i;a.transformOrigin=`${e} ${t} ${s}`}}let iS=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function ik(e,t,s){for(let a in t)eS(t[a])||iy(a,s)||(e[a]=t[a])}let iT={offset:"stroke-dashoffset",array:"stroke-dasharray"},iA={offset:"strokeDashoffset",array:"strokeDasharray"};function iP(e,{attrX:t,attrY:s,attrScale:a,pathLength:r,pathSpacing:i=1,pathOffset:n=0,...o},l,d,c){if(iC(e,o,d),l){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};let{attrs:u,style:m}=e;u.transform&&(m.transform=u.transform,delete u.transform),(m.transform||u.transformOrigin)&&(m.transformOrigin=u.transformOrigin??"50% 50%",delete u.transformOrigin),m.transform&&(m.transformBox=c?.transformBox??"fill-box",delete u.transformBox),void 0!==t&&(u.x=t),void 0!==s&&(u.y=s),void 0!==a&&(u.scale=a),void 0!==r&&function(e,t,s=1,a=0,r=!0){e.pathLength=1;let i=r?iT:iA;e[i.offset]=e5.transform(-a);let n=e5.transform(t),o=e5.transform(s);e[i.array]=`${n} ${o}`}(u,r,i,n,!1)}let iR=()=>({...iS(),attrs:{}}),iD=e=>"string"==typeof e&&"svg"===e.toLowerCase(),iM=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function i$(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||iM.has(e)}let iL=e=>!i$(e);try{!function(e){e&&(iL=t=>t.startsWith("on")?!i$(t):e(t))}(require("@emotion/is-prop-valid").default)}catch{}let iF=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function iV(e){if("string"!=typeof e||e.includes("-"));else if(iF.indexOf(e)>-1||/[A-Z]/u.test(e))return!0;return!1}let i_=e=>(t,s)=>{let a=(0,o.useContext)(id),r=(0,o.useContext)(aq),i=()=>(function({scrapeMotionValuesFromProps:e,createRenderState:t},s,a,r){return{latestValues:function(e,t,s,a){let r={},i=a(e,{});for(let e in i)r[e]=a5(i[e]);let{initial:n,animate:o}=e,l=ic(e),d=iu(e);t&&d&&!l&&!1!==e.inherit&&(void 0===n&&(n=t.initial),void 0===o&&(o=t.animate));let c=!!s&&!1===s.initial,u=(c=c||!1===n)?o:n;if(u&&"boolean"!=typeof u&&!Q(u)){let t=Array.isArray(u)?u:[u];for(let s=0;s<t.length;s++){let a=et(e,t[s]);if(a){let{transitionEnd:e,transition:t,...s}=a;for(let e in s){let t=s[e];if(Array.isArray(t)){let e=c?t.length-1:0;t=t[e]}null!==t&&(r[e]=t)}for(let t in e)r[t]=e[t]}}}return r}(s,a,r,e),renderState:t()}})(e,t,a,r);return s?i():function(e){let t=(0,o.useRef)(null);return null===t.current&&(t.current=e()),t.current}(i)};function iz(e,t,s){let{style:a}=e,r={};for(let i in a)(eS(a[i])||t.style&&eS(t.style[i])||iy(i,e)||s?.getValue(i)?.liveStyle!==void 0)&&(r[i]=a[i]);return r}let iI={useVisualState:i_({scrapeMotionValuesFromProps:iz,createRenderState:iS})};function iO(e,t,s){let a=iz(e,t,s);for(let s in e)(eS(e[s])||eS(t[s]))&&(a[-1!==eh.indexOf(s)?"attr"+s.charAt(0).toUpperCase()+s.substring(1):s]=e[s]);return a}let iB={useVisualState:i_({scrapeMotionValuesFromProps:iO,createRenderState:iR})},iU=e=>t=>t.test(e),iG=[eU,e5,e4,e2,e3,e6,{test:e=>"auto"===e,parse:e=>e}],iq=e=>iG.find(iU(e)),iH=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e),iW=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,iY=e=>/^0[^.\s]+$/u.test(e),iX=new Set(["brightness","contrast","saturate","opacity"]);function iK(e){let[t,s]=e.slice(0,-1).split("(");if("drop-shadow"===t)return e;let[a]=s.match(eW)||[];if(!a)return e;let r=s.replace(a,""),i=+!!iX.has(t);return a!==s&&(i*=100),t+"("+i+r+")"}let iZ=/\b([a-z-]*)\(.*?\)/gu,iJ={...tl,getAnimatableNone:e=>{let t=e.match(iZ);return t?t.map(iK).join(" "):e}},iQ={...iN,color:e9,backgroundColor:e9,outlineColor:e9,fill:e9,stroke:e9,borderColor:e9,borderTopColor:e9,borderRightColor:e9,borderBottomColor:e9,borderLeftColor:e9,filter:iJ,WebkitFilter:iJ},i0=e=>iQ[e];function i1(e,t){let s=i0(e);return s!==iJ&&(s=tl),s.getAnimatableNone?s.getAnimatableNone(t):void 0}let i2=new Set(["auto","none","0"]);class i4 extends sv{constructor(e,t,s,a,r){super(e,t,s,a,r,!0)}readKeyframes(){let{unresolvedKeyframes:e,element:t,name:s}=this;if(!t||!t.current)return;super.readKeyframes();for(let s=0;s<e.length;s++){let a=e[s];if("string"==typeof a&&eO(a=a.trim())){let r=function e(t,s,a=1){eV(a<=4,`Max CSS variable fallback depth detected in property "${t}". This may indicate a circular fallback dependency.`);let[r,i]=function(e){let t=iW.exec(e);if(!t)return[,];let[,s,a,r]=t;return[`--${s??a}`,r]}(t);if(!r)return;let n=window.getComputedStyle(s).getPropertyValue(r);if(n){let e=n.trim();return iH(e)?parseFloat(e):e}return eO(i)?e(i,s,a+1):i}(a,t.current);void 0!==r&&(e[s]=r),s===e.length-1&&(this.finalKeyframe=a)}}if(this.resolveNoneKeyframes(),!ep.has(s)||2!==e.length)return;let[a,r]=e,i=iq(a),n=iq(r);if(i!==n)if(sc(i)&&sc(n))for(let t=0;t<e.length;t++){let s=e[t];"string"==typeof s&&(e[t]=parseFloat(s))}else sh[s]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:e,name:t}=this,s=[];for(let t=0;t<e.length;t++){var a;(null===e[t]||("number"==typeof(a=e[t])?0===a:null===a||"none"===a||"0"===a||iY(a)))&&s.push(t)}s.length&&function(e,t,s){let a,r=0;for(;r<e.length&&!a;){let t=e[r];"string"==typeof t&&!i2.has(t)&&tr(t).values.length&&(a=e[r]),r++}if(a&&s)for(let r of t)e[r]=i1(s,a)}(e,s,t)}measureInitialState(){let{element:e,unresolvedKeyframes:t,name:s}=this;if(!e||!e.current)return;"height"===s&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=sh[s](e.measureViewportBox(),window.getComputedStyle(e.current)),t[0]=this.measuredOrigin;let a=t[t.length-1];void 0!==a&&e.getValue(s,a).jump(a,!1)}measureEndState(){let{element:e,name:t,unresolvedKeyframes:s}=this;if(!e||!e.current)return;let a=e.getValue(t);a&&a.jump(this.measuredOrigin,!1);let r=s.length-1,i=s[r];s[r]=sh[t](e.measureViewportBox(),window.getComputedStyle(e.current)),null!==i&&void 0===this.finalKeyframe&&(this.finalKeyframe=i),this.removedTransforms?.length&&this.removedTransforms.forEach(([t,s])=>{e.getValue(t).set(s)}),this.resolveNoneKeyframes()}}let i5=[...iG,e9,tl],i6=e=>i5.find(iU(e)),i3={current:null},i8={current:!1},i7=new WeakMap,i9=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class ne{scrapeMotionValuesFromProps(e,t,s){return{}}constructor({parent:e,props:t,presenceContext:s,reducedMotionConfig:a,blockInitialAnimation:r,visualState:i},n={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=sv,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let e=ev.now();this.renderScheduledAt<e&&(this.renderScheduledAt=e,ed.render(this.render,!1,!0))};let{latestValues:o,renderState:l}=i;this.latestValues=o,this.baseTarget={...o},this.initialValues=t.initial?{...o}:{},this.renderState=l,this.parent=e,this.props=t,this.presenceContext=s,this.depth=e?e.depth+1:0,this.reducedMotionConfig=a,this.options=n,this.blockInitialAnimation=!!r,this.isControllingVariants=ic(t),this.isVariantNode=iu(t),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);let{willChange:d,...c}=this.scrapeMotionValuesFromProps(t,{},this);for(let e in c){let t=c[e];void 0!==o[e]&&eS(t)&&t.set(o[e],!1)}}mount(e){this.current=e,i7.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((e,t)=>this.bindToMotionValue(t,e)),i8.current||function(){if(i8.current=!0,ih)if(window.matchMedia){let e=window.matchMedia("(prefers-reduced-motion)"),t=()=>i3.current=e.matches;e.addListener(t),t()}else i3.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||i3.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let e in this.projection&&this.projection.unmount(),ec(this.notifyUpdate),ec(this.render),this.valueSubscriptions.forEach(e=>e()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[e].clear();for(let e in this.features){let t=this.features[e];t&&(t.unmount(),t.isMounted=!1)}this.current=null}bindToMotionValue(e,t){let s;this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();let a=ex.has(e);a&&this.onBindTransform&&this.onBindTransform();let r=t.on("change",t=>{this.latestValues[e]=t,this.props.onUpdate&&ed.preRender(this.notifyUpdate),a&&this.projection&&(this.projection.isTransformDirty=!0)}),i=t.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(s=window.MotionCheckAppearSync(this,e,t)),this.valueSubscriptions.set(e,()=>{r(),i(),s&&s(),t.owner&&t.stop()})}sortNodePosition(e){return this.current&&this.sortInstanceNodePosition&&this.type===e.type?this.sortInstanceNodePosition(this.current,e.current):0}updateFeatures(){let e="animation";for(e in ip){let t=ip[e];if(!t)continue;let{isEnabled:s,Feature:a}=t;if(!this.features[e]&&a&&s(this.props)&&(this.features[e]=new a(this)),this.features[e]){let t=this.features[e];t.isMounted?t.update():(t.mount(),t.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):ah()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,t){this.latestValues[e]=t}update(e,t){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=t;for(let t=0;t<i9.length;t++){let s=i9[t];this.propEventSubscriptions[s]&&(this.propEventSubscriptions[s](),delete this.propEventSubscriptions[s]);let a=e["on"+s];a&&(this.propEventSubscriptions[s]=this.on(s,a))}this.prevMotionValues=function(e,t,s){for(let a in t){let r=t[a],i=s[a];if(eS(r))e.addValue(a,r);else if(eS(i))e.addValue(a,eE(r,{owner:e}));else if(i!==r)if(e.hasValue(a)){let t=e.getValue(a);!0===t.liveStyle?t.jump(r):t.hasAnimated||t.set(r)}else{let t=e.getStaticValue(a);e.addValue(a,eE(void 0!==t?t:r,{owner:e}))}}for(let a in s)void 0===t[a]&&e.removeValue(a);return t}(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){let t=this.getClosestVariantNode();if(t)return t.variantChildren&&t.variantChildren.add(e),()=>t.variantChildren.delete(e)}addValue(e,t){let s=this.values.get(e);t!==s&&(s&&this.removeValue(e),this.bindToMotionValue(e,t),this.values.set(e,t),this.latestValues[e]=t.get())}removeValue(e){this.values.delete(e);let t=this.valueSubscriptions.get(e);t&&(t(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];let s=this.values.get(e);return void 0===s&&void 0!==t&&(s=eE(null===t?void 0:t,{owner:this}),this.addValue(e,s)),s}readValue(e,t){let s=void 0===this.latestValues[e]&&this.current?this.getBaseTargetFromProps(this.props,e)??this.readValueFromInstance(this.current,e,this.options):this.latestValues[e];return null!=s&&("string"==typeof s&&(iH(s)||iY(s))?s=parseFloat(s):!i6(s)&&tl.test(t)&&(s=i1(e,t)),this.setBaseTarget(e,eS(s)?s.get():s)),eS(s)?s.get():s}setBaseTarget(e,t){this.baseTarget[e]=t}getBaseTarget(e){let t,{initial:s}=this.props;if("string"==typeof s||"object"==typeof s){let a=et(this.props,s,this.presenceContext?.custom);a&&(t=a[e])}if(s&&void 0!==t)return t;let a=this.getBaseTargetFromProps(this.props,e);return void 0===a||eS(a)?void 0!==this.initialValues[e]&&void 0===t?void 0:this.baseTarget[e]:a}on(e,t){return this.events[e]||(this.events[e]=new eb),this.events[e].add(t)}notify(e,...t){this.events[e]&&this.events[e].notify(...t)}}class nt extends ne{constructor(){super(...arguments),this.KeyframeResolver=i4}sortInstanceNodePosition(e,t){return 2&e.compareDocumentPosition(t)?1:-1}getBaseTargetFromProps(e,t){return e.style?e.style[t]:void 0}removeValueFromRenderState(e,{vars:t,style:s}){delete t[e],delete s[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:e}=this.props;eS(e)&&(this.childSubscription=e.on("change",e=>{this.current&&(this.current.textContent=`${e}`)}))}}function ns(e,{style:t,vars:s},a,r){for(let i in Object.assign(e.style,t,r&&r.getProjectionStyles(a)),s)e.style.setProperty(i,s[i])}class na extends nt{constructor(){super(...arguments),this.type="html",this.renderInstance=ns}readValueFromInstance(e,t){if(ex.has(t))return this.projection?.isProjecting?sn(t):sl(e,t);{let s=window.getComputedStyle(e),a=(ez(t)?s.getPropertyValue(t):s[t])||0;return"string"==typeof a?a.trim():a}}measureInstanceViewportBox(e,{transformPagePoint:t}){return aC(e,t)}build(e,t,s){iC(e,t,s.transformTemplate)}scrapeMotionValuesFromProps(e,t,s){return iz(e,t,s)}}let nr=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class ni extends nt{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=ah}getBaseTargetFromProps(e,t){return e[t]}readValueFromInstance(e,t){if(ex.has(t)){let e=i0(t);return e&&e.default||0}return t=nr.has(t)?t:eT(t),e.getAttribute(t)}scrapeMotionValuesFromProps(e,t,s){return iO(e,t,s)}build(e,t,s){iP(e,t,this.isSVGTag,s.transformTemplate,s.style)}renderInstance(e,t,s,a){for(let s in ns(e,t,void 0,a),t.attrs)e.setAttribute(nr.has(s)?s:eT(s),t.attrs[s])}mount(e){this.isSVGTag=iD(e.tagName),super.mount(e)}}let nn=function(e){if("undefined"==typeof Proxy)return e;let t=new Map;return new Proxy((...t)=>e(...t),{get:(s,a)=>"create"===a?e:(t.has(a)||t.set(a,e(a)),t.get(a))})}((r={animation:{Feature:s5},exit:{Feature:s3},inView:{Feature:ii},tap:{Feature:r9},focus:{Feature:r0},hover:{Feature:rQ},pan:{Feature:aU},drag:{Feature:aO,ProjectionNode:rX,MeasureLayout:aQ},layout:{ProjectionNode:rX,MeasureLayout:aQ}},i=(e,t)=>iV(e)?new ni(t):new na(t,{allowProjection:e!==o.Fragment}),function(e,{forwardMotionProps:t}={forwardMotionProps:!1}){return function({preloadedFeatures:e,createVisualElement:t,useRender:s,useVisualState:a,Component:r}){function i(e,i){var l,d,c;let u,m={...(0,o.useContext)(il),...e,layoutId:function({layoutId:e}){let t=(0,o.useContext)(aH).id;return t&&void 0!==e?t+"-"+e:e}(e)},{isStatic:h}=m,x=function(e){let{initial:t,animate:s}=function(e,t){if(ic(e)){let{initial:t,animate:s}=e;return{initial:!1===t||sX(t)?t:void 0,animate:sX(s)?s:void 0}}return!1!==e.inherit?t:{}}(e,(0,o.useContext)(id));return(0,o.useMemo)(()=>({initial:t,animate:s}),[im(t),im(s)])}(e),p=a(e,h);if(!h&&ih){d=0,c=0,(0,o.useContext)(io).strict;let e=function(e){let{drag:t,layout:s}=ip;if(!t&&!s)return{};let a={...t,...s};return{MeasureLayout:t?.isEnabled(e)||s?.isEnabled(e)?a.MeasureLayout:void 0,ProjectionNode:a.ProjectionNode}}(m);u=e.MeasureLayout,x.visualElement=function(e,t,s,a,r){let{visualElement:i}=(0,o.useContext)(id),n=(0,o.useContext)(io),l=(0,o.useContext)(aq),d=(0,o.useContext)(il).reducedMotion,c=(0,o.useRef)(null);a=a||n.renderer,!c.current&&a&&(c.current=a(e,{visualState:t,parent:i,props:s,presenceContext:l,blockInitialAnimation:!!l&&!1===l.initial,reducedMotionConfig:d}));let u=c.current,m=(0,o.useContext)(aW);u&&!u.projection&&r&&("html"===u.type||"svg"===u.type)&&function(e,t,s,a){let{layoutId:r,layout:i,drag:n,dragConstraints:o,layoutScroll:l,layoutRoot:d,layoutCrossfade:c}=t;e.projection=new s(e.latestValues,t["data-framer-portal-id"]?void 0:function e(t){if(t)return!1!==t.options.allowProjection?t.projection:e(t.parent)}(e.parent)),e.projection.setOptions({layoutId:r,layout:i,alwaysMeasureLayout:!!n||o&&ak(o),visualElement:e,animationType:"string"==typeof i?i:"both",initialPromotionConfig:a,crossfade:c,layoutScroll:l,layoutRoot:d})}(c.current,s,r,m);let h=(0,o.useRef)(!1);(0,o.useInsertionEffect)(()=>{u&&h.current&&u.update(s,l)});let x=s[eA],p=(0,o.useRef)(!!x&&!window.MotionHandoffIsComplete?.(x)&&window.MotionHasOptimisedAnimation?.(x));return ib(()=>{u&&(h.current=!0,window.MotionIsMounted=!0,u.updateFeatures(),aG.render(u.render),p.current&&u.animationState&&u.animationState.animateChanges())}),(0,o.useEffect)(()=>{u&&(!p.current&&u.animationState&&u.animationState.animateChanges(),p.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(x)}),p.current=!1))}),u}(r,p,m,t,e.ProjectionNode)}return(0,n.jsxs)(id.Provider,{value:x,children:[u&&x.visualElement?(0,n.jsx)(u,{visualElement:x.visualElement,...m}):null,s(r,e,(l=x.visualElement,(0,o.useCallback)(e=>{e&&p.onMount&&p.onMount(e),l&&(e?l.mount(e):l.unmount()),i&&("function"==typeof i?i(e):ak(i)&&(i.current=e))},[l])),p,h,x.visualElement)]})}e&&function(e){for(let t in e)ip[t]={...ip[t],...e[t]}}(e),i.displayName=`motion.${"string"==typeof r?r:`create(${r.displayName??r.name??""})`}`;let l=(0,o.forwardRef)(i);return l[ig]=r,l}({...iV(e)?iB:iI,preloadedFeatures:r,useRender:function(e=!1){return(t,s,a,{latestValues:r},i)=>{let n=(iV(t)?function(e,t,s,a){let r=(0,o.useMemo)(()=>{let s=iR();return iP(s,t,iD(a),e.transformTemplate,e.style),{...s.attrs,style:{...s.style}}},[t]);if(e.style){let t={};ik(t,e.style,e),r.style={...t,...r.style}}return r}:function(e,t){let s={},a=function(e,t){let s=e.style||{},a={};return ik(a,s,e),Object.assign(a,function({transformTemplate:e},t){return(0,o.useMemo)(()=>{let s=iS();return iC(s,t,e),Object.assign({},s.vars,s.style)},[t])}(e,t)),a}(e,t);return e.drag&&!1!==e.dragListener&&(s.draggable=!1,a.userSelect=a.WebkitUserSelect=a.WebkitTouchCallout="none",a.touchAction=!0===e.drag?"none":`pan-${"x"===e.drag?"y":"x"}`),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(s.tabIndex=0),s.style=a,s})(s,r,i,t),l=function(e,t,s){let a={};for(let r in e)("values"!==r||"object"!=typeof e.values)&&(iL(r)||!0===s&&i$(r)||!t&&!i$(r)||e.draggable&&r.startsWith("onDrag"))&&(a[r]=e[r]);return a}(s,"string"==typeof t,e),d=t!==o.Fragment?{...l,...n,ref:a}:{},{children:c}=s,u=(0,o.useMemo)(()=>eS(c)?c.get():c,[c]);return(0,o.createElement)(t,{...d,children:u})}}(t),createVisualElement:i,Component:e})})),no=({flashcards:e,activeIndex:t,respondiendo:s,onRespuesta:a,onNavigate:r,onVolver:i,onReiniciarProgreso:l,onVerHistorial:c})=>{let u=e[t],[m,h]=(0,o.useState)(!1);(0,o.useEffect)(()=>{h(!1)},[t]);let x=e=>{!(e&&e.target.closest("button"))&&(s||h(e=>!e))},p=(e,t)=>{e.stopPropagation(),s||a(t)},g=(e,t)=>{e.stopPropagation(),s||t()};return u?(0,n.jsxs)("div",{className:"flex flex-col items-center w-full",children:[(0,n.jsxs)("div",{className:"w-full flex justify-between items-center mb-4 px-2",children:[(0,n.jsxs)("button",{onClick:i,className:"flex items-center text-sm text-gray-600 hover:text-gray-900 p-2 rounded-md hover:bg-gray-100 transition-colors",disabled:s,children:[(0,n.jsx)(Z,{className:"mr-1"})," Volver"]}),(0,n.jsxs)("div",{className:"text-sm text-gray-500",children:[t+1," / ",e.length]})]}),(0,n.jsx)("div",{className:"w-full max-w-2xl mx-auto",children:(0,n.jsx)("div",{className:"relative w-full h-[24rem] sm:h-[28rem] perspective-1000",onClick:()=>x(),children:(0,n.jsxs)(nn.div,{className:"absolute w-full h-full transform-style-3d",animate:{rotateY:180*!!m},transition:{duration:.6},children:[(0,n.jsxs)("div",{className:"absolute w-full h-full backface-hidden bg-white rounded-xl shadow-xl border border-gray-200 p-6 flex flex-col justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsxs)("div",{className:"text-xs text-gray-400 mb-2",children:[u.progreso?.estado&&(0,n.jsx)("span",{className:`px-2 py-0.5 rounded-full font-medium ${"nuevo"===u.progreso.estado?"bg-blue-100 text-blue-700":"aprendiendo"===u.progreso.estado?"bg-yellow-100 text-yellow-700":"repasando"===u.progreso.estado?"bg-orange-100 text-orange-700":"bg-green-100 text-green-700"}`,children:u.progreso.estado.charAt(0).toUpperCase()+u.progreso.estado.slice(1)}),!u.progreso?.estado&&(0,n.jsx)("span",{className:"bg-blue-100 text-blue-700 px-2 py-0.5 rounded-full font-medium text-xs",children:"Nuevo"})]}),(0,n.jsx)("div",{className:"flex-grow flex items-center justify-center min-h-[150px] sm:min-h-[200px]",children:(0,n.jsx)("h3",{className:"text-lg sm:text-xl font-semibold text-center text-gray-800 break-words",children:u.pregunta})})]}),(0,n.jsx)("div",{className:"text-center mt-4",children:(0,n.jsx)("button",{onClick:e=>{e.stopPropagation(),x()},className:"bg-blue-500 hover:bg-blue-600 text-white font-semibold py-2 px-6 rounded-lg text-sm transition-colors",disabled:s,children:"Mostrar respuesta"})})]}),(0,n.jsxs)("div",{className:"absolute w-full h-full backface-hidden bg-white rounded-xl shadow-xl border border-gray-200 p-6 flex flex-col justify-between rotate-y-180",children:[(0,n.jsx)("div",{className:"flex-grow flex items-center justify-center min-h-[150px] sm:min-h-[200px] overflow-y-auto",children:(0,n.jsx)("p",{className:"text-base sm:text-lg text-center text-gray-700 whitespace-pre-wrap break-words transform-none",children:u.respuesta})}),(0,n.jsxs)("div",{className:"mt-4 space-y-3",children:[(0,n.jsx)("p",{className:"text-center text-sm font-medium text-gray-600",children:"\xbfQu\xe9 tal te ha resultado?"}),(0,n.jsxs)("div",{className:"flex justify-around space-x-2 sm:space-x-3",children:[(0,n.jsxs)("button",{onClick:e=>p(e,"dificil"),disabled:s,className:"flex-1 flex flex-col items-center p-3 rounded-lg bg-red-50 hover:bg-red-100 text-red-600 transition-colors disabled:opacity-50",children:[(0,n.jsx)(d.rxb,{className:"mb-1 text-xl"})," ",(0,n.jsx)("span",{className:"text-xs font-medium",children:"Dif\xedcil"})]}),(0,n.jsxs)("button",{onClick:e=>p(e,"normal"),disabled:s,className:"flex-1 flex flex-col items-center p-3 rounded-lg bg-yellow-50 hover:bg-yellow-100 text-yellow-600 transition-colors disabled:opacity-50",children:[(0,n.jsx)(d.YrT,{className:"mb-1 text-xl"})," ",(0,n.jsx)("span",{className:"text-xs font-medium",children:"Normal"})]}),(0,n.jsxs)("button",{onClick:e=>p(e,"facil"),disabled:s,className:"flex-1 flex flex-col items-center p-3 rounded-lg bg-green-50 hover:bg-green-100 text-green-600 transition-colors disabled:opacity-50",children:[(0,n.jsx)(d.Ydy,{className:"mb-1 text-xl"})," ",(0,n.jsx)("span",{className:"text-xs font-medium",children:"F\xe1cil"})]})]}),(l||c)&&(0,n.jsxs)("div",{className:"flex justify-center space-x-4 pt-2 text-xs",children:[l&&(0,n.jsxs)("button",{onClick:e=>g(e,()=>l(u.id)),disabled:s,className:"text-gray-500 hover:text-gray-700 underline flex items-center",children:[(0,n.jsx)(d.VI6,{size:12,className:"mr-1"})," Reiniciar"]}),c&&(0,n.jsxs)("button",{onClick:e=>g(e,()=>c(u.id)),disabled:s,className:"text-blue-500 hover:text-blue-700 underline flex items-center",children:[(0,n.jsx)(d.lrG,{size:12,className:"mr-1"})," Ver Historial"]})]})]})]})]})})}),(0,n.jsxs)("div",{className:"w-full max-w-2xl mx-auto flex justify-between mt-6 px-2",children:[(0,n.jsxs)("button",{onClick:()=>r("prev"),disabled:0===t||s,className:`flex items-center text-sm p-2 rounded-md transition-colors ${0===t||s?"text-gray-400 cursor-not-allowed":"text-gray-600 hover:text-gray-900 hover:bg-gray-100"}`,children:[(0,n.jsx)(Z,{className:"mr-1"})," Anterior"]}),(0,n.jsxs)("button",{onClick:()=>r("next"),disabled:t===e.length-1||s,className:`flex items-center text-sm p-2 rounded-md transition-colors ${t===e.length-1||s?"text-gray-400 cursor-not-allowed":"text-gray-600 hover:text-gray-900 hover:bg-gray-100"}`,children:["Siguiente ",(0,n.jsx)(J,{className:"ml-1"})]})]})]}):(0,n.jsxs)("div",{className:"flex flex-col items-center justify-center h-96 text-gray-500",children:["Cargando tarjeta...",(0,n.jsxs)("button",{onClick:i,className:"mt-4 flex items-center text-blue-600 hover:text-blue-800",children:[(0,n.jsx)(Z,{className:"mr-1"})," Volver"]})]})};function nl({flashcard:e,isOpen:t,onClose:s,onSave:a}){let[r,i]=(0,o.useState)(""),[l,c]=(0,o.useState)(""),[h,x]=(0,o.useState)(!1),p=async()=>{let t;if(!r.trim()||!l.trim())return void m.Ay.error("La pregunta y respuesta no pueden estar vac\xedas");x(!0);try{if(t=m.Ay.loading("Guardando cambios..."),await (0,u.xq)(e.id,r.trim(),l.trim())){m.Ay.success("Flashcard actualizada exitosamente",{id:t});let i={...e,pregunta:r.trim(),respuesta:l.trim()};a(i),s()}else m.Ay.error("Error al actualizar la flashcard",{id:t})}catch(e){console.error("Error al actualizar flashcard:",e),m.Ay.error("Error al actualizar la flashcard",{id:t})}finally{x(!1)}},g=()=>{i(e.pregunta),c(e.respuesta),s()};return t?(0,n.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,n.jsxs)("div",{className:"bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Editar Flashcard"}),(0,n.jsx)("button",{onClick:g,className:"text-gray-400 hover:text-gray-600 transition-colors",disabled:h,children:(0,n.jsx)(d.yGN,{size:24})})]}),(0,n.jsxs)("div",{className:"p-6 space-y-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Pregunta"}),(0,n.jsx)("textarea",{value:r,onChange:e=>i(e.target.value),className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none",rows:4,placeholder:"Escribe la pregunta aqu\xed...",disabled:h})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Respuesta"}),(0,n.jsx)("textarea",{value:l,onChange:e=>c(e.target.value),className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none",rows:6,placeholder:"Escribe la respuesta aqu\xed...",disabled:h})]}),e.progreso?.estado&&(0,n.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,n.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"Estado actual"}),(0,n.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-600",children:[(0,n.jsx)("span",{className:`px-2 py-1 rounded-full text-xs ${"nuevo"===e.progreso.estado?"bg-blue-100 text-blue-800":"aprendiendo"===e.progreso.estado?"bg-yellow-100 text-yellow-800":"repasando"===e.progreso.estado?"bg-orange-100 text-orange-800":"bg-green-100 text-green-800"}`,children:e.progreso.estado}),(0,n.jsxs)("span",{children:["Repeticiones: ",e.progreso.repeticiones]}),(0,n.jsxs)("span",{children:["Intervalo: ",e.progreso.intervalo," d\xedas"]})]})]})]}),(0,n.jsxs)("div",{className:"flex justify-end space-x-3 p-6 border-t border-gray-200",children:[(0,n.jsx)("button",{onClick:g,className:"px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors",disabled:h,children:"Cancelar"}),(0,n.jsx)("button",{onClick:p,disabled:h||!r.trim()||!l.trim(),className:"px-4 py-2 bg-blue-500 text-white hover:bg-blue-600 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center",children:h?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(d.TwU,{className:"animate-spin mr-2"}),"Guardando..."]}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(d.Bc_,{className:"mr-2"}),"Guardar cambios"]})})]})]})}):null}let nd=({onClose:e})=>{let[t,s]=(0,o.useState)(null),[a,r]=(0,o.useState)(!0),[i,l]=(0,o.useState)("");(0,o.useEffect)(()=>{c()},[]);let c=async()=>{try{r(!0);let e=await (0,u.oE)();if(0===e.length)return void s({totalColecciones:0,totalFlashcards:0,totalNuevas:0,totalAprendiendo:0,totalRepasando:0,totalAprendidas:0,totalParaHoy:0,coleccionesConMasActividad:[]});let t=await Promise.all(e.map(async e=>{let t=await (0,u.yV)(e.id);return{coleccion:e,estadisticas:t}})),a={totalColecciones:e.length,totalFlashcards:t.reduce((e,t)=>e+t.estadisticas.total,0),totalNuevas:t.reduce((e,t)=>e+t.estadisticas.nuevas,0),totalAprendiendo:t.reduce((e,t)=>e+t.estadisticas.aprendiendo,0),totalRepasando:t.reduce((e,t)=>e+t.estadisticas.repasando,0),totalAprendidas:t.reduce((e,t)=>e+t.estadisticas.aprendidas,0),totalParaHoy:t.reduce((e,t)=>e+t.estadisticas.paraHoy,0),coleccionesConMasActividad:t.map(e=>({id:e.coleccion.id,titulo:e.coleccion.titulo,totalRevisiones:e.estadisticas.total,paraHoy:e.estadisticas.paraHoy})).sort((e,t)=>t.paraHoy-e.paraHoy).slice(0,5)};s(a)}catch(e){console.error("Error al cargar estad\xedsticas generales:",e),l("No se pudieron cargar las estad\xedsticas generales")}finally{r(!1)}};return a?(0,n.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,n.jsx)("div",{className:"bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto",children:(0,n.jsx)("div",{className:"flex justify-center items-center h-40",children:(0,n.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"})})})}):i?(0,n.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,n.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto",children:[(0,n.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,n.jsx)("h2",{className:"text-xl font-bold",children:"Error"}),(0,n.jsx)("button",{onClick:e,className:"text-gray-500 hover:text-gray-700",children:(0,n.jsx)(d.yGN,{size:24})})]}),(0,n.jsx)("div",{className:"text-red-500",children:i})]})}):t?(0,n.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,n.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto",children:[(0,n.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,n.jsx)("h2",{className:"text-2xl font-bold",children:"Estad\xedsticas Generales de Flashcards"}),(0,n.jsx)("button",{onClick:e,className:"text-gray-500 hover:text-gray-700 transition-colors",children:(0,n.jsx)(d.yGN,{size:24})})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6",children:[(0,n.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,n.jsxs)("div",{className:"flex items-center mb-2",children:[(0,n.jsx)(d.D1A,{className:"text-blue-600 mr-2 text-xl"}),(0,n.jsx)("h4",{className:"font-semibold",children:"Total Colecciones"})]}),(0,n.jsx)("p",{className:"text-3xl font-bold text-blue-700",children:t.totalColecciones})]}),(0,n.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:[(0,n.jsxs)("div",{className:"flex items-center mb-2",children:[(0,n.jsx)(d.x_j,{className:"text-green-600 mr-2 text-xl"}),(0,n.jsx)("h4",{className:"font-semibold",children:"Total Flashcards"})]}),(0,n.jsx)("p",{className:"text-3xl font-bold text-green-700",children:t.totalFlashcards})]}),(0,n.jsxs)("div",{className:"bg-orange-50 border border-orange-200 rounded-lg p-4",children:[(0,n.jsxs)("div",{className:"flex items-center mb-2",children:[(0,n.jsx)(d.Ohp,{className:"text-orange-600 mr-2 text-xl"}),(0,n.jsx)("h4",{className:"font-semibold",children:"Para Estudiar Hoy"})]}),(0,n.jsx)("p",{className:"text-3xl font-bold text-orange-700",children:t.totalParaHoy})]}),(0,n.jsxs)("div",{className:"bg-purple-50 border border-purple-200 rounded-lg p-4",children:[(0,n.jsxs)("div",{className:"flex items-center mb-2",children:[(0,n.jsx)(d.TPq,{className:"text-purple-600 mr-2 text-xl"}),(0,n.jsx)("h4",{className:"font-semibold",children:"Aprendidas"})]}),(0,n.jsx)("p",{className:"text-3xl font-bold text-purple-700",children:t.totalAprendidas})]})]}),(0,n.jsxs)("div",{className:"mb-6",children:[(0,n.jsx)("h4",{className:"text-lg font-semibold mb-3",children:"Distribuci\xf3n por Estado"}),(0,n.jsx)("div",{className:"bg-gray-50 rounded-lg p-4",children:(0,n.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:t.totalNuevas}),(0,n.jsx)("div",{className:"text-sm text-gray-600",children:"Nuevas"})]}),(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("div",{className:"text-2xl font-bold text-yellow-600",children:t.totalAprendiendo}),(0,n.jsx)("div",{className:"text-sm text-gray-600",children:"Aprendiendo"})]}),(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("div",{className:"text-2xl font-bold text-orange-600",children:t.totalRepasando}),(0,n.jsx)("div",{className:"text-sm text-gray-600",children:"Repasando"})]}),(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("div",{className:"text-2xl font-bold text-green-600",children:t.totalAprendidas}),(0,n.jsx)("div",{className:"text-sm text-gray-600",children:"Aprendidas"})]})]})})]}),t.coleccionesConMasActividad.length>0&&(0,n.jsxs)("div",{className:"mb-6",children:[(0,n.jsx)("h4",{className:"text-lg font-semibold mb-3",children:"Colecciones con M\xe1s Actividad"}),(0,n.jsx)("div",{className:"space-y-3",children:t.coleccionesConMasActividad.map((e,t)=>(0,n.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-3",children:(0,n.jsxs)("div",{className:"flex justify-between items-center",children:[(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsxs)("div",{className:"text-lg font-bold text-blue-600 mr-3",children:["#",t+1]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"font-medium text-gray-900",children:e.titulo}),(0,n.jsxs)("p",{className:"text-sm text-gray-600",children:[e.totalRevisiones," flashcards total"]})]})]}),(0,n.jsxs)("div",{className:"text-right",children:[(0,n.jsx)("div",{className:"text-lg font-bold text-orange-600",children:e.paraHoy}),(0,n.jsx)("div",{className:"text-sm text-gray-600",children:"para hoy"})]})]})},e.id))})]}),(0,n.jsxs)("div",{className:"mb-6",children:[(0,n.jsx)("h4",{className:"text-lg font-semibold mb-3",children:"Resumen de Progreso"}),(0,n.jsx)("div",{className:"bg-gradient-to-r from-blue-50 to-green-50 rounded-lg p-4",children:(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 text-center",children:[(0,n.jsxs)("div",{children:[(0,n.jsxs)("div",{className:"text-2xl font-bold text-blue-600",children:[t.totalFlashcards>0?Math.round(t.totalAprendidas/t.totalFlashcards*100):0,"%"]}),(0,n.jsx)("div",{className:"text-sm text-gray-600",children:"Progreso General"})]}),(0,n.jsxs)("div",{children:[(0,n.jsxs)("div",{className:"text-2xl font-bold text-green-600",children:[t.totalFlashcards>0?Math.round((t.totalAprendidas+t.totalRepasando)/t.totalFlashcards*100):0,"%"]}),(0,n.jsx)("div",{className:"text-sm text-gray-600",children:"En Proceso"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("div",{className:"text-2xl font-bold text-orange-600",children:t.totalParaHoy}),(0,n.jsx)("div",{className:"text-sm text-gray-600",children:"Pendientes Hoy"})]})]})})]}),(0,n.jsx)("div",{className:"flex justify-end",children:(0,n.jsx)("button",{onClick:e,className:"bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Cerrar"})})]})}):null};function nc({coleccionId:e,onClose:t}){let[s,a]=(0,o.useState)(null),[r,i]=(0,o.useState)(!0),[l,d]=(0,o.useState)(""),[c,u]=(0,o.useState)("general"),m=e=>new Date(e).toLocaleDateString("es-ES",{day:"2-digit",month:"2-digit",year:"numeric"}),h=(e,t)=>0===t?0:Math.round(e/t*100);return(0,n.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] flex flex-col",children:[(0,n.jsxs)("div",{className:"p-4 border-b flex justify-between items-center",children:[(0,n.jsx)("h2",{className:"text-xl font-bold",children:"Estad\xedsticas detalladas de estudio"}),(0,n.jsx)("button",{onClick:t,className:"text-gray-500 hover:text-gray-700",children:(0,n.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,n.jsx)("div",{className:"border-b",children:(0,n.jsxs)("div",{className:"flex",children:[(0,n.jsx)("button",{onClick:()=>u("general"),className:`px-4 py-2 font-medium ${"general"===c?"border-b-2 border-orange-500 text-orange-600":"text-gray-600 hover:text-gray-800"}`,children:"General"}),(0,n.jsx)("button",{onClick:()=>u("progreso"),className:`px-4 py-2 font-medium ${"progreso"===c?"border-b-2 border-orange-500 text-orange-600":"text-gray-600 hover:text-gray-800"}`,children:"Progreso"}),(0,n.jsx)("button",{onClick:()=>u("dificiles"),className:`px-4 py-2 font-medium ${"dificiles"===c?"border-b-2 border-orange-500 text-orange-600":"text-gray-600 hover:text-gray-800"}`,children:"Tarjetas dif\xedciles"})]})}),(0,n.jsx)("div",{className:"p-4 overflow-y-auto flex-grow",children:r?(0,n.jsx)("div",{className:"flex justify-center items-center h-40",children:(0,n.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"})}):l?(0,n.jsx)("div",{className:"text-red-500 text-center py-4",children:l}):s?(0,n.jsxs)(n.Fragment,{children:["general"===c&&(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,n.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Sesiones de estudio"}),(0,n.jsx)("p",{className:"text-3xl font-bold text-orange-600",children:s.totalSesiones})]}),(0,n.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Total de revisiones"}),(0,n.jsx)("p",{className:"text-3xl font-bold text-orange-600",children:s.totalRevisiones})]}),(0,n.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Promedio por sesi\xf3n"}),(0,n.jsx)("p",{className:"text-3xl font-bold text-orange-600",children:s.totalSesiones>0?Math.round(s.totalRevisiones/s.totalSesiones):0})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"text-lg font-semibold mb-3",children:"Distribuci\xf3n de respuestas"}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsxs)("div",{children:[(0,n.jsxs)("div",{className:"flex justify-between mb-1",children:[(0,n.jsx)("span",{className:"text-sm font-medium",children:"Dif\xedcil"}),(0,n.jsxs)("span",{className:"text-sm font-medium",children:[s.distribucionDificultad.dificil," (",h(s.distribucionDificultad.dificil,s.totalRevisiones),"%)"]})]}),(0,n.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2.5",children:(0,n.jsx)("div",{className:"bg-red-500 h-2.5 rounded-full",style:{width:`${h(s.distribucionDificultad.dificil,s.totalRevisiones)}%`}})})]}),(0,n.jsxs)("div",{children:[(0,n.jsxs)("div",{className:"flex justify-between mb-1",children:[(0,n.jsx)("span",{className:"text-sm font-medium",children:"Normal"}),(0,n.jsxs)("span",{className:"text-sm font-medium",children:[s.distribucionDificultad.normal," (",h(s.distribucionDificultad.normal,s.totalRevisiones),"%)"]})]}),(0,n.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2.5",children:(0,n.jsx)("div",{className:"bg-yellow-500 h-2.5 rounded-full",style:{width:`${h(s.distribucionDificultad.normal,s.totalRevisiones)}%`}})})]}),(0,n.jsxs)("div",{children:[(0,n.jsxs)("div",{className:"flex justify-between mb-1",children:[(0,n.jsx)("span",{className:"text-sm font-medium",children:"F\xe1cil"}),(0,n.jsxs)("span",{className:"text-sm font-medium",children:[s.distribucionDificultad.facil," (",h(s.distribucionDificultad.facil,s.totalRevisiones),"%)"]})]}),(0,n.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2.5",children:(0,n.jsx)("div",{className:"bg-green-500 h-2.5 rounded-full",style:{width:`${h(s.distribucionDificultad.facil,s.totalRevisiones)}%`}})})]})]})]})]}),"progreso"===c&&(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"text-lg font-semibold mb-3",children:"Progreso a lo largo del tiempo"}),0===s.progresoTiempo.length?(0,n.jsx)("div",{className:"text-gray-500 text-center py-4",children:"No hay datos de progreso disponibles"}):(0,n.jsx)("div",{className:"overflow-x-auto",children:(0,n.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,n.jsx)("thead",{className:"bg-gray-50",children:(0,n.jsxs)("tr",{children:[(0,n.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Fecha"}),(0,n.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Nuevas"}),(0,n.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Aprendiendo"}),(0,n.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Repasando"}),(0,n.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Aprendidas"})]})}),(0,n.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:s.progresoTiempo.map((e,t)=>(0,n.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,n.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:m(e.fecha)}),(0,n.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(0,n.jsx)("span",{className:"px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800",children:e.nuevas})}),(0,n.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(0,n.jsx)("span",{className:"px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800",children:e.aprendiendo})}),(0,n.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(0,n.jsx)("span",{className:"px-2 py-1 text-xs font-semibold rounded-full bg-orange-100 text-orange-800",children:e.repasando})}),(0,n.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(0,n.jsx)("span",{className:"px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800",children:e.aprendidas})})]},t))})]})})]}),"dificiles"===c&&(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"text-lg font-semibold mb-3",children:"Tarjetas m\xe1s dif\xedciles"}),0===s.tarjetasMasDificiles.length?(0,n.jsx)("div",{className:"text-gray-500 text-center py-4",children:"No hay datos suficientes para determinar las tarjetas m\xe1s dif\xedciles"}):(0,n.jsx)("div",{className:"space-y-4",children:s.tarjetasMasDificiles.map(e=>(0,n.jsxs)("div",{className:"border rounded-lg p-4 hover:bg-gray-50",children:[(0,n.jsx)("p",{className:"font-medium mb-2",children:e.pregunta}),(0,n.jsxs)("div",{className:"flex space-x-4 text-sm",children:[(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)("span",{className:"inline-block w-3 h-3 rounded-full bg-red-500 mr-1"}),(0,n.jsxs)("span",{children:["Dif\xedcil: ",e.dificil]})]}),(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)("span",{className:"inline-block w-3 h-3 rounded-full bg-yellow-500 mr-1"}),(0,n.jsxs)("span",{children:["Normal: ",e.normal]})]}),(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)("span",{className:"inline-block w-3 h-3 rounded-full bg-green-500 mr-1"}),(0,n.jsxs)("span",{children:["F\xe1cil: ",e.facil]})]})]}),(0,n.jsx)("div",{className:"mt-2",children:(0,n.jsxs)("div",{className:"w-full bg-gray-200 rounded-full h-1.5 flex",children:[(0,n.jsx)("div",{className:"bg-red-500 h-1.5 rounded-l-full",style:{width:`${h(e.dificil,e.totalRevisiones)}%`}}),(0,n.jsx)("div",{className:"bg-yellow-500 h-1.5",style:{width:`${h(e.normal,e.totalRevisiones)}%`}}),(0,n.jsx)("div",{className:"bg-green-500 h-1.5 rounded-r-full",style:{width:`${h(e.facil,e.totalRevisiones)}%`}})]})})]},e.id))})]})]}):(0,n.jsx)("div",{className:"text-gray-500 text-center py-4",children:"No hay datos estad\xedsticos disponibles"})})]})})}function nu(){let[e,t]=(0,o.useState)([]),[s,a]=(0,o.useState)(null),[r,i]=(0,o.useState)([]),[l,c]=(0,o.useState)(!0),[u,m]=(0,o.useState)(!0),[h,x]=(0,o.useState)(""),[p,g]=(0,o.useState)(0),[f,b]=(0,o.useState)(null),[y,v]=(0,o.useState)(!1),[j,N]=(0,o.useState)(null),[w,E]=(0,o.useState)(!1),[C,S]=(0,o.useState)(!1),[k,T]=(0,o.useState)(!1),[A,P]=(0,o.useState)(!1),[R,D]=(0,o.useState)(!1),[M,$]=(0,o.useState)(null),[L,F]=(0,o.useState)(null),V=async e=>{c(!0),x(""),a(e),g(0),E(!1);try{let t=[...await (0,q.Og)(e.id)].sort((e,t)=>e.debeEstudiar&&!t.debeEstudiar?-1:!e.debeEstudiar&&t.debeEstudiar?1:0);i(t);let s=await (0,H.yV)(e.id);b(s)}catch(e){console.error("Error al cargar flashcards:",e),x("No se pudieron cargar las flashcards de esta colecci\xf3n")}finally{c(!1)}},_=async(e="programadas")=>{c(!0);try{if(s){let t=[];switch(e){case"programadas":default:t=(await (0,q.Og)(s.id)).filter(e=>e.debeEstudiar);break;case"dificiles":t=await (0,q.kO)(s.id,20);break;case"aleatorias":t=await (0,q._p)(s.id,20);break;case"no-recientes":t=await (0,q._W)(s.id,20);break;case"nuevas":t=await (0,q.Iv)(s.id,"nuevo",20);break;case"aprendiendo":t=await (0,q.Iv)(s.id,"aprendiendo",20);break;case"repasando":t=await (0,q.Iv)(s.id,"repasando",20);break;case"aprendidas":t=await (0,q.Iv)(s.id,"aprendido",20)}let a=await (0,H.yV)(s.id);if(b(a),0===t.length)if("programadas"===e)return void alert('No hay flashcards programadas para estudiar hoy. Puedes usar "Opciones de estudio" para elegir otro tipo de repaso.');else return void alert("No hay flashcards disponibles para el tipo de estudio seleccionado.");i(t),E(!0),g(0),S(!1)}}catch(e){console.error("Error al iniciar modo estudio:",e)}finally{c(!1)}},z=async()=>{if(E(!1),s)try{let e=[...await (0,q.Og)(s.id)].sort((e,t)=>e.debeEstudiar&&!t.debeEstudiar?-1:!e.debeEstudiar&&t.debeEstudiar?1:0);i(e);let t=await (0,H.yV)(s.id);b(t)}catch(e){console.error("Error al recargar datos:",e)}},I=async e=>{if(r[p]){v(!0);try{if(!await (0,q.yf)(r[p].id,e))throw Error("Error al registrar la respuesta");p>=r.length-1?(alert("\xa1Has completado la sesi\xf3n de estudio!"),await z()):g(p+1)}catch(e){console.error("Error al actualizar progreso:",e),x("Error al guardar tu respuesta. Por favor, int\xe9ntalo de nuevo.")}finally{v(!1)}}},O=async e=>{if(confirm("\xbfEst\xe1s seguro de que quieres eliminar esta colecci\xf3n? Esta acci\xf3n no se puede deshacer.")){N(e);try{if(await (0,q.as)(e)){let r=await (0,q.oE)();t(r),s?.id===e&&(a(null),i([]),b(null))}else x("No se pudo eliminar la colecci\xf3n")}catch(e){console.error("Error al eliminar colecci\xf3n:",e),x("Error al eliminar la colecci\xf3n")}finally{N(null)}}},B=async e=>{if(confirm("\xbfEst\xe1s seguro de que quieres eliminar esta flashcard? Esta acci\xf3n no se puede deshacer.")){F(e);try{if(await (0,q.QU)(e)){if(s){let e=[...await (0,q.Og)(s.id)].sort((e,t)=>e.debeEstudiar&&!t.debeEstudiar?-1:!e.debeEstudiar&&t.debeEstudiar?1:0);i(e);let t=await (0,H.yV)(s.id);b(t)}}else x("No se pudo eliminar la flashcard")}catch(e){console.error("Error al eliminar flashcard:",e),x("Error al eliminar la flashcard")}finally{F(null)}}},U=async e=>{i(t=>t.map(t=>t.id===e.id?e:t))};return(0,n.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[h&&(0,n.jsx)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4",children:h}),w?(0,n.jsx)(no,{flashcards:r,activeIndex:p,respondiendo:y,onRespuesta:I,onNavigate:e=>{"next"===e&&p<r.length-1?g(p+1):"prev"===e&&p>0&&g(p-1)},onVolver:z}):(0,n.jsxs)("div",{children:[(0,n.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,n.jsx)("h2",{className:"text-2xl font-bold",children:"Mis Flashcards"}),(0,n.jsxs)("button",{onClick:()=>P(!0),className:"bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline flex items-center",children:[(0,n.jsx)(d.vQY,{className:"mr-2"})," Estad\xedsticas Generales"]})]}),s?(0,n.jsxs)("div",{children:[(0,n.jsx)("button",{onClick:()=>a(null),className:"mb-4 text-blue-600 hover:text-blue-800 flex items-center",children:"← Volver a mis colecciones"}),(0,n.jsx)(Y,{coleccion:s,flashcards:r,estadisticas:f,isLoading:l,onStartStudy:()=>_("programadas"),onShowStudyOptions:()=>S(!0),onShowStatistics:()=>T(!0),onEditFlashcard:e=>{$(e),D(!0)},onDeleteFlashcard:B,deletingFlashcardId:L})]}):(0,n.jsx)(W,{colecciones:e,coleccionSeleccionada:s,onSeleccionarColeccion:V,onEliminarColeccion:O,isLoading:u,deletingId:j})]}),(0,n.jsx)(X,{isOpen:C,onClose:()=>S(!1),onSelectStudyType:_,estadisticas:f,isLoading:l}),k&&s&&(0,n.jsx)(nc,{coleccionId:s.id,onClose:()=>T(!1)}),A&&(0,n.jsx)(nd,{onClose:()=>P(!1)}),M&&(0,n.jsx)(nl,{flashcard:M,isOpen:R,onClose:()=>{D(!1),$(null)},onSave:U})]})}function nm({documentosSeleccionados:e}){let[t,s]=(0,o.useState)(""),[a,r]=(0,o.useState)(""),[i,l]=(0,o.useState)([]),[c,h]=(0,o.useState)(!1),[y,v]=(0,o.useState)(0),[j,w]=(0,o.useState)(!1),[E,T]=(0,o.useState)([]),[R,D]=(0,o.useState)("nuevo"),[M,$]=(0,o.useState)(!1),[L,F]=(0,o.useState)(""),{generateTest:V,isGenerating:_,getActiveTask:z}=S(),{getTask:I}=(0,C.M)(),{executeWithGuard:O}=A(),{user:B}=(0,f.A)(),{plan:U,isLoading:G}=P();z("test");let q=_("test");k({taskType:"test",onResult:e=>{l(e),m.oR.success("\xa1Test generado exitosamente!")},onError:e=>{m.oR.error(`Error al generar test: ${e}`)}});let{register:H,handleSubmit:W,formState:{errors:Y}}=(0,x.mN)({resolver:(0,p.u)(g.GS),defaultValues:{peticion:"",cantidad:10}}),X=async()=>{$(!0);try{let e=await (0,u.Lx)(),t=[];for(let s of e){let e=await (0,u.Kj)(s.id);t.push({...s,numPreguntas:e})}T(t)}catch(e){console.error("Error al cargar tests:",e),m.oR.error("No se pudieron cargar los tests existentes.")}finally{$(!1)}},K=async a=>{let r=e.map(e=>e.contenido);l([]),h(!1);let i=await O("tests",async()=>(await V({peticion:a.peticion,contextos:r,cantidad:a.cantidad}),t||s(`Test: ${a.peticion.substring(0,50)}${a.peticion.length>50?"...":""}`),!0),a.cantidad);i.success?m.oR.success("Generaci\xf3n iniciada en segundo plano. Puedes continuar usando la aplicaci\xf3n.",{duration:4e3}):m.oR.error(i.error||"Error al iniciar la generaci\xf3n del test")},Z=async()=>{if(0===i.length)return void F("No hay preguntas para guardar");if("nuevo"===R&&!t.trim())return void F("Por favor, proporciona un t\xedtulo para el nuevo test");if("nuevo"!==R&&""===R)return void F("Por favor, selecciona un test existente");F("");try{let n;if("nuevo"===R){if(!(n=await (0,u._4)(t,a,e.map(e=>e.id))))throw Error("No se pudo crear el test")}else n=R;let o=i.map(e=>({test_id:n,pregunta:e.pregunta,opcion_a:e.opciones.a,opcion_b:e.opciones.b,opcion_c:e.opciones.c,opcion_d:e.opciones.d,respuesta_correcta:e.respuesta_correcta}));if(!await (0,u.OA)(o))throw Error("No se pudieron guardar las preguntas");h(!0),"nuevo"===R&&await X(),setTimeout(()=>{l([]),h(!1),s(""),r(""),D("nuevo"),v(0),w(!1),m.oR.success("Test guardado correctamente. Puedes generar uno nuevo.")},3e3)}catch(e){console.error("Error al guardar las preguntas:",e),F("Ha ocurrido un error al guardar las preguntas. Por favor, int\xe9ntalo de nuevo.")}};return(0,n.jsxs)("div",{className:"mt-8 border-t pt-8",children:[(0,n.jsx)("h2",{className:"text-xl font-bold mb-4",children:"Generador de Tests"}),!G&&"free"===U&&(0,n.jsx)("div",{className:"mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg",children:(0,n.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,n.jsx)(d.F5$,{className:"w-5 h-5 text-blue-600 mt-0.5"}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"font-medium text-blue-900",children:"L\xedmites del Plan Gratuito"}),(0,n.jsxs)("p",{className:"text-sm text-blue-700 mt-1",children:["M\xe1ximo ",b.qo.free.limits.testsForTrial," preguntas de test durante el per\xedodo de prueba. Para generar tests ilimitados,",(0,n.jsx)(N(),{href:"/upgrade-plan",className:"font-medium underline hover:text-blue-800 ml-1",children:"actualiza tu plan"}),"."]})]})]})}),(0,n.jsxs)("form",{onSubmit:W(K),className:"space-y-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"peticion",className:"block text-gray-700 text-sm font-bold mb-2",children:"Describe el test que deseas generar:"}),(0,n.jsx)("textarea",{id:"peticion",className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",rows:3,...H("peticion"),placeholder:"Ej: Genera un test sobre los conceptos principales del tema 1",disabled:q}),Y.peticion&&(0,n.jsx)("span",{className:"text-red-500 text-sm",children:Y.peticion.message}),(0,n.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"La IA generar\xe1 preguntas de test basadas en los documentos seleccionados y tu petici\xf3n."})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"cantidad",className:"block text-gray-700 text-sm font-bold mb-2",children:"N\xfamero de preguntas:"}),(0,n.jsx)("input",{id:"cantidad",type:"number",min:"1",max:"50",className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",...H("cantidad",{valueAsNumber:!0}),disabled:q}),Y.cantidad&&(0,n.jsx)("span",{className:"text-red-500 text-sm",children:Y.cantidad.message}),(0,n.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"Especifica cu\xe1ntas preguntas quieres generar (entre 1 y 50)."})]}),L&&(0,n.jsx)("div",{className:"text-red-500 text-sm",children:L}),(0,n.jsx)("div",{children:(0,n.jsx)("button",{type:"submit",className:"bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",disabled:q||0===e.length,children:q?"Generando...":"Generar Test"})})]}),q&&(0,n.jsxs)("div",{className:"mt-4 text-center",children:[(0,n.jsx)("p",{className:"text-gray-600",children:"Generando test, por favor espera..."}),(0,n.jsx)("div",{className:"mt-2 flex justify-center",children:(0,n.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"})})]}),i.length>0&&(0,n.jsxs)("div",{className:"mt-6",children:[(0,n.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,n.jsxs)("h3",{className:"text-lg font-semibold",children:["Test generado (",i.length," preguntas)"]}),(0,n.jsx)("button",{onClick:()=>{l([]),h(!1),s(""),r(""),D("nuevo"),v(0),w(!1)},className:"bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline text-sm",title:"Limpiar test generado",children:"Limpiar"})]}),!c&&(0,n.jsxs)("div",{className:"bg-gray-100 p-4 rounded-lg mb-6",children:[(0,n.jsx)("h4",{className:"font-medium mb-2",children:"Guardar preguntas de test"}),(0,n.jsxs)("div",{className:"mb-4",children:[(0,n.jsx)("label",{htmlFor:"tipoTest",className:"block text-sm font-medium text-gray-700 mb-1",children:"\xbfD\xf3nde quieres guardar estas preguntas?"}),(0,n.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)("input",{type:"radio",id:"nuevoTest",name:"tipoTest",value:"nuevo",checked:"nuevo"===R,onChange:()=>D("nuevo"),className:"mr-2",disabled:q}),(0,n.jsx)("label",{htmlFor:"nuevoTest",className:"text-sm text-gray-700",children:"Crear nuevo test"})]}),(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)("input",{type:"radio",id:"testExistente",name:"tipoTest",value:"existente",checked:"nuevo"!==R,onChange:()=>{E.length>0?D(E[0].id):D("")},className:"mr-2",disabled:q||0===E.length}),(0,n.jsxs)("label",{htmlFor:"testExistente",className:"text-sm text-gray-700",children:["A\xf1adir a un test existente",0===E.length&&(0,n.jsx)("span",{className:"text-gray-500 ml-2",children:"(No hay tests disponibles)"})]})]})]})]}),"nuevo"===R&&(0,n.jsxs)("div",{className:"space-y-3",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"tituloTest",className:"block text-sm font-medium text-gray-700 mb-1",children:"T\xedtulo del nuevo test:"}),(0,n.jsx)("input",{type:"text",id:"tituloTest",className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",value:t,onChange:e=>s(e.target.value),disabled:q})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"descripcionTest",className:"block text-sm font-medium text-gray-700 mb-1",children:"Descripci\xf3n (opcional):"}),(0,n.jsx)("textarea",{id:"descripcionTest",className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",rows:2,value:a,onChange:e=>r(e.target.value),disabled:q})]})]}),"nuevo"!==R&&E.length>0&&(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"testExistenteSelect",className:"block text-sm font-medium text-gray-700 mb-1",children:"Selecciona un test:"}),(0,n.jsx)("select",{id:"testExistenteSelect",className:"shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",value:R,onChange:e=>D(e.target.value),disabled:q,children:E.map(e=>(0,n.jsxs)("option",{value:e.id,children:[e.titulo," ",e.numPreguntas?`(${e.numPreguntas} preguntas)`:""]},e.id))})]}),(0,n.jsx)("div",{className:"mt-4",children:(0,n.jsx)("button",{type:"button",onClick:Z,className:"bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",disabled:!1,children:"Guardar preguntas"})})]}),c&&(0,n.jsxs)("div",{className:"bg-green-100 text-green-800 p-4 rounded-lg mb-6",children:[(0,n.jsx)("p",{className:"font-medium",children:"nuevo"===R?"\xa1Nuevo test creado correctamente!":"\xa1Preguntas a\xf1adidas al test correctamente!"}),(0,n.jsxs)("p",{className:"text-sm mt-1",children:["Puedes acceder a ","nuevo"===R?"\xe9l":"las preguntas",' desde la secci\xf3n de "Mis Tests".']})]}),(0,n.jsxs)("div",{className:"bg-white border rounded-lg shadow-md p-6 mb-4",children:[(0,n.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,n.jsx)("button",{onClick:()=>{y>0&&(v(y-1),w(!1))},disabled:0===y,className:`p-2 rounded-full ${0===y?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-200"}`,children:(0,n.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})})}),(0,n.jsxs)("span",{className:"text-gray-600",children:["Pregunta ",y+1," de ",i.length]}),(0,n.jsx)("button",{onClick:()=>{y<i.length-1&&(v(y+1),w(!1))},disabled:y===i.length-1,className:`p-2 rounded-full ${y===i.length-1?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-200"}`,children:(0,n.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})})]}),(0,n.jsx)("div",{className:"min-h-[300px]",children:(0,n.jsxs)("div",{className:"mb-4",children:[(0,n.jsx)("h4",{className:"font-semibold text-lg mb-4",children:i[y]?.pregunta}),(0,n.jsx)("div",{className:"space-y-3 mt-6",children:["a","b","c","d"].map(e=>(0,n.jsx)("div",{className:`p-3 border rounded-lg ${j&&i[y].respuesta_correcta===e?"bg-green-100 border-green-500":"hover:bg-gray-50"}`,children:(0,n.jsxs)("div",{className:"flex items-start",children:[(0,n.jsx)("div",{className:`flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center mr-2 ${j&&i[y].respuesta_correcta===e?"bg-green-500 text-white":"bg-gray-200 text-gray-700"}`,children:e.toUpperCase()}),(0,n.jsx)("div",{className:"flex-grow",children:i[y]?.opciones[e]})]})},e))})]})}),(0,n.jsx)("div",{className:"mt-4 text-center",children:(0,n.jsx)("button",{onClick:()=>{w(!j)},className:"bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:j?"Ocultar respuesta":"Mostrar respuesta"})})]}),(0,n.jsxs)("div",{className:"mt-6",children:[(0,n.jsx)("h4",{className:"font-medium mb-2",children:"Todas las preguntas:"}),(0,n.jsx)("div",{className:"space-y-2",children:i.map((e,t)=>(0,n.jsx)("div",{className:`p-3 border rounded-lg cursor-pointer hover:bg-gray-50 ${t===y?"border-indigo-500 bg-indigo-50":""}`,onClick:()=>{v(t),w(!1)},children:(0,n.jsxs)("p",{className:"font-medium",children:[t+1,". ",e.pregunta]})},t))})]})]})]})}let nh=({estadisticas:e,onClose:t})=>(0,n.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,n.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto",children:[(0,n.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,n.jsx)("h2",{className:"text-2xl font-bold",children:"Estad\xedsticas Generales de Tests"}),(0,n.jsx)("button",{onClick:t,className:"text-gray-500 hover:text-gray-700 transition-colors",children:(0,n.jsx)(d.yGN,{size:24})})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6",children:[(0,n.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,n.jsxs)("div",{className:"flex items-center mb-2",children:[(0,n.jsx)(d.TPq,{className:"text-blue-600 mr-2 text-xl"}),(0,n.jsx)("h4",{className:"font-semibold",children:"Tests Realizados"})]}),(0,n.jsx)("p",{className:"text-3xl font-bold text-blue-700",children:e.totalTests})]}),(0,n.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:[(0,n.jsxs)("div",{className:"flex items-center mb-2",children:[(0,n.jsx)(d.YrT,{className:"text-green-600 mr-2 text-xl"}),(0,n.jsx)("h4",{className:"font-semibold",children:"Respuestas Correctas"})]}),(0,n.jsx)("p",{className:"text-3xl font-bold text-green-700",children:e.totalRespuestasCorrectas})]}),(0,n.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:[(0,n.jsxs)("div",{className:"flex items-center mb-2",children:[(0,n.jsx)(d.yGN,{className:"text-red-600 mr-2 text-xl"}),(0,n.jsx)("h4",{className:"font-semibold",children:"Respuestas Incorrectas"})]}),(0,n.jsx)("p",{className:"text-3xl font-bold text-red-700",children:e.totalRespuestasIncorrectas})]}),(0,n.jsxs)("div",{className:"bg-purple-50 border border-purple-200 rounded-lg p-4",children:[(0,n.jsxs)("div",{className:"flex items-center mb-2",children:[(0,n.jsx)(d.ARf,{className:"text-purple-600 mr-2 text-xl"}),(0,n.jsx)("h4",{className:"font-semibold",children:"Porcentaje de Acierto"})]}),(0,n.jsxs)("p",{className:"text-3xl font-bold text-purple-700",children:[e.porcentajeAcierto.toFixed(1),"%"]})]})]})]})}),nx=({estadisticas:e,testTitulo:t,onClose:s})=>(0,n.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,n.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto",children:[(0,n.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,n.jsxs)("h2",{className:"text-2xl font-bold",children:["Estad\xedsticas del Test: ",t]}),(0,n.jsx)("button",{onClick:s,className:"text-gray-500 hover:text-gray-700 transition-colors",children:(0,n.jsx)(d.yGN,{size:24})})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6",children:[(0,n.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,n.jsxs)("div",{className:"flex items-center mb-2",children:[(0,n.jsx)(d.Ohp,{className:"text-blue-600 mr-2 text-xl"}),(0,n.jsx)("h4",{className:"font-semibold",children:"Veces Realizado"})]}),(0,n.jsx)("p",{className:"text-3xl font-bold text-blue-700",children:e.fechasRealizacion.length})]}),(0,n.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:[(0,n.jsxs)("div",{className:"flex items-center mb-2",children:[(0,n.jsx)(d.YrT,{className:"text-green-600 mr-2 text-xl"}),(0,n.jsx)("h4",{className:"font-semibold",children:"Respuestas Correctas"})]}),(0,n.jsx)("p",{className:"text-3xl font-bold text-green-700",children:e.totalCorrectas})]}),(0,n.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:[(0,n.jsxs)("div",{className:"flex items-center mb-2",children:[(0,n.jsx)(d.yGN,{className:"text-red-600 mr-2 text-xl"}),(0,n.jsx)("h4",{className:"font-semibold",children:"Respuestas Incorrectas"})]}),(0,n.jsx)("p",{className:"text-3xl font-bold text-red-700",children:e.totalIncorrectas})]}),(0,n.jsxs)("div",{className:"bg-purple-50 border border-purple-200 rounded-lg p-4",children:[(0,n.jsxs)("div",{className:"flex items-center mb-2",children:[(0,n.jsx)(d.ARf,{className:"text-purple-600 mr-2 text-xl"}),(0,n.jsx)("h4",{className:"font-semibold",children:"Porcentaje de Acierto"})]}),(0,n.jsxs)("p",{className:"text-3xl font-bold text-purple-700",children:[e.porcentajeAcierto.toFixed(1),"%"]})]})]}),e.preguntasMasFalladas.length>0&&(0,n.jsxs)("div",{className:"mb-6",children:[(0,n.jsx)("h4",{className:"text-lg font-semibold mb-3",children:"Preguntas con M\xe1s Fallos"}),(0,n.jsx)("div",{className:"space-y-3",children:e.preguntasMasFalladas.map((e,t)=>(0,n.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3",children:(0,n.jsx)("div",{className:"flex justify-between items-start",children:(0,n.jsxs)("div",{className:"flex items-start",children:[(0,n.jsxs)("div",{className:"text-lg font-bold text-red-600 mr-3",children:["#",t+1]}),(0,n.jsxs)("div",{className:"flex-1",children:[(0,n.jsx)("p",{className:"font-medium text-gray-900",children:e.pregunta}),(0,n.jsxs)("div",{className:"flex items-center space-x-4 mt-2 text-sm",children:[(0,n.jsxs)("span",{className:"text-red-600",children:[(0,n.jsx)(d.yGN,{className:"inline mr-1"})," ",e.totalFallos," fallos"]}),(0,n.jsxs)("span",{className:"text-green-600",children:[(0,n.jsx)(d.YrT,{className:"inline mr-1"})," ",e.totalAciertos," aciertos"]})]})]})]})})},e.preguntaId))})]})]})});function np({onIniciarRepaso:e,onCancelar:t}){let[s,a]=(0,o.useState)([]),[r,i]=(0,o.useState)([]),[l,c]=(0,o.useState)(!1),[u,h]=(0,o.useState)(!1),x=(e,t)=>{i(s=>s.map(s=>s.testId===e?{...s,cantidad:Math.max(0,Math.min(t,s.maxPreguntas))}:s))},p=async()=>{let t=r.filter(e=>e.cantidad>0);if(0===t.length)return void m.oR.error("Selecciona al menos una pregunta de alg\xfan test.");h(!0);try{let s=await Object(function(){var e=Error("Cannot find module '../services/testsService'");throw e.code="MODULE_NOT_FOUND",e}())(t);if(0===s.length)return void m.oR.error("No se pudieron obtener preguntas para el repaso.");t.reduce((e,t)=>e+t.cantidad,0),m.oR.success(`Test de repaso creado con ${s.length} preguntas de ${t.length} test(s)`),e(s,t)}catch(e){console.error("Error al generar test de repaso:",e),m.oR.error("Error al generar el test de repaso.")}finally{h(!1)}},g=r.reduce((e,t)=>e+t.cantidad,0),f=r.filter(e=>e.cantidad>0).length;return l?(0,n.jsxs)("div",{className:"flex justify-center items-center py-8",children:[(0,n.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"}),(0,n.jsx)("span",{className:"ml-2 text-gray-600",children:"Cargando tests..."})]}):0===s.length?(0,n.jsxs)("div",{className:"text-center py-8",children:[(0,n.jsx)(d.NLe,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,n.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"No hay tests disponibles"}),(0,n.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Primero necesitas crear algunos tests con preguntas para poder hacer un repaso."}),(0,n.jsx)("button",{onClick:t,className:"mt-4 px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600",children:"Volver"})]}):(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)("div",{className:"flex justify-between items-center",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h2",{className:"text-xl font-bold",children:"Configurar Test de Repaso"}),(0,n.jsx)("p",{className:"text-gray-600",children:"Selecciona cu\xe1ntas preguntas quieres de cada test para crear tu repaso personalizado."})]}),(0,n.jsx)("button",{onClick:t,className:"p-2 text-gray-500 hover:text-gray-700",title:"Cancelar",children:(0,n.jsx)(d.yGN,{className:"h-6 w-6"})})]}),(0,n.jsxs)("div",{className:"flex justify-between items-center bg-gray-50 p-4 rounded-lg",children:[(0,n.jsxs)("div",{className:"flex gap-2",children:[(0,n.jsx)("button",{onClick:()=>{i(e=>e.map(e=>({...e,cantidad:e.maxPreguntas})))},className:"px-3 py-1 text-sm bg-indigo-100 text-indigo-700 rounded hover:bg-indigo-200",children:"Seleccionar todas"}),(0,n.jsx)("button",{onClick:()=>{i(e=>e.map(e=>({...e,cantidad:0})))},className:"px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200",children:"Limpiar todo"})]}),(0,n.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,n.jsx)("strong",{children:g})," preguntas de ",(0,n.jsx)("strong",{children:f})," test(s)"]})]}),(0,n.jsx)("div",{className:"space-y-4",children:s.map(e=>{let t=r.find(t=>t.testId===e.id);return t?(0,n.jsxs)("div",{className:"border rounded-lg p-4 bg-white",children:[(0,n.jsxs)("div",{className:"flex justify-between items-start mb-3",children:[(0,n.jsxs)("div",{className:"flex-1",children:[(0,n.jsx)("h3",{className:"font-medium text-gray-900",children:e.titulo}),e.descripcion&&(0,n.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:e.descripcion}),(0,n.jsxs)("p",{className:"text-xs text-gray-400 mt-1",children:["Creado: ",new Date(e.creado_en).toLocaleDateString()]})]}),(0,n.jsxs)("div",{className:"text-sm text-gray-500",children:[e.numPreguntas," pregunta",1!==e.numPreguntas?"s":""," disponible",1!==e.numPreguntas?"s":""]})]}),(0,n.jsxs)("div",{className:"flex items-center gap-4",children:[(0,n.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Preguntas a incluir:"}),(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)("input",{type:"range",min:"0",max:t.maxPreguntas,value:t.cantidad,onChange:t=>x(e.id,parseInt(t.target.value)),className:"flex-1"}),(0,n.jsx)("input",{type:"number",min:"0",max:t.maxPreguntas,value:t.cantidad,onChange:t=>x(e.id,parseInt(t.target.value)||0),className:"w-16 px-2 py-1 border rounded text-center text-sm"}),(0,n.jsxs)("span",{className:"text-sm text-gray-500",children:["/ ",t.maxPreguntas]})]})]})]},e.id):null})}),(0,n.jsx)("div",{className:"flex justify-center pt-4",children:(0,n.jsx)("button",{onClick:p,disabled:0===g||u,className:`flex items-center px-6 py-3 rounded-lg font-medium ${0===g||u?"bg-gray-300 text-gray-500 cursor-not-allowed":"bg-indigo-600 text-white hover:bg-indigo-700"}`,children:u?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(d.jTZ,{className:"mr-2 h-5 w-5 animate-spin"}),"Generando repaso..."]}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(d.aze,{className:"mr-2 h-5 w-5"}),"Iniciar Repaso (",g," preguntas)"]})})})]})}function ng({preguntas:e,configuracion:t,onFinalizar:s,onCancelar:a}){let[r,i]=(0,o.useState)(0),[l,c]=(0,o.useState)({}),[u,m]=(0,o.useState)(null),[h,x]=(0,o.useState)(Date.now()),[p,g]=(0,o.useState)({}),[f,b]=(0,o.useState)(!1),[y,v]=(0,o.useState)(null),j=e[r],N=t=>{if(!f){if(m(t),h){let t=Date.now(),s=t-h;g(t=>({...t,[e[r].id]:s})),x(t)}c(s=>({...s,[e[r].id]:t}))}},w=()=>{b(!0);let t=0,a=0,r=0,i=0,n=[];e.forEach(e=>{let s=l[e.id],o=p[e.id]||0;s?"blank"===s?r++:s===e.respuesta_correcta?t++:a++:r++,n.push({preguntaId:e.id,respuestaSeleccionada:s||"blank",esCorrecta:s===e.respuesta_correcta,tiempoRespuesta:o}),i+=o});let o=t/e.length*100;v({correctas:t,incorrectas:a,enBlanco:r,porcentaje:o,tiempoTotal:i}),s({totalPreguntas:e.length,respuestasCorrectas:t,respuestasIncorrectas:a,respuestasEnBlanco:r,tiempoTotal:i,porcentajeAcierto:o,respuestas:n})},E=e=>f?e===j.respuesta_correcta?"bg-green-100 border-green-500 text-green-800":e===u&&e!==j.respuesta_correcta?"bg-red-100 border-red-500 text-red-800":"bg-gray-50":u===e?"bg-indigo-100 border-indigo-500":"hover:bg-gray-50 cursor-pointer",C=e=>f?e===j.respuesta_correcta?(0,n.jsx)(d.YrT,{className:"text-green-600"}):e===u&&e!==j.respuesta_correcta?(0,n.jsx)(d.yGN,{className:"text-red-600"}):null:null,S=(r+1)/e.length*100,k=Math.floor((Date.now()-h)/1e3);return(0,n.jsxs)("div",{className:"max-w-4xl mx-auto space-y-6",children:[(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-4",children:[(0,n.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,n.jsx)("h2",{className:"text-xl font-bold",children:"Test de Repaso"}),(0,n.jsxs)("div",{className:"flex items-center gap-4 text-sm text-gray-600",children:[(0,n.jsxs)("div",{className:"flex items-center gap-1",children:[(0,n.jsx)(d.x_j,{className:"h-4 w-4"}),r+1," de ",e.length]}),!f&&(0,n.jsxs)("div",{className:"flex items-center gap-1",children:[(0,n.jsx)(d.Ohp,{className:"h-4 w-4"}),Math.floor(k/60),":",(k%60).toString().padStart(2,"0")]})]})]}),(0,n.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,n.jsx)("div",{className:"bg-indigo-600 h-2 rounded-full transition-all duration-300",style:{width:`${S}%`}})})]}),f&&y&&(0,n.jsxs)("div",{className:"bg-gray-50 border rounded-lg p-4",children:[(0,n.jsx)("h4",{className:"font-semibold text-lg mb-3",children:"Resultados del Test"}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-5 gap-4",children:[(0,n.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-3",children:[(0,n.jsx)("p",{className:"text-sm font-medium text-green-800",children:"Correctas"}),(0,n.jsx)("p",{className:"text-2xl font-bold text-green-700",children:y.correctas})]}),(0,n.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3",children:[(0,n.jsx)("p",{className:"text-sm font-medium text-red-800",children:"Incorrectas"}),(0,n.jsx)("p",{className:"text-2xl font-bold text-red-700",children:y.incorrectas})]}),(0,n.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-3",children:[(0,n.jsx)("p",{className:"text-sm font-medium text-yellow-800",children:"En Blanco"}),(0,n.jsx)("p",{className:"text-2xl font-bold text-yellow-700",children:y.enBlanco})]}),(0,n.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-3",children:[(0,n.jsx)("p",{className:"text-sm font-medium text-blue-800",children:"Porcentaje"}),(0,n.jsxs)("p",{className:"text-2xl font-bold text-blue-700",children:[y.porcentaje.toFixed(1),"%"]})]}),(0,n.jsxs)("div",{className:"bg-purple-50 border border-purple-200 rounded-lg p-3",children:[(0,n.jsx)("p",{className:"text-sm font-medium text-purple-800",children:"Tiempo Total"}),(0,n.jsx)("p",{className:"text-2xl font-bold text-purple-700",children:(e=>{let t=Math.floor(e/6e4),s=Math.floor(e%6e4/1e3);return`${t}:${s.toString().padStart(2,"0")}`})(y.tiempoTotal)})]})]}),(0,n.jsx)("div",{className:"mt-4 flex justify-center",children:(0,n.jsx)("button",{onClick:()=>{i(0),c({}),m(null),x(Date.now()),g({}),b(!1),v(null)},className:"bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded",children:"Realizar de nuevo"})})]}),(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:[(0,n.jsx)("h3",{className:"text-lg font-medium mb-6",children:j.pregunta}),(0,n.jsxs)("div",{className:"space-y-3",children:[["a","b","c","d"].map(e=>{let t=u===e;return(0,n.jsx)("div",{className:`p-3 border rounded-lg cursor-pointer transition-all ${E(e)}`,onClick:()=>!f&&N(e),children:(0,n.jsxs)("div",{className:"flex items-start",children:[(0,n.jsx)("div",{className:`flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center mr-2 ${f&&e===j.respuesta_correcta?"bg-green-500 text-white":f&&t&&e!==j.respuesta_correcta?"bg-red-500 text-white":t?"bg-indigo-500 text-white":"bg-gray-200 text-gray-700"}`,children:e.toUpperCase()}),(0,n.jsx)("div",{className:"flex-grow",children:j[`opcion_${e}`]}),C(e)]})},e)}),!f&&(0,n.jsx)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:(0,n.jsxs)("div",{className:`p-3 border rounded-lg cursor-pointer flex items-center ${"blank"===u?"bg-yellow-50 border-yellow-300":"hover:bg-gray-50 border-gray-300"}`,onClick:()=>N("blank"),children:[(0,n.jsx)("input",{type:"checkbox",checked:"blank"===u,onChange:()=>N("blank"),className:"mr-3 h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300 rounded",onClick:e=>e.stopPropagation()}),(0,n.jsx)("span",{className:"text-sm text-gray-600",children:"Marque si quiere dejar en blanco"})]})})]})]}),(0,n.jsxs)("div",{className:"flex justify-between items-center",children:[(0,n.jsxs)("button",{onClick:()=>{r>0&&i(e=>e-1)},disabled:0===r,className:`flex items-center gap-2 px-4 py-2 rounded-lg ${0===r?"bg-gray-100 text-gray-400 cursor-not-allowed":"bg-gray-200 text-gray-700 hover:bg-gray-300"}`,children:[(0,n.jsx)(d.irw,{className:"h-4 w-4"}),"Anterior"]}),(0,n.jsxs)("div",{className:"flex gap-2",children:[(0,n.jsx)("button",{onClick:a,className:"px-4 py-2 text-gray-600 hover:text-gray-800",children:"Cancelar"}),!f&&(0,n.jsxs)("button",{onClick:()=>{u||N("blank"),r<e.length-1?i(e=>e+1):w()},className:"bg-indigo-600 text-white py-2 px-4 rounded flex items-center hover:bg-indigo-700",children:[r===e.length-1?"Finalizar Test":"Siguiente",r<e.length-1&&(0,n.jsx)(d.fOo,{className:"ml-1"})]}),f&&r<e.length-1&&(0,n.jsxs)("button",{onClick:()=>i(e=>e+1),className:"text-indigo-600 hover:text-indigo-800 flex items-center",children:["Siguiente ",(0,n.jsx)(d.fOo,{className:"ml-1"})]})]})]}),!f&&!u&&(0,n.jsx)("div",{className:"text-center text-gray-500 text-sm",children:"Selecciona una respuesta para continuar"})]})}!function(){var e=Error("Cannot find module '../services/testsService'");throw e.code="MODULE_NOT_FOUND",e}();var nf=s(78286);let nb=({isOpen:e,onClose:t,test:s,onSave:a})=>{let[r,i]=(0,o.useState)(""),[l,c]=(0,o.useState)(""),[u,h]=(0,o.useState)(!1);(0,o.useEffect)(()=>{e&&s&&(i(s.titulo),c(s.descripcion||""))},[e,s]);let x=async()=>{let e;if(!r.trim())return void m.oR.error("El t\xedtulo del test es obligatorio");h(!0);try{e=m.oR.loading("Actualizando test...");let i=await (0,nf.actualizarTest)(s.id,r.trim(),l.trim()||void 0);i?(m.oR.success("Test actualizado correctamente",{id:e}),a(i),t()):m.oR.error("Error al actualizar el test",{id:e})}catch(t){console.error("Error al actualizar test:",t),m.oR.error("Error inesperado al actualizar el test",{id:e})}finally{h(!1)}},p=()=>{u||t()};return e?(0,n.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",onKeyDown:e=>{"Escape"!==e.key||u||t()},role:"dialog","aria-modal":"true","aria-labelledby":"test-edit-title",children:(0,n.jsxs)("div",{className:"bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,n.jsx)("h3",{id:"test-edit-title",className:"text-lg font-semibold text-gray-900",children:"Editar Test"}),(0,n.jsx)("button",{onClick:p,className:"text-gray-400 hover:text-gray-600 transition-colors",disabled:u,"aria-label":"Cerrar modal",children:(0,n.jsx)(d.yGN,{size:24})})]}),(0,n.jsxs)("div",{className:"p-6 space-y-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"test-titulo",className:"block text-sm font-medium text-gray-700 mb-2",children:"T\xedtulo del Test *"}),(0,n.jsx)("input",{id:"test-titulo",type:"text",value:r,onChange:e=>i(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Ingresa el t\xedtulo del test",disabled:u,maxLength:255,required:!0})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"test-descripcion",className:"block text-sm font-medium text-gray-700 mb-2",children:"Descripci\xf3n (opcional)"}),(0,n.jsx)("textarea",{id:"test-descripcion",value:l,onChange:e=>c(e.target.value),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-vertical",placeholder:"Describe brevemente el contenido del test",disabled:u,maxLength:1e3})]})]}),(0,n.jsxs)("div",{className:"flex items-center justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50",children:[(0,n.jsx)("button",{onClick:p,className:"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",disabled:u,children:"Cancelar"}),(0,n.jsxs)("button",{onClick:x,className:"px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2",disabled:u||!r.trim(),children:[(0,n.jsx)(d.Bc_,{size:16}),(0,n.jsx)("span",{children:u?"Guardando...":"Guardar"})]})]})]})}):null};function ny(){let[e,t]=(0,o.useState)([]),[s,a]=(0,o.useState)(null),[r,i]=(0,o.useState)([]),[l,c]=(0,o.useState)(0),[h,x]=(0,o.useState)(null),[p,g]=(0,o.useState)({}),[f,b]=(0,o.useState)(!1),[y,v]=(0,o.useState)(null),[j,N]=(0,o.useState)(null),[w,E]=(0,o.useState)({}),[C,S]=(0,o.useState)(!0),[k,T]=(0,o.useState)(""),[A,P]=(0,o.useState)(null),[R,D]=(0,o.useState)(null),[M,$]=(0,o.useState)("lista"),[L,F]=(0,o.useState)([]),[V,_]=(0,o.useState)([]),[z,I]=(0,o.useState)(null),[O,B]=(0,o.useState)(!1),[U,G]=(0,o.useState)(null),[q,H]=(0,o.useState)(!1),[W,Y]=(0,o.useState)(null),X=e=>{if(!f){if(x(e),j){let e=Date.now(),t=e-j;E(e=>({...e,[r[l].id]:t})),N(e)}g(t=>({...t,[r[l].id]:e}))}},K=async()=>{h||X("blank"),l<r.length-1?(await Z(),c(l+1),x(null)):(await Z(),J())},Z=async()=>{if(!s)return;let e=r[l],t=h||"blank",a="blank"!==t&&t===e.respuesta_correcta;try{await (0,u.Gl)(s.id,e.id,"blank"===t?"x":t,a)}catch(e){console.error("Error al guardar estad\xedstica:",e)}},J=()=>{b(!0);let e=0,t=0,s=0,a=0;r.forEach(r=>{let i=p[r.id];i?"blank"===i?s++:i===r.respuesta_correcta?e++:t++:s++,a+=w[r.id]||0});let i=e/r.length*100;v({correctas:e,incorrectas:t,enBlanco:s,porcentaje:i,tiempoTotal:a})},Q=async()=>{try{let e=await (0,u.oC)();P(e),$("estadisticas-generales")}catch(e){console.error("Error al cargar estad\xedsticas generales:",e),T("No se pudieron cargar las estad\xedsticas generales. Por favor, int\xe9ntalo de nuevo.")}},ee=async e=>{try{let t=await (0,u.dd)(e);D(t),$("estadisticas-test")}catch(e){console.error("Error al cargar estad\xedsticas del test:",e),T("No se pudieron cargar las estad\xedsticas del test. Por favor, int\xe9ntalo de nuevo.")}},et=e=>{I(e),B(!0)},es=()=>{I(null),B(!1)},ea=e=>{G(e),H(!0)},er=()=>{G(null),H(!1)},ei=async()=>{let e;if(U){Y(U);try{e=m.oR.loading("Eliminando test..."),await Object(function(){var e=Error("Cannot find module '../services/testsService'");throw e.code="MODULE_NOT_FOUND",e}())(U)?(m.oR.success("Test eliminado correctamente",{id:e}),t(e=>e.filter(e=>e.id!==U)),s&&s.id===U&&(a(null),$("lista")),er()):m.oR.error("Error al eliminar el test",{id:e})}catch(t){console.error("Error al eliminar test:",t),m.oR.error("Error inesperado al eliminar el test",{id:e})}finally{Y(null)}}};return(0,n.jsxs)("div",{className:"container mx-auto p-4",children:[k&&(0,n.jsx)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4",children:k}),(0,n.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,n.jsx)("h2",{className:"text-2xl font-bold",children:"Mis Tests"}),(0,n.jsxs)("div",{className:"flex gap-2",children:[(0,n.jsxs)("button",{onClick:()=>{$("repaso-config")},className:"bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded flex items-center focus:outline-none focus:shadow-outline",children:[(0,n.jsx)(d.jTZ,{className:"mr-2"})," Test de Repaso"]}),(0,n.jsxs)("button",{onClick:Q,className:"bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded flex items-center focus:outline-none focus:shadow-outline",children:[(0,n.jsx)(d.vQY,{className:"mr-2"})," Estad\xedsticas Generales"]})]})]}),"lista"===M&&(0,n.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6",children:e.map(e=>(0,n.jsxs)("div",{className:"border rounded-lg p-4 cursor-pointer hover:bg-gray-50 transition-colors flex flex-col justify-between",onClick:()=>a(e),children:[(0,n.jsxs)("div",{children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,n.jsx)("h3",{className:"font-semibold text-lg",children:e.titulo}),(0,n.jsx)("button",{onClick:t=>{t.stopPropagation(),et(e)},className:"p-1 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50",title:"Editar test",children:(0,n.jsx)(d.Pj4,{size:16})})]}),e.descripcion&&(0,n.jsx)("p",{className:"text-sm text-gray-600 mb-2 break-words",children:e.descripcion}),(0,n.jsxs)("p",{className:"text-sm text-gray-500 mb-1",children:["Preguntas: ","number"==typeof e.numero_preguntas?e.numero_preguntas:"Cargando..."]}),(0,n.jsxs)("p",{className:"text-xs text-gray-400",children:["Creado: ",new Date(e.creado_en).toLocaleDateString("es-ES")]})]}),(0,n.jsxs)("div",{className:"mt-4 space-y-2",children:[(0,n.jsxs)("div",{className:"flex gap-2",children:[(0,n.jsx)("button",{onClick:t=>{t.stopPropagation(),a(e),$("test")},className:"flex-1 bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50",children:"Realizar Test"}),(0,n.jsxs)("button",{onClick:t=>{t.stopPropagation(),ee(e.id),$("estadisticas-test")},className:"flex-1 bg-gray-500 hover:bg-gray-600 text-white font-semibold py-2 px-4 rounded text-sm flex items-center justify-center focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-opacity-50",children:[(0,n.jsx)(d.eXT,{className:"mr-2"})," Estad\xedsticas"]})]}),(0,n.jsx)("button",{onClick:t=>{t.stopPropagation(),ea(e.id)},className:"w-full bg-red-500 hover:bg-red-600 text-white font-semibold py-2 px-4 rounded text-sm flex items-center justify-center focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-50 disabled:opacity-50 disabled:cursor-not-allowed",disabled:W===e.id,children:W===e.id?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Eliminando..."]}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(d.IXo,{className:"mr-2",size:16}),"Eliminar"]})})]})]},e.id))}),"estadisticas-generales"===M&&A&&(0,n.jsx)(nh,{estadisticas:{totalTests:A.totalTests,totalRespuestasCorrectas:A.totalRespuestasCorrectas,totalRespuestasIncorrectas:A.totalRespuestasIncorrectas,porcentajeAcierto:A.porcentajeAcierto},onClose:()=>$("lista")}),"estadisticas-test"===M&&R&&(0,n.jsx)(nx,{estadisticas:{totalPreguntas:R.totalPreguntas,totalCorrectas:R.totalCorrectas,totalIncorrectas:R.totalIncorrectas,porcentajeAcierto:R.porcentajeAcierto,fechasRealizacion:R.fechasRealizacion,preguntasMasFalladas:R.preguntasMasFalladas},testTitulo:e.find(e=>e.id===R.testId)?.titulo||"Test",onClose:()=>$("lista")}),"repaso-config"===M&&(0,n.jsx)(np,{onIniciarRepaso:(e,t)=>{F(e),_(t),$("repaso-test")},onCancelar:()=>$("lista")}),"repaso-test"===M&&L.length>0&&(0,n.jsx)(ng,{preguntas:L,configuracion:V,onFinalizar:e=>{console.log("Test de repaso finalizado:",e)},onCancelar:()=>$("lista")}),"test"===M&&s&&r.length>0&&(0,n.jsxs)("div",{className:"bg-white border rounded-lg shadow-md p-6 mt-4",children:[(0,n.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,n.jsx)("h3",{className:"text-xl font-bold",children:s.titulo}),(0,n.jsx)("button",{onClick:()=>$("lista"),className:"bg-gray-200 hover:bg-gray-300 text-gray-800 py-1 px-3 rounded",children:"Volver"})]}),f&&y&&(0,n.jsxs)("div",{className:"bg-gray-50 border rounded-lg p-4 mb-6",children:[(0,n.jsx)("h4",{className:"font-semibold text-lg mb-3",children:"Resultados del Test"}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-5 gap-4",children:[(0,n.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-3",children:[(0,n.jsx)("p",{className:"text-sm font-medium text-green-800",children:"Correctas"}),(0,n.jsx)("p",{className:"text-2xl font-bold text-green-700",children:y.correctas})]}),(0,n.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3",children:[(0,n.jsx)("p",{className:"text-sm font-medium text-red-800",children:"Incorrectas"}),(0,n.jsx)("p",{className:"text-2xl font-bold text-red-700",children:y.incorrectas})]}),(0,n.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-3",children:[(0,n.jsx)("p",{className:"text-sm font-medium text-yellow-800",children:"En Blanco"}),(0,n.jsx)("p",{className:"text-2xl font-bold text-yellow-700",children:y.enBlanco})]}),(0,n.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-3",children:[(0,n.jsx)("p",{className:"text-sm font-medium text-blue-800",children:"Porcentaje"}),(0,n.jsxs)("p",{className:"text-2xl font-bold text-blue-700",children:[y.porcentaje.toFixed(1),"%"]})]}),(0,n.jsxs)("div",{className:"bg-purple-50 border border-purple-200 rounded-lg p-3",children:[(0,n.jsx)("p",{className:"text-sm font-medium text-purple-800",children:"Tiempo Total"}),(0,n.jsx)("p",{className:"text-2xl font-bold text-purple-700",children:(e=>{let t=Math.floor(e/1e3),s=Math.floor(t/60);return`${s}:${(t%60).toString().padStart(2,"0")}`})(y.tiempoTotal)})]})]}),(0,n.jsx)("div",{className:"mt-4 flex justify-center",children:(0,n.jsx)("button",{onClick:()=>{c(0),x(null),g({}),b(!1),v(null),N(Date.now()),E({})},className:"bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded",children:"Realizar de nuevo"})})]}),(0,n.jsxs)("div",{className:"mb-4",children:[(0,n.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,n.jsxs)("span",{className:"text-gray-600",children:["Pregunta ",l+1," de ",r.length]}),!f&&(0,n.jsxs)("span",{className:"text-sm text-gray-500",children:[(0,n.jsx)(d.Ohp,{className:"inline mr-1"})," Tiempo por pregunta"]})]}),(0,n.jsx)("div",{className:"h-2 bg-gray-200 rounded-full mb-4",children:(0,n.jsx)("div",{className:"h-2 bg-indigo-600 rounded-full",style:{width:`${(l+1)/r.length*100}%`}})})]}),(0,n.jsx)("div",{className:"min-h-[300px]",children:(0,n.jsxs)("div",{className:"mb-6",children:[(0,n.jsx)("h4",{className:"font-semibold text-lg mb-6",children:r[l]?.pregunta}),(0,n.jsxs)("div",{className:"space-y-3 mt-6",children:[["a","b","c","d"].map(e=>{let t=f&&e===r[l].respuesta_correcta,s=f&&h===e&&e!==r[l].respuesta_correcta,a=h===e;return(0,n.jsx)("div",{className:`p-3 border rounded-lg cursor-pointer ${t?"bg-green-100 border-green-500":s?"bg-red-100 border-red-500":a?"bg-indigo-100 border-indigo-500":"hover:bg-gray-50"}`,onClick:()=>!f&&X(e),children:(0,n.jsxs)("div",{className:"flex items-start",children:[(0,n.jsx)("div",{className:`flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center mr-2 ${t?"bg-green-500 text-white":s?"bg-red-500 text-white":a?"bg-indigo-500 text-white":"bg-gray-200 text-gray-700"}`,children:e.toUpperCase()}),(0,n.jsx)("div",{className:"flex-grow",children:r[l]?.[`opcion_${e}`]})]})},e)}),!f&&(0,n.jsx)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:(0,n.jsxs)("div",{className:`p-3 border rounded-lg cursor-pointer flex items-center ${"blank"===h?"bg-yellow-50 border-yellow-300":"hover:bg-gray-50 border-gray-300"}`,onClick:()=>X("blank"),children:[(0,n.jsx)("input",{type:"checkbox",checked:"blank"===h,onChange:()=>X("blank"),className:"mr-3 h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300 rounded",onClick:e=>e.stopPropagation()}),(0,n.jsx)("span",{className:"text-sm text-gray-600",children:"Marque si quiere dejar en blanco"})]})})]})]})}),(0,n.jsxs)("div",{className:"flex justify-between mt-6",children:[(0,n.jsxs)("button",{onClick:()=>{l>0&&(c(l-1),x(p[r[l-1].id]||null))},disabled:0===l,className:`flex items-center ${0===l?"text-gray-400 cursor-not-allowed":"text-indigo-600 hover:text-indigo-800"}`,children:[(0,n.jsx)(d.irw,{className:"mr-1"})," Anterior"]}),!f&&(0,n.jsxs)("button",{onClick:K,className:"bg-indigo-600 text-white py-2 px-4 rounded flex items-center hover:bg-indigo-700",children:[l===r.length-1?"Finalizar Test":"Siguiente",l<r.length-1&&(0,n.jsx)(d.fOo,{className:"ml-1"})]}),f&&l<r.length-1&&(0,n.jsxs)("button",{onClick:K,className:"text-indigo-600 hover:text-indigo-800 flex items-center",children:["Siguiente ",(0,n.jsx)(d.fOo,{className:"ml-1"})]})]})]}),C&&(0,n.jsx)("div",{className:"flex justify-center items-center h-40",children:(0,n.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"})}),!C&&0===e.length&&"lista"===M&&(0,n.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 text-yellow-800 p-4 rounded flex items-start",children:[(0,n.jsx)(d.eHT,{className:"mr-2 mt-1 flex-shrink-0"}),(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"font-medium",children:"No hay tests disponibles"}),(0,n.jsx)("p",{className:"text-sm mt-1",children:'Genera nuevos tests desde la secci\xf3n "Generar Tests".'})]})]}),z&&(0,n.jsx)(nb,{isOpen:O,onClose:es,test:z,onSave:e=>{t(t=>t.map(t=>t.id===e.id?e:t)),s&&s.id===e.id&&a(e),es()}}),q&&(0,n.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,n.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-md w-full mx-4",children:[(0,n.jsxs)("div",{className:"flex items-center mb-4",children:[(0,n.jsx)("div",{className:"flex-shrink-0",children:(0,n.jsx)(d.IXo,{className:"w-6 h-6 text-red-600"})}),(0,n.jsx)("div",{className:"ml-3",children:(0,n.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Eliminar Test"})})]}),(0,n.jsx)("div",{className:"mb-4",children:(0,n.jsx)("p",{className:"text-sm text-gray-500",children:"\xbfEst\xe1s seguro de que quieres eliminar este test? Esta acci\xf3n no se puede deshacer y se eliminar\xe1n todas las preguntas y estad\xedsticas asociadas."})}),(0,n.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,n.jsx)("button",{onClick:er,className:"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",disabled:null!==W,children:"Cancelar"}),(0,n.jsx)("button",{onClick:ei,className:"px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center",disabled:null!==W,children:null!==W?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Eliminando..."]}):"Eliminar"})]})]})})]})}!function(){var e=Error("Cannot find module '../services/testsService'");throw e.code="MODULE_NOT_FOUND",e}();var nv=s(97697),nj=s(94854);let nN=[{id:"a1_2019_junta",nombre:"Cuerpo Superior Facultativo - Inform\xe1tica (A1.2019)",descripcion:"Temario completo para oposiciones del Cuerpo Superior Facultativo, opci\xf3n Inform\xe1tica de la Junta de Andaluc\xeda",cuerpo:"CUERPO SUPERIOR FACULTATIVO, OPCI\xd3N INFORM\xc1TICA (A1.2019)",archivo:"a1_2019_junta.md"},{id:"c1_junta",nombre:"Cuerpo General de Administrativos (C1.1000)",descripcion:"Temario completo para oposiciones del Cuerpo General de Administrativos de la Junta de Andaluc\xeda",cuerpo:"CUERPO GENERAL DE ADMINISTRATIVOS (C1.1000)",archivo:"c1_junta.md"},{id:"c2_estado",nombre:"Cuerpo General Auxiliar del Estado (C2)",descripcion:"Temario para oposiciones del Cuerpo General Auxiliar del Estado",cuerpo:"CUERPO GENERAL AUXILIAR DEL ESTADO (C2)",archivo:"c2_estado.md"},{id:"c2_junta",nombre:"Cuerpo General Auxiliar - Junta de Andaluc\xeda (C2)",descripcion:"Temario para oposiciones del Cuerpo General Auxiliar de la Junta de Andaluc\xeda",cuerpo:"CUERPO GENERAL AUXILIAR - JUNTA DE ANDALUC\xcdA (C2)",archivo:"c2_junta.md"}];async function nw(e){try{let t=nN.find(t=>t.id===e);if(!t)return console.error("Temario predefinido no encontrado:",e),null;let s=await fetch(`/temarios/${t.archivo}`);if(!s.ok)return console.error("Error al cargar archivo de temario:",s.status),null;let a=await s.text(),r=function(e){let t=[],s=e.split("\n");for(let e=0;e<s.length;e++){let a=s[e].trim(),r=a.match(/^Tema\s+(\d+)\.\s*(.+)$/);if(r||(r=a.match(/^(\d+)\.\s*(.+)$/)),r){let e=parseInt(r[1]),s=r[2].trim();s.length>10&&!s.match(/^[IVX]+\s*$/)&&t.push({numero:e,titulo:s,descripcion:s.length>100?s.substring(0,100)+"...":s})}}return t}(a);return{...t,temas:r}}catch(e){return console.error("Error al cargar temario predefinido:",e),null}}async function nE(e){try{let t=await nw(e);if(!t)return null;return{totalTemas:t.temas.length,tipoTemario:"Temario Completo Predefinido",cuerpo:t.cuerpo}}catch(e){return console.error("Error al obtener estad\xedsticas:",e),null}}let nC=({onSeleccionar:e,onVolver:t})=>{let[s,a]=(0,o.useState)([]),[r,i]=(0,o.useState)(""),[l,c]=(0,o.useState)(null),[u,h]=(0,o.useState)(null),[x,p]=(0,o.useState)({});(0,o.useEffect)(()=>{g()},[]),(0,o.useEffect)(()=>{a(function(e){if(!e.trim())return nN;let t=e.toLowerCase();return nN.filter(e=>e.nombre.toLowerCase().includes(t)||e.descripcion.toLowerCase().includes(t)||e.cuerpo.toLowerCase().includes(t))}(r))},[r]);let g=()=>{a(nN),nN.forEach(async e=>{let t=await nE(e.id);t&&p(s=>({...s,[e.id]:t}))})},f=async t=>{h(t);try{let s=await nw(t);s?(e(s),m.oR.success("Temario predefinido cargado exitosamente")):m.oR.error("Error al cargar el temario predefinido")}catch(e){console.error("Error al cargar temario:",e),m.oR.error("Error al cargar el temario predefinido")}finally{h(null)}},b=(e,t=150)=>e.length<=t?e:e.substring(0,t)+"...";return(0,n.jsx)("div",{className:"min-h-screen bg-gray-50 p-4",children:(0,n.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,n.jsxs)("div",{className:"mb-8",children:[(0,n.jsx)("button",{onClick:t,className:"text-blue-600 hover:text-blue-700 text-sm font-medium mb-4 flex items-center",children:"← Volver a la selecci\xf3n"}),(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Seleccionar Temario Predefinido"}),(0,n.jsx)("p",{className:"text-lg text-gray-600 mb-2",children:"Elige uno de nuestros temarios oficiales predefinidos"}),(0,n.jsx)("p",{className:"text-gray-500",children:"Estos temarios est\xe1n basados en convocatorias oficiales y contienen todos los temas necesarios"})]})]}),(0,n.jsx)("div",{className:"mb-6",children:(0,n.jsxs)("div",{className:"relative max-w-md mx-auto",children:[(0,n.jsx)(d.CKj,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),(0,n.jsx)("input",{type:"text",placeholder:"Buscar por cuerpo, nivel o descripci\xf3n...",value:r,onChange:e=>i(e.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})}),(0,n.jsx)("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:s.map(e=>{let t=x[e.id],s=u===e.id;return(0,n.jsx)("div",{className:`bg-white rounded-xl shadow-sm border-2 transition-all duration-200 hover:shadow-md h-full flex flex-col ${l===e.id?"border-blue-500 ring-2 ring-blue-200":"border-gray-200 hover:border-blue-300"}`,children:(0,n.jsxs)("div",{className:"p-6 flex flex-col h-full",children:[(0,n.jsxs)("div",{className:"flex-grow",children:[(0,n.jsx)("div",{className:"flex items-start justify-between mb-4",children:(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)("div",{className:"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-3",children:(0,n.jsx)(d.H9b,{className:"w-6 h-6 text-blue-600"})}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"font-semibold text-gray-900 text-sm leading-tight",children:e.nombre}),(0,n.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:e.cuerpo})]})]})}),(0,n.jsx)("p",{className:"text-sm text-gray-600 mb-4 leading-relaxed",children:b(e.descripcion)}),t&&(0,n.jsxs)("div",{className:"bg-gray-50 rounded-lg p-3 mb-4",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,n.jsx)("span",{className:"text-gray-600",children:"Total de temas:"}),(0,n.jsx)("span",{className:"font-semibold text-gray-900",children:t.totalTemas})]}),(0,n.jsxs)("div",{className:"flex items-center justify-between text-sm mt-1",children:[(0,n.jsx)("span",{className:"text-gray-600",children:"Tipo:"}),(0,n.jsx)("span",{className:"text-xs bg-green-100 text-green-800 px-2 py-1 rounded",children:"Completo"})]})]})]}),(0,n.jsx)("button",{onClick:()=>f(e.id),disabled:s,className:`w-full py-3 px-4 rounded-lg font-medium transition-all duration-200 flex items-center justify-center ${s?"bg-gray-100 text-gray-400 cursor-not-allowed":"bg-blue-600 text-white hover:bg-blue-700 active:bg-blue-800"}`,children:s?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(d.TwU,{className:"w-4 h-4 mr-2 animate-spin"}),"Cargando..."]}):(0,n.jsxs)(n.Fragment,{children:["Seleccionar Temario",(0,n.jsx)(d.dyV,{className:"w-4 h-4 ml-2"})]})})]})},e.id)})}),0===s.length&&(0,n.jsxs)("div",{className:"text-center py-12",children:[(0,n.jsx)(d.S8s,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,n.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No se encontraron temarios"}),(0,n.jsx)("p",{className:"text-gray-600",children:"Intenta con otros t\xe9rminos de b\xfasqueda o revisa la ortograf\xeda"})]}),(0,n.jsx)("div",{className:"mt-12 bg-blue-50 border border-blue-200 rounded-lg p-6",children:(0,n.jsxs)("div",{className:"flex items-start",children:[(0,n.jsx)(d.S8s,{className:"w-5 h-5 text-blue-600 mr-3 flex-shrink-0 mt-0.5"}),(0,n.jsxs)("div",{className:"text-blue-800",children:[(0,n.jsx)("h4",{className:"font-semibold mb-2",children:"Sobre los temarios predefinidos"}),(0,n.jsxs)("ul",{className:"text-sm space-y-1",children:[(0,n.jsx)("li",{children:"• Basados en convocatorias oficiales reales"}),(0,n.jsx)("li",{children:"• Incluyen todos los temas necesarios para la oposici\xf3n"}),(0,n.jsx)("li",{children:"• Optimizados para usar con las funciones de IA de la plataforma"}),(0,n.jsx)("li",{children:"• Se pueden personalizar despu\xe9s de la importaci\xf3n"})]})]})]})})]})})},nS=({onComplete:e})=>{let[t,s]=(0,o.useState)("seleccion"),[a,r]=(0,o.useState)(null),[i,l]=(0,o.useState)(""),[c,u]=(0,o.useState)(""),[h,x]=(0,o.useState)([{numero:1,titulo:"",descripcion:""}]),[p,g]=(0,o.useState)(null),[f,b]=(0,o.useState)(!1),y=e=>{r(e),"predefinido"===e?s("predefinidos"):s("configuracion")},v=e=>{h.length>1&&x(h.filter((t,s)=>s!==e).map((e,t)=>({...e,numero:t+1})))},j=(e,t,s)=>{let a=[...h];a[e]={...a[e],[t]:s},x(a)},N=()=>i.trim()?!h.some(e=>!e.titulo.trim())||(m.oR.error("Todos los temas deben tener un t\xedtulo"),!1):(m.oR.error("El t\xedtulo del temario es obligatorio"),!1),w=async()=>{b(!0);try{let t;if("predefinido"===a&&p)t=function(e){return{titulo:e.nombre,descripcion:`${e.descripcion}

Cuerpo: ${e.cuerpo}`,tipo:"completo",temas:e.temas.map((e,t)=>({numero:e.numero,titulo:e.titulo,descripcion:e.descripcion,orden:t+1}))}}(p);else{if(!N()||!a)return;t={titulo:i,descripcion:c,tipo:a,temas:h.map((e,t)=>({numero:e.numero,titulo:e.titulo,descripcion:e.descripcion,orden:t+1}))}}let s=await (0,nj.r5)(t.titulo,t.descripcion,t.tipo);if(!s)return void m.oR.error("Error al crear el temario");if(!await (0,nj.sW)(s,t.temas))return void m.oR.error("Error al crear los temas");m.oR.success("\xa1Temario configurado exitosamente!"),e()}catch(e){console.error("Error al guardar temario:",e),m.oR.error("Error al configurar el temario")}finally{b(!1)}};return"seleccion"===t?(0,n.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center p-4",children:(0,n.jsxs)("div",{className:"max-w-4xl w-full",children:[(0,n.jsxs)("div",{className:"text-center mb-8",children:[(0,n.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"\xa1Bienvenido a OposiAI! \uD83C\uDF89"}),(0,n.jsx)("p",{className:"text-lg text-gray-600 mb-2",children:"Para comenzar, necesitamos configurar tu temario de estudio."}),(0,n.jsx)("p",{className:"text-gray-500",children:"Esto nos permitir\xe1 crear una planificaci\xf3n personalizada y hacer un seguimiento de tu progreso. El temario solo es el \xedndice de los temas necesarios para preparar la oposici\xf3n, los textos ser\xe1n a\xf1adidos por el opositor."})]}),(0,n.jsxs)("div",{className:"grid md:grid-cols-2 gap-8 max-w-4xl mx-auto",children:[(0,n.jsx)("div",{className:"bg-white rounded-xl p-6 shadow-sm border-2 border-gray-200 hover:border-green-500 cursor-pointer transition-all duration-200 hover:shadow-md",onClick:()=>y("predefinido"),children:(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,n.jsx)(d.a4x,{className:"w-8 h-8 text-green-600"})}),(0,n.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"Temarios Predefinidos"}),(0,n.jsx)("p",{className:"text-gray-600 mb-4",children:"Selecciona un temario oficial ya configurado y listo para usar."}),(0,n.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-3 mb-4",children:(0,n.jsxs)("div",{className:"flex items-center text-green-800 text-sm",children:[(0,n.jsx)(d.YrT,{className:"w-4 h-4 mr-2 flex-shrink-0"}),(0,n.jsx)("span",{children:"Basados en convocatorias oficiales"})]})}),(0,n.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-3",children:(0,n.jsxs)("div",{className:"flex items-center text-green-800 text-sm",children:[(0,n.jsx)(d.YrT,{className:"w-4 h-4 mr-2 flex-shrink-0"}),(0,n.jsx)("span",{children:"Configuraci\xf3n instant\xe1nea"})]})}),(0,n.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-3 mb-4",children:(0,n.jsxs)("div",{className:"flex items-center text-green-800 text-sm",children:[(0,n.jsx)(d.YrT,{className:"w-4 h-4 mr-2 flex-shrink-0"}),(0,n.jsx)("span",{children:"La IA podr\xe1 crear una planificaci\xf3n completa y personalizada"})]})}),(0,n.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-3",children:(0,n.jsxs)("div",{className:"flex items-center text-green-800 text-sm",children:[(0,n.jsx)(d.YrT,{className:"w-4 h-4 mr-2 flex-shrink-0"}),(0,n.jsx)("span",{children:"Seguimiento detallado del progreso por temas"})]})})]})}),(0,n.jsx)("div",{className:"bg-white rounded-xl p-6 shadow-sm border-2 border-gray-200 hover:border-blue-500 cursor-pointer transition-all duration-200 hover:shadow-md",onClick:()=>y("completo"),children:(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("div",{className:"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,n.jsx)(d.H9b,{className:"w-8 h-8 text-blue-600"})}),(0,n.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"Temario Personalizado"}),(0,n.jsx)("p",{className:"text-gray-600 mb-4",children:"Configura todos los temas de tu oposici\xf3n de forma estructurada."}),(0,n.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-3 mb-4",children:(0,n.jsxs)("div",{className:"flex items-center text-green-800 text-sm",children:[(0,n.jsx)(d.YrT,{className:"w-4 h-4 mr-2 flex-shrink-0"}),(0,n.jsx)("span",{children:"La IA podr\xe1 crear una planificaci\xf3n completa y personalizada"})]})}),(0,n.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-3",children:(0,n.jsxs)("div",{className:"flex items-center text-green-800 text-sm",children:[(0,n.jsx)(d.YrT,{className:"w-4 h-4 mr-2 flex-shrink-0"}),(0,n.jsx)("span",{children:"Seguimiento detallado del progreso por temas"})]})})]})})]}),(0,n.jsx)("div",{className:"text-center mt-8",children:(0,n.jsx)("p",{className:"text-sm text-gray-500",children:"Podr\xe1s modificar tu temario m\xe1s adelante desde la configuraci\xf3n"})})]})}):"predefinidos"===t?(0,n.jsx)(nC,{onSeleccionar:e=>{g(e),s("configuracion")},onVolver:()=>{s("seleccion"),r(null)}}):(0,n.jsx)("div",{className:"min-h-screen bg-gray-50 p-4",children:(0,n.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,n.jsxs)("div",{className:"bg-white rounded-xl shadow-sm p-6",children:[(0,n.jsxs)("div",{className:"mb-6",children:[(0,n.jsx)("button",{onClick:()=>s("seleccion"),className:"text-blue-600 hover:text-blue-700 text-sm font-medium mb-4",children:"← Volver a la selecci\xf3n"}),(0,n.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"predefinido"===a?"Confirmar Temario Predefinido":"Configurar Temario Completo"})]}),"predefinido"===a&&p?(0,n.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-6 mb-6",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-green-900 mb-4",children:"Temario Seleccionado"}),(0,n.jsxs)("div",{className:"space-y-3",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("span",{className:"text-sm font-medium text-green-800",children:"Nombre:"}),(0,n.jsx)("p",{className:"text-green-700",children:p.nombre})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("span",{className:"text-sm font-medium text-green-800",children:"Cuerpo:"}),(0,n.jsx)("p",{className:"text-green-700",children:p.cuerpo})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("span",{className:"text-sm font-medium text-green-800",children:"Descripci\xf3n:"}),(0,n.jsx)("p",{className:"text-green-700",children:p.descripcion})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("span",{className:"text-sm font-medium text-green-800",children:"Total de temas:"}),(0,n.jsxs)("p",{className:"text-green-700",children:[p.temas.length," temas"]})]})]})]}):(0,n.jsxs)("div",{className:"space-y-4 mb-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"titulo",className:"block text-sm font-medium text-gray-700 mb-1",children:"T\xedtulo del temario *"}),(0,n.jsx)("input",{type:"text",id:"titulo",value:i,onChange:e=>l(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Ej: Oposiciones Auxiliar Administrativo 2024"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"descripcion",className:"block text-sm font-medium text-gray-700 mb-1",children:"Descripci\xf3n (opcional)"}),(0,n.jsx)("textarea",{id:"descripcion",value:c,onChange:e=>u(e.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Describe brevemente tu temario..."})]})]}),"predefinido"!==a&&(0,n.jsxs)("div",{className:"mb-6",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,n.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Temas"}),(0,n.jsxs)("button",{onClick:()=>{let e=h.length+1;x([...h,{numero:e,titulo:"",descripcion:""}])},className:"bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded-md text-sm flex items-center",children:[(0,n.jsx)(d.GGD,{className:"w-4 h-4 mr-1"}),"A\xf1adir tema"]})]}),(0,n.jsx)("div",{className:"space-y-3",children:h.map((e,t)=>(0,n.jsxs)("div",{className:"flex items-start space-x-3 p-3 border border-gray-200 rounded-lg",children:[(0,n.jsxs)("div",{className:"w-16",children:[(0,n.jsx)("label",{className:"block text-xs text-gray-500 mb-1",children:"Tema"}),(0,n.jsx)("input",{type:"number",value:e.numero,onChange:e=>j(t,"numero",parseInt(e.target.value)||1),className:"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500",min:"1"})]}),(0,n.jsxs)("div",{className:"flex-1",children:[(0,n.jsx)("label",{className:"block text-xs text-gray-500 mb-1",children:"T\xedtulo *"}),(0,n.jsx)("input",{type:"text",value:e.titulo,onChange:e=>j(t,"titulo",e.target.value),className:"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500",placeholder:"T\xedtulo del tema"})]}),(0,n.jsxs)("div",{className:"flex-1",children:[(0,n.jsx)("label",{className:"block text-xs text-gray-500 mb-1",children:"Descripci\xf3n"}),(0,n.jsx)("input",{type:"text",value:e.descripcion,onChange:e=>j(t,"descripcion",e.target.value),className:"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500",placeholder:"Descripci\xf3n opcional"})]}),h.length>1&&(0,n.jsx)("button",{onClick:()=>v(t),className:"text-red-600 hover:text-red-700 p-1",title:"Eliminar tema",children:(0,n.jsx)(d.IXo,{className:"w-4 h-4"})})]},t))})]}),(0,n.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,n.jsx)("button",{onClick:()=>s("seleccion"),className:"px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors",disabled:f,children:"Cancelar"}),(0,n.jsx)("button",{onClick:w,disabled:f,className:"px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center",children:f?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Guardando..."]}):"Guardar temario"})]})]})})})},nk=({onNavigateToTab:e})=>{let{user:t}=(0,f.A)(),[s,a]=(0,o.useState)(null),[r,i]=(0,o.useState)([]),[l,c]=(0,o.useState)(!0),[u,m]=(0,o.useState)(!1);(0,o.useEffect)(()=>{h()},[]);let h=async()=>{c(!0);try{let[e,t,s]=await Promise.all([(0,nv.x)(),(0,nv.w)(5),(0,nj.yr)()]);a(e),i(t),s||m(!0)}catch(e){console.error("Error al cargar datos del dashboard:",e),a({totalDocumentos:0,totalColeccionesFlashcards:0,totalTests:0,totalFlashcards:0,flashcardsParaHoy:0,flashcardsNuevas:0,flashcardsAprendiendo:0,flashcardsRepasando:0,testsRealizados:0,porcentajeAcierto:0,coleccionesRecientes:[],testsRecientes:[]}),i([]),m(!0)}finally{c(!1)}};return u?(0,n.jsx)(nS,{onComplete:()=>{m(!1),h()}}):l?(0,n.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,n.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)("div",{className:"bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl p-6 text-white",children:[(0,n.jsxs)("h1",{className:"text-2xl font-bold mb-2",children:[(()=>{let e=new Date().getHours();return e<12?"Buenos d\xedas":e<18?"Buenas tardes":"Buenas noches"})(),", ",t?.email?.split("@")[0]||"Estudiante","! \uD83D\uDC4B"]}),(0,n.jsx)("p",{className:"text-blue-100",children:"\xbfListo para continuar con tu preparaci\xf3n? Aqu\xed tienes un resumen de tu progreso."})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,n.jsx)("div",{className:"bg-white rounded-lg p-4 shadow-sm border",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Documentos"}),(0,n.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:s?.totalDocumentos||0})]}),(0,n.jsx)(d.jH2,{className:"h-8 w-8 text-blue-600"})]})}),(0,n.jsx)("div",{className:"bg-white rounded-lg p-4 shadow-sm border",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Colecciones"}),(0,n.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:s?.totalColeccionesFlashcards||0})]}),(0,n.jsx)(d.H9b,{className:"h-8 w-8 text-emerald-600"})]})}),(0,n.jsx)("div",{className:"bg-white rounded-lg p-4 shadow-sm border",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Tests"}),(0,n.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:s?.totalTests||0})]}),(0,n.jsx)(d.NLe,{className:"h-8 w-8 text-pink-600"})]})}),(0,n.jsx)("div",{className:"bg-white rounded-lg p-4 shadow-sm border",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Flashcards"}),(0,n.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:s?.totalFlashcards||0})]}),(0,n.jsx)(d.x_j,{className:"h-8 w-8 text-orange-600"})]})})]}),(0,n.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-sm border",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,n.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:"Estudio de Hoy"}),(0,n.jsx)(d.wIk,{className:"h-6 w-6 text-gray-400"})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,n.jsx)("div",{className:"bg-orange-50 rounded-lg p-4 border border-orange-200",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm font-medium text-orange-800",children:"Para Repasar Hoy"}),(0,n.jsx)("p",{className:"text-2xl font-bold text-orange-600",children:s?.flashcardsParaHoy||0})]}),(0,n.jsx)(d.Ohp,{className:"h-6 w-6 text-orange-600"})]})}),(0,n.jsx)("div",{className:"bg-blue-50 rounded-lg p-4 border border-blue-200",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm font-medium text-blue-800",children:"Nuevas"}),(0,n.jsx)("p",{className:"text-2xl font-bold text-blue-600",children:s?.flashcardsNuevas||0})]}),(0,n.jsx)(d.D1A,{className:"h-6 w-6 text-blue-600"})]})}),(0,n.jsx)("div",{className:"bg-green-50 rounded-lg p-4 border border-green-200",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm font-medium text-green-800",children:"% Acierto Tests"}),(0,n.jsxs)("p",{className:"text-2xl font-bold text-green-600",children:[s?.porcentajeAcierto.toFixed(1)||0,"%"]})]}),(0,n.jsx)(d.ARf,{className:"h-6 w-6 text-green-600"})]})})]}),s&&s.flashcardsParaHoy>0&&(0,n.jsx)("div",{className:"mt-4",children:(0,n.jsxs)("button",{onClick:()=>e("misFlashcards"),className:"bg-orange-600 hover:bg-orange-700 text-white font-semibold py-2 px-4 rounded-lg flex items-center transition-colors",children:[(0,n.jsx)(d.aze,{className:"mr-2"}),"Comenzar Estudio"]})})]}),r.length>0&&(0,n.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-sm border",children:[(0,n.jsx)("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"Pr\xf3ximas Flashcards"}),(0,n.jsx)("div",{className:"space-y-3",children:r.slice(0,3).map(e=>(0,n.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,n.jsxs)("div",{className:"flex-1",children:[(0,n.jsx)("p",{className:"font-medium text-gray-900 truncate",children:e.pregunta}),(0,n.jsx)("p",{className:"text-sm text-gray-600",children:e.coleccionTitulo})]}),(0,n.jsx)("div",{className:"text-right",children:(0,n.jsx)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${"nuevo"===e.estado?"bg-blue-100 text-blue-800":"aprendiendo"===e.estado?"bg-yellow-100 text-yellow-800":"repasando"===e.estado?"bg-orange-100 text-orange-800":"bg-green-100 text-green-800"}`,children:e.estado})})]},e.id))}),r.length>3&&(0,n.jsx)("button",{onClick:()=>e("misFlashcards"),className:"mt-3 text-blue-600 hover:text-blue-800 text-sm font-medium",children:"Ver todas las flashcards pendientes"})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,n.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-sm border",children:[(0,n.jsx)("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"Colecciones Recientes"}),(0,n.jsx)("div",{className:"space-y-3",children:s?.coleccionesRecientes.map(e=>(0,n.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,n.jsxs)("div",{className:"flex-1",children:[(0,n.jsx)("p",{className:"font-medium text-gray-900",children:e.titulo}),(0,n.jsxs)("p",{className:"text-sm text-gray-600",children:["Creada: ",new Date(e.fechaCreacion).toLocaleDateString("es-ES")]})]}),(0,n.jsx)("div",{className:"text-right",children:(0,n.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800",children:[e.paraHoy," para hoy"]})})]},e.id))}),(0,n.jsx)("button",{onClick:()=>e("misFlashcards"),className:"mt-3 text-emerald-600 hover:text-emerald-800 text-sm font-medium",children:"Ver todas las colecciones"})]}),(0,n.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-sm border",children:[(0,n.jsx)("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"Tests Recientes"}),(0,n.jsx)("div",{className:"space-y-3",children:s?.testsRecientes.map(e=>(0,n.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,n.jsxs)("div",{className:"flex-1",children:[(0,n.jsx)("p",{className:"font-medium text-gray-900",children:e.titulo}),(0,n.jsxs)("p",{className:"text-sm text-gray-600",children:["Creado: ",new Date(e.fechaCreacion).toLocaleDateString("es-ES")]})]}),(0,n.jsx)("div",{className:"text-right",children:(0,n.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-pink-100 text-pink-800",children:[e.numeroPreguntas," preguntas"]})})]},e.id))}),(0,n.jsx)("button",{onClick:()=>e("misTests"),className:"mt-3 text-pink-600 hover:text-pink-800 text-sm font-medium",children:"Ver todos los tests"})]})]}),(0,n.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-sm border",children:[(0,n.jsx)("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"Acciones R\xe1pidas"}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,n.jsxs)("button",{onClick:()=>e("preguntas"),className:"flex items-center p-4 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors border border-blue-200",children:[(0,n.jsx)(d.jH2,{className:"h-6 w-6 text-blue-600 mr-3"}),(0,n.jsx)("span",{className:"font-medium text-blue-900",children:"Hacer Preguntas"})]}),(0,n.jsxs)("button",{onClick:()=>e("flashcards"),className:"flex items-center p-4 bg-orange-50 hover:bg-orange-100 rounded-lg transition-colors border border-orange-200",children:[(0,n.jsx)(d.GGD,{className:"h-6 w-6 text-orange-600 mr-3"}),(0,n.jsx)("span",{className:"font-medium text-orange-900",children:"Crear Flashcards"})]}),(0,n.jsxs)("button",{onClick:()=>e("tests"),className:"flex items-center p-4 bg-indigo-50 hover:bg-indigo-100 rounded-lg transition-colors border border-indigo-200",children:[(0,n.jsx)(d.NLe,{className:"h-6 w-6 text-indigo-600 mr-3"}),(0,n.jsx)("span",{className:"font-medium text-indigo-900",children:"Generar Tests"})]}),(0,n.jsxs)("button",{onClick:()=>e("mapas"),className:"flex items-center p-4 bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors border border-purple-200",children:[(0,n.jsx)(d.s_k,{className:"h-6 w-6 text-purple-600 mr-3"}),(0,n.jsx)("span",{className:"font-medium text-purple-900",children:"Mapas Mentales"})]}),(0,n.jsxs)("button",{onClick:()=>e("planEstudios"),className:"flex items-center p-4 bg-teal-50 hover:bg-teal-100 rounded-lg transition-colors border border-teal-200",children:[(0,n.jsx)(d.wIk,{className:"h-6 w-6 text-teal-600 mr-3"}),(0,n.jsx)("span",{className:"font-medium text-teal-900",children:"Plan de Estudios"})]})]})]})]})};var nT=s(32332),nA=s(79481);let nP=[{key:"lunes",label:"Lunes"},{key:"martes",label:"Martes"},{key:"miercoles",label:"Mi\xe9rcoles"},{key:"jueves",label:"Jueves"},{key:"viernes",label:"Viernes"},{key:"sabado",label:"S\xe1bado"},{key:"domingo",label:"Domingo"}],nR=({temario:e,onComplete:t,onCancel:s,isEditing:a=!1})=>{let[r,i]=(0,o.useState)(1),[l,c]=(0,o.useState)([]),[u,h]=(0,o.useState)(!0),[x,p]=(0,o.useState)(!1),[g,f]=(0,o.useState)({tiempoDiarioPromedio:2,tiempoPorDia:{},fechaExamen:"",fechaExamenAproximada:"",familiaridadGeneral:3,estimacionesTemas:{},preferenciasHorario:[],frecuenciaRepasos:"semanal"});(0,o.useEffect)(()=>{b()},[e.id,a]);let b=async()=>{h(!0);try{let t=await (0,nj.cm)(e.id);if(c(t),a){let t=await (0,nT.u9)(e.id);t&&f({tiempoDiarioPromedio:t.tiempo_diario_promedio||2,tiempoPorDia:t.tiempo_por_dia||{},fechaExamen:t.fecha_examen||"",fechaExamenAproximada:t.fecha_examen_aproximada||"",familiaridadGeneral:t.familiaridad_general||3,estimacionesTemas:{},preferenciasHorario:t.preferencias_horario||[],frecuenciaRepasos:t.frecuencia_repasos||"semanal"})}}catch(e){console.error("Error al cargar datos:",e),m.oR.error(a?"Error al cargar la planificaci\xf3n existente":"Error al cargar los temas del temario")}finally{h(!1)}},y=(e,t)=>{f(s=>({...s,[e]:t}))},v=(e,t)=>{f(s=>({...s,tiempoPorDia:{...s.tiempoPorDia,[e]:t}}))},j=e=>{f(t=>({...t,preferenciasHorario:t.preferenciasHorario.includes(e)?t.preferenciasHorario.filter(t=>t!==e):[...t.preferenciasHorario,e]}))},N=async()=>{p(!0);try{if(!await (0,nT.Pk)(e.id,{tiempo_diario_promedio:g.tiempoDiarioPromedio,tiempo_por_dia:g.tiempoPorDia,fecha_examen:g.fechaExamen||void 0,fecha_examen_aproximada:g.fechaExamenAproximada||void 0,familiaridad_general:g.familiaridadGeneral,preferencias_horario:g.preferenciasHorario,frecuencia_repasos:g.frecuenciaRepasos}))throw Error("Error al guardar la planificaci\xf3n");m.oR.success(a?"\xa1Planificaci\xf3n actualizada exitosamente!":"\xa1Planificaci\xf3n configurada exitosamente!"),t()}catch(e){console.error("Error al finalizar asistente:",e),m.oR.error(a?"Error al actualizar la planificaci\xf3n":"Error al guardar la planificaci\xf3n")}finally{p(!1)}};if(u)return(0,n.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,n.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})});let w=()=>(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)(d.Ohp,{className:"w-12 h-12 text-blue-600 mx-auto mb-4"}),(0,n.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Disponibilidad de Tiempo"}),(0,n.jsx)("p",{className:"text-gray-600",children:"Para empezar, \xbfcu\xe1nto tiempo REAL estimas que puedes dedicar al estudio cada d\xeda?"}),(0,n.jsx)("p",{className:"text-sm text-blue-600 mt-2",children:"S\xe9 realista. Considera tu trabajo, familia y otros compromisos."})]}),(0,n.jsxs)("div",{className:"bg-gray-50 rounded-lg p-6",children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"Tiempo promedio diario (horas)"}),(0,n.jsx)("input",{type:"number",min:"0.5",max:"12",step:"0.5",value:g.tiempoDiarioPromedio,onChange:e=>y("tiempoDiarioPromedio",parseFloat(e.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,n.jsxs)("div",{className:"bg-gray-50 rounded-lg p-6",children:[(0,n.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Tiempo espec\xedfico por d\xeda (opcional)"}),(0,n.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:nP.map(e=>(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsx)("label",{className:"text-sm font-medium text-gray-700",children:e.label}),(0,n.jsx)("input",{type:"number",min:"0",max:"12",step:"0.5",value:g.tiempoPorDia[e.key]||"",onChange:t=>v(e.key,parseFloat(t.target.value)||0),className:"w-20 px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"0"})]},e.key))})]})]}),E=()=>(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)(d.wIk,{className:"w-12 h-12 text-blue-600 mx-auto mb-4"}),(0,n.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Fecha del Examen"}),(0,n.jsx)("p",{className:"text-gray-600",children:"\xbfCu\xe1l es la fecha (aproximada o exacta) de tu pr\xf3xima convocatoria o examen principal?"}),(0,n.jsx)("p",{className:"text-sm text-blue-600 mt-2",children:"Esto nos ayudar\xe1 a distribuir el temario en el tiempo disponible."})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,n.jsxs)("div",{className:"bg-gray-50 rounded-lg p-6",children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"Fecha exacta del examen"}),(0,n.jsx)("input",{type:"date",value:g.fechaExamen,onChange:e=>y("fechaExamen",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,n.jsxs)("div",{className:"bg-gray-50 rounded-lg p-6",children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"O fecha aproximada"}),(0,n.jsxs)("select",{value:g.fechaExamenAproximada,onChange:e=>y("fechaExamenAproximada",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,n.jsx)("option",{value:"",children:"Selecciona una opci\xf3n"}),(0,n.jsx)("option",{value:"1-3_meses",children:"En 1-3 meses"}),(0,n.jsx)("option",{value:"3-6_meses",children:"En 3-6 meses"}),(0,n.jsx)("option",{value:"6-12_meses",children:"En 6-12 meses"}),(0,n.jsx)("option",{value:"mas_12_meses",children:"M\xe1s de 12 meses"}),(0,n.jsx)("option",{value:"primavera_2025",children:"Primavera 2025"}),(0,n.jsx)("option",{value:"verano_2025",children:"Verano 2025"}),(0,n.jsx)("option",{value:"otono_2025",children:"Oto\xf1o 2025"}),(0,n.jsx)("option",{value:"invierno_2025",children:"Invierno 2025"})]})]})]})]}),C=()=>(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)(d.x_j,{className:"w-12 h-12 text-blue-600 mx-auto mb-4"}),(0,n.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Familiaridad con el Temario"}),(0,n.jsx)("p",{className:"text-gray-600",children:"En una escala del 1 al 5, \xbfc\xf3mo describir\xedas tu familiaridad general actual con el conjunto del temario?"})]}),(0,n.jsx)("div",{className:"bg-gray-50 rounded-lg p-6",children:(0,n.jsx)("div",{className:"grid grid-cols-5 gap-4",children:[1,2,3,4,5].map(e=>(0,n.jsxs)("button",{onClick:()=>y("familiaridadGeneral",e),className:`p-4 rounded-lg border-2 transition-all ${g.familiaridadGeneral===e?"border-blue-500 bg-blue-50 text-blue-700":"border-gray-200 hover:border-gray-300"}`,children:[(0,n.jsx)("div",{className:"text-2xl font-bold mb-2",children:e}),(0,n.jsxs)("div",{className:"text-xs",children:[1===e&&"Muy poco",2===e&&"Poco",3===e&&"Moderado",4===e&&"Bastante",5===e&&"Muy familiarizado"]})]},e))})})]}),S=()=>(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)(d.usP,{className:"w-12 h-12 text-blue-600 mx-auto mb-4"}),(0,n.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Preferencias de Estudio"}),(0,n.jsx)("p",{className:"text-gray-600",children:"Configura tus preferencias de horario y frecuencia de repasos."})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,n.jsxs)("div",{className:"bg-gray-50 rounded-lg p-6",children:[(0,n.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Preferencias de Horario"}),(0,n.jsx)("div",{className:"space-y-2",children:["ma\xf1ana","tarde","noche"].map(e=>(0,n.jsxs)("label",{className:"flex items-center",children:[(0,n.jsx)("input",{type:"checkbox",checked:g.preferenciasHorario.includes(e),onChange:()=>j(e),className:"mr-3"}),(0,n.jsx)("span",{className:"capitalize",children:e})]},e))})]}),(0,n.jsxs)("div",{className:"bg-gray-50 rounded-lg p-6",children:[(0,n.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Frecuencia de Repasos"}),(0,n.jsxs)("select",{value:g.frecuenciaRepasos,onChange:e=>y("frecuenciaRepasos",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,n.jsx)("option",{value:"semanal",children:"Semanal"}),(0,n.jsx)("option",{value:"quincenal",children:"Quincenal"}),(0,n.jsx)("option",{value:"mensual",children:"Mensual"})]}),(0,n.jsx)("p",{className:"text-sm text-gray-600 mt-2",children:"Los repasos son fundamentales para consolidar el aprendizaje a largo plazo."})]})]})]});return(0,n.jsxs)("div",{className:"max-w-4xl mx-auto p-6",children:[(0,n.jsxs)("div",{className:"mb-8",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,n.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:a?"Modificar Planificaci\xf3n IA":"Asistente de Planificaci\xf3n IA"}),(0,n.jsxs)("span",{className:"text-sm text-gray-500",children:["Paso ",r," de 4"]})]}),(0,n.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,n.jsx)("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:`${r/4*100}%`}})})]}),(0,n.jsx)("div",{className:"bg-white rounded-xl shadow-sm border p-8 mb-6",children:(()=>{switch(r){case 1:return w();case 2:return E();case 3:return C();case 4:return S();default:return null}})()}),(0,n.jsxs)("div",{className:"flex justify-between",children:[(0,n.jsx)("button",{onClick:s,className:"px-6 py-2 text-gray-600 hover:text-gray-800 transition-colors",children:"Cancelar"}),(0,n.jsxs)("div",{className:"flex space-x-3",children:[r>1&&(0,n.jsxs)("button",{onClick:()=>{r>1&&i(r-1)},className:"flex items-center px-6 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors",children:[(0,n.jsx)(d.kRp,{className:"w-4 h-4 mr-2"}),"Anterior"]}),r<4?(0,n.jsxs)("button",{onClick:()=>{r<4&&i(r+1)},className:"flex items-center px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:["Siguiente",(0,n.jsx)(d.dyV,{className:"w-4 h-4 ml-2"})]}):(0,n.jsxs)("button",{onClick:N,disabled:x,className:"flex items-center px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50",children:[x?(0,n.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}):(0,n.jsx)(d.YrT,{className:"w-4 h-4 mr-2"}),a?"Actualizar":"Finalizar"]})]})]})]})},nD=({isOpen:e,onClose:t,temario:s,onSave:a})=>{let[r,i]=(0,o.useState)(""),[l,c]=(0,o.useState)(""),[u,h]=(0,o.useState)(!1);(0,o.useEffect)(()=>{e&&s&&(i(s.titulo),c(s.descripcion||""))},[e,s]);let x=async()=>{let e;if(!r.trim())return void m.oR.error("El t\xedtulo del temario es obligatorio");h(!0);try{if(e=m.oR.loading("Actualizando temario..."),await (0,nj.Se)(s.id,r.trim(),l.trim())){m.oR.success("Temario actualizado exitosamente",{id:e});let i={...s,titulo:r.trim(),descripcion:l.trim(),actualizado_en:new Date().toISOString()};a(i),t()}else m.oR.error("Error al actualizar el temario",{id:e})}catch(t){console.error("Error al actualizar temario:",t),m.oR.error("Error al actualizar el temario",{id:e})}finally{h(!1)}},p=()=>{i(s.titulo),c(s.descripcion||""),t()};return e?(0,n.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,n.jsxs)("div",{className:"bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Editar Temario"}),(0,n.jsx)("button",{onClick:p,className:"text-gray-400 hover:text-gray-600 transition-colors",disabled:u,children:(0,n.jsx)(d.yGN,{size:24})})]}),(0,n.jsxs)("div",{className:"p-6 space-y-4",onKeyDown:e=>{"Escape"===e.key?p():"Enter"===e.key&&e.ctrlKey&&x()},children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"titulo",className:"block text-sm font-medium text-gray-700 mb-2",children:"T\xedtulo del temario *"}),(0,n.jsx)("input",{type:"text",id:"titulo",value:r,onChange:e=>i(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Ej: Oposiciones Auxiliar Administrativo 2024",disabled:u,autoFocus:!0})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"descripcion",className:"block text-sm font-medium text-gray-700 mb-2",children:"Descripci\xf3n (opcional)"}),(0,n.jsx)("textarea",{id:"descripcion",value:l,onChange:e=>c(e.target.value),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none",placeholder:"Describe brevemente tu temario...",disabled:u})]}),(0,n.jsx)("div",{className:"bg-blue-50 rounded-lg p-4",children:(0,n.jsxs)("div",{className:"flex items-start",children:[(0,n.jsx)("div",{className:"flex-shrink-0",children:(0,n.jsx)("div",{className:"w-2 h-2 bg-blue-400 rounded-full mt-2"})}),(0,n.jsx)("div",{className:"ml-3",children:(0,n.jsxs)("p",{className:"text-sm text-blue-800",children:[(0,n.jsx)("strong",{children:"Nota:"})," Solo puedes editar el t\xedtulo y la descripci\xf3n del temario. Para modificar los temas, utiliza las opciones individuales de cada tema."]})})]})})]}),(0,n.jsxs)("div",{className:"flex items-center justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50",children:[(0,n.jsx)("button",{onClick:p,className:"px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors",disabled:u,children:"Cancelar"}),(0,n.jsx)("button",{onClick:x,disabled:u||!r.trim(),className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:u?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Guardando..."]}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(d.Bc_,{className:"w-4 h-4 mr-2"}),"Guardar cambios"]})})]}),(0,n.jsx)("div",{className:"px-6 pb-4",children:(0,n.jsxs)("p",{className:"text-xs text-gray-500",children:[(0,n.jsx)("strong",{children:"Atajos:"})," Esc para cancelar • Ctrl+Enter para guardar"]})})]})}):null},nM=({isOpen:e,onClose:t,tema:s,onSave:a})=>{let[r,i]=(0,o.useState)(""),[l,c]=(0,o.useState)(""),[u,h]=(0,o.useState)(!1);(0,o.useEffect)(()=>{e&&s&&(i(s.titulo),c(s.descripcion||""))},[e,s]);let x=async()=>{let e;if(!r.trim())return void m.oR.error("El t\xedtulo del tema es obligatorio");h(!0);try{if(e=m.oR.loading("Actualizando tema..."),await (0,nj.oS)(s.id,r.trim(),l.trim())){m.oR.success("Tema actualizado exitosamente",{id:e});let i={...s,titulo:r.trim(),descripcion:l.trim(),actualizado_en:new Date().toISOString()};a(i),t()}else m.oR.error("Error al actualizar el tema",{id:e})}catch(t){console.error("Error al actualizar tema:",t),m.oR.error("Error al actualizar el tema",{id:e})}finally{h(!1)}},p=()=>{i(s.titulo),c(s.descripcion||""),t()};return e?(0,n.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,n.jsxs)("div",{className:"bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,n.jsxs)("h3",{className:"text-lg font-semibold text-gray-900",children:["Editar Tema ",s.numero]}),(0,n.jsx)("button",{onClick:p,className:"text-gray-400 hover:text-gray-600 transition-colors",disabled:u,children:(0,n.jsx)(d.yGN,{size:24})})]}),(0,n.jsxs)("div",{className:"p-6 space-y-4",onKeyDown:e=>{"Escape"===e.key?p():"Enter"===e.key&&e.ctrlKey&&x()},children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"titulo",className:"block text-sm font-medium text-gray-700 mb-2",children:"T\xedtulo del tema *"}),(0,n.jsx)("input",{type:"text",id:"titulo",value:r,onChange:e=>i(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Ej: Introducci\xf3n al Derecho Administrativo",disabled:u,autoFocus:!0})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"descripcion",className:"block text-sm font-medium text-gray-700 mb-2",children:"Descripci\xf3n (opcional)"}),(0,n.jsx)("textarea",{id:"descripcion",value:l,onChange:e=>c(e.target.value),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none",placeholder:"Describe brevemente el contenido del tema...",disabled:u})]}),(0,n.jsx)("div",{className:"bg-blue-50 rounded-lg p-4",children:(0,n.jsxs)("div",{className:"flex items-start",children:[(0,n.jsx)("div",{className:"flex-shrink-0",children:(0,n.jsx)("div",{className:"w-2 h-2 bg-blue-400 rounded-full mt-2"})}),(0,n.jsx)("div",{className:"ml-3",children:(0,n.jsxs)("p",{className:"text-sm text-blue-800",children:[(0,n.jsx)("strong",{children:"Nota:"})," El n\xfamero del tema y su estado de completado no se pueden modificar desde aqu\xed. Solo puedes editar el t\xedtulo y la descripci\xf3n."]})})]})})]}),(0,n.jsxs)("div",{className:"flex items-center justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50",children:[(0,n.jsx)("button",{onClick:p,className:"px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors",disabled:u,children:"Cancelar"}),(0,n.jsx)("button",{onClick:x,disabled:u||!r.trim(),className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:u?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Guardando..."]}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(d.Bc_,{className:"w-4 h-4 mr-2"}),"Guardar cambios"]})})]}),(0,n.jsx)("div",{className:"px-6 pb-4",children:(0,n.jsxs)("p",{className:"text-xs text-gray-500",children:[(0,n.jsx)("strong",{children:"Atajos:"})," Esc para cancelar • Ctrl+Enter para guardar"]})})]})}):null},n$=({tema:e,onEdit:t,onDelete:s,onToggleCompletado:a,isUpdating:r})=>{let[i,l]=(0,o.useState)(!1),[c,u]=(0,o.useState)(!1),h=async()=>{let t;u(!0);try{t=m.oR.loading("Eliminando tema..."),await (0,nj.B$)(e.id)?(m.oR.success("Tema eliminado exitosamente",{id:t}),s(e.id)):m.oR.error("Error al eliminar el tema",{id:t})}catch(e){console.error("Error al eliminar tema:",e),m.oR.error("Error al eliminar el tema",{id:t})}finally{u(!1),l(!1)}};return i?(0,n.jsxs)("div",{className:"flex items-center space-x-2 bg-red-50 border border-red-200 rounded-lg p-3",children:[(0,n.jsx)(d.eHT,{className:"w-4 h-4 text-red-600 flex-shrink-0"}),(0,n.jsx)("div",{className:"flex-1 min-w-0",children:(0,n.jsxs)("p",{className:"text-sm text-red-800",children:['\xbfEliminar tema "',e.titulo,'"?']})}),(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)("button",{onClick:()=>{l(!1)},className:"px-2 py-1 text-xs text-gray-600 bg-white border border-gray-300 rounded hover:bg-gray-50 transition-colors",disabled:c,children:"Cancelar"}),(0,n.jsx)("button",{onClick:h,disabled:c,className:"px-2 py-1 text-xs text-white bg-red-600 rounded hover:bg-red-700 transition-colors disabled:opacity-50 flex items-center",children:c?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:"animate-spin rounded-full h-3 w-3 border-b border-white mr-1"}),"Eliminando..."]}):"Eliminar"})]})]}):(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)("button",{onClick:()=>{t(e)},className:"p-2 text-gray-400 hover:text-blue-600 rounded-lg hover:bg-blue-50 transition-colors",title:"Editar tema",disabled:r,children:(0,n.jsx)(d.SG1,{className:"w-4 h-4"})}),(0,n.jsx)("button",{onClick:()=>{l(!0)},className:"p-2 text-gray-400 hover:text-red-600 rounded-lg hover:bg-red-50 transition-colors",title:"Eliminar tema",disabled:r,children:(0,n.jsx)(d.IXo,{className:"w-4 h-4"})}),(0,n.jsxs)("button",{onClick:()=>{a(e.id,e.completado)},disabled:r,className:`flex items-center px-3 py-1.5 rounded-md text-sm font-medium transition-colors ${e.completado?"bg-gray-100 text-gray-700 hover:bg-gray-200":"bg-green-100 text-green-700 hover:bg-green-200"} disabled:opacity-50 disabled:cursor-not-allowed`,children:[r?(0,n.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-1"}):e.completado?(0,n.jsx)(d.Ohp,{className:"w-4 h-4 mr-1"}):(0,n.jsx)(d.YrT,{className:"w-4 h-4 mr-1"}),e.completado?"Marcar pendiente":"Marcar completado"]})]})},nL=()=>{let[e,t]=(0,o.useState)(null),[s,a]=(0,o.useState)([]),[r,i]=(0,o.useState)(null),[l,c]=(0,o.useState)(!0),[u,h]=(0,o.useState)(null),[x,p]=(0,o.useState)(!1),[g,f]=(0,o.useState)(!1),[b,y]=(0,o.useState)(!1),[v,j]=(0,o.useState)(!1),[N,w]=(0,o.useState)(null),[E,C]=(0,o.useState)(!1),[S,k]=(0,o.useState)(!1);(0,o.useEffect)(()=>{T()},[]);let T=async()=>{c(!0);try{let e=await (0,nj.jg)();if(e){t(e);let[s,r,n]=await Promise.all([(0,nj.cm)(e.id),(0,nj.Il)(e.id),(0,nT.vD)(e.id)]);a(s),i(r),p(n)}}catch(e){console.error("Error al cargar datos del temario:",e),m.oR.error("Error al cargar el temario")}finally{c(!1)}},A=async(t,r)=>{h(t);try{if(await (0,nj.cN)(t,!r)){if(a(s.map(e=>e.id===t?{...e,completado:!r,fecha_completado:r?void 0:new Date().toISOString()}:e)),e){let t=await (0,nj.Il)(e.id);i(t)}m.oR.success(r?"Tema marcado como pendiente":"Tema marcado como completado")}else m.oR.error("Error al actualizar el estado del tema")}catch(e){console.error("Error al actualizar tema:",e),m.oR.error("Error al actualizar el tema")}finally{h(null)}},P=e=>new Date(e).toLocaleDateString("es-ES",{day:"2-digit",month:"2-digit",year:"numeric"}),R=e=>{w(e),j(!0)},D=async t=>{a(s.filter(e=>e.id!==t)),e&&i(await (0,nj.Il)(e.id))},M=async()=>{let s;if(e){k(!0);try{s=m.oR.loading("Eliminando temario y desactivando plan de estudios...");let{user:r}=await (0,L.iF)();r&&await nA.N.from("planificacion_usuario").delete().eq("user_id",r.id).eq("temario_id",e.id),await nA.N.from("planes_estudios").update({activo:!1}).eq("temario_id",e.id),await (0,nj.xv)(e.id)?(m.oR.success("Temario eliminado y plan de estudios desactivado exitosamente",{id:s}),t(null),a([]),i(null),p(!1)):m.oR.error("Error al eliminar el temario",{id:s})}catch(e){console.error("Error al eliminar temario:",e),m.oR.error("Error al eliminar el temario",{id:s})}finally{k(!1),C(!1)}}};return l?(0,n.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,n.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):e?g?(0,n.jsx)(nR,{temario:e,onComplete:()=>{f(!1),p(!0),T()},onCancel:()=>{f(!1)},isEditing:x}):(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-sm border",children:[(0,n.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:e.titulo}),e.descripcion&&(0,n.jsx)("p",{className:"text-gray-600",children:e.descripcion}),(0,n.jsxs)("div",{className:"flex items-center mt-2 space-x-4",children:[(0,n.jsx)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${"completo"===e.tipo?"bg-green-100 text-green-800":"bg-orange-100 text-orange-800"}`,children:"completo"===e.tipo?"Temario Completo":"Temas Sueltos"}),(0,n.jsxs)("span",{className:"text-sm text-gray-500",children:["Creado el ",P(e.creado_en)]})]})]}),(0,n.jsxs)("div",{className:"flex space-x-2",children:[(0,n.jsx)("button",{onClick:()=>{y(!0)},className:"p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors",title:"Editar temario",children:(0,n.jsx)(d.SG1,{className:"w-5 h-5"})}),(0,n.jsx)("button",{onClick:()=>{C(!0)},className:"p-2 text-gray-400 hover:text-red-600 rounded-lg hover:bg-red-50 transition-colors",title:"Eliminar temario",children:(0,n.jsx)(d.IXo,{className:"w-5 h-5"})})]})]}),r&&(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,n.jsx)("div",{className:"bg-blue-50 rounded-lg p-4",children:(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)(d.H9b,{className:"w-5 h-5 text-blue-600 mr-2"}),(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm font-medium text-blue-800",children:"Total Temas"}),(0,n.jsx)("p",{className:"text-2xl font-bold text-blue-600",children:r.totalTemas})]})]})}),(0,n.jsx)("div",{className:"bg-green-50 rounded-lg p-4",children:(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)(d.YrT,{className:"w-5 h-5 text-green-600 mr-2"}),(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm font-medium text-green-800",children:"Completados"}),(0,n.jsx)("p",{className:"text-2xl font-bold text-green-600",children:r.temasCompletados})]})]})}),(0,n.jsx)("div",{className:"bg-purple-50 rounded-lg p-4",children:(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)(d.ARf,{className:"w-5 h-5 text-purple-600 mr-2"}),(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm font-medium text-purple-800",children:"Progreso"}),(0,n.jsxs)("p",{className:"text-2xl font-bold text-purple-600",children:[r.porcentajeCompletado.toFixed(1),"%"]})]})]})})]}),r&&(0,n.jsxs)("div",{className:"mt-4",children:[(0,n.jsxs)("div",{className:"flex justify-between text-sm text-gray-600 mb-1",children:[(0,n.jsx)("span",{children:"Progreso del temario"}),(0,n.jsxs)("span",{children:[r.porcentajeCompletado.toFixed(1),"%"]})]}),(0,n.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,n.jsx)("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:`${r.porcentajeCompletado}%`}})})]})]}),"completo"===e.tipo&&(0,n.jsx)("div",{className:`border rounded-xl p-6 ${x?"bg-green-50 border-green-200":"bg-blue-50 border-blue-200"}`,children:(0,n.jsxs)("div",{className:"flex items-start justify-between",children:[(0,n.jsxs)("div",{className:"flex items-start",children:[x?(0,n.jsx)(d.YrT,{className:"w-6 h-6 text-green-600 mr-3 flex-shrink-0 mt-0.5"}):(0,n.jsx)(d.FrA,{className:"w-6 h-6 text-blue-600 mr-3 flex-shrink-0 mt-0.5"}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:`text-lg font-medium mb-2 ${x?"text-green-900":"text-blue-900"}`,children:x?"Planificaci\xf3n Configurada":"Planificaci\xf3n Inteligente con IA"}),(0,n.jsx)("p",{className:`text-sm mb-3 ${x?"text-green-800":"text-blue-800"}`,children:x?"Ya tienes configurada tu planificaci\xf3n de estudio personalizada. Pronto podr\xe1s ver tu calendario y seguimiento.":"Configura tu planificaci\xf3n personalizada con nuestro asistente inteligente:"}),!x&&(0,n.jsxs)("ul",{className:"text-blue-800 text-sm space-y-1",children:[(0,n.jsx)("li",{children:"• Planificaci\xf3n autom\xe1tica de estudio con IA"}),(0,n.jsx)("li",{children:"• Seguimiento de progreso personalizado"}),(0,n.jsx)("li",{children:"• Recomendaciones de orden de estudio"}),(0,n.jsx)("li",{children:"• Estimaci\xf3n de tiempos por tema"})]})]})]}),(0,n.jsx)("div",{className:"flex gap-2",children:x?(0,n.jsxs)("button",{onClick:()=>{f(!0)},className:"flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors",children:[(0,n.jsx)(d.VSk,{className:"w-4 h-4 mr-2"}),"Modificar Planificaci\xf3n"]}):(0,n.jsxs)("button",{onClick:()=>{f(!0)},className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,n.jsx)(d.FrA,{className:"w-4 h-4 mr-2"}),"Configurar Planificaci\xf3n"]})})]})}),(0,n.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border",children:[(0,n.jsxs)("div",{className:"p-6 border-b border-gray-200",children:[(0,n.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Temas del Temario"}),(0,n.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"Marca los temas como completados seg\xfan vayas estudi\xe1ndolos"})]}),(0,n.jsx)("div",{className:"divide-y divide-gray-200",children:s.map(e=>(0,n.jsx)("div",{className:"p-6 hover:bg-gray-50 transition-colors",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"flex items-start space-x-4 flex-1",children:[(0,n.jsx)("div",{className:"flex-shrink-0",children:(0,n.jsx)("span",{className:"inline-flex items-center justify-center w-8 h-8 rounded-full bg-blue-100 text-blue-800 text-sm font-medium",children:e.numero})}),(0,n.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,n.jsx)("h3",{className:`text-lg font-medium ${e.completado?"text-gray-500 line-through":"text-gray-900"}`,children:e.titulo}),e.descripcion&&(0,n.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:e.descripcion}),e.fecha_completado&&(0,n.jsxs)("div",{className:"flex items-center mt-2 text-sm text-green-600",children:[(0,n.jsx)(d.YrT,{className:"w-4 h-4 mr-1"}),"Completado el ",P(e.fecha_completado)]})]})]}),(0,n.jsx)("div",{className:"flex items-center space-x-3",children:(0,n.jsx)(n$,{tema:e,onEdit:R,onDelete:D,onToggleCompletado:A,isUpdating:u===e.id})})]})},e.id))})]}),e&&(0,n.jsx)(nD,{isOpen:b,onClose:()=>{y(!1)},temario:e,onSave:e=>{t(e),y(!1)}}),N&&(0,n.jsx)(nM,{isOpen:v,onClose:()=>{j(!1),w(null)},tema:N,onSave:e=>{a(s.map(t=>t.id===e.id?e:t)),j(!1),w(null)}}),E&&(0,n.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,n.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-md w-full mx-4",children:[(0,n.jsxs)("div",{className:"flex items-center mb-4",children:[(0,n.jsx)("div",{className:"flex-shrink-0",children:(0,n.jsx)(d.IXo,{className:"w-6 h-6 text-red-600"})}),(0,n.jsx)("div",{className:"ml-3",children:(0,n.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Eliminar Temario"})})]}),(0,n.jsxs)("div",{className:"mb-6",children:[(0,n.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:"\xbfEst\xe1s seguro de que quieres eliminar este temario? Esta acci\xf3n:"}),(0,n.jsxs)("ul",{className:"text-sm text-red-600 space-y-1 ml-4",children:[(0,n.jsxs)("li",{children:['• Eliminar\xe1 permanentemente el temario "',(0,n.jsx)("strong",{children:e?.titulo}),'"']}),(0,n.jsx)("li",{children:"• Eliminar\xe1 todos los temas asociados"}),(0,n.jsx)("li",{children:"• Eliminar\xe1 la planificaci\xf3n de estudios configurada"}),(0,n.jsx)("li",{children:"• Desactivar\xe1 los planes de estudios generados (se conservan en el historial)"})]}),(0,n.jsx)("p",{className:"text-sm text-gray-600 mt-3 font-medium",children:"Esta acci\xf3n no se puede deshacer."})]}),(0,n.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,n.jsx)("button",{onClick:()=>{C(!1)},disabled:S,className:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 disabled:opacity-50",children:"Cancelar"}),(0,n.jsx)("button",{onClick:M,disabled:S,className:"px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 flex items-center",children:S?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Eliminando..."]}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(d.IXo,{className:"w-4 h-4 mr-2"}),"Eliminar Temario"]})})]})]})})]}):(0,n.jsxs)("div",{className:"text-center py-12",children:[(0,n.jsx)(d.H9b,{className:"w-16 h-16 text-gray-400 mx-auto mb-4"}),(0,n.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No hay temario configurado"}),(0,n.jsx)("p",{className:"text-gray-500",children:"Configura tu temario desde el dashboard para comenzar."})]})};function nF(){let[e,t]=(0,o.useState)(null),[s,a]=(0,o.useState)(!1);return e?(0,n.jsxs)("div",{className:"fixed bottom-4 right-4 z-50",children:[(0,n.jsx)("button",{onClick:()=>a(!s),className:"bg-blue-500 text-white px-3 py-2 rounded-full text-sm shadow-lg hover:bg-blue-600 transition-colors",children:"\uD83D\uDC1B Debug"}),s&&(0,n.jsxs)("div",{className:"absolute bottom-12 right-0 bg-black bg-opacity-90 text-white p-4 rounded-lg shadow-xl max-w-sm text-xs",children:[(0,n.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,n.jsx)("h3",{className:"font-bold text-sm",children:"Debug Info"}),(0,n.jsx)("button",{onClick:()=>a(!1),className:"text-gray-300 hover:text-white",children:"✕"})]}),(0,n.jsxs)("div",{className:"space-y-1",children:[(0,n.jsxs)("div",{className:"flex justify-between",children:[(0,n.jsx)("span",{children:"M\xf3vil:"}),(0,n.jsx)("span",{className:e.isMobile?"text-green-400":"text-red-400",children:e.isMobile?"S\xed":"No"})]}),(0,n.jsxs)("div",{className:"flex justify-between",children:[(0,n.jsx)("span",{children:"LocalStorage:"}),(0,n.jsx)("span",{className:e.hasLocalStorage?"text-green-400":"text-red-400",children:e.hasLocalStorage?"S\xed":"No"})]}),(0,n.jsxs)("div",{className:"flex justify-between",children:[(0,n.jsx)("span",{children:"Cookies:"}),(0,n.jsx)("span",{className:e.hasCookies?"text-green-400":"text-red-400",children:e.hasCookies?"S\xed":"No"})]}),(0,n.jsxs)("div",{className:"flex justify-between",children:[(0,n.jsx)("span",{children:"Sesi\xf3n Supabase:"}),(0,n.jsx)("span",{className:e.supabaseSession?"text-green-400":"text-red-400",children:e.supabaseSession?"S\xed":"No"})]}),(0,n.jsxs)("div",{className:"flex justify-between",children:[(0,n.jsx)("span",{children:"Token localStorage:"}),(0,n.jsx)("span",{className:e.localStorageToken?"text-green-400":"text-red-400",children:e.localStorageToken?"S\xed":"No"})]}),(0,n.jsxs)("div",{className:"flex justify-between",children:[(0,n.jsx)("span",{children:"Token Cookie:"}),(0,n.jsx)("span",{className:e.cookieToken?"text-green-400":"text-red-400",children:e.cookieToken?"S\xed":"No"})]}),(0,n.jsx)("div",{className:"mt-2 pt-2 border-t border-gray-600",children:(0,n.jsxs)("div",{className:"text-gray-300 break-all",children:[(0,n.jsx)("strong",{children:"User Agent:"}),(0,n.jsx)("br",{}),e.userAgent.substring(0,100),"..."]})}),(0,n.jsxs)("div",{className:"text-gray-400 text-xs mt-1",children:["Actualizado: ",new Date(e.timestamp).toLocaleTimeString()]})]})]})]}):null}var nV=s(72971);function n_(){let[e,t]=(0,o.useState)(!1),[s,a]=(0,o.useState)({supabaseConnection:!1,userAuthenticated:!1,geminiApiKey:!1,conversationsCount:0,documentsCount:0,lastError:null}),[r,i]=(0,o.useState)(!1),{user:l,session:d}=(0,f.A)(),c=async()=>{try{i(!0);let e=await (0,nV.Yp)("Conversaci\xf3n de prueba",!1);e?(a(t=>({...t,lastError:`✅ Conversaci\xf3n de prueba creada exitosamente: ${e}`})),u()):a(e=>({...e,lastError:"❌ No se pudo crear la conversaci\xf3n de prueba"}))}catch(e){a(t=>({...t,lastError:`❌ Error al crear conversaci\xf3n de prueba: ${e instanceof Error?e.message:"Unknown error"}`}))}finally{i(!1)}},u=async()=>{i(!0);let e={supabaseConnection:!1,userAuthenticated:!1,geminiApiKey:!1,conversationsCount:0,documentsCount:0,lastError:null};try{e.userAuthenticated=!!l&&!!d;try{let{data:t,error:s}=await $.N.from("conversaciones").select("id").limit(1);if(s)e.lastError=`Supabase error: ${s.message}`;else{e.supabaseConnection=!0;let{count:t,error:s}=await $.N.from("conversaciones").select("*",{count:"exact",head:!0}).eq("user_id",l&&l.id?l.id:"");s?(e.conversationsCount=0,"406"===s.code?e.lastError="Error 406 al contar conversaciones - esto es normal para usuarios nuevos":e.lastError=`Error al contar conversaciones: ${s.message}`):e.conversationsCount=t||0}}catch(t){e.lastError=`Supabase connection error: ${t instanceof Error?t.message:"Unknown error"}`}try{let{data:t,error:s}=await $.N.from("documentos").select("count",{count:"exact",head:!0});s||(e.documentsCount=t?.length||0)}catch(e){console.error("Error checking documents:",e)}e.geminiApiKey=!!process.env.OPENAI_API_KEY}catch(t){e.lastError=`General error: ${t instanceof Error?t.message:"Unknown error"}`}a(e),i(!1)};return e?(0,n.jsxs)("div",{className:"fixed bottom-4 right-4 bg-white border border-gray-300 rounded-lg shadow-lg p-4 w-80 z-50",children:[(0,n.jsxs)("div",{className:"flex justify-between items-center mb-3",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold",children:"Diagn\xf3stico del Sistema"}),(0,n.jsx)("button",{onClick:()=>t(!1),className:"text-gray-500 hover:text-gray-700",children:"✕"})]}),r?(0,n.jsxs)("div",{className:"text-center py-4",children:[(0,n.jsx)("div",{className:"animate-spin h-6 w-6 border-2 border-blue-500 border-t-transparent rounded-full mx-auto"}),(0,n.jsx)("p",{className:"mt-2 text-sm text-gray-600",children:"Ejecutando diagn\xf3sticos..."})]}):(0,n.jsxs)("div",{className:"space-y-3",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsx)("span",{className:"text-sm",children:"Usuario autenticado:"}),(0,n.jsx)("span",{className:`text-sm font-semibold ${s.userAuthenticated?"text-green-600":"text-red-600"}`,children:s.userAuthenticated?"✓":"✗"})]}),(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsx)("span",{className:"text-sm",children:"Conexi\xf3n Supabase:"}),(0,n.jsx)("span",{className:`text-sm font-semibold ${s.supabaseConnection?"text-green-600":"text-red-600"}`,children:s.supabaseConnection?"✓":"✗"})]}),(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsx)("span",{className:"text-sm",children:"API Key Gemini:"}),(0,n.jsx)("span",{className:`text-sm font-semibold ${s.geminiApiKey?"text-green-600":"text-red-600"}`,children:s.geminiApiKey?"✓":"✗"})]}),(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsx)("span",{className:"text-sm",children:"Conversaciones:"}),(0,n.jsx)("span",{className:"text-sm font-semibold text-blue-600",children:s.conversationsCount})]}),(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsx)("span",{className:"text-sm",children:"Documentos:"}),(0,n.jsx)("span",{className:"text-sm font-semibold text-blue-600",children:s.documentsCount})]}),s.lastError&&(0,n.jsxs)("div",{className:"mt-3 p-2 bg-red-50 border border-red-200 rounded",children:[(0,n.jsx)("p",{className:"text-xs text-red-700 font-semibold",children:"\xdaltimo error:"}),(0,n.jsx)("p",{className:"text-xs text-red-600 mt-1",children:s.lastError})]}),(0,n.jsxs)("div",{className:"mt-4 space-y-2",children:[(0,n.jsxs)("div",{className:"flex space-x-2",children:[(0,n.jsx)("button",{onClick:u,className:"flex-1 bg-blue-500 hover:bg-blue-600 text-white text-xs py-2 px-3 rounded",disabled:r,children:"Actualizar"}),(0,n.jsx)("button",{onClick:()=>{console.log("Diagn\xf3sticos completos:",s),console.log("Usuario:",l),console.log("Sesi\xf3n:",d)},className:"flex-1 bg-gray-500 hover:bg-gray-600 text-white text-xs py-2 px-3 rounded",children:"Log Info"})]}),(0,n.jsx)("button",{onClick:c,className:"w-full bg-green-500 hover:bg-green-600 text-white text-xs py-2 px-3 rounded",disabled:r||!s.userAuthenticated,children:"Probar Crear Conversaci\xf3n"})]})]})]}):(0,n.jsx)("button",{onClick:()=>t(!0),className:"fixed bottom-4 right-4 bg-red-500 hover:bg-red-600 text-white p-2 rounded-full shadow-lg z-50",title:"Abrir panel de diagn\xf3stico",children:"\uD83D\uDD27"})}let nz=({activeTab:e,onTabChange:t,children:s})=>{let[a,r]=(0,o.useState)([]),[i,l]=(0,o.useState)(!1);(0,o.useEffect)(()=>{let e=localStorage.getItem("sidebarCollapsed");e&&l(JSON.parse(e))},[]),(0,o.useEffect)(()=>{localStorage.setItem("sidebarCollapsed",JSON.stringify(i))},[i]);let c=[{id:"dashboard",label:"Principal",icon:(0,n.jsx)(d.jTZ,{}),color:"bg-gradient-to-r from-blue-600 to-purple-600"},{id:"mi-temario-group",label:"Mi Temario",icon:(0,n.jsx)(d.H9b,{}),color:"bg-green-600",isGroup:!0,children:[{id:"temario",label:"Mi Temario",icon:(0,n.jsx)(d.H9b,{}),color:"bg-green-600"},{id:"gestionar",label:"Gestionar Documentos",icon:(0,n.jsx)(d.VSk,{}),color:"bg-gray-600"}]},{id:"planEstudios",label:"Mi Plan de Estudios",icon:(0,n.jsx)(d.wIk,{}),color:"bg-teal-600"},{id:"preguntas",label:"Habla con tu preparador",icon:(0,n.jsx)(d.mEP,{}),color:"bg-blue-600"},{id:"herramientas-group",label:"Herramientas de estudio",icon:(0,n.jsx)(d.x_j,{}),color:"bg-purple-600",isGroup:!0,children:[{id:"flashcards-group",label:"Flashcards",icon:(0,n.jsx)(d.lZI,{}),color:"bg-orange-500",isGroup:!0,children:[{id:"flashcards",label:"Generador de Flashcards",icon:(0,n.jsx)(d.GGD,{}),color:"bg-orange-500"},{id:"misFlashcards",label:"Mis Flashcards",icon:(0,n.jsx)(d.lZI,{}),color:"bg-emerald-600"}]},{id:"tests-group",label:"Tests",icon:(0,n.jsx)(d.NLe,{}),color:"bg-indigo-600",isGroup:!0,children:[{id:"tests",label:"Generador de Tests",icon:(0,n.jsx)(d.GGD,{}),color:"bg-indigo-600"},{id:"misTests",label:"Mis Tests",icon:(0,n.jsx)(d.NLe,{}),color:"bg-pink-600"}]}]},{id:"resumenes",label:"Res\xfamenes",icon:(0,n.jsx)(d.D1A,{}),color:"bg-green-600"},{id:"mapas",label:"Mapas Mentales",icon:(0,n.jsx)(d.s_k,{}),color:"bg-purple-600"}],u=e=>{r(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e])},m=e=>a.includes(e),h=()=>{r([])},x=(s,a=0)=>{let r=s.children&&s.children.length>0,o=e===s.id&&!s.isGroup,c=m(s.id);return i&&a>0?null:(0,n.jsxs)("div",{children:[(0,n.jsxs)("div",{className:`flex items-center ${i?"justify-center tooltip-hover":"justify-between"} px-${i?"2":2+2*a} py-2 rounded-lg transition-all duration-300 cursor-pointer ${o&&!r?`text-white ${s.color} shadow-md`:"text-gray-600 hover:bg-gray-100 hover:text-gray-800"}`,onClick:()=>{i&&r?(l(!1),u(s.id)):r||s.isGroup?u(s.id):(h(),t(s.id))},title:i?s.label:void 0,"data-tooltip":i?s.label:void 0,"aria-label":s.label,children:[(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)("span",{className:`${i?"":"mr-2"} text-sm`,children:s.icon}),!i&&(0,n.jsx)("span",{className:"text-sm font-medium",children:s.label})]}),r&&!i&&(0,n.jsx)("span",{className:"text-xs",children:c?(0,n.jsx)(d.fK4,{}):(0,n.jsx)(d.fOo,{})})]}),r&&c&&!i&&(0,n.jsx)("div",{className:"ml-2 mt-1 space-y-1",children:s.children.map(e=>x(e,a+1))})]},`${s.id}-${a}`)};return(0,n.jsxs)("div",{className:`${i?"w-16":"w-80"} flex-shrink-0 space-y-4 sidebar-transition`,children:[(0,n.jsxs)("div",{className:"bg-white rounded-xl shadow-sm p-4 sticky top-6",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[!i&&(0,n.jsx)("h2",{className:"text-sm font-semibold text-gray-500 uppercase tracking-wider px-2",children:"Men\xfa de Estudio"}),(0,n.jsx)("button",{onClick:()=>{l(e=>{let t=!e;return t&&r([]),t})},className:"p-1 rounded-lg hover:bg-gray-100 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",title:i?"Expandir men\xfa":"Colapsar men\xfa","aria-label":i?"Expandir men\xfa de navegaci\xf3n":"Colapsar men\xfa de navegaci\xf3n","aria-expanded":!i,children:i?(0,n.jsx)(d.ND1,{className:"w-4 h-4 text-gray-600"}):(0,n.jsx)(d.pM3,{className:"w-4 h-4 text-gray-600"})})]}),(0,n.jsx)("nav",{className:"space-y-1",children:c.map(e=>x(e))})]}),!i&&s]})},nI=(e={})=>{let{tasks:t}=(0,C.M)(),[s,a]=(0,o.useState)(null),[r,i]=(0,o.useState)(!1);return(0,o.useEffect)(()=>{let r=t.filter(e=>"plan-estudios"===e.type);i(r.some(e=>"pending"===e.status||"processing"===e.status));let n=r.filter(e=>"completed"===e.status&&e.result).sort((e,t)=>new Date(t.createdAt).getTime()-new Date(e.createdAt).getTime());if(n.length>0){let t=n[0];a(t.result),e.onResult&&t.result!==s&&e.onResult(t.result)}let o=r.filter(e=>"error"===e.status).sort((e,t)=>new Date(t.createdAt).getTime()-new Date(e.createdAt).getTime());if(o.length>0&&e.onError){let t=o[0];e.onError(t.error||"Error desconocido")}},[t,e,s]),{latestResult:s,isLoading:r,hasResults:!!s}};s(81552);var nO=s(3930);class nB{static async logWebhookEvent(e){try{let t=e.success?"✅":"❌",s=new Date().toISOString();console.log(`${t} [WEBHOOK] ${s}`,{eventType:e.eventType,eventId:e.eventId,success:e.success,processingTime:`${e.processingTime}ms`,message:e.message,...e.error&&{error:e.error},...e.data&&{data:e.data}}),await this.logToExternalService(e)}catch(e){console.error("Error logging webhook event:",e)}}static async logFeatureAccess(e,t,a,r,i=0,n){try{if(process.env.SUPABASE_SERVICE_ROLE_KEY){let{SupabaseAdminService:o}=await s.e(1155).then(s.bind(s,91155));await o.logFeatureAccess({user_id:e,feature_name:t,access_granted:a,plan_at_time:r,tokens_used:i,denial_reason:n})}let o=a?"✅":"❌";console.log(`${o} [FEATURE_ACCESS]`,{userId:e,feature:t,granted:a,plan:r,tokens:i,...n&&{reason:n}})}catch(e){console.error("Error logging feature access:",e)}}static async logPlanChange(e,t,a,r,i,n){try{if(process.env.SUPABASE_SERVICE_ROLE_KEY){let{SupabaseAdminService:o}=await s.e(1155).then(s.bind(s,91155));await o.logPlanChange({user_id:e,old_plan:t||void 0,new_plan:a,changed_by:r,reason:i,transaction_id:n})}console.log("\uD83D\uDD04 [PLAN_CHANGE]",{userId:e,oldPlan:t,newPlan:a,changedBy:r,reason:i,transactionId:n})}catch(e){console.error("Error logging plan change:",e)}}static async logCriticalError(e,t,s){try{let a={context:e,message:t.message,stack:t.stack,timestamp:new Date().toISOString(),additionalData:s};console.error("\uD83D\uDEA8 [CRITICAL_ERROR]",a),await this.sendCriticalAlert(a)}catch(e){console.error("Error logging critical error:",e)}}static logPerformanceMetrics(e,t,s,a){let r={operation:e,duration:`${t}ms`,success:s,timestamp:new Date().toISOString(),...a};console.log("\uD83D\uDCCA [PERFORMANCE]",r),this.sendMetrics(r)}static async logToExternalService(e){}static async sendCriticalAlert(e){}static sendMetrics(e){"true"===process.env.ENABLE_METRICS_LOGGING&&console.log("\uD83D\uDCC8 [METRICS_EXPORT]",e)}static async getWebhookStats(e="day"){return{totalEvents:0,successRate:0,averageProcessingTime:0,errorsByType:{}}}}class nU{static async checkUserLimits(e){try{let{SupabaseAdminService:t}=await s.e(1155).then(s.bind(s,91155)),a=await t.getUserProfile(e);if(!a)return[];let r=[],i=await this.checkTokenLimits(a);i&&r.push(i);let n=await this.checkPlanLimits(a);return n&&r.push(n),r}catch(e){return console.error("Error checking user limits:",e),[]}}static async checkClientUserLimits(){try{let e=(0,$.U)(),{data:{user:t},error:s}=await e.auth.getUser();if(s||!t)return[];let{data:a,error:r}=await e.from("user_profiles").select("subscription_plan, payment_verified, monthly_token_limit, current_month_tokens, current_month, plan_expires_at").eq("user_id",t.id).single();if(r&&"PGRST116"!==r.code)return console.error("Error fetching profile for limits check:",r),[];if(!a)return[];let i=[],n=await this.checkTokenLimits(a);n&&i.push(n);let o=await this.checkPlanLimits(a);return o&&i.push(o),i}catch(e){return console.error("Error checking client user limits:",e),[]}}static async checkTokenLimits(e){let t=new Date().toISOString().slice(0,7)+"-01",s=e.current_month===t?e.current_month_tokens:0,a=s/e.monthly_token_limit*100,r="warning",i="",n=!1,o="";if(a>=100)r="exceeded",i=`Has excedido tu l\xedmite mensual de tokens (${s.toLocaleString()}/${e.monthly_token_limit.toLocaleString()})`,n=!0,o="Actualiza tu plan para obtener m\xe1s tokens";else if(a>=90)r="limit_reached",i=`Est\xe1s cerca de tu l\xedmite mensual de tokens (${Math.round(a)}% usado)`,n=!0,o="Considera actualizar tu plan antes de alcanzar el l\xedmite";else{if(!(a>=75))return null;r="warning",i=`Has usado ${Math.round(a)}% de tus tokens mensuales`,n=!1,o="Monitorea tu uso para evitar alcanzar el l\xedmite"}let l=this.getUpgradeOptions(e.subscription_plan);return{type:"tokens",severity:r,current:s,limit:e.monthly_token_limit,percentage:Math.round(a),message:i,actionRequired:n,suggestedAction:o,upgradeOptions:l}}static async checkPlanLimits(e){if("free"!==e.subscription_plan&&!e.payment_verified)return{type:"plan",severity:"exceeded",current:0,limit:1,percentage:0,message:"Tu pago est\xe1 pendiente de verificaci\xf3n",actionRequired:!0,suggestedAction:"Completa el proceso de pago para activar tu plan",upgradeOptions:[]};if(e.plan_expires_at){let t=new Date(e.plan_expires_at),s=new Date,a=Math.ceil((t.getTime()-s.getTime())/864e5);if(a<=0)return{type:"plan",severity:"exceeded",current:0,limit:1,percentage:0,message:"Tu plan ha expirado",actionRequired:!0,suggestedAction:"Renueva tu suscripci\xf3n para continuar usando las funciones premium",upgradeOptions:this.getUpgradeOptions(e.subscription_plan)};if(a<=7)return{type:"plan",severity:"warning",current:a,limit:30,percentage:Math.round((30-a)/30*100),message:`Tu plan expira en ${a} d\xeda${1!==a?"s":""}`,actionRequired:!1,suggestedAction:"Renueva tu suscripci\xf3n para evitar la interrupci\xf3n del servicio",upgradeOptions:this.getUpgradeOptions(e.subscription_plan)}}return null}static getUpgradeOptions(e){let t=[];if("free"===e){let e=(0,b.IE)("usuario"),s=(0,b.IE)("pro");if(e){let s=e.limits.monthlyTokens||1e6;t.push({plan:"usuario",benefits:["Chat con preparador IA",`${s.toLocaleString()} tokens mensuales`,"Tests y flashcards ilimitados"],newLimit:s})}if(s){let e=s.limits.monthlyTokens||1e6;t.push({plan:"pro",benefits:["Todas las funciones del plan Usuario","Planificaci\xf3n de estudios con IA","Res\xfamenes A1 y A2",`${e.toLocaleString()} tokens mensuales`],newLimit:e})}}else if("usuario"===e){let e=(0,b.IE)("pro");if(e){let s=e.limits.monthlyTokens||1e6;t.push({plan:"pro",benefits:["Planificaci\xf3n de estudios con IA","Res\xfamenes A1 y A2","Funciones avanzadas",`${s.toLocaleString()} tokens mensuales`],newLimit:s})}}return t}static async createLimitNotification(e,t){let s={userId:e,type:`limit_${t.type}`,severity:"exceeded"===t.severity?"error":"limit_reached"===t.severity?"warning":"info",title:this.getNotificationTitle(t),message:t.message,metadata:{limitType:t.type,current:t.current,limit:t.limit,percentage:t.percentage}};return t.actionRequired&&t.upgradeOptions&&t.upgradeOptions.length>0&&(s.actionUrl="/payment",s.actionText="Actualizar Plan"),await nB.logFeatureAccess(e,`limit_notification_${t.type}`,!1,"system",0,`Limit notification: ${t.severity}`),s}static getNotificationTitle(e){switch(e.type){case"tokens":if("exceeded"===e.severity)return"L\xedmite de tokens excedido";if("limit_reached"===e.severity)return"L\xedmite de tokens casi alcanzado";return"Uso elevado de tokens";case"plan":if("exceeded"===e.severity)return"Plan expirado o pago pendiente";return"Plan pr\xf3ximo a expirar";default:return"L\xedmite alcanzado"}}static async isActionBlocked(e,t,s=0){try{let t=await this.checkUserLimits(e);if(s>0){let e=t.find(e=>"tokens"===e.type);if(e&&"exceeded"===e.severity)return{blocked:!0,reason:"L\xedmite mensual de tokens excedido",limitStatus:e};if(e&&e.current+s>e.limit)return{blocked:!0,reason:`Esta acci\xf3n requiere ${s} tokens pero solo tienes ${e.limit-e.current} disponibles`,limitStatus:e}}let a=t.find(e=>"plan"===e.type&&"exceeded"===e.severity);if(a)return{blocked:!0,reason:a.message,limitStatus:a};return{blocked:!1}}catch(e){return console.error("Error checking if action is blocked:",e),{blocked:!0,reason:"Error verificando l\xedmites"}}}static async isClientActionBlocked(e,t=0){try{let e=await this.checkClientUserLimits();if(t>0){let s=e.find(e=>"tokens"===e.type);if(s&&"exceeded"===s.severity)return{blocked:!0,reason:"L\xedmite mensual de tokens excedido",limitStatus:s};if(s&&s.current+t>s.limit)return{blocked:!0,reason:`Esta acci\xf3n requiere ${t} tokens pero solo tienes ${s.limit-s.current} disponibles`,limitStatus:s}}let s=e.find(e=>"plan"===e.type&&"exceeded"===e.severity);if(s)return{blocked:!0,reason:s.message,limitStatus:s};return{blocked:!1}}catch(e){return console.error("Error checking if client action is blocked:",e),{blocked:!0,reason:"Error verificando l\xedmites"}}}static async recordUsage(e,t,a){try{if(a>0){let{SupabaseAdminService:t}=await s.e(1155).then(s.bind(s,91155)),r=await t.getUserProfile(e);if(r){let s=new Date().toISOString().slice(0,7)+"-01",i=r.current_month===s?r.current_month_tokens:0;await t.upsertUserProfile({...r,current_month_tokens:i+a,current_month:s,updated_at:new Date().toISOString()}),console.log(`✅ Tokens actualizados: +${a} para usuario ${e}`)}}await nB.logFeatureAccess(e,t,!0,"system",a,"Action completed successfully")}catch(e){console.error("Error recording usage:",e)}}static async recordClientUsage(e,t){try{let s=(0,$.U)(),{data:{user:a},error:r}=await s.auth.getUser();if(r||!a)return void console.warn("Cannot record usage: user not authenticated");if(t>0){let{data:e,error:r}=await s.from("user_profiles").select("subscription_plan, monthly_token_limit, current_month_tokens, current_month").eq("user_id",a.id).single();if(r)return void console.error("Error fetching profile for usage recording:",r);if(e){let r=new Date().toISOString().slice(0,7)+"-01",i=e.current_month===r?e.current_month_tokens:0,{error:n}=await s.from("user_profiles").update({current_month_tokens:i+t,current_month:r,updated_at:new Date().toISOString()}).eq("user_id",a.id);n?console.error("Error updating token usage:",n):console.log(`✅ Tokens actualizados: +${t} para usuario ${a.id}`)}}await nB.logFeatureAccess(a.id,e,!0,"system",t,"Action completed successfully")}catch(e){console.error("Error recording client usage:",e)}}}function nG({used:e,limit:t,percentage:s,remaining:a}){let r=s||0,i=e=>{let t=e||0;return t>=1e6?`${(t/1e6).toFixed(1)}M`:t>=1e3?`${(t/1e3).toFixed(1)}K`:t.toLocaleString()};return(0,n.jsxs)("div",{className:"space-y-3",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsx)("h3",{className:"text-sm font-medium text-gray-700",children:"Uso de Tokens"}),(0,n.jsxs)("span",{className:`text-sm font-semibold ${r<50?"text-green-600":r<80?"text-yellow-600":"text-red-600"}`,children:[r,"%"]})]}),(0,n.jsx)("div",{className:`w-full ${r<50?"bg-green-100":r<80?"bg-yellow-100":"bg-red-100"} rounded-full h-3`,children:(0,n.jsx)("div",{className:`h-3 rounded-full transition-all duration-300 ${r<50?"bg-green-500":r<80?"bg-yellow-500":"bg-red-500"}`,style:{width:`${Math.min(r,100)}%`}})}),(0,n.jsxs)("div",{className:"flex items-center justify-between text-xs text-gray-600",children:[(0,n.jsxs)("span",{children:[(0,n.jsx)("strong",{children:i(e||0)})," usados"]}),(0,n.jsxs)("span",{children:[(0,n.jsx)("strong",{children:i(a||0)})," restantes"]})]}),(0,n.jsx)("div",{className:"text-center",children:(0,n.jsxs)("span",{className:"text-xs text-gray-500",children:["L\xedmite mensual: ",(0,n.jsx)("strong",{children:i(t||0)})," tokens"]})}),r>=80&&(0,n.jsx)("div",{className:`p-2 rounded-lg text-xs ${r>=95?"bg-red-50 text-red-700 border border-red-200":"bg-yellow-50 text-yellow-700 border border-yellow-200"}`,children:r>=95?(0,n.jsx)("span",{children:"⚠️ L\xedmite casi alcanzado. Considera comprar m\xe1s tokens."}):(0,n.jsx)("span",{children:"⚠️ Te est\xe1s acercando al l\xedmite mensual de tokens."})})]})}function nq({isOpen:e,onClose:t,shouldRefreshOnOpen:s=!1}){let a=function(){let[e,t]=(0,o.useState)({loading:!0,userPlan:null,tokenUsage:null,limits:[],paymentVerified:!1,error:null}),s=(0,o.useCallback)(async()=>{try{t(e=>({...e,loading:!0,error:null}));let e=(0,$.U)(),{data:{user:s},error:a}=await e.auth.getUser();if(a||!s)return void t(e=>({...e,loading:!1,error:"Usuario no autenticado"}));let{data:r,error:i}=await e.from("user_profiles").select("*").eq("user_id",s.id).single();if(i||!r)return void t(e=>({...e,loading:!1,error:"Perfil no encontrado"}));let n=new Date().toISOString().slice(0,7)+"-01",o=r.current_month===n&&r.current_month_tokens||0,l=r.monthly_token_limit||0,d=await nU.checkClientUserLimits();t({loading:!1,userPlan:r.subscription_plan,tokenUsage:{current:o,limit:l,percentage:l>0?Math.round(o/l*100):0,remaining:Math.max(0,l-o)},limits:d,paymentVerified:r.payment_verified||"free"===r.subscription_plan,error:null})}catch(e){console.error("Error loading plan limits:",e),t(e=>({...e,loading:!1,error:"Error cargando l\xedmites"}))}},[]),a=(0,o.useCallback)(()=>{s()},[s]);return{...e,refresh:a}}();return e?(0,n.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,n.jsxs)("div",{className:"bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,n.jsx)(d.hht,{className:"w-6 h-6 text-blue-600"}),(0,n.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Estad\xedsticas de Uso de IA"})]}),(0,n.jsx)("button",{onClick:t,className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:(0,n.jsx)(d.yGN,{className:"w-5 h-5 text-gray-500"})})]}),(0,n.jsx)("div",{className:"p-6 space-y-6",children:a.loading?(0,n.jsxs)("div",{className:"flex justify-center items-center py-8",children:[(0,n.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),(0,n.jsx)("span",{className:"ml-2 text-gray-600",children:"Verificando plan..."})]}):"free"===a.userPlan?(0,n.jsxs)("div",{className:"text-center py-12",children:[(0,n.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Estad\xedsticas de Tokens no disponibles"}),(0,n.jsx)("p",{className:"text-gray-600 mb-6",children:"Las estad\xedsticas detalladas est\xe1n disponibles para planes de pago."}),(0,n.jsx)("div",{children:(0,n.jsx)(N(),{href:"/",className:"inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors",children:"Ver Planes Disponibles"})})]}):a.tokenUsage&&a.tokenUsage.limit>0?(0,n.jsx)("div",{className:"mb-6",children:(0,n.jsx)(nG,{used:a.tokenUsage.current||0,limit:a.tokenUsage.limit||0,percentage:a.tokenUsage.percentage||0,remaining:a.tokenUsage.remaining||0})}):(0,n.jsx)("div",{className:"flex justify-center items-center py-8",children:(0,n.jsx)("p",{className:"text-gray-600",children:"No hay datos de uso disponibles."})})}),(0,n.jsx)("div",{className:"flex justify-end p-6 border-t border-gray-200",children:(0,n.jsx)("button",{onClick:t,className:"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors",children:"Cerrar"})})]})}):null}function nH(){let[e,t]=(0,o.useState)([]),[s,a]=(0,o.useState)(!1),[r,i]=(0,o.useState)("dashboard"),[u,h]=(0,o.useState)(!1),[x,p]=(0,o.useState)(!1),[g,b]=(0,o.useState)(null),[j,N]=(0,o.useState)(null),[w,C]=(0,o.useState)(!1),[k,T]=(0,o.useState)(!1),[A,P]=(0,o.useState)(0),[M,$]=(0,o.useState)(!1),[L,F]=(0,o.useState)(!1),[V,_]=(0,o.useState)(!1),[z,I]=(0,o.useState)(!1),{cerrarSesion:O,user:q,isLoading:H}=(0,f.A)(),W=(0,l.useRouter)(),Y=(0,o.useRef)(null),{generatePlanEstudios:X,isGenerating:K}=S(),{latestResult:Z,isLoading:J}=nI({onResult:e=>{b(e),m.oR.success("\xa1Plan de estudios generado exitosamente!")},onError:e=>{m.oR.error(`Error al generar plan: ${e}`)}});if(H||!q)return(0,n.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"}),(0,n.jsx)("p",{className:"mt-4 text-gray-600",children:"Cargando..."})]})});let Q=async()=>{h(!0),p(!0);try{await Y.current?.recargarDocumentos()}catch(e){console.error("Error al recargar documentos:",e)}finally{p(!1)}setTimeout(()=>h(!1),5e3)},ee=async()=>{try{await Y.current?.recargarDocumentos()}catch(e){console.error("Error al recargar documentos despu\xe9s de eliminar:",e)}},et=async()=>{await O()},es=async()=>{if(!j)return void m.oR.error("No se encontr\xf3 un temario configurado");if(!w)return void m.oR.error('Necesitas completar la configuraci\xf3n de planificaci\xf3n en "Mi Temario"');try{await X({temarioId:j,onComplete:e=>{b(e)},onError:e=>{e.includes("planificaci\xf3n configurada")?m.oR.error('Necesitas completar la configuraci\xf3n de planificaci\xf3n en "Mi Temario"'):m.oR.error("Error al generar el plan de estudios. Int\xe9ntalo de nuevo.")}})}catch(e){console.error("Error al iniciar generaci\xf3n del plan:",e)}},ea=e=>{let t=`# Plan de Estudios Personalizado

`;return t+=`${e.introduccion}

## Resumen del Plan

- **Tiempo total de estudio:** ${e.resumen.tiempoTotalEstudio}
- **N\xfamero de temas:** ${e.resumen.numeroTemas}
- **Duraci\xf3n estudio nuevo:** ${e.resumen.duracionEstudioNuevo}
- **Duraci\xf3n repaso final:** ${e.resumen.duracionRepasoFinal}

## Cronograma Semanal

`,e.semanas.forEach(e=>{t+=`### Semana ${e.numero} (${e.fechaInicio} - ${e.fechaFin})

**Objetivo:** ${e.objetivoPrincipal}

`,e.dias.forEach(e=>{t+=`**${e.dia} (${e.horas}h):**
`,e.tareas.forEach(e=>{t+=`- ${e.titulo} (${e.duracionEstimada})
`,e.descripcion&&(t+=`  ${e.descripcion}
`)}),t+="\n"})}),t+=`## Estrategia de Repasos

${e.estrategiaRepasos}

## Pr\xf3ximos Pasos

${e.proximosPasos}
`};return(0,n.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,n.jsx)("header",{className:"bg-white shadow-sm",children:(0,n.jsxs)("div",{className:"max-w-12xl mx-auto px-4 sm:px-6 lg:px-8 py-2",children:[" ",(0,n.jsxs)("div",{className:"flex justify-between items-center",children:[(0,n.jsx)("div",{className:"flex items-center -ml-4",children:(0,n.jsx)("div",{className:"flex flex-col items-center",children:(0,n.jsx)("img",{src:"/logo2.png",alt:"OposiAI Logo",className:"h-20 w-20 object-contain"})})}),(0,n.jsxs)("div",{className:"flex items-center space-x-4",children:[q&&(0,n.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,n.jsxs)("div",{className:"text-sm text-gray-600",children:["Hola, ",q.email?.split("@")[0]]}),(0,n.jsx)("button",{onClick:()=>W.push("/profile"),className:"inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",title:"Ver perfil",children:(0,n.jsx)(d.JXP,{className:"w-4 h-4"})})]}),(0,n.jsxs)("button",{onClick:()=>T(!0),className:"inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50",title:"Ver estad\xedsticas de uso de IA",children:[(0,n.jsx)(d.hht,{className:"w-4 h-4 mr-2"}),"Estad\xedsticas"]}),(0,n.jsxs)("button",{onClick:()=>a(!s),className:"inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700",children:[(0,n.jsx)(d.GGD,{className:"w-4 h-4 mr-2"}),"Nuevo documento"]}),(0,n.jsxs)("button",{onClick:et,className:"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:[(0,n.jsx)(d.QeK,{className:"mr-2"}),"Cerrar sesi\xf3n"]})]})]})]})}),(0,n.jsxs)("main",{className:"px-4 sm:px-6 lg:px-8 py-8",children:[s&&(0,n.jsx)("div",{className:"mb-8 transition-all duration-300 ease-in-out",children:(0,n.jsx)(E,{onSuccess:Q})}),u&&(0,n.jsx)("div",{className:"mb-6 p-4 bg-green-50 text-green-800 rounded-lg border border-green-200",children:(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)(d.YrT,{className:"text-green-500 mr-2 flex-shrink-0"}),(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"font-medium",children:"\xa1Documento subido exitosamente!"}),(0,n.jsx)("p",{className:"text-sm text-green-700 mt-1",children:x?(0,n.jsxs)("span",{className:"flex items-center",children:[(0,n.jsx)(d.jTZ,{className:"animate-spin mr-1"}),"Actualizando lista de documentos..."]}):"El documento ya est\xe1 disponible en los desplegables de selecci\xf3n."})]})]})}),(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-3 mb-4",children:[(0,n.jsxs)("div",{className:"flex items-center mb-2",children:[(0,n.jsx)(d.jH2,{className:"w-4 h-4 text-blue-600 mr-2"}),(0,n.jsx)("h2",{className:"text-base font-semibold text-gray-900",children:"Documentos Seleccionados"})]}),(0,n.jsx)("p",{className:"text-xs text-gray-600 mb-2",children:"Selecciona los documentos que quieres usar para generar contenido con IA."}),(0,n.jsx)(c.A,{ref:Y,onSelectionChange:t}),e.length>0&&(0,n.jsxs)("div",{className:"mt-2 p-2 bg-blue-50 rounded-lg",children:[(0,n.jsxs)("p",{className:"text-xs text-blue-800 font-medium",children:[(0,n.jsx)("strong",{children:e.length})," documento",1!==e.length?"s":""," seleccionado",1!==e.length?"s":"","."]}),(0,n.jsx)("div",{className:"mt-1 flex flex-wrap gap-1",children:e.map(e=>(0,n.jsxs)("span",{className:"inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800",children:[e.numero_tema&&`Tema ${e.numero_tema}: `,e.titulo]},e.id))})]})]}),(0,n.jsxs)("div",{className:"flex gap-6 mb-8",children:[(0,n.jsx)(nz,{activeTab:r,onTabChange:i}),(0,n.jsx)("div",{className:"flex-1",children:"dashboard"===r?(0,n.jsx)(nk,{onNavigateToTab:e=>{i(e)}}):(0,n.jsx)("div",{className:"bg-white rounded-xl shadow-sm overflow-hidden",children:(0,n.jsxs)("div",{className:"p-6",children:["temario"===r&&(0,n.jsx)(nL,{}),"planEstudios"===r&&(0,n.jsx)("div",{children:M?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,n.jsx)("h2",{className:"text-2xl font-semibold text-gray-900",children:"Mi Plan de Estudios"}),(0,n.jsx)("div",{className:"flex gap-2",children:g&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("button",{onClick:es,disabled:J||K("plan-estudios"),className:"flex items-center gap-2 px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:[(0,n.jsx)(d.jTZ,{className:`w-4 h-4 ${J||K("plan-estudios")?"animate-spin":""}`}),"Regenerar Plan"]}),(0,n.jsxs)("button",{onClick:()=>{if(!g)return;let e=new Blob([ea(g)],{type:"text/markdown"}),t=URL.createObjectURL(e),s=document.createElement("a");s.href=t,s.download=`plan-estudios-${new Date().toISOString().split("T")[0]}.md`,document.body.appendChild(s),s.click(),document.body.removeChild(s),URL.revokeObjectURL(t),m.oR.success("Plan descargado exitosamente")},className:"flex items-center gap-2 px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,n.jsx)(d.a4x,{className:"w-4 h-4"}),"Descargar"]}),(0,n.jsxs)("button",{onClick:()=>{if(!g)return;let e=ea(g),t=window.open("","_blank");t&&(t.document.write(`
        <html>
          <head>
            <title>Plan de Estudios Personalizado</title>
            <style>
              body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
              h1, h2, h3 { color: #333; }
              h1 { border-bottom: 2px solid #333; padding-bottom: 10px; }
              h2 { border-bottom: 1px solid #666; padding-bottom: 5px; }
              ul, ol { margin-left: 20px; }
              strong { color: #2563eb; }
              @media print { body { margin: 0; } }
            </style>
          </head>
          <body>
            <div id="content"></div>
            <script>
              // Convertir markdown a HTML b\xe1sico para impresi\xf3n
              const markdown = ${JSON.stringify(e)};
              const content = markdown
                .replace(/^# (.*$)/gim, '<h1>$1</h1>')
                .replace(/^## (.*$)/gim, '<h2>$1</h2>')
                .replace(/^### (.*$)/gim, '<h3>$1</h3>')
                .replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>')
                .replace(/\\*(.*?)\\*/g, '<em>$1</em>')
                .replace(/^- (.*$)/gim, '<li>$1</li>')
                .replace(/(<li>.*<\\/li>)/s, '<ul>$1</ul>')
                .replace(/\\n/g, '<br>');
              document.getElementById('content').innerHTML = content;
              window.print();
            </script>
          </body>
        </html>
      `),t.document.close())},className:"flex items-center gap-2 px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,n.jsx)(d.Mvz,{className:"w-4 h-4"}),"Imprimir"]})]})})]}),J||K("plan-estudios")?(0,n.jsxs)("div",{className:"text-center py-12",children:[(0,n.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600 mx-auto mb-4"}),(0,n.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Generando tu plan personalizado"}),(0,n.jsx)("p",{className:"text-gray-600",children:"La IA est\xe1 analizando tu temario y configuraci\xf3n..."})]}):g&&j?(0,n.jsx)(nO.A,{plan:g,temarioId:j}):(0,n.jsxs)("div",{className:"text-center py-12",children:[(0,n.jsx)("div",{className:"w-20 h-20 bg-teal-100 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,n.jsx)(d.wIk,{className:"w-10 h-10 text-teal-600"})}),(0,n.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Genera tu Plan de Estudios Personalizado"}),(0,n.jsx)("p",{className:"text-gray-600 mb-8 max-w-2xl mx-auto",children:"Crea un plan de estudios personalizado basado en tu temario y configuraci\xf3n de planificaci\xf3n"}),(0,n.jsxs)("button",{onClick:es,disabled:!w,className:"inline-flex items-center px-6 py-3 bg-teal-600 text-white rounded-lg hover:bg-teal-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:[(0,n.jsx)(d.wIk,{className:"w-5 h-5 mr-3"}),"Generar Plan de Estudios"]}),!w&&(0,n.jsx)("p",{className:"text-sm text-red-600 mt-4",children:'Necesitas completar la configuraci\xf3n de planificaci\xf3n en "Mi Temario"'})]})]}):(0,n.jsx)(y.A,{feature:"study_planning",benefits:["Planes de estudio personalizados con IA","Cronogramas adaptativos a tu ritmo","Seguimiento autom\xe1tico de progreso","Recomendaciones inteligentes de repaso"],className:"min-h-[600px]"})}),"preguntas"===r&&(L?(0,n.jsx)(v,{documentosSeleccionados:e}):(0,n.jsx)(y.A,{feature:"ai_tutor_chat",benefits:["Chat ilimitado con IA especializada","Respuestas personalizadas a tus documentos","Historial completo de conversaciones","Explicaciones detalladas y ejemplos"],className:"min-h-[600px]"})),"mapas"===r&&(0,n.jsx)(R,{documentosSeleccionados:e}),"flashcards"===r&&(0,n.jsx)(D,{documentosSeleccionados:e}),"tests"===r&&(0,n.jsx)(nm,{documentosSeleccionados:e}),"misTests"===r&&(0,n.jsx)(ny,{}),"misFlashcards"===r&&(0,n.jsx)(nu,{}),"resumenes"===r&&(V?(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsx)(B,{documentosSeleccionados:e,onSummaryGenerated:e=>{P(e=>e+1),m.oR.success("Resumen generado exitosamente")}}),(0,n.jsx)("hr",{className:"border-gray-200"}),(0,n.jsx)(U,{refreshTrigger:A})]}):(0,n.jsx)(y.A,{feature:"summary_a1_a2",benefits:["Res\xfamenes inteligentes con IA","Formato A1 y A2 optimizado","Edici\xf3n autom\xe1tica de contenido","Exportaci\xf3n a PDF de alta calidad"],className:"min-h-[600px]"})),"gestionar"===r&&(0,n.jsx)(G,{onDocumentDeleted:ee})]})})})]})]}),(0,n.jsx)("footer",{className:"bg-white border-t border-gray-200 mt-12",children:(0,n.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:(0,n.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center",children:[(0,n.jsx)("div",{className:"flex items-center",children:(0,n.jsxs)("span",{className:"text-gray-500 text-sm",children:["\xa9 ",new Date().getFullYear()," OposiAI - Asistente para Oposiciones"]})}),(0,n.jsx)("div",{className:"mt-4 md:mt-0",children:(0,n.jsxs)("nav",{className:"flex space-x-6",children:[(0,n.jsx)("a",{href:"#",className:"text-gray-500 hover:text-gray-700 text-sm",children:"T\xe9rminos"}),(0,n.jsx)("a",{href:"#",className:"text-gray-500 hover:text-gray-700 text-sm",children:"Privacidad"}),(0,n.jsx)("a",{href:"#",className:"text-gray-500 hover:text-gray-700 text-sm",children:"Contacto"})]})})]})})}),(0,n.jsx)(nF,{}),(0,n.jsx)(n_,{}),(0,n.jsx)(nq,{isOpen:k,onClose:()=>{T(!1),I(!1)},shouldRefreshOnOpen:z})]})}},41835:(e,t,s)=>{"use strict";s.d(t,{E9:()=>d,GS:()=>n,MZ:()=>l,oO:()=>o,oS:()=>r});var a=s(9275);let r=a.z.object({pregunta:a.z.string().min(1,"La pregunta es obligatoria").max(500,"M\xe1ximo 500 caracteres"),documentos:a.z.array(a.z.object({id:a.z.string().optional(),titulo:a.z.string().min(1),contenido:a.z.string().min(1),categoria:a.z.string().optional().nullable(),numero_tema:a.z.union([a.z.number().int().positive(),a.z.string(),a.z.null(),a.z.undefined()]).optional(),creado_en:a.z.string().optional(),actualizado_en:a.z.string().optional(),user_id:a.z.string().optional(),tipo_original:a.z.string().optional()})).min(1,"Debes seleccionar al menos un documento")}),i=a.z.object({peticion:a.z.string().min(1,"La petici\xf3n es obligatoria").max(500,"M\xe1ximo 500 caracteres")}),n=a.z.object({peticion:a.z.string().min(1,"La petici\xf3n es obligatoria").max(500,"M\xe1ximo 500 caracteres"),cantidad:a.z.number().min(1,"M\xednimo 1 pregunta").max(50,"M\xe1ximo 50 preguntas").default(10)}),o=a.z.object({peticion:a.z.string().min(1,"La petici\xf3n es obligatoria").max(500,"M\xe1ximo 500 caracteres"),cantidad:a.z.number().min(1,"M\xednimo 1 flashcard").max(30,"M\xe1ximo 30 flashcards").default(10)}),l=i,d=a.z.object({email:a.z.string().min(1,"El email es obligatorio").email("Por favor, ingresa un email v\xe1lido").max(255,"El email es demasiado largo")});a.z.object({password:a.z.string().min(8,"La contrase\xf1a debe tener al menos 8 caracteres").max(128,"La contrase\xf1a es demasiado larga"),confirmPassword:a.z.string()}).refine(e=>e.password===e.confirmPassword,{message:"Las contrase\xf1as no coinciden",path:["confirmPassword"]})},48302:(e,t,s)=>{Promise.resolve().then(s.bind(s,25380))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},88054:(e,t,s)=>{Promise.resolve().then(s.bind(s,37922))},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[4447,6126,7019,5814,8348,2212,1191,9587],()=>s(18307));module.exports=a})();