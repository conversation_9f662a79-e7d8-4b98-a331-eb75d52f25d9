"use strict";(()=>{var e={};e.id=3700,e.ids=[3700],e.modules={2507:(e,t,a)=>{a.d(t,{createServerSupabaseClient:()=>s});var r=a(34386),n=a(44999);async function s(){let e=await (0,n.UL)();return(0,r.createServerClient)("https://fxnhpxjijinfuxxxplzj.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4",{auth:{persistSession:!1,autoRefreshToken:!1,detectSessionInUrl:!1},cookies:{getAll:()=>e.getAll(),setAll(t){try{t.filter(e=>!e.name.includes("auth-token")&&!e.name.includes("refresh-token")).forEach(({name:t,value:a,options:r})=>e.set(t,a,{...r,maxAge:void 0,expires:void 0}))}catch{}}}})}},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10959:(e,t,a)=>{let r;a.r(t),a.d(t,{patchFetch:()=>tu,routeModule:()=>to,serverHooks:()=>tc,workAsyncStorage:()=>tl,workUnitAsyncStorage:()=>td});var n,s,i,o,l,d,c={};a.r(c),a.d(c,{POST:()=>ti});var u=a(96559),p=a(48088),h=a(37719),m=a(32190),f=a(2507),g=a(90787),y=a(85905),x=a(36191),_=a(36446);async function v(e,t){try{if(!e||"string"!=typeof e||""===e.trim())return console.warn("Se recibi\xf3 una pregunta vac\xeda o inv\xe1lida"),"Por favor, proporciona una pregunta v\xe1lida.";if(!t||!Array.isArray(t)||0===t.length)return console.warn("No se proporcionaron documentos v\xe1lidos para obtenerRespuestaIA"),"No se han proporcionado documentos para responder a esta pregunta.";let a=(0,g.Jo)(t);if(!a)return console.warn("No se pudo preparar el contenido de los documentos"),"No se han podido procesar los documentos proporcionados. Por favor, verifica que los documentos contengan informaci\xf3n v\xe1lida.";let r=y.zM.replace("{documentos}",a).replace("{pregunta}",e),n=(0,_.Vj)("CONVERSACIONES");return console.log(`💬 Generando respuesta con modelo: ${n.model} (max_tokens: ${n.max_tokens})`),await (0,x.y5)([{role:"user",content:r}],{...n,activityName:"Conversaci\xf3n/Q&A"})}catch(e){if(console.error("Error al obtener respuesta de la IA:",e),e instanceof Error)return`Lo siento, ha ocurrido un error al procesar tu pregunta: ${e.message}. Por favor, int\xe9ntalo de nuevo m\xe1s tarde.`;return"Lo siento, ha ocurrido un error al procesar tu pregunta. Por favor, int\xe9ntalo de nuevo m\xe1s tarde."}}async function b(e,t,r=10){let n=t.map((e,t)=>({titulo:`Documento ${t+1}`,contenido:e}));return await Promise.resolve().then(a.bind(a,98234)).then(t=>t.generarFlashcards(n,r,e))}async function w(e,t){let r=t.map((e,t)=>({titulo:`Documento ${t+1}`,contenido:e}));return await Promise.resolve().then(a.bind(a,78072)).then(t=>t.generarMapaMental(r,e))}async function E(e,t,r=10){let n=t.map((e,t)=>({titulo:`Documento ${t+1}`,contenido:e}));return(await Promise.resolve().then(a.bind(a,17118)).then(t=>t.generarTest(n,r,e))).map(e=>({pregunta:e.pregunta,opciones:{a:e.opcion_a,b:e.opcion_b,c:e.opcion_c,d:e.opcion_d},respuesta_correcta:e.respuesta_correcta}))}async function A(e,t){return await Promise.resolve().then(a.bind(a,33503)).then(a=>a.generarResumen(e,t))}async function S(e,t){try{let{createServerSupabaseClient:r}=await Promise.resolve().then(a.bind(a,2507)),n=await r();console.log("\uD83D\uDD0D Consultando planificaci\xf3n con:",{userId:t,temarioId:e});let{data:s,error:i}=await n.from("planificacion_usuario").select("*").eq("user_id",t).eq("temario_id",e).single();if(console.log("\uD83D\uDD0D Resultado consulta planificaci\xf3n:",{data:s?"ENCONTRADA":"NO ENCONTRADA",error:i?.code,errorMessage:i?.message}),i){if("PGRST116"===i.code)return console.log("❌ No hay planificaci\xf3n configurada (PGRST116)"),null;return console.error("❌ Error al obtener planificaci\xf3n:",i),null}return console.log("✅ Planificaci\xf3n encontrada:",s.id),s}catch(e){return console.error("❌ Error al obtener planificaci\xf3n:",e),null}}async function I(e){try{let{createServerSupabaseClient:t}=await Promise.resolve().then(a.bind(a,2507)),r=await t(),{data:n,error:s}=await r.from("temas").select("*").eq("temario_id",e).order("orden",{ascending:!0});if(s)return console.error("Error al obtener temas:",s),[];return n||[]}catch(e){return console.error("Error al obtener temas:",e),[]}}async function N(e,t){try{let r=t;if(!r){let{createServerSupabaseClient:e}=await Promise.resolve().then(a.bind(a,2507)),t=await e(),{data:{user:n},error:s}=await t.auth.getUser();if(!n||s)throw Error("No hay usuario autenticado para generar el plan de estudios");r=n}let{createServerSupabaseClient:n}=await Promise.resolve().then(a.bind(a,2507)),s=await n(),{data:i,error:o}=await s.from("temarios").select("id, titulo").eq("id",e).eq("user_id",r.id).single();if(o||!i)throw Error(`El temario no existe o ha sido eliminado: ${e}`);let l=await S(e,r.id);if(!l)throw Error(`No se encontr\xf3 planificaci\xf3n configurada para el temario: ${i.titulo}`);let d=await I(e),c=d.map(e=>({id:e.id,numero:e.numero,titulo:e.titulo,descripcion:e.descripcion,horasEstimadas:0,esDificil:!1,esMuyImportante:!1,yaDominado:!1,notas:""})),u=function(e,t){let a="",r=new Date,n=r.toISOString().split("T")[0],s=26,i="";if(e.fecha_examen)s=Math.max(1,Math.ceil(Math.ceil((new Date(e.fecha_examen).getTime()-r.getTime())/864e5)/7)),i=e.fecha_examen;else if(e.fecha_examen_aproximada){let t=function(e,t){let a=new Date(t);switch(e){case"1-3_meses":a.setMonth(a.getMonth()+2);break;case"3-6_meses":a.setMonth(a.getMonth()+4);break;case"6-12_meses":a.setMonth(a.getMonth()+9);break;case"mas_12_meses":a.setMonth(a.getMonth()+18);break;case"primavera_2025":a.setFullYear(2025,3,15);break;case"verano_2025":a.setFullYear(2025,6,15);break;case"otono_2025":a.setFullYear(2025,9,15);break;case"invierno_2025":a.setFullYear(2025,11,15);break;case"primavera_2026":a.setFullYear(2026,3,15);break;case"verano_2026":a.setFullYear(2026,6,15);break;default:let r=new Date(e);if(!isNaN(r.getTime()))return r;return console.warn(`Fecha aproximada no reconocida: ${e}`),null}return a}(e.fecha_examen_aproximada,r);t&&(s=Math.max(1,Math.ceil(Math.ceil((t.getTime()-r.getTime())/864e5)/7)),i=t.toISOString().split("T")[0])}return(a+=`**FECHA ACTUAL:** ${n}
**DURACI\xd3N DEL PLAN:** ${s} semanas (desde ${n} hasta ${i||"fecha estimada"})
**CR\xcdTICO:** Debes generar EXACTAMENTE ${s} semanas en el array "semanas". No menos.
**IMPORTANTE:** Todas las fechas del plan deben ser calculadas a partir de la fecha actual (${n}).
- La Semana 1 debe comenzar el ${n}
- Cada semana siguiente debe calcularse sumando 7 d\xedas a la anterior
- La \xfaltima semana (Semana ${s}) debe terminar cerca de la fecha del examen
- Usa el formato YYYY-MM-DD para todas las fechas

`,e.tiempo_por_dia&&Object.keys(e.tiempo_por_dia).length>0)?(a+="**Disponibilidad de Tiempo Diario:**\n",["lunes","martes","miercoles","jueves","viernes","sabado","domingo"].forEach(t=>{let r=e.tiempo_por_dia[t];r&&(a+=`- ${t.charAt(0).toUpperCase()+t.slice(1)}: ${r}h
`)})):e.tiempo_diario_promedio&&(a+=`**Disponibilidad de Tiempo Diario:** Promedio ${e.tiempo_diario_promedio}h/d\xeda
`),e.fecha_examen?a+=`
**Fecha del Examen:** ${e.fecha_examen}
`:e.fecha_examen_aproximada&&(a+=`
**Fecha del Examen (aproximada):** ${e.fecha_examen_aproximada}
`),e.familiaridad_general&&(a+=`
**Familiaridad General con el Temario (1-5):** ${e.familiaridad_general}
`),e.preferencias_horario&&e.preferencias_horario.length>0&&(a+=`
**Preferencias de Horario:** ${e.preferencias_horario.join(", ")}
`),e.frecuencia_repasos&&(a+=`
**Frecuencia de Repasos Deseada:** ${e.frecuencia_repasos}
`),a+="\n**\xcdndice del Temario del Opositor:**\n**IMPORTANTE:** Como experto preparador, debes analizar autom\xe1ticamente cada tema y determinar:\n- Horas de estudio necesarias seg\xfan la complejidad y extensi\xf3n aparente\n- Nivel de dificultad basado en el t\xedtulo y descripci\xf3n\n- Importancia relativa dentro del conjunto del temario\n- Orden de estudio m\xe1s eficiente\n\n",t.forEach(e=>{a+=`- **Tema ${e.numero}: ${e.titulo}**
`,e.descripcion&&(a+=`  - Descripci\xf3n: ${e.descripcion}
`),a+="\n"}),a}(l,c),p=y.y1.replace("{informacionUsuario}",u),h=await (0,x.y5)([{role:"system",content:"Eres un experto preparador de oposiciones. Tu tarea es crear planes de estudio detallados y personalizados en formato JSON v\xe1lido."},{role:"user",content:p}],{...(0,_.Vj)("PLAN_ESTUDIOS"),activityName:"Generaci\xf3n de Plan de Estudios"});if(!h||0===h.trim().length)throw Error("La IA no gener\xf3 ning\xfan contenido para el plan de estudios");try{console.log("\uD83D\uDD0D Respuesta completa de IA (primeros 1000 caracteres):",h.substring(0,1e3));let t=h.trim();if(t.includes("```json")){let e=t.match(/```json\s*([\s\S]*?)\s*```/);e&&(t=e[1].trim())}else if(t.includes("```")){let e=t.match(/```\s*([\s\S]*?)\s*```/);e&&(t=e[1].trim())}let n=t.indexOf("{"),s=t.lastIndexOf("}");-1!==n&&-1!==s&&s>n&&(t=t.substring(n,s+1)),t=t.replace(/[\u0000-\u001F\u007F-\u009F]/g,"").replace(/\n\s*\n/g,"\n").trim(),console.log("\uD83D\uDD0D JSON limpio (primeros 500 caracteres):",t.substring(0,500)),console.log("\uD83D\uDD0D JSON limpio (\xfaltimos 500 caracteres):",t.substring(Math.max(0,t.length-500))),t=function(e){try{return JSON.parse(e),e}catch(l){console.log("\uD83D\uDD27 Intentando reparar JSON truncado...");let t=e,a=((t=(t=(t=(t=(t=(t=t.replace(/\/\/.*$/gm,"")).replace(/\/\*[\s\S]*?\*\//g,"")).replace(/\s*\/\/\s*\(.*?\)\s*/g,"")).replace(/\s*\/\/\s*\.\.\.\s*/g,"")).replace(/\n\s*\n/g,"\n")).trim()).match(/\{/g)||[]).length,r=(t.match(/\}/g)||[]).length,n=(t.match(/\[/g)||[]).length,s=(t.match(/\]/g)||[]).length;console.log(`🔧 Llaves: ${a} abiertas, ${r} cerradas`),console.log(`🔧 Corchetes: ${n} abiertos, ${s} cerrados`),t.endsWith('"')&&(t=t.slice(0,-1)),t=t.replace(/,\s*$/,"");let i=n-s;for(let e=0;e<i;e++)t+="]";let o=a-r;for(let e=0;e<o;e++)t+="}";console.log("\uD83D\uDD27 JSON reparado (\xfaltimos 200 caracteres):",t.substring(Math.max(0,t.length-200)));try{return JSON.parse(t),console.log("✅ JSON reparado exitosamente"),t}catch(e){throw console.log("❌ No se pudo reparar el JSON, usando fallback"),e}}}(t);let i=JSON.parse(t);console.log("✅ Plan parseado exitosamente. N\xfamero de semanas:",i.semanas?.length||0);let{guardarPlanEstudiosServidor:o}=await a.e(5613).then(a.bind(a,45613)),l=await o(e,i,r,`Plan de Estudios - ${new Date().toLocaleDateString()}`);return l?console.log("✅ Plan de estudios guardado con ID:",l):console.warn("⚠️ No se pudo guardar el plan en la base de datos"),i}catch(o){console.error("❌ Error al parsear JSON del plan:",o),console.error("\uD83D\uDCC4 Respuesta completa de IA:",h);let t="Plan de estudios generado por IA";if(h&&h.length>100){let e=h.split("\n").filter(e=>e.trim().length>0);e.length>0&&(t=e[0].substring(0,200)+"...")}let n={introduccion:`${t}

NOTA: Hubo un problema al procesar la respuesta completa de la IA. Este es un plan b\xe1sico de respaldo.`,resumen:{tiempoTotalEstudio:"Por determinar",numeroTemas:d.length,duracionEstudioNuevo:"Por determinar",duracionRepasoFinal:"Por determinar"},semanas:[{numero:1,fechaInicio:new Date().toISOString().split("T")[0],fechaFin:new Date(Date.now()+5184e5).toISOString().split("T")[0],objetivoPrincipal:"Comenzar el estudio del temario",dias:[{dia:"Lunes",horas:4,tareas:[{titulo:"Revisar plan detallado",descripcion:"Por favor, regenera el plan para obtener una versi\xf3n completa.",tipo:"estudio",duracionEstimada:"4h"}]}]}],estrategiaRepasos:"Regenera el plan para obtener la estrategia de repasos completa",proximosPasos:"Regenera el plan para obtener los pr\xf3ximos pasos detallados"},{guardarPlanEstudiosServidor:s}=await a.e(5613).then(a.bind(a,45613)),i=await s(e,n,r,`Plan de Estudios (Fallback) - ${new Date().toLocaleDateString()}`);return i&&console.log("✅ Plan fallback guardado con ID:",i),n}}catch(e){throw console.error("Error al generar plan de estudios:",e),e}}async function T(e){try{if(!e||0===e.trim().length)throw Error("No se ha proporcionado contenido v\xe1lido para editar.");if(e.trim().length<100)throw Error("El contenido del resumen es demasiado corto para ser editado.");console.log("\uD83D\uDCDD Iniciando edici\xf3n de resumen..."),console.log(`📄 Longitud del contenido original: ${e.length} caracteres`);let t=y.HV.replace("{texto_largo_del_paso_1}",e),a=(0,_.Vj)("RESUMENES");console.log(`📄 Editando resumen con modelo: ${a.model} (max_tokens: ${a.max_tokens})`);let r=await (0,x.y5)([{role:"user",content:t}],{model:a.model,max_tokens:a.max_tokens,temperature:a.temperature,activityName:"Edici\xf3n de Resumen"});if(!r||0===r.trim().length)throw Error("La IA no pudo generar una edici\xf3n v\xe1lida del resumen.");return console.log("✅ Resumen editado exitosamente"),console.log(`📄 Longitud del contenido editado: ${r.length} caracteres`),r.trim()}catch(e){throw console.error("❌ Error al editar resumen:",e),e}}a(98234),a(17118),a(78072),a(33503),!function(e){e.assertEqual=e=>{},e.assertIs=function(e){},e.assertNever=function(e){throw Error()},e.arrayToEnum=e=>{let t={};for(let a of e)t[a]=a;return t},e.getValidEnumValues=t=>{let a=e.objectKeys(t).filter(e=>"number"!=typeof t[t[e]]),r={};for(let e of a)r[e]=t[e];return e.objectValues(r)},e.objectValues=t=>e.objectKeys(t).map(function(e){return t[e]}),e.objectKeys="function"==typeof Object.keys?e=>Object.keys(e):e=>{let t=[];for(let a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.push(a);return t},e.find=(e,t)=>{for(let a of e)if(t(a))return a},e.isInteger="function"==typeof Number.isInteger?e=>Number.isInteger(e):e=>"number"==typeof e&&Number.isFinite(e)&&Math.floor(e)===e,e.joinValues=function(e,t=" | "){return e.map(e=>"string"==typeof e?`'${e}'`:e).join(t)},e.jsonStringifyReplacer=(e,t)=>"bigint"==typeof t?t.toString():t}(n||(n={})),(s||(s={})).mergeShapes=(e,t)=>({...e,...t});let O=n.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),C=e=>{switch(typeof e){case"undefined":return O.undefined;case"string":return O.string;case"number":return Number.isNaN(e)?O.nan:O.number;case"boolean":return O.boolean;case"function":return O.function;case"bigint":return O.bigint;case"symbol":return O.symbol;case"object":if(Array.isArray(e))return O.array;if(null===e)return O.null;if(e.then&&"function"==typeof e.then&&e.catch&&"function"==typeof e.catch)return O.promise;if("undefined"!=typeof Map&&e instanceof Map)return O.map;if("undefined"!=typeof Set&&e instanceof Set)return O.set;if("undefined"!=typeof Date&&e instanceof Date)return O.date;return O.object;default:return O.unknown}},k=n.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);class R extends Error{get errors(){return this.issues}constructor(e){super(),this.issues=[],this.addIssue=e=>{this.issues=[...this.issues,e]},this.addIssues=(e=[])=>{this.issues=[...this.issues,...e]};let t=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,t):this.__proto__=t,this.name="ZodError",this.issues=e}format(e){let t=e||function(e){return e.message},a={_errors:[]},r=e=>{for(let n of e.issues)if("invalid_union"===n.code)n.unionErrors.map(r);else if("invalid_return_type"===n.code)r(n.returnTypeError);else if("invalid_arguments"===n.code)r(n.argumentsError);else if(0===n.path.length)a._errors.push(t(n));else{let e=a,r=0;for(;r<n.path.length;){let a=n.path[r];r===n.path.length-1?(e[a]=e[a]||{_errors:[]},e[a]._errors.push(t(n))):e[a]=e[a]||{_errors:[]},e=e[a],r++}}};return r(this),a}static assert(e){if(!(e instanceof R))throw Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,n.jsonStringifyReplacer,2)}get isEmpty(){return 0===this.issues.length}flatten(e=e=>e.message){let t={},a=[];for(let r of this.issues)r.path.length>0?(t[r.path[0]]=t[r.path[0]]||[],t[r.path[0]].push(e(r))):a.push(e(r));return{formErrors:a,fieldErrors:t}}get formErrors(){return this.flatten()}}R.create=e=>new R(e);let P=(e,t)=>{let a;switch(e.code){case k.invalid_type:a=e.received===O.undefined?"Required":`Expected ${e.expected}, received ${e.received}`;break;case k.invalid_literal:a=`Invalid literal value, expected ${JSON.stringify(e.expected,n.jsonStringifyReplacer)}`;break;case k.unrecognized_keys:a=`Unrecognized key(s) in object: ${n.joinValues(e.keys,", ")}`;break;case k.invalid_union:a="Invalid input";break;case k.invalid_union_discriminator:a=`Invalid discriminator value. Expected ${n.joinValues(e.options)}`;break;case k.invalid_enum_value:a=`Invalid enum value. Expected ${n.joinValues(e.options)}, received '${e.received}'`;break;case k.invalid_arguments:a="Invalid function arguments";break;case k.invalid_return_type:a="Invalid function return type";break;case k.invalid_date:a="Invalid date";break;case k.invalid_string:"object"==typeof e.validation?"includes"in e.validation?(a=`Invalid input: must include "${e.validation.includes}"`,"number"==typeof e.validation.position&&(a=`${a} at one or more positions greater than or equal to ${e.validation.position}`)):"startsWith"in e.validation?a=`Invalid input: must start with "${e.validation.startsWith}"`:"endsWith"in e.validation?a=`Invalid input: must end with "${e.validation.endsWith}"`:n.assertNever(e.validation):a="regex"!==e.validation?`Invalid ${e.validation}`:"Invalid";break;case k.too_small:a="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at least":"more than"} ${e.minimum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at least":"over"} ${e.minimum} character(s)`:"number"===e.type?`Number must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${e.minimum}`:"date"===e.type?`Date must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(e.minimum))}`:"Invalid input";break;case k.too_big:a="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at most":"less than"} ${e.maximum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at most":"under"} ${e.maximum} character(s)`:"number"===e.type?`Number must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"bigint"===e.type?`BigInt must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"date"===e.type?`Date must be ${e.exact?"exactly":e.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(e.maximum))}`:"Invalid input";break;case k.custom:a="Invalid input";break;case k.invalid_intersection_types:a="Intersection results could not be merged";break;case k.not_multiple_of:a=`Number must be a multiple of ${e.multipleOf}`;break;case k.not_finite:a="Number must be finite";break;default:a=t.defaultError,n.assertNever(e)}return{message:a}},$=e=>{let{data:t,path:a,errorMaps:r,issueData:n}=e,s=[...a,...n.path||[]],i={...n,path:s};if(void 0!==n.message)return{...n,path:s,message:n.message};let o="";for(let e of r.filter(e=>!!e).slice().reverse())o=e(i,{data:t,defaultError:o}).message;return{...n,path:s,message:o}};function D(e,t){let a=$({issueData:t,data:e.data,path:e.path,errorMaps:[e.common.contextualErrorMap,e.schemaErrorMap,P,P==P?void 0:P].filter(e=>!!e)});e.common.issues.push(a)}class j{constructor(){this.value="valid"}dirty(){"valid"===this.value&&(this.value="dirty")}abort(){"aborted"!==this.value&&(this.value="aborted")}static mergeArray(e,t){let a=[];for(let r of t){if("aborted"===r.status)return L;"dirty"===r.status&&e.dirty(),a.push(r.value)}return{status:e.value,value:a}}static async mergeObjectAsync(e,t){let a=[];for(let e of t){let t=await e.key,r=await e.value;a.push({key:t,value:r})}return j.mergeObjectSync(e,a)}static mergeObjectSync(e,t){let a={};for(let r of t){let{key:t,value:n}=r;if("aborted"===t.status||"aborted"===n.status)return L;"dirty"===t.status&&e.dirty(),"dirty"===n.status&&e.dirty(),"__proto__"!==t.value&&(void 0!==n.value||r.alwaysSet)&&(a[t.value]=n.value)}return{status:e.value,value:a}}}let L=Object.freeze({status:"aborted"}),M=e=>({status:"dirty",value:e}),U=e=>({status:"valid",value:e}),F=e=>"aborted"===e.status,q=e=>"dirty"===e.status,z=e=>"valid"===e.status,Z=e=>"undefined"!=typeof Promise&&e instanceof Promise;!function(e){e.errToObj=e=>"string"==typeof e?{message:e}:e||{},e.toString=e=>"string"==typeof e?e:e?.message}(i||(i={}));var B=function(e,t,a,r){if("a"===a&&!r)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!r:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===a?r:"a"===a?r.call(e):r?r.value:t.get(e)},W=function(e,t,a,r,n){if("m"===r)throw TypeError("Private method is not writable");if("a"===r&&!n)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===r?n.call(e,a):n?n.value=a:t.set(e,a),a};class V{constructor(e,t,a,r){this._cachedPath=[],this.parent=e,this.data=t,this._path=a,this._key=r}get path(){return this._cachedPath.length||(Array.isArray(this._key)?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}let J=(e,t)=>{if(z(t))return{success:!0,data:t.value};if(!e.common.issues.length)throw Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;let t=new R(e.common.issues);return this._error=t,this._error}}};function X(e){if(!e)return{};let{errorMap:t,invalid_type_error:a,required_error:r,description:n}=e;if(t&&(a||r))throw Error('Can\'t use "invalid_type_error" or "required_error" in conjunction with custom error map.');return t?{errorMap:t,description:n}:{errorMap:(t,n)=>{let{message:s}=e;return"invalid_enum_value"===t.code?{message:s??n.defaultError}:void 0===n.data?{message:s??r??n.defaultError}:"invalid_type"!==t.code?{message:n.defaultError}:{message:s??a??n.defaultError}},description:n}}class H{get description(){return this._def.description}_getType(e){return C(e.data)}_getOrReturnCtx(e,t){return t||{common:e.parent.common,data:e.data,parsedType:C(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new j,ctx:{common:e.parent.common,data:e.data,parsedType:C(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){let t=this._parse(e);if(Z(t))throw Error("Synchronous parse encountered promise.");return t}_parseAsync(e){return Promise.resolve(this._parse(e))}parse(e,t){let a=this.safeParse(e,t);if(a.success)return a.data;throw a.error}safeParse(e,t){let a={common:{issues:[],async:t?.async??!1,contextualErrorMap:t?.errorMap},path:t?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:C(e)},r=this._parseSync({data:e,path:a.path,parent:a});return J(a,r)}"~validate"(e){let t={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:C(e)};if(!this["~standard"].async)try{let a=this._parseSync({data:e,path:[],parent:t});return z(a)?{value:a.value}:{issues:t.common.issues}}catch(e){e?.message?.toLowerCase()?.includes("encountered")&&(this["~standard"].async=!0),t.common={issues:[],async:!0}}return this._parseAsync({data:e,path:[],parent:t}).then(e=>z(e)?{value:e.value}:{issues:t.common.issues})}async parseAsync(e,t){let a=await this.safeParseAsync(e,t);if(a.success)return a.data;throw a.error}async safeParseAsync(e,t){let a={common:{issues:[],contextualErrorMap:t?.errorMap,async:!0},path:t?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:C(e)},r=this._parse({data:e,path:a.path,parent:a});return J(a,await (Z(r)?r:Promise.resolve(r)))}refine(e,t){let a=e=>"string"==typeof t||void 0===t?{message:t}:"function"==typeof t?t(e):t;return this._refinement((t,r)=>{let n=e(t),s=()=>r.addIssue({code:k.custom,...a(t)});return"undefined"!=typeof Promise&&n instanceof Promise?n.then(e=>!!e||(s(),!1)):!!n||(s(),!1)})}refinement(e,t){return this._refinement((a,r)=>!!e(a)||(r.addIssue("function"==typeof t?t(a,r):t),!1))}_refinement(e){return new ez({schema:this,typeName:d.ZodEffects,effect:{type:"refinement",refinement:e}})}superRefine(e){return this._refinement(e)}constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:e=>this["~validate"](e)}}optional(){return eZ.create(this,this._def)}nullable(){return eB.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return eS.create(this)}promise(){return eq.create(this,this._def)}or(e){return eN.create([this,e],this._def)}and(e){return eC.create(this,e,this._def)}transform(e){return new ez({...X(this._def),schema:this,typeName:d.ZodEffects,effect:{type:"transform",transform:e}})}default(e){return new eW({...X(this._def),innerType:this,defaultValue:"function"==typeof e?e:()=>e,typeName:d.ZodDefault})}brand(){return new eX({typeName:d.ZodBranded,type:this,...X(this._def)})}catch(e){return new eV({...X(this._def),innerType:this,catchValue:"function"==typeof e?e:()=>e,typeName:d.ZodCatch})}describe(e){return new this.constructor({...this._def,description:e})}pipe(e){return eH.create(this,e)}readonly(){return eG.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}let G=/^c[^\s-]{8,}$/i,Y=/^[0-9a-z]+$/,K=/^[0-9A-HJKMNP-TV-Z]{26}$/i,Q=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,ee=/^[a-z0-9_-]{21}$/i,et=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,ea=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,er=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i,en=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,es=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,ei=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,eo=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,el=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,ed=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,ec="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",eu=RegExp(`^${ec}$`);function ep(e){let t="[0-5]\\d";e.precision?t=`${t}\\.\\d{${e.precision}}`:null==e.precision&&(t=`${t}(\\.\\d+)?`);let a=e.precision?"+":"?";return`([01]\\d|2[0-3]):[0-5]\\d(:${t})${a}`}class eh extends H{_parse(e){var t,a,s,i;let o;if(this._def.coerce&&(e.data=String(e.data)),this._getType(e)!==O.string){let t=this._getOrReturnCtx(e);return D(t,{code:k.invalid_type,expected:O.string,received:t.parsedType}),L}let l=new j;for(let d of this._def.checks)if("min"===d.kind)e.data.length<d.value&&(D(o=this._getOrReturnCtx(e,o),{code:k.too_small,minimum:d.value,type:"string",inclusive:!0,exact:!1,message:d.message}),l.dirty());else if("max"===d.kind)e.data.length>d.value&&(D(o=this._getOrReturnCtx(e,o),{code:k.too_big,maximum:d.value,type:"string",inclusive:!0,exact:!1,message:d.message}),l.dirty());else if("length"===d.kind){let t=e.data.length>d.value,a=e.data.length<d.value;(t||a)&&(o=this._getOrReturnCtx(e,o),t?D(o,{code:k.too_big,maximum:d.value,type:"string",inclusive:!0,exact:!0,message:d.message}):a&&D(o,{code:k.too_small,minimum:d.value,type:"string",inclusive:!0,exact:!0,message:d.message}),l.dirty())}else if("email"===d.kind)er.test(e.data)||(D(o=this._getOrReturnCtx(e,o),{validation:"email",code:k.invalid_string,message:d.message}),l.dirty());else if("emoji"===d.kind)r||(r=RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),r.test(e.data)||(D(o=this._getOrReturnCtx(e,o),{validation:"emoji",code:k.invalid_string,message:d.message}),l.dirty());else if("uuid"===d.kind)Q.test(e.data)||(D(o=this._getOrReturnCtx(e,o),{validation:"uuid",code:k.invalid_string,message:d.message}),l.dirty());else if("nanoid"===d.kind)ee.test(e.data)||(D(o=this._getOrReturnCtx(e,o),{validation:"nanoid",code:k.invalid_string,message:d.message}),l.dirty());else if("cuid"===d.kind)G.test(e.data)||(D(o=this._getOrReturnCtx(e,o),{validation:"cuid",code:k.invalid_string,message:d.message}),l.dirty());else if("cuid2"===d.kind)Y.test(e.data)||(D(o=this._getOrReturnCtx(e,o),{validation:"cuid2",code:k.invalid_string,message:d.message}),l.dirty());else if("ulid"===d.kind)K.test(e.data)||(D(o=this._getOrReturnCtx(e,o),{validation:"ulid",code:k.invalid_string,message:d.message}),l.dirty());else if("url"===d.kind)try{new URL(e.data)}catch{D(o=this._getOrReturnCtx(e,o),{validation:"url",code:k.invalid_string,message:d.message}),l.dirty()}else"regex"===d.kind?(d.regex.lastIndex=0,d.regex.test(e.data)||(D(o=this._getOrReturnCtx(e,o),{validation:"regex",code:k.invalid_string,message:d.message}),l.dirty())):"trim"===d.kind?e.data=e.data.trim():"includes"===d.kind?e.data.includes(d.value,d.position)||(D(o=this._getOrReturnCtx(e,o),{code:k.invalid_string,validation:{includes:d.value,position:d.position},message:d.message}),l.dirty()):"toLowerCase"===d.kind?e.data=e.data.toLowerCase():"toUpperCase"===d.kind?e.data=e.data.toUpperCase():"startsWith"===d.kind?e.data.startsWith(d.value)||(D(o=this._getOrReturnCtx(e,o),{code:k.invalid_string,validation:{startsWith:d.value},message:d.message}),l.dirty()):"endsWith"===d.kind?e.data.endsWith(d.value)||(D(o=this._getOrReturnCtx(e,o),{code:k.invalid_string,validation:{endsWith:d.value},message:d.message}),l.dirty()):"datetime"===d.kind?(function(e){let t=`${ec}T${ep(e)}`,a=[];return a.push(e.local?"Z?":"Z"),e.offset&&a.push("([+-]\\d{2}:?\\d{2})"),t=`${t}(${a.join("|")})`,RegExp(`^${t}$`)})(d).test(e.data)||(D(o=this._getOrReturnCtx(e,o),{code:k.invalid_string,validation:"datetime",message:d.message}),l.dirty()):"date"===d.kind?eu.test(e.data)||(D(o=this._getOrReturnCtx(e,o),{code:k.invalid_string,validation:"date",message:d.message}),l.dirty()):"time"===d.kind?RegExp(`^${ep(d)}$`).test(e.data)||(D(o=this._getOrReturnCtx(e,o),{code:k.invalid_string,validation:"time",message:d.message}),l.dirty()):"duration"===d.kind?ea.test(e.data)||(D(o=this._getOrReturnCtx(e,o),{validation:"duration",code:k.invalid_string,message:d.message}),l.dirty()):"ip"===d.kind?(t=e.data,!(("v4"===(a=d.version)||!a)&&en.test(t)||("v6"===a||!a)&&ei.test(t))&&1&&(D(o=this._getOrReturnCtx(e,o),{validation:"ip",code:k.invalid_string,message:d.message}),l.dirty())):"jwt"===d.kind?!function(e,t){if(!et.test(e))return!1;try{let[a]=e.split("."),r=a.replace(/-/g,"+").replace(/_/g,"/").padEnd(a.length+(4-a.length%4)%4,"="),n=JSON.parse(atob(r));if("object"!=typeof n||null===n||"typ"in n&&n?.typ!=="JWT"||!n.alg||t&&n.alg!==t)return!1;return!0}catch{return!1}}(e.data,d.alg)&&(D(o=this._getOrReturnCtx(e,o),{validation:"jwt",code:k.invalid_string,message:d.message}),l.dirty()):"cidr"===d.kind?(s=e.data,!(("v4"===(i=d.version)||!i)&&es.test(s)||("v6"===i||!i)&&eo.test(s))&&1&&(D(o=this._getOrReturnCtx(e,o),{validation:"cidr",code:k.invalid_string,message:d.message}),l.dirty())):"base64"===d.kind?el.test(e.data)||(D(o=this._getOrReturnCtx(e,o),{validation:"base64",code:k.invalid_string,message:d.message}),l.dirty()):"base64url"===d.kind?ed.test(e.data)||(D(o=this._getOrReturnCtx(e,o),{validation:"base64url",code:k.invalid_string,message:d.message}),l.dirty()):n.assertNever(d);return{status:l.value,value:e.data}}_regex(e,t,a){return this.refinement(t=>e.test(t),{validation:t,code:k.invalid_string,...i.errToObj(a)})}_addCheck(e){return new eh({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",...i.errToObj(e)})}url(e){return this._addCheck({kind:"url",...i.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",...i.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",...i.errToObj(e)})}nanoid(e){return this._addCheck({kind:"nanoid",...i.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",...i.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",...i.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",...i.errToObj(e)})}base64(e){return this._addCheck({kind:"base64",...i.errToObj(e)})}base64url(e){return this._addCheck({kind:"base64url",...i.errToObj(e)})}jwt(e){return this._addCheck({kind:"jwt",...i.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",...i.errToObj(e)})}cidr(e){return this._addCheck({kind:"cidr",...i.errToObj(e)})}datetime(e){return"string"==typeof e?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:"datetime",precision:void 0===e?.precision?null:e?.precision,offset:e?.offset??!1,local:e?.local??!1,...i.errToObj(e?.message)})}date(e){return this._addCheck({kind:"date",message:e})}time(e){return"string"==typeof e?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck({kind:"time",precision:void 0===e?.precision?null:e?.precision,...i.errToObj(e?.message)})}duration(e){return this._addCheck({kind:"duration",...i.errToObj(e)})}regex(e,t){return this._addCheck({kind:"regex",regex:e,...i.errToObj(t)})}includes(e,t){return this._addCheck({kind:"includes",value:e,position:t?.position,...i.errToObj(t?.message)})}startsWith(e,t){return this._addCheck({kind:"startsWith",value:e,...i.errToObj(t)})}endsWith(e,t){return this._addCheck({kind:"endsWith",value:e,...i.errToObj(t)})}min(e,t){return this._addCheck({kind:"min",value:e,...i.errToObj(t)})}max(e,t){return this._addCheck({kind:"max",value:e,...i.errToObj(t)})}length(e,t){return this._addCheck({kind:"length",value:e,...i.errToObj(t)})}nonempty(e){return this.min(1,i.errToObj(e))}trim(){return new eh({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new eh({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new eh({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(e=>"datetime"===e.kind)}get isDate(){return!!this._def.checks.find(e=>"date"===e.kind)}get isTime(){return!!this._def.checks.find(e=>"time"===e.kind)}get isDuration(){return!!this._def.checks.find(e=>"duration"===e.kind)}get isEmail(){return!!this._def.checks.find(e=>"email"===e.kind)}get isURL(){return!!this._def.checks.find(e=>"url"===e.kind)}get isEmoji(){return!!this._def.checks.find(e=>"emoji"===e.kind)}get isUUID(){return!!this._def.checks.find(e=>"uuid"===e.kind)}get isNANOID(){return!!this._def.checks.find(e=>"nanoid"===e.kind)}get isCUID(){return!!this._def.checks.find(e=>"cuid"===e.kind)}get isCUID2(){return!!this._def.checks.find(e=>"cuid2"===e.kind)}get isULID(){return!!this._def.checks.find(e=>"ulid"===e.kind)}get isIP(){return!!this._def.checks.find(e=>"ip"===e.kind)}get isCIDR(){return!!this._def.checks.find(e=>"cidr"===e.kind)}get isBase64(){return!!this._def.checks.find(e=>"base64"===e.kind)}get isBase64url(){return!!this._def.checks.find(e=>"base64url"===e.kind)}get minLength(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxLength(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}eh.create=e=>new eh({checks:[],typeName:d.ZodString,coerce:e?.coerce??!1,...X(e)});class em extends H{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){let t;if(this._def.coerce&&(e.data=Number(e.data)),this._getType(e)!==O.number){let t=this._getOrReturnCtx(e);return D(t,{code:k.invalid_type,expected:O.number,received:t.parsedType}),L}let a=new j;for(let r of this._def.checks)"int"===r.kind?n.isInteger(e.data)||(D(t=this._getOrReturnCtx(e,t),{code:k.invalid_type,expected:"integer",received:"float",message:r.message}),a.dirty()):"min"===r.kind?(r.inclusive?e.data<r.value:e.data<=r.value)&&(D(t=this._getOrReturnCtx(e,t),{code:k.too_small,minimum:r.value,type:"number",inclusive:r.inclusive,exact:!1,message:r.message}),a.dirty()):"max"===r.kind?(r.inclusive?e.data>r.value:e.data>=r.value)&&(D(t=this._getOrReturnCtx(e,t),{code:k.too_big,maximum:r.value,type:"number",inclusive:r.inclusive,exact:!1,message:r.message}),a.dirty()):"multipleOf"===r.kind?0!==function(e,t){let a=(e.toString().split(".")[1]||"").length,r=(t.toString().split(".")[1]||"").length,n=a>r?a:r;return Number.parseInt(e.toFixed(n).replace(".",""))%Number.parseInt(t.toFixed(n).replace(".",""))/10**n}(e.data,r.value)&&(D(t=this._getOrReturnCtx(e,t),{code:k.not_multiple_of,multipleOf:r.value,message:r.message}),a.dirty()):"finite"===r.kind?Number.isFinite(e.data)||(D(t=this._getOrReturnCtx(e,t),{code:k.not_finite,message:r.message}),a.dirty()):n.assertNever(r);return{status:a.value,value:e.data}}gte(e,t){return this.setLimit("min",e,!0,i.toString(t))}gt(e,t){return this.setLimit("min",e,!1,i.toString(t))}lte(e,t){return this.setLimit("max",e,!0,i.toString(t))}lt(e,t){return this.setLimit("max",e,!1,i.toString(t))}setLimit(e,t,a,r){return new em({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:a,message:i.toString(r)}]})}_addCheck(e){return new em({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:i.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:i.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:i.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:i.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:i.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:i.toString(t)})}finite(e){return this._addCheck({kind:"finite",message:i.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:i.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:i.toString(e)})}get minValue(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}get isInt(){return!!this._def.checks.find(e=>"int"===e.kind||"multipleOf"===e.kind&&n.isInteger(e.value))}get isFinite(){let e=null,t=null;for(let a of this._def.checks)if("finite"===a.kind||"int"===a.kind||"multipleOf"===a.kind)return!0;else"min"===a.kind?(null===t||a.value>t)&&(t=a.value):"max"===a.kind&&(null===e||a.value<e)&&(e=a.value);return Number.isFinite(t)&&Number.isFinite(e)}}em.create=e=>new em({checks:[],typeName:d.ZodNumber,coerce:e?.coerce||!1,...X(e)});class ef extends H{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){let t;if(this._def.coerce)try{e.data=BigInt(e.data)}catch{return this._getInvalidInput(e)}if(this._getType(e)!==O.bigint)return this._getInvalidInput(e);let a=new j;for(let r of this._def.checks)"min"===r.kind?(r.inclusive?e.data<r.value:e.data<=r.value)&&(D(t=this._getOrReturnCtx(e,t),{code:k.too_small,type:"bigint",minimum:r.value,inclusive:r.inclusive,message:r.message}),a.dirty()):"max"===r.kind?(r.inclusive?e.data>r.value:e.data>=r.value)&&(D(t=this._getOrReturnCtx(e,t),{code:k.too_big,type:"bigint",maximum:r.value,inclusive:r.inclusive,message:r.message}),a.dirty()):"multipleOf"===r.kind?e.data%r.value!==BigInt(0)&&(D(t=this._getOrReturnCtx(e,t),{code:k.not_multiple_of,multipleOf:r.value,message:r.message}),a.dirty()):n.assertNever(r);return{status:a.value,value:e.data}}_getInvalidInput(e){let t=this._getOrReturnCtx(e);return D(t,{code:k.invalid_type,expected:O.bigint,received:t.parsedType}),L}gte(e,t){return this.setLimit("min",e,!0,i.toString(t))}gt(e,t){return this.setLimit("min",e,!1,i.toString(t))}lte(e,t){return this.setLimit("max",e,!0,i.toString(t))}lt(e,t){return this.setLimit("max",e,!1,i.toString(t))}setLimit(e,t,a,r){return new ef({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:a,message:i.toString(r)}]})}_addCheck(e){return new ef({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:i.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:i.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:i.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:i.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:i.toString(t)})}get minValue(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}ef.create=e=>new ef({checks:[],typeName:d.ZodBigInt,coerce:e?.coerce??!1,...X(e)});class eg extends H{_parse(e){if(this._def.coerce&&(e.data=!!e.data),this._getType(e)!==O.boolean){let t=this._getOrReturnCtx(e);return D(t,{code:k.invalid_type,expected:O.boolean,received:t.parsedType}),L}return U(e.data)}}eg.create=e=>new eg({typeName:d.ZodBoolean,coerce:e?.coerce||!1,...X(e)});class ey extends H{_parse(e){let t;if(this._def.coerce&&(e.data=new Date(e.data)),this._getType(e)!==O.date){let t=this._getOrReturnCtx(e);return D(t,{code:k.invalid_type,expected:O.date,received:t.parsedType}),L}if(Number.isNaN(e.data.getTime()))return D(this._getOrReturnCtx(e),{code:k.invalid_date}),L;let a=new j;for(let r of this._def.checks)"min"===r.kind?e.data.getTime()<r.value&&(D(t=this._getOrReturnCtx(e,t),{code:k.too_small,message:r.message,inclusive:!0,exact:!1,minimum:r.value,type:"date"}),a.dirty()):"max"===r.kind?e.data.getTime()>r.value&&(D(t=this._getOrReturnCtx(e,t),{code:k.too_big,message:r.message,inclusive:!0,exact:!1,maximum:r.value,type:"date"}),a.dirty()):n.assertNever(r);return{status:a.value,value:new Date(e.data.getTime())}}_addCheck(e){return new ey({...this._def,checks:[...this._def.checks,e]})}min(e,t){return this._addCheck({kind:"min",value:e.getTime(),message:i.toString(t)})}max(e,t){return this._addCheck({kind:"max",value:e.getTime(),message:i.toString(t)})}get minDate(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return null!=e?new Date(e):null}get maxDate(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return null!=e?new Date(e):null}}ey.create=e=>new ey({checks:[],coerce:e?.coerce||!1,typeName:d.ZodDate,...X(e)});class ex extends H{_parse(e){if(this._getType(e)!==O.symbol){let t=this._getOrReturnCtx(e);return D(t,{code:k.invalid_type,expected:O.symbol,received:t.parsedType}),L}return U(e.data)}}ex.create=e=>new ex({typeName:d.ZodSymbol,...X(e)});class e_ extends H{_parse(e){if(this._getType(e)!==O.undefined){let t=this._getOrReturnCtx(e);return D(t,{code:k.invalid_type,expected:O.undefined,received:t.parsedType}),L}return U(e.data)}}e_.create=e=>new e_({typeName:d.ZodUndefined,...X(e)});class ev extends H{_parse(e){if(this._getType(e)!==O.null){let t=this._getOrReturnCtx(e);return D(t,{code:k.invalid_type,expected:O.null,received:t.parsedType}),L}return U(e.data)}}ev.create=e=>new ev({typeName:d.ZodNull,...X(e)});class eb extends H{constructor(){super(...arguments),this._any=!0}_parse(e){return U(e.data)}}eb.create=e=>new eb({typeName:d.ZodAny,...X(e)});class ew extends H{constructor(){super(...arguments),this._unknown=!0}_parse(e){return U(e.data)}}ew.create=e=>new ew({typeName:d.ZodUnknown,...X(e)});class eE extends H{_parse(e){let t=this._getOrReturnCtx(e);return D(t,{code:k.invalid_type,expected:O.never,received:t.parsedType}),L}}eE.create=e=>new eE({typeName:d.ZodNever,...X(e)});class eA extends H{_parse(e){if(this._getType(e)!==O.undefined){let t=this._getOrReturnCtx(e);return D(t,{code:k.invalid_type,expected:O.void,received:t.parsedType}),L}return U(e.data)}}eA.create=e=>new eA({typeName:d.ZodVoid,...X(e)});class eS extends H{_parse(e){let{ctx:t,status:a}=this._processInputParams(e),r=this._def;if(t.parsedType!==O.array)return D(t,{code:k.invalid_type,expected:O.array,received:t.parsedType}),L;if(null!==r.exactLength){let e=t.data.length>r.exactLength.value,n=t.data.length<r.exactLength.value;(e||n)&&(D(t,{code:e?k.too_big:k.too_small,minimum:n?r.exactLength.value:void 0,maximum:e?r.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:r.exactLength.message}),a.dirty())}if(null!==r.minLength&&t.data.length<r.minLength.value&&(D(t,{code:k.too_small,minimum:r.minLength.value,type:"array",inclusive:!0,exact:!1,message:r.minLength.message}),a.dirty()),null!==r.maxLength&&t.data.length>r.maxLength.value&&(D(t,{code:k.too_big,maximum:r.maxLength.value,type:"array",inclusive:!0,exact:!1,message:r.maxLength.message}),a.dirty()),t.common.async)return Promise.all([...t.data].map((e,a)=>r.type._parseAsync(new V(t,e,t.path,a)))).then(e=>j.mergeArray(a,e));let n=[...t.data].map((e,a)=>r.type._parseSync(new V(t,e,t.path,a)));return j.mergeArray(a,n)}get element(){return this._def.type}min(e,t){return new eS({...this._def,minLength:{value:e,message:i.toString(t)}})}max(e,t){return new eS({...this._def,maxLength:{value:e,message:i.toString(t)}})}length(e,t){return new eS({...this._def,exactLength:{value:e,message:i.toString(t)}})}nonempty(e){return this.min(1,e)}}eS.create=(e,t)=>new eS({type:e,minLength:null,maxLength:null,exactLength:null,typeName:d.ZodArray,...X(t)});class eI extends H{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(null!==this._cached)return this._cached;let e=this._def.shape(),t=n.objectKeys(e);return this._cached={shape:e,keys:t},this._cached}_parse(e){if(this._getType(e)!==O.object){let t=this._getOrReturnCtx(e);return D(t,{code:k.invalid_type,expected:O.object,received:t.parsedType}),L}let{status:t,ctx:a}=this._processInputParams(e),{shape:r,keys:n}=this._getCached(),s=[];if(!(this._def.catchall instanceof eE&&"strip"===this._def.unknownKeys))for(let e in a.data)n.includes(e)||s.push(e);let i=[];for(let e of n){let t=r[e],n=a.data[e];i.push({key:{status:"valid",value:e},value:t._parse(new V(a,n,a.path,e)),alwaysSet:e in a.data})}if(this._def.catchall instanceof eE){let e=this._def.unknownKeys;if("passthrough"===e)for(let e of s)i.push({key:{status:"valid",value:e},value:{status:"valid",value:a.data[e]}});else if("strict"===e)s.length>0&&(D(a,{code:k.unrecognized_keys,keys:s}),t.dirty());else if("strip"===e);else throw Error("Internal ZodObject error: invalid unknownKeys value.")}else{let e=this._def.catchall;for(let t of s){let r=a.data[t];i.push({key:{status:"valid",value:t},value:e._parse(new V(a,r,a.path,t)),alwaysSet:t in a.data})}}return a.common.async?Promise.resolve().then(async()=>{let e=[];for(let t of i){let a=await t.key,r=await t.value;e.push({key:a,value:r,alwaysSet:t.alwaysSet})}return e}).then(e=>j.mergeObjectSync(t,e)):j.mergeObjectSync(t,i)}get shape(){return this._def.shape()}strict(e){return i.errToObj,new eI({...this._def,unknownKeys:"strict",...void 0!==e?{errorMap:(t,a)=>{let r=this._def.errorMap?.(t,a).message??a.defaultError;return"unrecognized_keys"===t.code?{message:i.errToObj(e).message??r}:{message:r}}}:{}})}strip(){return new eI({...this._def,unknownKeys:"strip"})}passthrough(){return new eI({...this._def,unknownKeys:"passthrough"})}extend(e){return new eI({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new eI({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:d.ZodObject})}setKey(e,t){return this.augment({[e]:t})}catchall(e){return new eI({...this._def,catchall:e})}pick(e){let t={};for(let a of n.objectKeys(e))e[a]&&this.shape[a]&&(t[a]=this.shape[a]);return new eI({...this._def,shape:()=>t})}omit(e){let t={};for(let a of n.objectKeys(this.shape))e[a]||(t[a]=this.shape[a]);return new eI({...this._def,shape:()=>t})}deepPartial(){return function e(t){if(t instanceof eI){let a={};for(let r in t.shape){let n=t.shape[r];a[r]=eZ.create(e(n))}return new eI({...t._def,shape:()=>a})}if(t instanceof eS)return new eS({...t._def,type:e(t.element)});if(t instanceof eZ)return eZ.create(e(t.unwrap()));if(t instanceof eB)return eB.create(e(t.unwrap()));if(t instanceof ek)return ek.create(t.items.map(t=>e(t)));else return t}(this)}partial(e){let t={};for(let a of n.objectKeys(this.shape)){let r=this.shape[a];e&&!e[a]?t[a]=r:t[a]=r.optional()}return new eI({...this._def,shape:()=>t})}required(e){let t={};for(let a of n.objectKeys(this.shape))if(e&&!e[a])t[a]=this.shape[a];else{let e=this.shape[a];for(;e instanceof eZ;)e=e._def.innerType;t[a]=e}return new eI({...this._def,shape:()=>t})}keyof(){return eM(n.objectKeys(this.shape))}}eI.create=(e,t)=>new eI({shape:()=>e,unknownKeys:"strip",catchall:eE.create(),typeName:d.ZodObject,...X(t)}),eI.strictCreate=(e,t)=>new eI({shape:()=>e,unknownKeys:"strict",catchall:eE.create(),typeName:d.ZodObject,...X(t)}),eI.lazycreate=(e,t)=>new eI({shape:e,unknownKeys:"strip",catchall:eE.create(),typeName:d.ZodObject,...X(t)});class eN extends H{_parse(e){let{ctx:t}=this._processInputParams(e),a=this._def.options;if(t.common.async)return Promise.all(a.map(async e=>{let a={...t,common:{...t.common,issues:[]},parent:null};return{result:await e._parseAsync({data:t.data,path:t.path,parent:a}),ctx:a}})).then(function(e){for(let t of e)if("valid"===t.result.status)return t.result;for(let a of e)if("dirty"===a.result.status)return t.common.issues.push(...a.ctx.common.issues),a.result;let a=e.map(e=>new R(e.ctx.common.issues));return D(t,{code:k.invalid_union,unionErrors:a}),L});{let e,r=[];for(let n of a){let a={...t,common:{...t.common,issues:[]},parent:null},s=n._parseSync({data:t.data,path:t.path,parent:a});if("valid"===s.status)return s;"dirty"!==s.status||e||(e={result:s,ctx:a}),a.common.issues.length&&r.push(a.common.issues)}if(e)return t.common.issues.push(...e.ctx.common.issues),e.result;let n=r.map(e=>new R(e));return D(t,{code:k.invalid_union,unionErrors:n}),L}}get options(){return this._def.options}}eN.create=(e,t)=>new eN({options:e,typeName:d.ZodUnion,...X(t)});let eT=e=>{if(e instanceof ej)return eT(e.schema);if(e instanceof ez)return eT(e.innerType());if(e instanceof eL)return[e.value];if(e instanceof eU)return e.options;if(e instanceof eF)return n.objectValues(e.enum);else if(e instanceof eW)return eT(e._def.innerType);else if(e instanceof e_)return[void 0];else if(e instanceof ev)return[null];else if(e instanceof eZ)return[void 0,...eT(e.unwrap())];else if(e instanceof eB)return[null,...eT(e.unwrap())];else if(e instanceof eX)return eT(e.unwrap());else if(e instanceof eG)return eT(e.unwrap());else if(e instanceof eV)return eT(e._def.innerType);else return[]};class eO extends H{_parse(e){let{ctx:t}=this._processInputParams(e);if(t.parsedType!==O.object)return D(t,{code:k.invalid_type,expected:O.object,received:t.parsedType}),L;let a=this.discriminator,r=t.data[a],n=this.optionsMap.get(r);return n?t.common.async?n._parseAsync({data:t.data,path:t.path,parent:t}):n._parseSync({data:t.data,path:t.path,parent:t}):(D(t,{code:k.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[a]}),L)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(e,t,a){let r=new Map;for(let a of t){let t=eT(a.shape[e]);if(!t.length)throw Error(`A discriminator value for key \`${e}\` could not be extracted from all schema options`);for(let n of t){if(r.has(n))throw Error(`Discriminator property ${String(e)} has duplicate value ${String(n)}`);r.set(n,a)}}return new eO({typeName:d.ZodDiscriminatedUnion,discriminator:e,options:t,optionsMap:r,...X(a)})}}class eC extends H{_parse(e){let{status:t,ctx:a}=this._processInputParams(e),r=(e,r)=>{if(F(e)||F(r))return L;let s=function e(t,a){let r=C(t),s=C(a);if(t===a)return{valid:!0,data:t};if(r===O.object&&s===O.object){let r=n.objectKeys(a),s=n.objectKeys(t).filter(e=>-1!==r.indexOf(e)),i={...t,...a};for(let r of s){let n=e(t[r],a[r]);if(!n.valid)return{valid:!1};i[r]=n.data}return{valid:!0,data:i}}if(r===O.array&&s===O.array){if(t.length!==a.length)return{valid:!1};let r=[];for(let n=0;n<t.length;n++){let s=e(t[n],a[n]);if(!s.valid)return{valid:!1};r.push(s.data)}return{valid:!0,data:r}}if(r===O.date&&s===O.date&&+t==+a)return{valid:!0,data:t};return{valid:!1}}(e.value,r.value);return s.valid?((q(e)||q(r))&&t.dirty(),{status:t.value,value:s.data}):(D(a,{code:k.invalid_intersection_types}),L)};return a.common.async?Promise.all([this._def.left._parseAsync({data:a.data,path:a.path,parent:a}),this._def.right._parseAsync({data:a.data,path:a.path,parent:a})]).then(([e,t])=>r(e,t)):r(this._def.left._parseSync({data:a.data,path:a.path,parent:a}),this._def.right._parseSync({data:a.data,path:a.path,parent:a}))}}eC.create=(e,t,a)=>new eC({left:e,right:t,typeName:d.ZodIntersection,...X(a)});class ek extends H{_parse(e){let{status:t,ctx:a}=this._processInputParams(e);if(a.parsedType!==O.array)return D(a,{code:k.invalid_type,expected:O.array,received:a.parsedType}),L;if(a.data.length<this._def.items.length)return D(a,{code:k.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),L;!this._def.rest&&a.data.length>this._def.items.length&&(D(a,{code:k.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),t.dirty());let r=[...a.data].map((e,t)=>{let r=this._def.items[t]||this._def.rest;return r?r._parse(new V(a,e,a.path,t)):null}).filter(e=>!!e);return a.common.async?Promise.all(r).then(e=>j.mergeArray(t,e)):j.mergeArray(t,r)}get items(){return this._def.items}rest(e){return new ek({...this._def,rest:e})}}ek.create=(e,t)=>{if(!Array.isArray(e))throw Error("You must pass an array of schemas to z.tuple([ ... ])");return new ek({items:e,typeName:d.ZodTuple,rest:null,...X(t)})};class eR extends H{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){let{status:t,ctx:a}=this._processInputParams(e);if(a.parsedType!==O.object)return D(a,{code:k.invalid_type,expected:O.object,received:a.parsedType}),L;let r=[],n=this._def.keyType,s=this._def.valueType;for(let e in a.data)r.push({key:n._parse(new V(a,e,a.path,e)),value:s._parse(new V(a,a.data[e],a.path,e)),alwaysSet:e in a.data});return a.common.async?j.mergeObjectAsync(t,r):j.mergeObjectSync(t,r)}get element(){return this._def.valueType}static create(e,t,a){return new eR(t instanceof H?{keyType:e,valueType:t,typeName:d.ZodRecord,...X(a)}:{keyType:eh.create(),valueType:e,typeName:d.ZodRecord,...X(t)})}}class eP extends H{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){let{status:t,ctx:a}=this._processInputParams(e);if(a.parsedType!==O.map)return D(a,{code:k.invalid_type,expected:O.map,received:a.parsedType}),L;let r=this._def.keyType,n=this._def.valueType,s=[...a.data.entries()].map(([e,t],s)=>({key:r._parse(new V(a,e,a.path,[s,"key"])),value:n._parse(new V(a,t,a.path,[s,"value"]))}));if(a.common.async){let e=new Map;return Promise.resolve().then(async()=>{for(let a of s){let r=await a.key,n=await a.value;if("aborted"===r.status||"aborted"===n.status)return L;("dirty"===r.status||"dirty"===n.status)&&t.dirty(),e.set(r.value,n.value)}return{status:t.value,value:e}})}{let e=new Map;for(let a of s){let r=a.key,n=a.value;if("aborted"===r.status||"aborted"===n.status)return L;("dirty"===r.status||"dirty"===n.status)&&t.dirty(),e.set(r.value,n.value)}return{status:t.value,value:e}}}}eP.create=(e,t,a)=>new eP({valueType:t,keyType:e,typeName:d.ZodMap,...X(a)});class e$ extends H{_parse(e){let{status:t,ctx:a}=this._processInputParams(e);if(a.parsedType!==O.set)return D(a,{code:k.invalid_type,expected:O.set,received:a.parsedType}),L;let r=this._def;null!==r.minSize&&a.data.size<r.minSize.value&&(D(a,{code:k.too_small,minimum:r.minSize.value,type:"set",inclusive:!0,exact:!1,message:r.minSize.message}),t.dirty()),null!==r.maxSize&&a.data.size>r.maxSize.value&&(D(a,{code:k.too_big,maximum:r.maxSize.value,type:"set",inclusive:!0,exact:!1,message:r.maxSize.message}),t.dirty());let n=this._def.valueType;function s(e){let a=new Set;for(let r of e){if("aborted"===r.status)return L;"dirty"===r.status&&t.dirty(),a.add(r.value)}return{status:t.value,value:a}}let i=[...a.data.values()].map((e,t)=>n._parse(new V(a,e,a.path,t)));return a.common.async?Promise.all(i).then(e=>s(e)):s(i)}min(e,t){return new e$({...this._def,minSize:{value:e,message:i.toString(t)}})}max(e,t){return new e$({...this._def,maxSize:{value:e,message:i.toString(t)}})}size(e,t){return this.min(e,t).max(e,t)}nonempty(e){return this.min(1,e)}}e$.create=(e,t)=>new e$({valueType:e,minSize:null,maxSize:null,typeName:d.ZodSet,...X(t)});class eD extends H{constructor(){super(...arguments),this.validate=this.implement}_parse(e){let{ctx:t}=this._processInputParams(e);if(t.parsedType!==O.function)return D(t,{code:k.invalid_type,expected:O.function,received:t.parsedType}),L;function a(e,a){return $({data:e,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,P,P].filter(e=>!!e),issueData:{code:k.invalid_arguments,argumentsError:a}})}function r(e,a){return $({data:e,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,P,P].filter(e=>!!e),issueData:{code:k.invalid_return_type,returnTypeError:a}})}let n={errorMap:t.common.contextualErrorMap},s=t.data;if(this._def.returns instanceof eq){let e=this;return U(async function(...t){let i=new R([]),o=await e._def.args.parseAsync(t,n).catch(e=>{throw i.addIssue(a(t,e)),i}),l=await Reflect.apply(s,this,o);return await e._def.returns._def.type.parseAsync(l,n).catch(e=>{throw i.addIssue(r(l,e)),i})})}{let e=this;return U(function(...t){let i=e._def.args.safeParse(t,n);if(!i.success)throw new R([a(t,i.error)]);let o=Reflect.apply(s,this,i.data),l=e._def.returns.safeParse(o,n);if(!l.success)throw new R([r(o,l.error)]);return l.data})}}parameters(){return this._def.args}returnType(){return this._def.returns}args(...e){return new eD({...this._def,args:ek.create(e).rest(ew.create())})}returns(e){return new eD({...this._def,returns:e})}implement(e){return this.parse(e)}strictImplement(e){return this.parse(e)}static create(e,t,a){return new eD({args:e||ek.create([]).rest(ew.create()),returns:t||ew.create(),typeName:d.ZodFunction,...X(a)})}}class ej extends H{get schema(){return this._def.getter()}_parse(e){let{ctx:t}=this._processInputParams(e);return this._def.getter()._parse({data:t.data,path:t.path,parent:t})}}ej.create=(e,t)=>new ej({getter:e,typeName:d.ZodLazy,...X(t)});class eL extends H{_parse(e){if(e.data!==this._def.value){let t=this._getOrReturnCtx(e);return D(t,{received:t.data,code:k.invalid_literal,expected:this._def.value}),L}return{status:"valid",value:e.data}}get value(){return this._def.value}}function eM(e,t){return new eU({values:e,typeName:d.ZodEnum,...X(t)})}eL.create=(e,t)=>new eL({value:e,typeName:d.ZodLiteral,...X(t)});class eU extends H{constructor(){super(...arguments),o.set(this,void 0)}_parse(e){if("string"!=typeof e.data){let t=this._getOrReturnCtx(e),a=this._def.values;return D(t,{expected:n.joinValues(a),received:t.parsedType,code:k.invalid_type}),L}if(B(this,o,"f")||W(this,o,new Set(this._def.values),"f"),!B(this,o,"f").has(e.data)){let t=this._getOrReturnCtx(e),a=this._def.values;return D(t,{received:t.data,code:k.invalid_enum_value,options:a}),L}return U(e.data)}get options(){return this._def.values}get enum(){let e={};for(let t of this._def.values)e[t]=t;return e}get Values(){let e={};for(let t of this._def.values)e[t]=t;return e}get Enum(){let e={};for(let t of this._def.values)e[t]=t;return e}extract(e,t=this._def){return eU.create(e,{...this._def,...t})}exclude(e,t=this._def){return eU.create(this.options.filter(t=>!e.includes(t)),{...this._def,...t})}}o=new WeakMap,eU.create=eM;class eF extends H{constructor(){super(...arguments),l.set(this,void 0)}_parse(e){let t=n.getValidEnumValues(this._def.values),a=this._getOrReturnCtx(e);if(a.parsedType!==O.string&&a.parsedType!==O.number){let e=n.objectValues(t);return D(a,{expected:n.joinValues(e),received:a.parsedType,code:k.invalid_type}),L}if(B(this,l,"f")||W(this,l,new Set(n.getValidEnumValues(this._def.values)),"f"),!B(this,l,"f").has(e.data)){let e=n.objectValues(t);return D(a,{received:a.data,code:k.invalid_enum_value,options:e}),L}return U(e.data)}get enum(){return this._def.values}}l=new WeakMap,eF.create=(e,t)=>new eF({values:e,typeName:d.ZodNativeEnum,...X(t)});class eq extends H{unwrap(){return this._def.type}_parse(e){let{ctx:t}=this._processInputParams(e);return t.parsedType!==O.promise&&!1===t.common.async?(D(t,{code:k.invalid_type,expected:O.promise,received:t.parsedType}),L):U((t.parsedType===O.promise?t.data:Promise.resolve(t.data)).then(e=>this._def.type.parseAsync(e,{path:t.path,errorMap:t.common.contextualErrorMap})))}}eq.create=(e,t)=>new eq({type:e,typeName:d.ZodPromise,...X(t)});class ez extends H{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===d.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(e){let{status:t,ctx:a}=this._processInputParams(e),r=this._def.effect||null,s={addIssue:e=>{D(a,e),e.fatal?t.abort():t.dirty()},get path(){return a.path}};if(s.addIssue=s.addIssue.bind(s),"preprocess"===r.type){let e=r.transform(a.data,s);if(a.common.async)return Promise.resolve(e).then(async e=>{if("aborted"===t.value)return L;let r=await this._def.schema._parseAsync({data:e,path:a.path,parent:a});return"aborted"===r.status?L:"dirty"===r.status||"dirty"===t.value?M(r.value):r});{if("aborted"===t.value)return L;let r=this._def.schema._parseSync({data:e,path:a.path,parent:a});return"aborted"===r.status?L:"dirty"===r.status||"dirty"===t.value?M(r.value):r}}if("refinement"===r.type){let e=e=>{let t=r.refinement(e,s);if(a.common.async)return Promise.resolve(t);if(t instanceof Promise)throw Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return e};if(!1!==a.common.async)return this._def.schema._parseAsync({data:a.data,path:a.path,parent:a}).then(a=>"aborted"===a.status?L:("dirty"===a.status&&t.dirty(),e(a.value).then(()=>({status:t.value,value:a.value}))));{let r=this._def.schema._parseSync({data:a.data,path:a.path,parent:a});return"aborted"===r.status?L:("dirty"===r.status&&t.dirty(),e(r.value),{status:t.value,value:r.value})}}if("transform"===r.type)if(!1!==a.common.async)return this._def.schema._parseAsync({data:a.data,path:a.path,parent:a}).then(e=>z(e)?Promise.resolve(r.transform(e.value,s)).then(e=>({status:t.value,value:e})):e);else{let e=this._def.schema._parseSync({data:a.data,path:a.path,parent:a});if(!z(e))return e;let n=r.transform(e.value,s);if(n instanceof Promise)throw Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:t.value,value:n}}n.assertNever(r)}}ez.create=(e,t,a)=>new ez({schema:e,typeName:d.ZodEffects,effect:t,...X(a)}),ez.createWithPreprocess=(e,t,a)=>new ez({schema:t,effect:{type:"preprocess",transform:e},typeName:d.ZodEffects,...X(a)});class eZ extends H{_parse(e){return this._getType(e)===O.undefined?U(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}eZ.create=(e,t)=>new eZ({innerType:e,typeName:d.ZodOptional,...X(t)});class eB extends H{_parse(e){return this._getType(e)===O.null?U(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}eB.create=(e,t)=>new eB({innerType:e,typeName:d.ZodNullable,...X(t)});class eW extends H{_parse(e){let{ctx:t}=this._processInputParams(e),a=t.data;return t.parsedType===O.undefined&&(a=this._def.defaultValue()),this._def.innerType._parse({data:a,path:t.path,parent:t})}removeDefault(){return this._def.innerType}}eW.create=(e,t)=>new eW({innerType:e,typeName:d.ZodDefault,defaultValue:"function"==typeof t.default?t.default:()=>t.default,...X(t)});class eV extends H{_parse(e){let{ctx:t}=this._processInputParams(e),a={...t,common:{...t.common,issues:[]}},r=this._def.innerType._parse({data:a.data,path:a.path,parent:{...a}});return Z(r)?r.then(e=>({status:"valid",value:"valid"===e.status?e.value:this._def.catchValue({get error(){return new R(a.common.issues)},input:a.data})})):{status:"valid",value:"valid"===r.status?r.value:this._def.catchValue({get error(){return new R(a.common.issues)},input:a.data})}}removeCatch(){return this._def.innerType}}eV.create=(e,t)=>new eV({innerType:e,typeName:d.ZodCatch,catchValue:"function"==typeof t.catch?t.catch:()=>t.catch,...X(t)});class eJ extends H{_parse(e){if(this._getType(e)!==O.nan){let t=this._getOrReturnCtx(e);return D(t,{code:k.invalid_type,expected:O.nan,received:t.parsedType}),L}return{status:"valid",value:e.data}}}eJ.create=e=>new eJ({typeName:d.ZodNaN,...X(e)}),Symbol("zod_brand");class eX extends H{_parse(e){let{ctx:t}=this._processInputParams(e),a=t.data;return this._def.type._parse({data:a,path:t.path,parent:t})}unwrap(){return this._def.type}}class eH extends H{_parse(e){let{status:t,ctx:a}=this._processInputParams(e);if(a.common.async)return(async()=>{let e=await this._def.in._parseAsync({data:a.data,path:a.path,parent:a});return"aborted"===e.status?L:"dirty"===e.status?(t.dirty(),M(e.value)):this._def.out._parseAsync({data:e.value,path:a.path,parent:a})})();{let e=this._def.in._parseSync({data:a.data,path:a.path,parent:a});return"aborted"===e.status?L:"dirty"===e.status?(t.dirty(),{status:"dirty",value:e.value}):this._def.out._parseSync({data:e.value,path:a.path,parent:a})}}static create(e,t){return new eH({in:e,out:t,typeName:d.ZodPipeline})}}class eG extends H{_parse(e){let t=this._def.innerType._parse(e),a=e=>(z(e)&&(e.value=Object.freeze(e.value)),e);return Z(t)?t.then(e=>a(e)):a(t)}unwrap(){return this._def.innerType}}eG.create=(e,t)=>new eG({innerType:e,typeName:d.ZodReadonly,...X(t)}),eI.lazycreate,function(e){e.ZodString="ZodString",e.ZodNumber="ZodNumber",e.ZodNaN="ZodNaN",e.ZodBigInt="ZodBigInt",e.ZodBoolean="ZodBoolean",e.ZodDate="ZodDate",e.ZodSymbol="ZodSymbol",e.ZodUndefined="ZodUndefined",e.ZodNull="ZodNull",e.ZodAny="ZodAny",e.ZodUnknown="ZodUnknown",e.ZodNever="ZodNever",e.ZodVoid="ZodVoid",e.ZodArray="ZodArray",e.ZodObject="ZodObject",e.ZodUnion="ZodUnion",e.ZodDiscriminatedUnion="ZodDiscriminatedUnion",e.ZodIntersection="ZodIntersection",e.ZodTuple="ZodTuple",e.ZodRecord="ZodRecord",e.ZodMap="ZodMap",e.ZodSet="ZodSet",e.ZodFunction="ZodFunction",e.ZodLazy="ZodLazy",e.ZodLiteral="ZodLiteral",e.ZodEnum="ZodEnum",e.ZodEffects="ZodEffects",e.ZodNativeEnum="ZodNativeEnum",e.ZodOptional="ZodOptional",e.ZodNullable="ZodNullable",e.ZodDefault="ZodDefault",e.ZodCatch="ZodCatch",e.ZodPromise="ZodPromise",e.ZodBranded="ZodBranded",e.ZodPipeline="ZodPipeline",e.ZodReadonly="ZodReadonly"}(d||(d={}));let eY=eh.create,eK=em.create;eJ.create,ef.create,eg.create,ey.create,ex.create;let eQ=e_.create,e0=ev.create;eb.create,ew.create,eE.create,eA.create;let e1=eS.create,e3=eI.create;eI.strictCreate;let e2=eN.create;eO.create,eC.create,ek.create,eR.create,eP.create,e$.create,eD.create,ej.create;let e9=eL.create;eU.create,eF.create,eq.create,ez.create,eZ.create,eB.create,ez.createWithPreprocess,eH.create;let e4=e3({id:eY().optional(),titulo:eY().min(1).max(200),contenido:eY().min(1),categoria:eY().optional().nullable(),numero_tema:e2([eK().int().positive(),eY(),e0(),eQ()]).optional(),creado_en:eY().optional(),actualizado_en:eY().optional(),user_id:eY().optional(),tipo_original:eY().optional()}),e5=e3({pregunta:eY().min(1).max(500),documentos:e1(e4).min(1)}),e6=e3({action:e9("generarTest"),peticion:eY().min(1).max(500),contextos:e1(eY().min(1)),cantidad:eK().int().min(1).max(50).optional()}),e8=e3({action:e9("generarFlashcards"),peticion:eY().min(1).max(500),contextos:e1(eY().min(1)),cantidad:eK().int().min(1).max(50).optional()}),e7=e3({action:e9("generarMapaMental"),peticion:eY().min(1).max(500),contextos:e1(eY().min(1))}),te=e3({action:e9("generarPlanEstudios"),peticion:eY().min(1),contextos:e1(eY()).optional()}),tt=e2([e5,e6,e8,e7,te,e3({action:e9("generarResumen"),peticion:eY().min(1).max(1e3),contextos:e1(eY().min(1)).length(1)}),e3({action:e9("editarResumen"),contextos:e1(eY().min(1)).length(1)})]);var ta=a(32571),tr=a(89546),tn=a(42049);async function ts(e,t,a=1){try{let r=await tn.SupabaseAdminService.getUserProfile(e);r?.subscription_plan==="free"&&(await tr.FreeAccountService.incrementUsageCount(e,t,a)?console.log(`✅ Incremented ${t} usage by ${a} for free user ${e}`):console.warn(`⚠️ Failed to increment ${t} usage count for user ${e}`))}catch(t){console.error(`❌ Error incrementing usage for user ${e}:`,t)}}async function ti(e){try{let t,a=await (0,f.createServerSupabaseClient)(),{data:{user:r},error:n}=await a.auth.getUser();if(!r)return m.NextResponse.json({error:"Unauthorized",debug:{userError:n?.message,hasCookies:!!e.headers.get("cookie")}},{status:401});let s=await e.json(),i=tt.safeParse(s);if(!i.success)return m.NextResponse.json({error:"Datos inv\xe1lidos",detalles:i.error.errors},{status:400});if(s.pregunta&&s.documentos){let e=await ta.o.canUserPerformAction(r.id,"ai_chat",1);if(!e.allowed)return m.NextResponse.json({error:"Acceso denegado: "+e.reason},{status:403});let t=await v(s.pregunta,s.documentos);return m.NextResponse.json({result:t})}let{action:o,peticion:l,contextos:d,cantidad:c,temarioId:u}=s;switch(o){case"generarTest":let p=await ta.o.canUserPerformAction(r.id,"test_generation",c||1);if(!p.allowed)return m.NextResponse.json({error:"Acceso denegado: "+p.reason},{status:403});t=await E(l,d,c),await ts(r.id,"tests",c||1);break;case"generarFlashcards":let h=await ta.o.canUserPerformAction(r.id,"flashcard_generation",c||1);if(!h.allowed)return m.NextResponse.json({error:"Acceso denegado: "+h.reason},{status:403});t=await b(l,d,c),await ts(r.id,"flashcards",c||1);break;case"generarMapaMental":let g=await ta.o.canUserPerformAction(r.id,"mind_map_generation",1);if(!g.allowed)return m.NextResponse.json({error:"Acceso denegado: "+g.reason},{status:403});t=await w(l,d),await ts(r.id,"mindMaps",1);break;case"generarResumen":let y=await ta.o.canUserPerformAction(r.id,"summary_generation",1);if(!y.allowed)return m.NextResponse.json({error:"Acceso denegado: "+y.reason},{status:403});if(!d||1!==d.length)throw Error("Se requiere exactamente un documento para generar un resumen");let x={titulo:l.split("|")[0]||"Documento sin t\xedtulo",contenido:d[0],categoria:l.split("|")[1],numero_tema:l.split("|")[2]?parseInt(l.split("|")[2]):void 0},_=l.split("|")[3]||void 0;t=await A(x,_);break;case"editarResumen":if(!d[0])throw Error("Se requiere el contenido del resumen para editarlo");t=await T(d[0]);break;case"generarPlanEstudios":let S=await ta.o.canUserPerformAction(r.id,"study_planning",1);if(!S.allowed)return m.NextResponse.json({error:"Acceso denegado: "+S.reason},{status:403});let I=l||u;if(!I)throw Error("Se requiere temarioId para generar el plan de estudios");t=await N(I,r);break;default:return m.NextResponse.json({error:"Acci\xf3n no soportada"},{status:400})}return m.NextResponse.json({result:t})}catch(e){return console.error("❌ Error en API AI:",e),m.NextResponse.json({error:"Error interno del servidor",detalles:e.message},{status:500})}}let to=new u.AppRouteRouteModule({definition:{kind:p.RouteKind.APP_ROUTE,page:"/api/ai/route",pathname:"/api/ai",filename:"route",bundlePath:"app/api/ai/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\ai\\route.ts",nextConfigOutput:"",userland:c}),{workAsyncStorage:tl,workUnitAsyncStorage:td,serverHooks:tc}=to;function tu(){return(0,h.patchFetch)({workAsyncStorage:tl,workUnitAsyncStorage:td})}},11997:e=>{e.exports=require("punycode")},17118:(e,t,a)=>{a.r(t),a.d(t,{generarTest:()=>o});var r=a(90787),n=a(85905),s=a(36191),i=a(36446);async function o(e,t=10,a){try{let o=(0,r.Jo)(e);if(!o)throw Error("No se han proporcionado documentos para generar el test.");let l=n.fD.replace("{documentos}",o).replace("{cantidad}",t.toString());l=a?l.replace("{instrucciones}",`Instrucciones adicionales:
- ${a}`):l.replace("{instrucciones}","");let d=(0,i.Vj)("TESTS");console.log(`🧪 Generando test con modelo: ${d.model} (max_tokens: ${d.max_tokens})`);let c=[{role:"user",content:l}],u=await (0,s.y5)(c,{...d,activityName:`Generaci\xf3n de Test (${t||"N/A"} preguntas)`}),p=u.match(/\[\s*\{[\s\S]*\}\s*\]/);if(!p)throw console.log("❌ No se encontr\xf3 JSON en la respuesta. Respuesta recibida:",u.substring(0,500)),Error("No se pudo extraer el formato JSON de la respuesta.");let h=p[0].replace(/"opcion([abcd])"/g,'"opcion_$1"').replace(/"opciona"/g,'"opcion_a"').replace(/"opcionb"/g,'"opcion_b"').replace(/"opcionc"/g,'"opcion_c"').replace(/"opciond"/g,'"opcion_d"'),m=JSON.parse(h);if(console.log(`📊 Preguntas generadas: ${m.length} de ${t} solicitadas`),!Array.isArray(m)||0===m.length)throw Error("El formato de las preguntas generadas no es v\xe1lido.");return m.length!==t&&console.log(`⚠️ Advertencia: Se generaron ${m.length} preguntas en lugar de ${t}`),m.forEach((e,t)=>{if(!e.pregunta||!e.opcion_a||!e.opcion_b||!e.opcion_c||!e.opcion_d||!e.respuesta_correcta)throw Error(`La pregunta ${t+1} no tiene el formato correcto.`);if(!["a","b","c","d"].includes(e.respuesta_correcta))throw Error(`La respuesta correcta de la pregunta ${t+1} no es v\xe1lida.`)}),m}catch(e){throw console.error("Error al generar test:",e),e}}},27910:e=>{e.exports=require("stream")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32571:(e,t,a)=>{a.d(t,{o:()=>o});var r=a(42049),n=a(8814),s=a(89546);class i{static async logWebhookEvent(e){try{let t=e.success?"✅":"❌",a=new Date().toISOString();console.log(`${t} [WEBHOOK] ${a}`,{eventType:e.eventType,eventId:e.eventId,success:e.success,processingTime:`${e.processingTime}ms`,message:e.message,...e.error&&{error:e.error},...e.data&&{data:e.data}}),await this.logToExternalService(e)}catch(e){console.error("Error logging webhook event:",e)}}static async logFeatureAccess(e,t,r,n,s=0,i){try{if(process.env.SUPABASE_SERVICE_ROLE_KEY){let{SupabaseAdminService:o}=await Promise.resolve().then(a.bind(a,42049));await o.logFeatureAccess({user_id:e,feature_name:t,access_granted:r,plan_at_time:n,tokens_used:s,denial_reason:i})}let o=r?"✅":"❌";console.log(`${o} [FEATURE_ACCESS]`,{userId:e,feature:t,granted:r,plan:n,tokens:s,...i&&{reason:i}})}catch(e){console.error("Error logging feature access:",e)}}static async logPlanChange(e,t,r,n,s,i){try{if(process.env.SUPABASE_SERVICE_ROLE_KEY){let{SupabaseAdminService:o}=await Promise.resolve().then(a.bind(a,42049));await o.logPlanChange({user_id:e,old_plan:t||void 0,new_plan:r,changed_by:n,reason:s,transaction_id:i})}console.log("\uD83D\uDD04 [PLAN_CHANGE]",{userId:e,oldPlan:t,newPlan:r,changedBy:n,reason:s,transactionId:i})}catch(e){console.error("Error logging plan change:",e)}}static async logCriticalError(e,t,a){try{let r={context:e,message:t.message,stack:t.stack,timestamp:new Date().toISOString(),additionalData:a};console.error("\uD83D\uDEA8 [CRITICAL_ERROR]",r),await this.sendCriticalAlert(r)}catch(e){console.error("Error logging critical error:",e)}}static logPerformanceMetrics(e,t,a,r){let n={operation:e,duration:`${t}ms`,success:a,timestamp:new Date().toISOString(),...r};console.log("\uD83D\uDCCA [PERFORMANCE]",n),this.sendMetrics(n)}static async logToExternalService(e){}static async sendCriticalAlert(e){}static sendMetrics(e){"true"===process.env.ENABLE_METRICS_LOGGING&&console.log("\uD83D\uDCC8 [METRICS_EXPORT]",e)}static async getWebhookStats(e="day"){return{totalEvents:0,successRate:0,averageProcessingTime:0,errorsByType:{}}}}class o{static async validateFeatureAccess(e,t,a=0){try{let s=await r.SupabaseAdminService.getUserProfile(e);if(!s)return await i.logFeatureAccess(e,t,!1,"unknown",0,"User profile not found"),{allowed:!1,reason:"Perfil de usuario no encontrado"};if("free"!==s.subscription_plan&&!s.payment_verified)return await i.logFeatureAccess(e,t,!1,s.subscription_plan,0,"Payment not verified"),{allowed:!1,reason:"Pago no verificado. Complete el proceso de pago para acceder a esta caracter\xedstica."};if(!(0,n.Nu)(s.subscription_plan,t))return await i.logFeatureAccess(e,t,!1,s.subscription_plan,0,`Feature not available in ${s.subscription_plan} plan`),{allowed:!1,reason:`La caracter\xedstica ${t} no est\xe1 disponible en su plan ${s.subscription_plan}`};if(a>0){let r=await this.validateTokenUsage(s,a);if(!r.allowed)return await i.logFeatureAccess(e,t,!1,s.subscription_plan,a,r.reason),r}return await i.logFeatureAccess(e,t,!0,s.subscription_plan,a),{allowed:!0,remainingUsage:s.monthly_token_limit-s.current_month_tokens,planLimits:{monthlyTokens:s.monthly_token_limit,currentTokens:s.current_month_tokens}}}catch(r){return console.error("Error validating feature access:",r),await i.logFeatureAccess(e,t,!1,"error",a,"Internal validation error"),{allowed:!1,reason:"Error interno de validaci\xf3n"}}}static async validateTokenUsage(e,t){let a=new Date().toISOString().slice(0,7)+"-01";e.current_month!==a&&(await r.SupabaseAdminService.upsertUserProfile({...e,current_month_tokens:0,current_month:a,updated_at:new Date().toISOString()}),e.current_month_tokens=0,e.current_month=a);let n=e.current_month_tokens+t;return n>e.monthly_token_limit?{allowed:!1,reason:`L\xedmite mensual de tokens alcanzado. Usado: ${e.current_month_tokens}/${e.monthly_token_limit}`,remainingUsage:Math.max(0,e.monthly_token_limit-e.current_month_tokens)}:{allowed:!0,remainingUsage:e.monthly_token_limit-n}}static async getUserAccessInfo(e){try{let t=await r.SupabaseAdminService.getUserProfile(e);if(!t)return null;let s=(0,n.IE)(t.subscription_plan);if(!s)return null;let i={tokens:t.current_month_tokens,tokenLimit:t.monthly_token_limit,month:t.current_month,documents:0,tests:0,flashcards:0,mindMaps:0};try{let t=await r.SupabaseAdminService.getDocumentsCount(e);i.documents=t}catch(e){console.error("Error getting documents count:",e)}if("free"===t.subscription_plan)try{let{FreeAccountService:t}=await Promise.resolve().then(a.bind(a,89546)),r=await t.getFreeAccountStatus(e);r&&(i={...i,tests:r.usageCount.tests||0,flashcards:r.usageCount.flashcards||0,mindMaps:r.usageCount.mindMaps||0})}catch(e){console.error("Error getting free account usage:",e)}let o={...s.limits,tests:s.limits.testsPerWeek??0,flashcards:s.limits.flashcardsPerWeek??0,mindMaps:s.limits.mindMapsPerWeek??0};return{userId:e,plan:t.subscription_plan,paymentVerified:t.payment_verified,features:s.features||[],limits:o,currentUsage:i}}catch(e){return console.error("Error getting user access info:",e),null}}static async canUserPerformAction(e,t,a=1){try{let n=await r.SupabaseAdminService.getUserProfile(e);if(!n)return{allowed:!1,reason:"Usuario no encontrado"};let i=await this.validateFeatureAccess(e,{test_generation:"test_generation",flashcard_generation:"flashcard_generation",mind_map_generation:"mind_map_generation",ai_chat:"ai_tutor_chat",study_planning:"study_planning",summary_generation:"summary_a1_a2"}[t]);if(!i.allowed)return i;if("free"===n.subscription_plan){let r={test_generation:"tests",flashcard_generation:"flashcards",mind_map_generation:"mindMaps"}[t];if(r){let n=await s.FreeAccountService.canPerformAction(e,r,a);if(!n.allowed)return{allowed:!1,reason:n.reason||`L\xedmite alcanzado para ${t}`,remainingUsage:n.remaining}}}return{allowed:!0,remainingUsage:i.remainingUsage}}catch(e){return console.error("Error checking user action:",e),{allowed:!1,reason:"Error interno de validaci\xf3n"}}}static async updateTokenUsage(e,t,a){try{let a=await r.SupabaseAdminService.getUserProfile(e);if(!a)return!1;let n=a.current_month_tokens+t;return await r.SupabaseAdminService.upsertUserProfile({...a,current_month_tokens:n,updated_at:new Date().toISOString()}),console.log(`✅ Tokens actualizados para usuario ${e}: +${t} (Total: ${n}/${a.monthly_token_limit})`),!0}catch(e){return console.error("Error updating token usage:",e),!1}}static async checkUpgradeNeeded(e){try{let t=await r.SupabaseAdminService.getUserProfile(e);if(!t)return{needsUpgrade:!1};let a=t.current_month_tokens/t.monthly_token_limit*100;if(a>=90){let e="free"===t.subscription_plan?"usuario":"pro";return{needsUpgrade:!0,reason:`Has usado el ${a.toFixed(1)}% de tus tokens mensuales`,suggestedPlan:e}}return{needsUpgrade:!1}}catch(e){return console.error("Error checking upgrade need:",e),{needsUpgrade:!1}}}}},33503:(e,t,a)=>{a.r(t),a.d(t,{generarResumen:()=>o});var r=a(90787),n=a(85905),s=a(36191),i=a(36446);async function o(e,t){try{if(!e||!e.contenido)throw Error("No se ha proporcionado un documento v\xe1lido para generar el resumen.");if(0===e.contenido.trim().length)throw Error("El contenido del documento est\xe1 vac\xedo.");let a=[e],o=(0,r.Jo)(a);if(!o)throw Error("No se pudo preparar el contenido del documento.");let l=t?.trim()||"Crea un resumen completo y estructurado del tema proporcionado.",d=n.RT.replace("{titulo_del_tema}",e.titulo||"Tema sin t\xedtulo");d=(d=d.replace("{documento}",o)).replace("{instrucciones}",l);let c=(0,i.Vj)("RESUMENES");console.log(`📄 Generando resumen con modelo: ${c.model} (max_tokens: ${c.max_tokens})`);let u=[{role:"user",content:d}],p=await (0,s.y5)(u,{...c,activityName:"Generaci\xf3n de Resumen"});if(!p||0===p.trim().length)throw Error("La IA no gener\xf3 ning\xfan contenido para el resumen.");let h=p.trim();if(h.length<100)throw Error("El resumen generado es demasiado corto. Por favor, int\xe9ntalo de nuevo.");return console.log("✅ Resumen generado exitosamente"),h}catch(e){if(console.error("Error al generar resumen:",e),e instanceof Error)throw Error(`Error al generar el resumen: ${e.message}`);throw Error("Ha ocurrido un error inesperado al generar el resumen.")}}},34631:e=>{e.exports=require("tls")},36191:(e,t,a)=>{var r,n,s,i,o,l,d,c,u,p,h,m,f,g,y,x,_,v,b,w,E,A,S,I,N,T,O,C,k,R,P,$,D,j,L,M,U,F,q,z,Z,B,W,V,J,X,H,G,Y,K,Q,ee,et,ea,er,en,es,ei,eo,el,ed,ec,eu,ep,eh,em,ef,eg,ey,ex;let e_,ev,eb;function ew(e,t,a,r,n){if("m"===r)throw TypeError("Private method is not writable");if("a"===r&&!n)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===r?n.call(e,a):n?n.value=a:t.set(e,a),a}function eE(e,t,a,r){if("a"===a&&!r)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!r:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===a?r:"a"===a?r.call(e):r?r.value:t.get(e)}a.d(t,{y5:()=>a7,Jo:()=>a8});let eA=function(){let{crypto:e}=globalThis;if(e?.randomUUID)return eA=e.randomUUID.bind(e),e.randomUUID();let t=new Uint8Array(1),a=e?()=>e.getRandomValues(t)[0]:()=>255*Math.random()&255;return"10000000-1000-4000-8000-100000000000".replace(/[018]/g,e=>(e^a()&15>>e/4).toString(16))};function eS(e){return"object"==typeof e&&null!==e&&("name"in e&&"AbortError"===e.name||"message"in e&&String(e.message).includes("FetchRequestCanceledException"))}let eI=e=>{if(e instanceof Error)return e;if("object"==typeof e&&null!==e){try{if("[object Error]"===Object.prototype.toString.call(e)){let t=Error(e.message,e.cause?{cause:e.cause}:{});return e.stack&&(t.stack=e.stack),e.cause&&!t.cause&&(t.cause=e.cause),e.name&&(t.name=e.name),t}}catch{}try{return Error(JSON.stringify(e))}catch{}}return Error(e)};class eN extends Error{}class eT extends eN{constructor(e,t,a,r){super(`${eT.makeMessage(e,t,a)}`),this.status=e,this.headers=r,this.requestID=r?.get("x-request-id"),this.error=t,this.code=t?.code,this.param=t?.param,this.type=t?.type}static makeMessage(e,t,a){let r=t?.message?"string"==typeof t.message?t.message:JSON.stringify(t.message):t?JSON.stringify(t):a;return e&&r?`${e} ${r}`:e?`${e} status code (no body)`:r||"(no status code or body)"}static generate(e,t,a,r){if(!e||!r)return new eC({message:a,cause:eI(t)});let n=t?.error;return 400===e?new eR(e,n,a,r):401===e?new eP(e,n,a,r):403===e?new e$(e,n,a,r):404===e?new eD(e,n,a,r):409===e?new ej(e,n,a,r):422===e?new eL(e,n,a,r):429===e?new eM(e,n,a,r):e>=500?new eU(e,n,a,r):new eT(e,n,a,r)}}class eO extends eT{constructor({message:e}={}){super(void 0,void 0,e||"Request was aborted.",void 0)}}class eC extends eT{constructor({message:e,cause:t}){super(void 0,void 0,e||"Connection error.",void 0),t&&(this.cause=t)}}class ek extends eC{constructor({message:e}={}){super({message:e??"Request timed out."})}}class eR extends eT{}class eP extends eT{}class e$ extends eT{}class eD extends eT{}class ej extends eT{}class eL extends eT{}class eM extends eT{}class eU extends eT{}class eF extends eN{constructor(){super("Could not parse response content as the length limit was reached")}}class eq extends eN{constructor(){super("Could not parse response content as the request was rejected by the content filter")}}let ez=/^[a-z][a-z0-9+.-]*:/i,eZ=e=>ez.test(e);function eB(e){return null!=e&&"object"==typeof e&&!Array.isArray(e)}let eW=(e,t)=>{if("number"!=typeof t||!Number.isInteger(t))throw new eN(`${e} must be an integer`);if(t<0)throw new eN(`${e} must be a positive integer`);return t},eV=e=>{try{return JSON.parse(e)}catch(e){return}},eJ=e=>new Promise(t=>setTimeout(t,e)),eX={off:0,error:200,warn:300,info:400,debug:500},eH=(e,t,a)=>{if(e){if(Object.prototype.hasOwnProperty.call(eX,e))return e;e0(a).warn(`${t} was set to ${JSON.stringify(e)}, expected one of ${JSON.stringify(Object.keys(eX))}`)}};function eG(){}function eY(e,t,a){return!t||eX[e]>eX[a]?eG:t[e].bind(t)}let eK={error:eG,warn:eG,info:eG,debug:eG},eQ=new WeakMap;function e0(e){let t=e.logger,a=e.logLevel??"off";if(!t)return eK;let r=eQ.get(t);if(r&&r[0]===a)return r[1];let n={error:eY("error",t,a),warn:eY("warn",t,a),info:eY("info",t,a),debug:eY("debug",t,a)};return eQ.set(t,[a,n]),n}let e1=e=>(e.options&&(e.options={...e.options},delete e.options.headers),e.headers&&(e.headers=Object.fromEntries((e.headers instanceof Headers?[...e.headers]:Object.entries(e.headers)).map(([e,t])=>[e,"authorization"===e.toLowerCase()||"cookie"===e.toLowerCase()||"set-cookie"===e.toLowerCase()?"***":t]))),"retryOfRequestLogID"in e&&(e.retryOfRequestLogID&&(e.retryOf=e.retryOfRequestLogID),delete e.retryOfRequestLogID),e),e3="5.1.1",e2=()=>"undefined"!=typeof window&&void 0!==window.document&&"undefined"!=typeof navigator,e9=()=>{let e="undefined"!=typeof Deno&&null!=Deno.build?"deno":"undefined"!=typeof EdgeRuntime?"edge":"[object process]"===Object.prototype.toString.call(void 0!==globalThis.process?globalThis.process:0)?"node":"unknown";if("deno"===e)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":e3,"X-Stainless-OS":e5(Deno.build.os),"X-Stainless-Arch":e4(Deno.build.arch),"X-Stainless-Runtime":"deno","X-Stainless-Runtime-Version":"string"==typeof Deno.version?Deno.version:Deno.version?.deno??"unknown"};if("undefined"!=typeof EdgeRuntime)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":e3,"X-Stainless-OS":"Unknown","X-Stainless-Arch":`other:${EdgeRuntime}`,"X-Stainless-Runtime":"edge","X-Stainless-Runtime-Version":globalThis.process.version};if("node"===e)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":e3,"X-Stainless-OS":e5(globalThis.process.platform??"unknown"),"X-Stainless-Arch":e4(globalThis.process.arch??"unknown"),"X-Stainless-Runtime":"node","X-Stainless-Runtime-Version":globalThis.process.version??"unknown"};let t=function(){if("undefined"==typeof navigator||!navigator)return null;for(let{key:e,pattern:t}of[{key:"edge",pattern:/Edge(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/MSIE(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/Trident(?:.*rv\:(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"chrome",pattern:/Chrome(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"firefox",pattern:/Firefox(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"safari",pattern:/(?:Version\W+(\d+)\.(\d+)(?:\.(\d+))?)?(?:\W+Mobile\S*)?\W+Safari/}]){let a=t.exec(navigator.userAgent);if(a){let t=a[1]||0,r=a[2]||0,n=a[3]||0;return{browser:e,version:`${t}.${r}.${n}`}}}return null}();return t?{"X-Stainless-Lang":"js","X-Stainless-Package-Version":e3,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":`browser:${t.browser}`,"X-Stainless-Runtime-Version":t.version}:{"X-Stainless-Lang":"js","X-Stainless-Package-Version":e3,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":"unknown","X-Stainless-Runtime-Version":"unknown"}},e4=e=>"x32"===e?"x32":"x86_64"===e||"x64"===e?"x64":"arm"===e?"arm":"aarch64"===e||"arm64"===e?"arm64":e?`other:${e}`:"unknown",e5=e=>(e=e.toLowerCase()).includes("ios")?"iOS":"android"===e?"Android":"darwin"===e?"MacOS":"win32"===e?"Windows":"freebsd"===e?"FreeBSD":"openbsd"===e?"OpenBSD":"linux"===e?"Linux":e?`Other:${e}`:"Unknown",e6=()=>e_??(e_=e9());function e8(...e){let t=globalThis.ReadableStream;if(void 0===t)throw Error("`ReadableStream` is not defined as a global; You will need to polyfill it, `globalThis.ReadableStream = ReadableStream`");return new t(...e)}function e7(e){let t=Symbol.asyncIterator in e?e[Symbol.asyncIterator]():e[Symbol.iterator]();return e8({start(){},async pull(e){let{done:a,value:r}=await t.next();a?e.close():e.enqueue(r)},async cancel(){await t.return?.()}})}function te(e){if(e[Symbol.asyncIterator])return e;let t=e.getReader();return{async next(){try{let e=await t.read();return e?.done&&t.releaseLock(),e}catch(e){throw t.releaseLock(),e}},async return(){let e=t.cancel();return t.releaseLock(),await e,{done:!0,value:void 0}},[Symbol.asyncIterator](){return this}}}async function tt(e){if(null===e||"object"!=typeof e)return;if(e[Symbol.asyncIterator])return void await e[Symbol.asyncIterator]().return?.();let t=e.getReader(),a=t.cancel();t.releaseLock(),await a}let ta=({headers:e,body:t})=>({bodyHeaders:{"content-type":"application/json"},body:JSON.stringify(t)}),tr="RFC3986",tn={RFC1738:e=>String(e).replace(/%20/g,"+"),RFC3986:e=>String(e)},ts=(Object.prototype.hasOwnProperty,Array.isArray),ti=(()=>{let e=[];for(let t=0;t<256;++t)e.push("%"+((t<16?"0":"")+t.toString(16)).toUpperCase());return e})();function to(e,t){if(ts(e)){let a=[];for(let r=0;r<e.length;r+=1)a.push(t(e[r]));return a}return t(e)}let tl=Object.prototype.hasOwnProperty,td={brackets:e=>String(e)+"[]",comma:"comma",indices:(e,t)=>String(e)+"["+t+"]",repeat:e=>String(e)},tc=Array.isArray,tu=Array.prototype.push,tp=function(e,t){tu.apply(e,tc(t)?t:[t])},th=Date.prototype.toISOString,tm={addQueryPrefix:!1,allowDots:!1,allowEmptyArrays:!1,arrayFormat:"indices",charset:"utf-8",charsetSentinel:!1,delimiter:"&",encode:!0,encodeDotInKeys:!1,encoder:(e,t,a,r,n)=>{if(0===e.length)return e;let s=e;if("symbol"==typeof e?s=Symbol.prototype.toString.call(e):"string"!=typeof e&&(s=String(e)),"iso-8859-1"===a)return escape(s).replace(/%u[0-9a-f]{4}/gi,function(e){return"%26%23"+parseInt(e.slice(2),16)+"%3B"});let i="";for(let e=0;e<s.length;e+=1024){let t=s.length>=1024?s.slice(e,e+1024):s,a=[];for(let e=0;e<t.length;++e){let r=t.charCodeAt(e);if(45===r||46===r||95===r||126===r||r>=48&&r<=57||r>=65&&r<=90||r>=97&&r<=122||"RFC1738"===n&&(40===r||41===r)){a[a.length]=t.charAt(e);continue}if(r<128){a[a.length]=ti[r];continue}if(r<2048){a[a.length]=ti[192|r>>6]+ti[128|63&r];continue}if(r<55296||r>=57344){a[a.length]=ti[224|r>>12]+ti[128|r>>6&63]+ti[128|63&r];continue}e+=1,r=65536+((1023&r)<<10|1023&t.charCodeAt(e)),a[a.length]=ti[240|r>>18]+ti[128|r>>12&63]+ti[128|r>>6&63]+ti[128|63&r]}i+=a.join("")}return i},encodeValuesOnly:!1,format:tr,formatter:tn[tr],indices:!1,serializeDate:e=>th.call(e),skipNulls:!1,strictNullHandling:!1},tf={};function tg(e){let t;return(ev??(ev=(t=new globalThis.TextEncoder).encode.bind(t)))(e)}function ty(e){let t;return(eb??(eb=(t=new globalThis.TextDecoder).decode.bind(t)))(e)}class tx{constructor(){r.set(this,void 0),n.set(this,void 0),ew(this,r,new Uint8Array,"f"),ew(this,n,null,"f")}decode(e){let t;if(null==e)return[];let a=e instanceof ArrayBuffer?new Uint8Array(e):"string"==typeof e?tg(e):e;ew(this,r,function(e){let t=0;for(let a of e)t+=a.length;let a=new Uint8Array(t),r=0;for(let t of e)a.set(t,r),r+=t.length;return a}([eE(this,r,"f"),a]),"f");let s=[];for(;null!=(t=function(e,t){for(let a=t??0;a<e.length;a++){if(10===e[a])return{preceding:a,index:a+1,carriage:!1};if(13===e[a])return{preceding:a,index:a+1,carriage:!0}}return null}(eE(this,r,"f"),eE(this,n,"f")));){if(t.carriage&&null==eE(this,n,"f")){ew(this,n,t.index,"f");continue}if(null!=eE(this,n,"f")&&(t.index!==eE(this,n,"f")+1||t.carriage)){s.push(ty(eE(this,r,"f").subarray(0,eE(this,n,"f")-1))),ew(this,r,eE(this,r,"f").subarray(eE(this,n,"f")),"f"),ew(this,n,null,"f");continue}let e=null!==eE(this,n,"f")?t.preceding-1:t.preceding,a=ty(eE(this,r,"f").subarray(0,e));s.push(a),ew(this,r,eE(this,r,"f").subarray(t.index),"f"),ew(this,n,null,"f")}return s}flush(){return eE(this,r,"f").length?this.decode("\n"):[]}}r=new WeakMap,n=new WeakMap,tx.NEWLINE_CHARS=new Set(["\n","\r"]),tx.NEWLINE_REGEXP=/\r\n|[\n\r]/g;class t_{constructor(e,t){this.iterator=e,this.controller=t}static fromSSEResponse(e,t){let a=!1;async function*r(){if(a)throw new eN("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");a=!0;let r=!1;try{for await(let a of tv(e,t))if(!r){if(a.data.startsWith("[DONE]")){r=!0;continue}if(null===a.event||a.event.startsWith("response.")||a.event.startsWith("transcript.")){let t;try{t=JSON.parse(a.data)}catch(e){throw console.error("Could not parse message into JSON:",a.data),console.error("From chunk:",a.raw),e}if(t&&t.error)throw new eT(void 0,t.error,void 0,e.headers);yield t}else{let e;try{e=JSON.parse(a.data)}catch(e){throw console.error("Could not parse message into JSON:",a.data),console.error("From chunk:",a.raw),e}if("error"==a.event)throw new eT(void 0,e.error,e.message,void 0);yield{event:a.event,data:e}}}r=!0}catch(e){if(eS(e))return;throw e}finally{r||t.abort()}}return new t_(r,t)}static fromReadableStream(e,t){let a=!1;async function*r(){let t=new tx;for await(let a of te(e))for(let e of t.decode(a))yield e;for(let e of t.flush())yield e}return new t_(async function*(){if(a)throw new eN("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");a=!0;let e=!1;try{for await(let t of r())!e&&t&&(yield JSON.parse(t));e=!0}catch(e){if(eS(e))return;throw e}finally{e||t.abort()}},t)}[Symbol.asyncIterator](){return this.iterator()}tee(){let e=[],t=[],a=this.iterator(),r=r=>({next:()=>{if(0===r.length){let r=a.next();e.push(r),t.push(r)}return r.shift()}});return[new t_(()=>r(e),this.controller),new t_(()=>r(t),this.controller)]}toReadableStream(){let e,t=this;return e8({async start(){e=t[Symbol.asyncIterator]()},async pull(t){try{let{value:a,done:r}=await e.next();if(r)return t.close();let n=tg(JSON.stringify(a)+"\n");t.enqueue(n)}catch(e){t.error(e)}},async cancel(){await e.return?.()}})}}async function*tv(e,t){if(!e.body){if(t.abort(),void 0!==globalThis.navigator&&"ReactNative"===globalThis.navigator.product)throw new eN("The default react-native fetch implementation does not support streaming. Please use expo/fetch: https://docs.expo.dev/versions/latest/sdk/expo/#expofetch-api");throw new eN("Attempted to iterate over a response with no body")}let a=new tw,r=new tx;for await(let t of tb(te(e.body)))for(let e of r.decode(t)){let t=a.decode(e);t&&(yield t)}for(let e of r.flush()){let t=a.decode(e);t&&(yield t)}}async function*tb(e){let t=new Uint8Array;for await(let a of e){let e;if(null==a)continue;let r=a instanceof ArrayBuffer?new Uint8Array(a):"string"==typeof a?tg(a):a,n=new Uint8Array(t.length+r.length);for(n.set(t),n.set(r,t.length),t=n;-1!==(e=function(e){for(let t=0;t<e.length-1;t++){if(10===e[t]&&10===e[t+1]||13===e[t]&&13===e[t+1])return t+2;if(13===e[t]&&10===e[t+1]&&t+3<e.length&&13===e[t+2]&&10===e[t+3])return t+4}return -1}(t));)yield t.slice(0,e),t=t.slice(e)}t.length>0&&(yield t)}class tw{constructor(){this.event=null,this.data=[],this.chunks=[]}decode(e){if(e.endsWith("\r")&&(e=e.substring(0,e.length-1)),!e){if(!this.event&&!this.data.length)return null;let e={event:this.event,data:this.data.join("\n"),raw:this.chunks};return this.event=null,this.data=[],this.chunks=[],e}if(this.chunks.push(e),e.startsWith(":"))return null;let[t,a,r]=function(e,t){let a=e.indexOf(":");return -1!==a?[e.substring(0,a),t,e.substring(a+t.length)]:[e,"",""]}(e,":");return r.startsWith(" ")&&(r=r.substring(1)),"event"===t?this.event=r:"data"===t&&this.data.push(r),null}}async function tE(e,t){let{response:a,requestLogID:r,retryOfRequestLogID:n,startTime:s}=t,i=await (async()=>{if(t.options.stream)return(e0(e).debug("response",a.status,a.url,a.headers,a.body),t.options.__streamClass)?t.options.__streamClass.fromSSEResponse(a,t.controller):t_.fromSSEResponse(a,t.controller);if(204===a.status)return null;if(t.options.__binaryResponse)return a;let r=a.headers.get("content-type"),n=r?.split(";")[0]?.trim();return n?.includes("application/json")||n?.endsWith("+json")?tA(await a.json(),a):await a.text()})();return e0(e).debug(`[${r}] response parsed`,e1({retryOfRequestLogID:n,url:a.url,status:a.status,body:i,durationMs:Date.now()-s})),i}function tA(e,t){return!e||"object"!=typeof e||Array.isArray(e)?e:Object.defineProperty(e,"_request_id",{value:t.headers.get("x-request-id"),enumerable:!1})}class tS extends Promise{constructor(e,t,a=tE){super(e=>{e(null)}),this.responsePromise=t,this.parseResponse=a,s.set(this,void 0),ew(this,s,e,"f")}_thenUnwrap(e){return new tS(eE(this,s,"f"),this.responsePromise,async(t,a)=>tA(e(await this.parseResponse(t,a),a),a.response))}asResponse(){return this.responsePromise.then(e=>e.response)}async withResponse(){let[e,t]=await Promise.all([this.parse(),this.asResponse()]);return{data:e,response:t,request_id:t.headers.get("x-request-id")}}parse(){return this.parsedPromise||(this.parsedPromise=this.responsePromise.then(e=>this.parseResponse(eE(this,s,"f"),e))),this.parsedPromise}then(e,t){return this.parse().then(e,t)}catch(e){return this.parse().catch(e)}finally(e){return this.parse().finally(e)}}s=new WeakMap;class tI{constructor(e,t,a,r){i.set(this,void 0),ew(this,i,e,"f"),this.options=r,this.response=t,this.body=a}hasNextPage(){return!!this.getPaginatedItems().length&&null!=this.nextPageRequestOptions()}async getNextPage(){let e=this.nextPageRequestOptions();if(!e)throw new eN("No next page expected; please check `.hasNextPage()` before calling `.getNextPage()`.");return await eE(this,i,"f").requestAPIList(this.constructor,e)}async *iterPages(){let e=this;for(yield e;e.hasNextPage();)e=await e.getNextPage(),yield e}async *[(i=new WeakMap,Symbol.asyncIterator)](){for await(let e of this.iterPages())for(let t of e.getPaginatedItems())yield t}}class tN extends tS{constructor(e,t,a){super(e,t,async(e,t)=>new a(e,t.response,await tE(e,t),t.options))}async *[Symbol.asyncIterator](){for await(let e of(await this))yield e}}class tT extends tI{constructor(e,t,a,r){super(e,t,a,r),this.data=a.data||[],this.object=a.object}getPaginatedItems(){return this.data??[]}nextPageRequestOptions(){return null}}class tO extends tI{constructor(e,t,a,r){super(e,t,a,r),this.data=a.data||[],this.has_more=a.has_more||!1}getPaginatedItems(){return this.data??[]}hasNextPage(){return!1!==this.has_more&&super.hasNextPage()}nextPageRequestOptions(){var e;let t=this.getPaginatedItems(),a=t[t.length-1]?.id;return a?{...this.options,query:{..."object"!=typeof(e=this.options.query)?{}:e??{},after:a}}:null}}let tC=()=>{if("undefined"==typeof File){let{process:e}=globalThis;throw Error("`File` is not defined as a global, which is required for file uploads."+("string"==typeof e?.versions?.node&&20>parseInt(e.versions.node.split("."))?" Update to Node 20 LTS or newer, or set `globalThis.File` to `import('node:buffer').File`.":""))}};function tk(e,t,a){return tC(),new File(e,t??"unknown_file",a)}function tR(e){return("object"==typeof e&&null!==e&&("name"in e&&e.name&&String(e.name)||"url"in e&&e.url&&String(e.url)||"filename"in e&&e.filename&&String(e.filename)||"path"in e&&e.path&&String(e.path))||"").split(/[\\/]/).pop()||void 0}let tP=e=>null!=e&&"object"==typeof e&&"function"==typeof e[Symbol.asyncIterator],t$=async(e,t)=>({...e,body:await tj(e.body,t)}),tD=new WeakMap,tj=async(e,t)=>{if(!await function(e){let t="function"==typeof e?e:e.fetch,a=tD.get(t);if(a)return a;let r=(async()=>{try{let e="Response"in t?t.Response:(await t("data:,")).constructor,a=new FormData;if(a.toString()===await new e(a).text())return!1;return!0}catch{return!0}})();return tD.set(t,r),r}(t))throw TypeError("The provided fetch function does not support file uploads with the current global FormData class.");let a=new FormData;return await Promise.all(Object.entries(e||{}).map(([e,t])=>tF(a,e,t))),a},tL=e=>e instanceof Blob&&"name"in e,tM=e=>"object"==typeof e&&null!==e&&(e instanceof Response||tP(e)||tL(e)),tU=e=>{if(tM(e))return!0;if(Array.isArray(e))return e.some(tU);if(e&&"object"==typeof e){for(let t in e)if(tU(e[t]))return!0}return!1},tF=async(e,t,a)=>{if(void 0!==a){if(null==a)throw TypeError(`Received null for "${t}"; to pass null in FormData, you must use the string 'null'`);if("string"==typeof a||"number"==typeof a||"boolean"==typeof a)e.append(t,String(a));else if(a instanceof Response)e.append(t,tk([await a.blob()],tR(a)));else if(tP(a))e.append(t,tk([await new Response(e7(a)).blob()],tR(a)));else if(tL(a))e.append(t,a,tR(a));else if(Array.isArray(a))await Promise.all(a.map(a=>tF(e,t+"[]",a)));else if("object"==typeof a)await Promise.all(Object.entries(a).map(([a,r])=>tF(e,`${t}[${a}]`,r)));else throw TypeError(`Invalid value given to form, expected a string, number, boolean, object, Array, File or Blob but got ${a} instead`)}},tq=e=>null!=e&&"object"==typeof e&&"number"==typeof e.size&&"string"==typeof e.type&&"function"==typeof e.text&&"function"==typeof e.slice&&"function"==typeof e.arrayBuffer,tz=e=>null!=e&&"object"==typeof e&&"string"==typeof e.name&&"number"==typeof e.lastModified&&tq(e),tZ=e=>null!=e&&"object"==typeof e&&"string"==typeof e.url&&"function"==typeof e.blob;async function tB(e,t,a){if(tC(),tz(e=await e))return e instanceof File?e:tk([await e.arrayBuffer()],e.name);if(tZ(e)){let r=await e.blob();return t||(t=new URL(e.url).pathname.split(/[\\/]/).pop()),tk(await tW(r),t,a)}let r=await tW(e);if(t||(t=tR(e)),!a?.type){let e=r.find(e=>"object"==typeof e&&"type"in e&&e.type);"string"==typeof e&&(a={...a,type:e})}return tk(r,t,a)}async function tW(e){let t=[];if("string"==typeof e||ArrayBuffer.isView(e)||e instanceof ArrayBuffer)t.push(e);else if(tq(e))t.push(e instanceof Blob?e:await e.arrayBuffer());else if(tP(e))for await(let a of e)t.push(...await tW(a));else{let t=e?.constructor?.name;throw Error(`Unexpected data type: ${typeof e}${t?`; constructor: ${t}`:""}${function(e){if("object"!=typeof e||null===e)return"";let t=Object.getOwnPropertyNames(e);return`; props: [${t.map(e=>`"${e}"`).join(", ")}]`}(e)}`)}return t}class tV{constructor(e){this._client=e}}function tJ(e){return e.replace(/[^A-Za-z0-9\-._~!$&'()*+,;=:@]+/g,encodeURIComponent)}let tX=((e=tJ)=>function(t,...a){let r;if(1===t.length)return t[0];let n=!1,s=t.reduce((t,r,s)=>(/[?#]/.test(r)&&(n=!0),t+r+(s===a.length?"":(n?encodeURIComponent:e)(String(a[s])))),""),i=s.split(/[?#]/,1)[0],o=[],l=/(?<=^|\/)(?:\.|%2e){1,2}(?=\/|$)/gi;for(;null!==(r=l.exec(i));)o.push({start:r.index,length:r[0].length});if(o.length>0){let e=0,t=o.reduce((t,a)=>{let r=" ".repeat(a.start-e),n="^".repeat(a.length);return e=a.start+a.length,t+r+n},"");throw new eN(`Path parameters result in path with invalid segments:
${s}
${t}`)}return s})(tJ);class tH extends tV{list(e,t={},a){return this._client.getAPIList(tX`/chat/completions/${e}/messages`,tO,{query:t,...a})}}let tG=e=>e?.role==="assistant",tY=e=>e?.role==="tool";class tK{constructor(){o.add(this),this.controller=new AbortController,l.set(this,void 0),d.set(this,()=>{}),c.set(this,()=>{}),u.set(this,void 0),p.set(this,()=>{}),h.set(this,()=>{}),m.set(this,{}),f.set(this,!1),g.set(this,!1),y.set(this,!1),x.set(this,!1),ew(this,l,new Promise((e,t)=>{ew(this,d,e,"f"),ew(this,c,t,"f")}),"f"),ew(this,u,new Promise((e,t)=>{ew(this,p,e,"f"),ew(this,h,t,"f")}),"f"),eE(this,l,"f").catch(()=>{}),eE(this,u,"f").catch(()=>{})}_run(e){setTimeout(()=>{e().then(()=>{this._emitFinal(),this._emit("end")},eE(this,o,"m",_).bind(this))},0)}_connected(){this.ended||(eE(this,d,"f").call(this),this._emit("connect"))}get ended(){return eE(this,f,"f")}get errored(){return eE(this,g,"f")}get aborted(){return eE(this,y,"f")}abort(){this.controller.abort()}on(e,t){return(eE(this,m,"f")[e]||(eE(this,m,"f")[e]=[])).push({listener:t}),this}off(e,t){let a=eE(this,m,"f")[e];if(!a)return this;let r=a.findIndex(e=>e.listener===t);return r>=0&&a.splice(r,1),this}once(e,t){return(eE(this,m,"f")[e]||(eE(this,m,"f")[e]=[])).push({listener:t,once:!0}),this}emitted(e){return new Promise((t,a)=>{ew(this,x,!0,"f"),"error"!==e&&this.once("error",a),this.once(e,t)})}async done(){ew(this,x,!0,"f"),await eE(this,u,"f")}_emit(e,...t){if(eE(this,f,"f"))return;"end"===e&&(ew(this,f,!0,"f"),eE(this,p,"f").call(this));let a=eE(this,m,"f")[e];if(a&&(eE(this,m,"f")[e]=a.filter(e=>!e.once),a.forEach(({listener:e})=>e(...t))),"abort"===e){let e=t[0];eE(this,x,"f")||a?.length||Promise.reject(e),eE(this,c,"f").call(this,e),eE(this,h,"f").call(this,e),this._emit("end");return}if("error"===e){let e=t[0];eE(this,x,"f")||a?.length||Promise.reject(e),eE(this,c,"f").call(this,e),eE(this,h,"f").call(this,e),this._emit("end")}}_emitFinal(){}}function tQ(e){return e?.$brand==="auto-parseable-response-format"}function t0(e){return e?.$brand==="auto-parseable-tool"}function t1(e,t){let a=e.choices.map(e=>{var a,r;if("length"===e.finish_reason)throw new eF;if("content_filter"===e.finish_reason)throw new eq;return{...e,message:{...e.message,...e.message.tool_calls?{tool_calls:e.message.tool_calls?.map(e=>(function(e,t){let a=e.tools?.find(e=>e.function?.name===t.function.name);return{...t,function:{...t.function,parsed_arguments:t0(a)?a.$parseRaw(t.function.arguments):a?.function.strict?JSON.parse(t.function.arguments):null}}})(t,e))??void 0}:void 0,parsed:e.message.content&&!e.message.refusal?(a=t,r=e.message.content,a.response_format?.type!=="json_schema"?null:a.response_format?.type==="json_schema"?"$parseRaw"in a.response_format?a.response_format.$parseRaw(r):JSON.parse(r):null):null}}});return{...e,choices:a}}function t3(e){return!!tQ(e.response_format)||(e.tools?.some(e=>t0(e)||"function"===e.type&&!0===e.function.strict)??!1)}l=new WeakMap,d=new WeakMap,c=new WeakMap,u=new WeakMap,p=new WeakMap,h=new WeakMap,m=new WeakMap,f=new WeakMap,g=new WeakMap,y=new WeakMap,x=new WeakMap,o=new WeakSet,_=function(e){if(ew(this,g,!0,"f"),e instanceof Error&&"AbortError"===e.name&&(e=new eO),e instanceof eO)return ew(this,y,!0,"f"),this._emit("abort",e);if(e instanceof eN)return this._emit("error",e);if(e instanceof Error){let t=new eN(e.message);return t.cause=e,this._emit("error",t)}return this._emit("error",new eN(String(e)))};class t2 extends tK{constructor(){super(...arguments),v.add(this),this._chatCompletions=[],this.messages=[]}_addChatCompletion(e){this._chatCompletions.push(e),this._emit("chatCompletion",e);let t=e.choices[0]?.message;return t&&this._addMessage(t),e}_addMessage(e,t=!0){if("content"in e||(e.content=null),this.messages.push(e),t){if(this._emit("message",e),tY(e)&&e.content)this._emit("functionToolCallResult",e.content);else if(tG(e)&&e.tool_calls)for(let t of e.tool_calls)"function"===t.type&&this._emit("functionToolCall",t.function)}}async finalChatCompletion(){await this.done();let e=this._chatCompletions[this._chatCompletions.length-1];if(!e)throw new eN("stream ended without producing a ChatCompletion");return e}async finalContent(){return await this.done(),eE(this,v,"m",b).call(this)}async finalMessage(){return await this.done(),eE(this,v,"m",w).call(this)}async finalFunctionToolCall(){return await this.done(),eE(this,v,"m",E).call(this)}async finalFunctionToolCallResult(){return await this.done(),eE(this,v,"m",A).call(this)}async totalUsage(){return await this.done(),eE(this,v,"m",S).call(this)}allChatCompletions(){return[...this._chatCompletions]}_emitFinal(){let e=this._chatCompletions[this._chatCompletions.length-1];e&&this._emit("finalChatCompletion",e);let t=eE(this,v,"m",w).call(this);t&&this._emit("finalMessage",t);let a=eE(this,v,"m",b).call(this);a&&this._emit("finalContent",a);let r=eE(this,v,"m",E).call(this);r&&this._emit("finalFunctionToolCall",r);let n=eE(this,v,"m",A).call(this);null!=n&&this._emit("finalFunctionToolCallResult",n),this._chatCompletions.some(e=>e.usage)&&this._emit("totalUsage",eE(this,v,"m",S).call(this))}async _createChatCompletion(e,t,a){let r=a?.signal;r&&(r.aborted&&this.controller.abort(),r.addEventListener("abort",()=>this.controller.abort())),eE(this,v,"m",I).call(this,t);let n=await e.chat.completions.create({...t,stream:!1},{...a,signal:this.controller.signal});return this._connected(),this._addChatCompletion(t1(n,t))}async _runChatCompletion(e,t,a){for(let e of t.messages)this._addMessage(e,!1);return await this._createChatCompletion(e,t,a)}async _runTools(e,t,a){let r="tool",{tool_choice:n="auto",stream:s,...i}=t,o="string"!=typeof n&&n?.function?.name,{maxChatCompletions:l=10}=a||{},d=t.tools.map(e=>{if(t0(e)){if(!e.$callback)throw new eN("Tool given to `.runTools()` that does not have an associated function");return{type:"function",function:{function:e.$callback,name:e.function.name,description:e.function.description||"",parameters:e.function.parameters,parse:e.$parseRaw,strict:!0}}}return e}),c={};for(let e of d)"function"===e.type&&(c[e.function.name||e.function.function.name]=e.function);let u="tools"in t?d.map(e=>"function"===e.type?{type:"function",function:{name:e.function.name||e.function.function.name,parameters:e.function.parameters,description:e.function.description,strict:e.function.strict}}:e):void 0;for(let e of t.messages)this._addMessage(e,!1);for(let t=0;t<l;++t){let t=await this._createChatCompletion(e,{...i,tool_choice:n,tools:u,messages:[...this.messages]},a),s=t.choices[0]?.message;if(!s)throw new eN("missing message in ChatCompletion response");if(!s.tool_calls?.length)break;for(let e of s.tool_calls){let t;if("function"!==e.type)continue;let a=e.id,{name:n,arguments:s}=e.function,i=c[n];if(i){if(o&&o!==n){let e=`Invalid tool_call: ${JSON.stringify(n)}. ${JSON.stringify(o)} requested. Please try again`;this._addMessage({role:r,tool_call_id:a,content:e});continue}}else{let e=`Invalid tool_call: ${JSON.stringify(n)}. Available options are: ${Object.keys(c).map(e=>JSON.stringify(e)).join(", ")}. Please try again`;this._addMessage({role:r,tool_call_id:a,content:e});continue}try{t="function"==typeof i.parse?await i.parse(s):s}catch(t){let e=t instanceof Error?t.message:String(t);this._addMessage({role:r,tool_call_id:a,content:e});continue}let l=await i.function(t,this),d=eE(this,v,"m",N).call(this,l);if(this._addMessage({role:r,tool_call_id:a,content:d}),o)return}}}}v=new WeakSet,b=function(){return eE(this,v,"m",w).call(this).content??null},w=function(){let e=this.messages.length;for(;e-- >0;){let t=this.messages[e];if(tG(t))return{...t,content:t.content??null,refusal:t.refusal??null}}throw new eN("stream ended without producing a ChatCompletionMessage with role=assistant")},E=function(){for(let e=this.messages.length-1;e>=0;e--){let t=this.messages[e];if(tG(t)&&t?.tool_calls?.length)return t.tool_calls.at(-1)?.function}},A=function(){for(let e=this.messages.length-1;e>=0;e--){let t=this.messages[e];if(tY(t)&&null!=t.content&&"string"==typeof t.content&&this.messages.some(e=>"assistant"===e.role&&e.tool_calls?.some(e=>"function"===e.type&&e.id===t.tool_call_id)))return t.content}},S=function(){let e={completion_tokens:0,prompt_tokens:0,total_tokens:0};for(let{usage:t}of this._chatCompletions)t&&(e.completion_tokens+=t.completion_tokens,e.prompt_tokens+=t.prompt_tokens,e.total_tokens+=t.total_tokens);return e},I=function(e){if(null!=e.n&&e.n>1)throw new eN("ChatCompletion convenience helpers only support n=1 at this time. To use n>1, please use chat.completions.create() directly.")},N=function(e){return"string"==typeof e?e:void 0===e?"undefined":JSON.stringify(e)};class t9 extends t2{static runTools(e,t,a){let r=new t9,n={...a,headers:{...a?.headers,"X-Stainless-Helper-Method":"runTools"}};return r._run(()=>r._runTools(e,t,n)),r}_addMessage(e,t=!0){super._addMessage(e,t),tG(e)&&e.content&&this._emit("content",e.content)}}let t4={STR:1,NUM:2,ARR:4,OBJ:8,NULL:16,BOOL:32,NAN:64,INFINITY:128,MINUS_INFINITY:256,ALL:511};class t5 extends Error{}class t6 extends Error{}let t8=(e,t)=>{let a=e.length,r=0,n=e=>{throw new t5(`${e} at position ${r}`)},s=e=>{throw new t6(`${e} at position ${r}`)},i=()=>(u(),r>=a&&n("Unexpected end of input"),'"'===e[r])?o():"{"===e[r]?l():"["===e[r]?d():"null"===e.substring(r,r+4)||t4.NULL&t&&a-r<4&&"null".startsWith(e.substring(r))?(r+=4,null):"true"===e.substring(r,r+4)||t4.BOOL&t&&a-r<4&&"true".startsWith(e.substring(r))?(r+=4,!0):"false"===e.substring(r,r+5)||t4.BOOL&t&&a-r<5&&"false".startsWith(e.substring(r))?(r+=5,!1):"Infinity"===e.substring(r,r+8)||t4.INFINITY&t&&a-r<8&&"Infinity".startsWith(e.substring(r))?(r+=8,1/0):"-Infinity"===e.substring(r,r+9)||t4.MINUS_INFINITY&t&&1<a-r&&a-r<9&&"-Infinity".startsWith(e.substring(r))?(r+=9,-1/0):"NaN"===e.substring(r,r+3)||t4.NAN&t&&a-r<3&&"NaN".startsWith(e.substring(r))?(r+=3,NaN):c(),o=()=>{let i=r,o=!1;for(r++;r<a&&('"'!==e[r]||o&&"\\"===e[r-1]);)o="\\"===e[r]&&!o,r++;if('"'==e.charAt(r))try{return JSON.parse(e.substring(i,++r-Number(o)))}catch(e){s(String(e))}else if(t4.STR&t)try{return JSON.parse(e.substring(i,r-Number(o))+'"')}catch(t){return JSON.parse(e.substring(i,e.lastIndexOf("\\"))+'"')}n("Unterminated string literal")},l=()=>{r++,u();let s={};try{for(;"}"!==e[r];){if(u(),r>=a&&t4.OBJ&t)return s;let n=o();u(),r++;try{let e=i();Object.defineProperty(s,n,{value:e,writable:!0,enumerable:!0,configurable:!0})}catch(e){if(t4.OBJ&t)return s;throw e}u(),","===e[r]&&r++}}catch(e){if(t4.OBJ&t)return s;n("Expected '}' at end of object")}return r++,s},d=()=>{r++;let a=[];try{for(;"]"!==e[r];)a.push(i()),u(),","===e[r]&&r++}catch(e){if(t4.ARR&t)return a;n("Expected ']' at end of array")}return r++,a},c=()=>{if(0===r){"-"===e&&t4.NUM&t&&n("Not sure what '-' is");try{return JSON.parse(e)}catch(a){if(t4.NUM&t)try{if("."===e[e.length-1])return JSON.parse(e.substring(0,e.lastIndexOf(".")));return JSON.parse(e.substring(0,e.lastIndexOf("e")))}catch(e){}s(String(a))}}let i=r;for("-"===e[r]&&r++;e[r]&&!",]}".includes(e[r]);)r++;r!=a||t4.NUM&t||n("Unterminated number literal");try{return JSON.parse(e.substring(i,r))}catch(a){"-"===e.substring(i,r)&&t4.NUM&t&&n("Not sure what '-' is");try{return JSON.parse(e.substring(i,e.lastIndexOf("e")))}catch(e){s(String(e))}}},u=()=>{for(;r<a&&" \n\r	".includes(e[r]);)r++};return i()},t7=e=>(function(e,t=t4.ALL){if("string"!=typeof e)throw TypeError(`expecting str, got ${typeof e}`);if(!e.trim())throw Error(`${e} is empty`);return t8(e.trim(),t)})(e,t4.ALL^t4.NUM);class ae extends t2{constructor(e){super(),T.add(this),O.set(this,void 0),C.set(this,void 0),k.set(this,void 0),ew(this,O,e,"f"),ew(this,C,[],"f")}get currentChatCompletionSnapshot(){return eE(this,k,"f")}static fromReadableStream(e){let t=new ae(null);return t._run(()=>t._fromReadableStream(e)),t}static createChatCompletion(e,t,a){let r=new ae(t);return r._run(()=>r._runChatCompletion(e,{...t,stream:!0},{...a,headers:{...a?.headers,"X-Stainless-Helper-Method":"stream"}})),r}async _createChatCompletion(e,t,a){super._createChatCompletion;let r=a?.signal;r&&(r.aborted&&this.controller.abort(),r.addEventListener("abort",()=>this.controller.abort())),eE(this,T,"m",R).call(this);let n=await e.chat.completions.create({...t,stream:!0},{...a,signal:this.controller.signal});for await(let e of(this._connected(),n))eE(this,T,"m",$).call(this,e);if(n.controller.signal?.aborted)throw new eO;return this._addChatCompletion(eE(this,T,"m",L).call(this))}async _fromReadableStream(e,t){let a,r=t?.signal;r&&(r.aborted&&this.controller.abort(),r.addEventListener("abort",()=>this.controller.abort())),eE(this,T,"m",R).call(this),this._connected();let n=t_.fromReadableStream(e,this.controller);for await(let e of n)a&&a!==e.id&&this._addChatCompletion(eE(this,T,"m",L).call(this)),eE(this,T,"m",$).call(this,e),a=e.id;if(n.controller.signal?.aborted)throw new eO;return this._addChatCompletion(eE(this,T,"m",L).call(this))}[(O=new WeakMap,C=new WeakMap,k=new WeakMap,T=new WeakSet,R=function(){this.ended||ew(this,k,void 0,"f")},P=function(e){let t=eE(this,C,"f")[e.index];return t||(t={content_done:!1,refusal_done:!1,logprobs_content_done:!1,logprobs_refusal_done:!1,done_tool_calls:new Set,current_tool_call_index:null},eE(this,C,"f")[e.index]=t),t},$=function(e){if(this.ended)return;let t=eE(this,T,"m",U).call(this,e);for(let a of(this._emit("chunk",e,t),e.choices)){let e=t.choices[a.index];null!=a.delta.content&&e.message?.role==="assistant"&&e.message?.content&&(this._emit("content",a.delta.content,e.message.content),this._emit("content.delta",{delta:a.delta.content,snapshot:e.message.content,parsed:e.message.parsed})),null!=a.delta.refusal&&e.message?.role==="assistant"&&e.message?.refusal&&this._emit("refusal.delta",{delta:a.delta.refusal,snapshot:e.message.refusal}),a.logprobs?.content!=null&&e.message?.role==="assistant"&&this._emit("logprobs.content.delta",{content:a.logprobs?.content,snapshot:e.logprobs?.content??[]}),a.logprobs?.refusal!=null&&e.message?.role==="assistant"&&this._emit("logprobs.refusal.delta",{refusal:a.logprobs?.refusal,snapshot:e.logprobs?.refusal??[]});let r=eE(this,T,"m",P).call(this,e);for(let t of(e.finish_reason&&(eE(this,T,"m",j).call(this,e),null!=r.current_tool_call_index&&eE(this,T,"m",D).call(this,e,r.current_tool_call_index)),a.delta.tool_calls??[]))r.current_tool_call_index!==t.index&&(eE(this,T,"m",j).call(this,e),null!=r.current_tool_call_index&&eE(this,T,"m",D).call(this,e,r.current_tool_call_index)),r.current_tool_call_index=t.index;for(let t of a.delta.tool_calls??[]){let a=e.message.tool_calls?.[t.index];a?.type&&(a?.type==="function"?this._emit("tool_calls.function.arguments.delta",{name:a.function?.name,index:t.index,arguments:a.function.arguments,parsed_arguments:a.function.parsed_arguments,arguments_delta:t.function?.arguments??""}):a?.type)}}},D=function(e,t){if(eE(this,T,"m",P).call(this,e).done_tool_calls.has(t))return;let a=e.message.tool_calls?.[t];if(!a)throw Error("no tool call snapshot");if(!a.type)throw Error("tool call snapshot missing `type`");if("function"===a.type){let e=eE(this,O,"f")?.tools?.find(e=>"function"===e.type&&e.function.name===a.function.name);this._emit("tool_calls.function.arguments.done",{name:a.function.name,index:t,arguments:a.function.arguments,parsed_arguments:t0(e)?e.$parseRaw(a.function.arguments):e?.function.strict?JSON.parse(a.function.arguments):null})}else a.type},j=function(e){let t=eE(this,T,"m",P).call(this,e);if(e.message.content&&!t.content_done){t.content_done=!0;let a=eE(this,T,"m",M).call(this);this._emit("content.done",{content:e.message.content,parsed:a?a.$parseRaw(e.message.content):null})}e.message.refusal&&!t.refusal_done&&(t.refusal_done=!0,this._emit("refusal.done",{refusal:e.message.refusal})),e.logprobs?.content&&!t.logprobs_content_done&&(t.logprobs_content_done=!0,this._emit("logprobs.content.done",{content:e.logprobs.content})),e.logprobs?.refusal&&!t.logprobs_refusal_done&&(t.logprobs_refusal_done=!0,this._emit("logprobs.refusal.done",{refusal:e.logprobs.refusal}))},L=function(){if(this.ended)throw new eN("stream has ended, this shouldn't happen");let e=eE(this,k,"f");if(!e)throw new eN("request ended without sending any chunks");return ew(this,k,void 0,"f"),ew(this,C,[],"f"),function(e,t){var a;let{id:r,choices:n,created:s,model:i,system_fingerprint:o,...l}=e;return a={...l,id:r,choices:n.map(({message:t,finish_reason:a,index:r,logprobs:n,...s})=>{if(!a)throw new eN(`missing finish_reason for choice ${r}`);let{content:i=null,function_call:o,tool_calls:l,...d}=t,c=t.role;if(!c)throw new eN(`missing role for choice ${r}`);if(o){let{arguments:e,name:l}=o;if(null==e)throw new eN(`missing function_call.arguments for choice ${r}`);if(!l)throw new eN(`missing function_call.name for choice ${r}`);return{...s,message:{content:i,function_call:{arguments:e,name:l},role:c,refusal:t.refusal??null},finish_reason:a,index:r,logprobs:n}}return l?{...s,index:r,finish_reason:a,logprobs:n,message:{...d,role:c,content:i,refusal:t.refusal??null,tool_calls:l.map((t,a)=>{let{function:n,type:s,id:i,...o}=t,{arguments:l,name:d,...c}=n||{};if(null==i)throw new eN(`missing choices[${r}].tool_calls[${a}].id
${at(e)}`);if(null==s)throw new eN(`missing choices[${r}].tool_calls[${a}].type
${at(e)}`);if(null==d)throw new eN(`missing choices[${r}].tool_calls[${a}].function.name
${at(e)}`);if(null==l)throw new eN(`missing choices[${r}].tool_calls[${a}].function.arguments
${at(e)}`);return{...o,id:i,type:s,function:{...c,name:d,arguments:l}}})}}:{...s,message:{...d,content:i,role:c,refusal:t.refusal??null},finish_reason:a,index:r,logprobs:n}}),created:s,model:i,object:"chat.completion",...o?{system_fingerprint:o}:{}},t&&t3(t)?t1(a,t):{...a,choices:a.choices.map(e=>({...e,message:{...e.message,parsed:null,...e.message.tool_calls?{tool_calls:e.message.tool_calls}:void 0}}))}}(e,eE(this,O,"f"))},M=function(){let e=eE(this,O,"f")?.response_format;return tQ(e)?e:null},U=function(e){var t,a,r,n;let s=eE(this,k,"f"),{choices:i,...o}=e;for(let{delta:i,finish_reason:l,index:d,logprobs:c=null,...u}of(s?Object.assign(s,o):s=ew(this,k,{...o,choices:[]},"f"),e.choices)){let e=s.choices[d];if(e||(e=s.choices[d]={finish_reason:l,index:d,message:{},logprobs:c,...u}),c)if(e.logprobs){let{content:r,refusal:n,...s}=c;Object.assign(e.logprobs,s),r&&((t=e.logprobs).content??(t.content=[]),e.logprobs.content.push(...r)),n&&((a=e.logprobs).refusal??(a.refusal=[]),e.logprobs.refusal.push(...n))}else e.logprobs=Object.assign({},c);if(l&&(e.finish_reason=l,eE(this,O,"f")&&t3(eE(this,O,"f")))){if("length"===l)throw new eF;if("content_filter"===l)throw new eq}if(Object.assign(e,u),!i)continue;let{content:o,refusal:p,function_call:h,role:m,tool_calls:f,...g}=i;if(Object.assign(e.message,g),p&&(e.message.refusal=(e.message.refusal||"")+p),m&&(e.message.role=m),h&&(e.message.function_call?(h.name&&(e.message.function_call.name=h.name),h.arguments&&((r=e.message.function_call).arguments??(r.arguments=""),e.message.function_call.arguments+=h.arguments)):e.message.function_call=h),o&&(e.message.content=(e.message.content||"")+o,!e.message.refusal&&eE(this,T,"m",M).call(this)&&(e.message.parsed=t7(e.message.content))),f)for(let{index:t,id:a,type:r,function:s,...i}of(e.message.tool_calls||(e.message.tool_calls=[]),f)){let o=(n=e.message.tool_calls)[t]??(n[t]={});Object.assign(o,i),a&&(o.id=a),r&&(o.type=r),s&&(o.function??(o.function={name:s.name??"",arguments:""})),s?.name&&(o.function.name=s.name),s?.arguments&&(o.function.arguments+=s.arguments,function(e,t){if(!e)return!1;let a=e.tools?.find(e=>e.function?.name===t.function.name);return t0(a)||a?.function.strict||!1}(eE(this,O,"f"),o)&&(o.function.parsed_arguments=t7(o.function.arguments)))}}return s},Symbol.asyncIterator)](){let e=[],t=[],a=!1;return this.on("chunk",a=>{let r=t.shift();r?r.resolve(a):e.push(a)}),this.on("end",()=>{for(let e of(a=!0,t))e.resolve(void 0);t.length=0}),this.on("abort",e=>{for(let r of(a=!0,t))r.reject(e);t.length=0}),this.on("error",e=>{for(let r of(a=!0,t))r.reject(e);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:a?{value:void 0,done:!0}:new Promise((e,a)=>t.push({resolve:e,reject:a})).then(e=>e?{value:e,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}toReadableStream(){return new t_(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}}function at(e){return JSON.stringify(e)}class aa extends ae{static fromReadableStream(e){let t=new aa(null);return t._run(()=>t._fromReadableStream(e)),t}static runTools(e,t,a){let r=new aa(t),n={...a,headers:{...a?.headers,"X-Stainless-Helper-Method":"runTools"}};return r._run(()=>r._runTools(e,t,n)),r}}class ar extends tV{constructor(){super(...arguments),this.messages=new tH(this._client)}create(e,t){return this._client.post("/chat/completions",{body:e,...t,stream:e.stream??!1})}retrieve(e,t){return this._client.get(tX`/chat/completions/${e}`,t)}update(e,t,a){return this._client.post(tX`/chat/completions/${e}`,{body:t,...a})}list(e={},t){return this._client.getAPIList("/chat/completions",tO,{query:e,...t})}delete(e,t){return this._client.delete(tX`/chat/completions/${e}`,t)}parse(e,t){for(let t of e.tools??[]){if("function"!==t.type)throw new eN(`Currently only \`function\` tool types support auto-parsing; Received \`${t.type}\``);if(!0!==t.function.strict)throw new eN(`The \`${t.function.name}\` tool is not marked with \`strict: true\`. Only strict function tools can be auto-parsed`)}return this._client.chat.completions.create(e,{...t,headers:{...t?.headers,"X-Stainless-Helper-Method":"chat.completions.parse"}})._thenUnwrap(t=>t1(t,e))}runTools(e,t){return e.stream?aa.runTools(this._client,e,t):t9.runTools(this._client,e,t)}stream(e,t){return ae.createChatCompletion(this._client,e,t)}}ar.Messages=tH;class an extends tV{constructor(){super(...arguments),this.completions=new ar(this._client)}}an.Completions=ar;let as=Symbol("brand.privateNullableHeaders"),ai=Array.isArray,ao=e=>{let t=new Headers,a=new Set;for(let r of e){let e=new Set;for(let[n,s]of function*(e){let t;if(!e)return;if(as in e){let{values:t,nulls:a}=e;for(let e of(yield*t.entries(),a))yield[e,null];return}let a=!1;for(let r of(e instanceof Headers?t=e.entries():ai(e)?t=e:(a=!0,t=Object.entries(e??{})),t)){let e=r[0];if("string"!=typeof e)throw TypeError("expected header name to be a string");let t=ai(r[1])?r[1]:[r[1]],n=!1;for(let r of t)void 0!==r&&(a&&!n&&(n=!0,yield[e,null]),yield[e,r])}}(r)){let r=n.toLowerCase();e.has(r)||(t.delete(n),e.add(r)),null===s?(t.delete(n),a.add(r)):(t.append(n,s),a.delete(r))}}return{[as]:!0,values:t,nulls:a}};class al extends tV{create(e,t){return this._client.post("/audio/speech",{body:e,...t,headers:ao([{Accept:"application/octet-stream"},t?.headers]),__binaryResponse:!0})}}class ad extends tV{create(e,t){return this._client.post("/audio/transcriptions",t$({body:e,...t,stream:e.stream??!1,__metadata:{model:e.model}},this._client))}}class ac extends tV{create(e,t){return this._client.post("/audio/translations",t$({body:e,...t,__metadata:{model:e.model}},this._client))}}class au extends tV{constructor(){super(...arguments),this.transcriptions=new ad(this._client),this.translations=new ac(this._client),this.speech=new al(this._client)}}au.Transcriptions=ad,au.Translations=ac,au.Speech=al;class ap extends tV{create(e,t){return this._client.post("/batches",{body:e,...t})}retrieve(e,t){return this._client.get(tX`/batches/${e}`,t)}list(e={},t){return this._client.getAPIList("/batches",tO,{query:e,...t})}cancel(e,t){return this._client.post(tX`/batches/${e}/cancel`,t)}}class ah extends tV{create(e,t){return this._client.post("/assistants",{body:e,...t,headers:ao([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}retrieve(e,t){return this._client.get(tX`/assistants/${e}`,{...t,headers:ao([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}update(e,t,a){return this._client.post(tX`/assistants/${e}`,{body:t,...a,headers:ao([{"OpenAI-Beta":"assistants=v2"},a?.headers])})}list(e={},t){return this._client.getAPIList("/assistants",tO,{query:e,...t,headers:ao([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}delete(e,t){return this._client.delete(tX`/assistants/${e}`,{...t,headers:ao([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}}class am extends tV{create(e,t){return this._client.post("/realtime/sessions",{body:e,...t,headers:ao([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}}class af extends tV{create(e,t){return this._client.post("/realtime/transcription_sessions",{body:e,...t,headers:ao([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}}class ag extends tV{constructor(){super(...arguments),this.sessions=new am(this._client),this.transcriptionSessions=new af(this._client)}}ag.Sessions=am,ag.TranscriptionSessions=af;class ay extends tV{create(e,t,a){return this._client.post(tX`/threads/${e}/messages`,{body:t,...a,headers:ao([{"OpenAI-Beta":"assistants=v2"},a?.headers])})}retrieve(e,t,a){let{thread_id:r}=t;return this._client.get(tX`/threads/${r}/messages/${e}`,{...a,headers:ao([{"OpenAI-Beta":"assistants=v2"},a?.headers])})}update(e,t,a){let{thread_id:r,...n}=t;return this._client.post(tX`/threads/${r}/messages/${e}`,{body:n,...a,headers:ao([{"OpenAI-Beta":"assistants=v2"},a?.headers])})}list(e,t={},a){return this._client.getAPIList(tX`/threads/${e}/messages`,tO,{query:t,...a,headers:ao([{"OpenAI-Beta":"assistants=v2"},a?.headers])})}delete(e,t,a){let{thread_id:r}=t;return this._client.delete(tX`/threads/${r}/messages/${e}`,{...a,headers:ao([{"OpenAI-Beta":"assistants=v2"},a?.headers])})}}class ax extends tV{retrieve(e,t,a){let{thread_id:r,run_id:n,...s}=t;return this._client.get(tX`/threads/${r}/runs/${n}/steps/${e}`,{query:s,...a,headers:ao([{"OpenAI-Beta":"assistants=v2"},a?.headers])})}list(e,t,a){let{thread_id:r,...n}=t;return this._client.getAPIList(tX`/threads/${r}/runs/${e}/steps`,tO,{query:n,...a,headers:ao([{"OpenAI-Beta":"assistants=v2"},a?.headers])})}}let a_=e=>{if("undefined"!=typeof Buffer){let t=Buffer.from(e,"base64");return Array.from(new Float32Array(t.buffer,t.byteOffset,t.length/Float32Array.BYTES_PER_ELEMENT))}{let t=atob(e),a=t.length,r=new Uint8Array(a);for(let e=0;e<a;e++)r[e]=t.charCodeAt(e);return Array.from(new Float32Array(r.buffer))}},av=e=>void 0!==globalThis.process?globalThis.process.env?.[e]?.trim()??void 0:void 0!==globalThis.Deno?globalThis.Deno.env?.get?.(e)?.trim():void 0;class ab extends tK{constructor(){super(...arguments),F.add(this),z.set(this,[]),Z.set(this,{}),B.set(this,{}),W.set(this,void 0),V.set(this,void 0),J.set(this,void 0),X.set(this,void 0),H.set(this,void 0),G.set(this,void 0),Y.set(this,void 0),K.set(this,void 0),Q.set(this,void 0)}[(z=new WeakMap,Z=new WeakMap,B=new WeakMap,W=new WeakMap,V=new WeakMap,J=new WeakMap,X=new WeakMap,H=new WeakMap,G=new WeakMap,Y=new WeakMap,K=new WeakMap,Q=new WeakMap,F=new WeakSet,Symbol.asyncIterator)](){let e=[],t=[],a=!1;return this.on("event",a=>{let r=t.shift();r?r.resolve(a):e.push(a)}),this.on("end",()=>{for(let e of(a=!0,t))e.resolve(void 0);t.length=0}),this.on("abort",e=>{for(let r of(a=!0,t))r.reject(e);t.length=0}),this.on("error",e=>{for(let r of(a=!0,t))r.reject(e);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:a?{value:void 0,done:!0}:new Promise((e,a)=>t.push({resolve:e,reject:a})).then(e=>e?{value:e,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}static fromReadableStream(e){let t=new q;return t._run(()=>t._fromReadableStream(e)),t}async _fromReadableStream(e,t){let a=t?.signal;a&&(a.aborted&&this.controller.abort(),a.addEventListener("abort",()=>this.controller.abort())),this._connected();let r=t_.fromReadableStream(e,this.controller);for await(let e of r)eE(this,F,"m",ee).call(this,e);if(r.controller.signal?.aborted)throw new eO;return this._addRun(eE(this,F,"m",et).call(this))}toReadableStream(){return new t_(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}static createToolAssistantStream(e,t,a,r){let n=new q;return n._run(()=>n._runToolAssistantStream(e,t,a,{...r,headers:{...r?.headers,"X-Stainless-Helper-Method":"stream"}})),n}async _createToolAssistantStream(e,t,a,r){let n=r?.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort()));let s={...a,stream:!0},i=await e.submitToolOutputs(t,s,{...r,signal:this.controller.signal});for await(let e of(this._connected(),i))eE(this,F,"m",ee).call(this,e);if(i.controller.signal?.aborted)throw new eO;return this._addRun(eE(this,F,"m",et).call(this))}static createThreadAssistantStream(e,t,a){let r=new q;return r._run(()=>r._threadAssistantStream(e,t,{...a,headers:{...a?.headers,"X-Stainless-Helper-Method":"stream"}})),r}static createAssistantStream(e,t,a,r){let n=new q;return n._run(()=>n._runAssistantStream(e,t,a,{...r,headers:{...r?.headers,"X-Stainless-Helper-Method":"stream"}})),n}currentEvent(){return eE(this,Y,"f")}currentRun(){return eE(this,K,"f")}currentMessageSnapshot(){return eE(this,W,"f")}currentRunStepSnapshot(){return eE(this,Q,"f")}async finalRunSteps(){return await this.done(),Object.values(eE(this,Z,"f"))}async finalMessages(){return await this.done(),Object.values(eE(this,B,"f"))}async finalRun(){if(await this.done(),!eE(this,V,"f"))throw Error("Final run was not received.");return eE(this,V,"f")}async _createThreadAssistantStream(e,t,a){let r=a?.signal;r&&(r.aborted&&this.controller.abort(),r.addEventListener("abort",()=>this.controller.abort()));let n={...t,stream:!0},s=await e.createAndRun(n,{...a,signal:this.controller.signal});for await(let e of(this._connected(),s))eE(this,F,"m",ee).call(this,e);if(s.controller.signal?.aborted)throw new eO;return this._addRun(eE(this,F,"m",et).call(this))}async _createAssistantStream(e,t,a,r){let n=r?.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort()));let s={...a,stream:!0},i=await e.create(t,s,{...r,signal:this.controller.signal});for await(let e of(this._connected(),i))eE(this,F,"m",ee).call(this,e);if(i.controller.signal?.aborted)throw new eO;return this._addRun(eE(this,F,"m",et).call(this))}static accumulateDelta(e,t){for(let[a,r]of Object.entries(t)){if(!e.hasOwnProperty(a)){e[a]=r;continue}let t=e[a];if(null==t||"index"===a||"type"===a){e[a]=r;continue}if("string"==typeof t&&"string"==typeof r)t+=r;else if("number"==typeof t&&"number"==typeof r)t+=r;else if(eB(t)&&eB(r))t=this.accumulateDelta(t,r);else if(Array.isArray(t)&&Array.isArray(r)){if(t.every(e=>"string"==typeof e||"number"==typeof e)){t.push(...r);continue}for(let e of r){if(!eB(e))throw Error(`Expected array delta entry to be an object but got: ${e}`);let a=e.index;if(null==a)throw console.error(e),Error("Expected array delta entry to have an `index` property");if("number"!=typeof a)throw Error(`Expected array delta entry \`index\` property to be a number but got ${a}`);let r=t[a];null==r?t.push(e):t[a]=this.accumulateDelta(r,e)}continue}else throw Error(`Unhandled record type: ${a}, deltaValue: ${r}, accValue: ${t}`);e[a]=t}return e}_addRun(e){return e}async _threadAssistantStream(e,t,a){return await this._createThreadAssistantStream(t,e,a)}async _runAssistantStream(e,t,a,r){return await this._createAssistantStream(t,e,a,r)}async _runToolAssistantStream(e,t,a,r){return await this._createToolAssistantStream(t,e,a,r)}}q=ab,ee=function(e){if(!this.ended)switch(ew(this,Y,e,"f"),eE(this,F,"m",en).call(this,e),e.event){case"thread.created":break;case"thread.run.created":case"thread.run.queued":case"thread.run.in_progress":case"thread.run.requires_action":case"thread.run.completed":case"thread.run.incomplete":case"thread.run.failed":case"thread.run.cancelling":case"thread.run.cancelled":case"thread.run.expired":eE(this,F,"m",el).call(this,e);break;case"thread.run.step.created":case"thread.run.step.in_progress":case"thread.run.step.delta":case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":eE(this,F,"m",er).call(this,e);break;case"thread.message.created":case"thread.message.in_progress":case"thread.message.delta":case"thread.message.completed":case"thread.message.incomplete":eE(this,F,"m",ea).call(this,e);break;case"error":throw Error("Encountered an error event in event processing - errors should be processed earlier")}},et=function(){if(this.ended)throw new eN("stream has ended, this shouldn't happen");if(!eE(this,V,"f"))throw Error("Final run has not been received");return eE(this,V,"f")},ea=function(e){let[t,a]=eE(this,F,"m",ei).call(this,e,eE(this,W,"f"));for(let e of(ew(this,W,t,"f"),eE(this,B,"f")[t.id]=t,a)){let a=t.content[e.index];a?.type=="text"&&this._emit("textCreated",a.text)}switch(e.event){case"thread.message.created":this._emit("messageCreated",e.data);break;case"thread.message.in_progress":break;case"thread.message.delta":if(this._emit("messageDelta",e.data.delta,t),e.data.delta.content)for(let a of e.data.delta.content){if("text"==a.type&&a.text){let e=a.text,r=t.content[a.index];if(r&&"text"==r.type)this._emit("textDelta",e,r.text);else throw Error("The snapshot associated with this text delta is not text or missing")}if(a.index!=eE(this,J,"f")){if(eE(this,X,"f"))switch(eE(this,X,"f").type){case"text":this._emit("textDone",eE(this,X,"f").text,eE(this,W,"f"));break;case"image_file":this._emit("imageFileDone",eE(this,X,"f").image_file,eE(this,W,"f"))}ew(this,J,a.index,"f")}ew(this,X,t.content[a.index],"f")}break;case"thread.message.completed":case"thread.message.incomplete":if(void 0!==eE(this,J,"f")){let t=e.data.content[eE(this,J,"f")];if(t)switch(t.type){case"image_file":this._emit("imageFileDone",t.image_file,eE(this,W,"f"));break;case"text":this._emit("textDone",t.text,eE(this,W,"f"))}}eE(this,W,"f")&&this._emit("messageDone",e.data),ew(this,W,void 0,"f")}},er=function(e){let t=eE(this,F,"m",es).call(this,e);switch(ew(this,Q,t,"f"),e.event){case"thread.run.step.created":this._emit("runStepCreated",e.data);break;case"thread.run.step.delta":let a=e.data.delta;if(a.step_details&&"tool_calls"==a.step_details.type&&a.step_details.tool_calls&&"tool_calls"==t.step_details.type)for(let e of a.step_details.tool_calls)e.index==eE(this,H,"f")?this._emit("toolCallDelta",e,t.step_details.tool_calls[e.index]):(eE(this,G,"f")&&this._emit("toolCallDone",eE(this,G,"f")),ew(this,H,e.index,"f"),ew(this,G,t.step_details.tool_calls[e.index],"f"),eE(this,G,"f")&&this._emit("toolCallCreated",eE(this,G,"f")));this._emit("runStepDelta",e.data.delta,t);break;case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":ew(this,Q,void 0,"f"),"tool_calls"==e.data.step_details.type&&eE(this,G,"f")&&(this._emit("toolCallDone",eE(this,G,"f")),ew(this,G,void 0,"f")),this._emit("runStepDone",e.data,t)}},en=function(e){eE(this,z,"f").push(e),this._emit("event",e)},es=function(e){switch(e.event){case"thread.run.step.created":return eE(this,Z,"f")[e.data.id]=e.data,e.data;case"thread.run.step.delta":let t=eE(this,Z,"f")[e.data.id];if(!t)throw Error("Received a RunStepDelta before creation of a snapshot");let a=e.data;if(a.delta){let r=q.accumulateDelta(t,a.delta);eE(this,Z,"f")[e.data.id]=r}return eE(this,Z,"f")[e.data.id];case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":case"thread.run.step.in_progress":eE(this,Z,"f")[e.data.id]=e.data}if(eE(this,Z,"f")[e.data.id])return eE(this,Z,"f")[e.data.id];throw Error("No snapshot available")},ei=function(e,t){let a=[];switch(e.event){case"thread.message.created":return[e.data,a];case"thread.message.delta":if(!t)throw Error("Received a delta with no existing snapshot (there should be one from message creation)");let r=e.data;if(r.delta.content)for(let e of r.delta.content)if(e.index in t.content){let a=t.content[e.index];t.content[e.index]=eE(this,F,"m",eo).call(this,e,a)}else t.content[e.index]=e,a.push(e);return[t,a];case"thread.message.in_progress":case"thread.message.completed":case"thread.message.incomplete":if(t)return[t,a];throw Error("Received thread message event with no existing snapshot")}throw Error("Tried to accumulate a non-message event")},eo=function(e,t){return q.accumulateDelta(t,e)},el=function(e){switch(ew(this,K,e.data,"f"),e.event){case"thread.run.created":case"thread.run.queued":case"thread.run.in_progress":break;case"thread.run.requires_action":case"thread.run.cancelled":case"thread.run.failed":case"thread.run.completed":case"thread.run.expired":case"thread.run.incomplete":ew(this,V,e.data,"f"),eE(this,G,"f")&&(this._emit("toolCallDone",eE(this,G,"f")),ew(this,G,void 0,"f"))}};class aw extends tV{constructor(){super(...arguments),this.steps=new ax(this._client)}create(e,t,a){let{include:r,...n}=t;return this._client.post(tX`/threads/${e}/runs`,{query:{include:r},body:n,...a,headers:ao([{"OpenAI-Beta":"assistants=v2"},a?.headers]),stream:t.stream??!1})}retrieve(e,t,a){let{thread_id:r}=t;return this._client.get(tX`/threads/${r}/runs/${e}`,{...a,headers:ao([{"OpenAI-Beta":"assistants=v2"},a?.headers])})}update(e,t,a){let{thread_id:r,...n}=t;return this._client.post(tX`/threads/${r}/runs/${e}`,{body:n,...a,headers:ao([{"OpenAI-Beta":"assistants=v2"},a?.headers])})}list(e,t={},a){return this._client.getAPIList(tX`/threads/${e}/runs`,tO,{query:t,...a,headers:ao([{"OpenAI-Beta":"assistants=v2"},a?.headers])})}cancel(e,t,a){let{thread_id:r}=t;return this._client.post(tX`/threads/${r}/runs/${e}/cancel`,{...a,headers:ao([{"OpenAI-Beta":"assistants=v2"},a?.headers])})}async createAndPoll(e,t,a){let r=await this.create(e,t,a);return await this.poll(r.id,{thread_id:e},a)}createAndStream(e,t,a){return ab.createAssistantStream(e,this._client.beta.threads.runs,t,a)}async poll(e,t,a){let r=ao([a?.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":a?.pollIntervalMs?.toString()??void 0}]);for(;;){let{data:n,response:s}=await this.retrieve(e,t,{...a,headers:{...a?.headers,...r}}).withResponse();switch(n.status){case"queued":case"in_progress":case"cancelling":let i=5e3;if(a?.pollIntervalMs)i=a.pollIntervalMs;else{let e=s.headers.get("openai-poll-after-ms");if(e){let t=parseInt(e);isNaN(t)||(i=t)}}await eJ(i);break;case"requires_action":case"incomplete":case"cancelled":case"completed":case"failed":case"expired":return n}}}stream(e,t,a){return ab.createAssistantStream(e,this._client.beta.threads.runs,t,a)}submitToolOutputs(e,t,a){let{thread_id:r,...n}=t;return this._client.post(tX`/threads/${r}/runs/${e}/submit_tool_outputs`,{body:n,...a,headers:ao([{"OpenAI-Beta":"assistants=v2"},a?.headers]),stream:t.stream??!1})}async submitToolOutputsAndPoll(e,t,a){let r=await this.submitToolOutputs(e,t,a);return await this.poll(r.id,t,a)}submitToolOutputsStream(e,t,a){return ab.createToolAssistantStream(e,this._client.beta.threads.runs,t,a)}}aw.Steps=ax;class aE extends tV{constructor(){super(...arguments),this.runs=new aw(this._client),this.messages=new ay(this._client)}create(e={},t){return this._client.post("/threads",{body:e,...t,headers:ao([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}retrieve(e,t){return this._client.get(tX`/threads/${e}`,{...t,headers:ao([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}update(e,t,a){return this._client.post(tX`/threads/${e}`,{body:t,...a,headers:ao([{"OpenAI-Beta":"assistants=v2"},a?.headers])})}delete(e,t){return this._client.delete(tX`/threads/${e}`,{...t,headers:ao([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}createAndRun(e,t){return this._client.post("/threads/runs",{body:e,...t,headers:ao([{"OpenAI-Beta":"assistants=v2"},t?.headers]),stream:e.stream??!1})}async createAndRunPoll(e,t){let a=await this.createAndRun(e,t);return await this.runs.poll(a.id,{thread_id:a.thread_id},t)}createAndRunStream(e,t){return ab.createThreadAssistantStream(e,this._client.beta.threads,t)}}aE.Runs=aw,aE.Messages=ay;class aA extends tV{constructor(){super(...arguments),this.realtime=new ag(this._client),this.assistants=new ah(this._client),this.threads=new aE(this._client)}}aA.Realtime=ag,aA.Assistants=ah,aA.Threads=aE;class aS extends tV{create(e,t){return this._client.post("/completions",{body:e,...t,stream:e.stream??!1})}}class aI extends tV{retrieve(e,t,a){let{container_id:r}=t;return this._client.get(tX`/containers/${r}/files/${e}/content`,{...a,headers:ao([{Accept:"application/binary"},a?.headers]),__binaryResponse:!0})}}class aN extends tV{constructor(){super(...arguments),this.content=new aI(this._client)}create(e,t,a){return this._client.post(tX`/containers/${e}/files`,t$({body:t,...a},this._client))}retrieve(e,t,a){let{container_id:r}=t;return this._client.get(tX`/containers/${r}/files/${e}`,a)}list(e,t={},a){return this._client.getAPIList(tX`/containers/${e}/files`,tO,{query:t,...a})}delete(e,t,a){let{container_id:r}=t;return this._client.delete(tX`/containers/${r}/files/${e}`,{...a,headers:ao([{Accept:"*/*"},a?.headers])})}}aN.Content=aI;class aT extends tV{constructor(){super(...arguments),this.files=new aN(this._client)}create(e,t){return this._client.post("/containers",{body:e,...t})}retrieve(e,t){return this._client.get(tX`/containers/${e}`,t)}list(e={},t){return this._client.getAPIList("/containers",tO,{query:e,...t})}delete(e,t){return this._client.delete(tX`/containers/${e}`,{...t,headers:ao([{Accept:"*/*"},t?.headers])})}}aT.Files=aN;class aO extends tV{create(e,t){let a=!!e.encoding_format,r=a?e.encoding_format:"base64";a&&e0(this._client).debug("embeddings/user defined encoding_format:",e.encoding_format);let n=this._client.post("/embeddings",{body:{...e,encoding_format:r},...t});return a?n:(e0(this._client).debug("embeddings/decoding base64 embeddings from base64"),n._thenUnwrap(e=>(e&&e.data&&e.data.forEach(e=>{let t=e.embedding;e.embedding=a_(t)}),e)))}}class aC extends tV{retrieve(e,t,a){let{eval_id:r,run_id:n}=t;return this._client.get(tX`/evals/${r}/runs/${n}/output_items/${e}`,a)}list(e,t,a){let{eval_id:r,...n}=t;return this._client.getAPIList(tX`/evals/${r}/runs/${e}/output_items`,tO,{query:n,...a})}}class ak extends tV{constructor(){super(...arguments),this.outputItems=new aC(this._client)}create(e,t,a){return this._client.post(tX`/evals/${e}/runs`,{body:t,...a})}retrieve(e,t,a){let{eval_id:r}=t;return this._client.get(tX`/evals/${r}/runs/${e}`,a)}list(e,t={},a){return this._client.getAPIList(tX`/evals/${e}/runs`,tO,{query:t,...a})}delete(e,t,a){let{eval_id:r}=t;return this._client.delete(tX`/evals/${r}/runs/${e}`,a)}cancel(e,t,a){let{eval_id:r}=t;return this._client.post(tX`/evals/${r}/runs/${e}`,a)}}ak.OutputItems=aC;class aR extends tV{constructor(){super(...arguments),this.runs=new ak(this._client)}create(e,t){return this._client.post("/evals",{body:e,...t})}retrieve(e,t){return this._client.get(tX`/evals/${e}`,t)}update(e,t,a){return this._client.post(tX`/evals/${e}`,{body:t,...a})}list(e={},t){return this._client.getAPIList("/evals",tO,{query:e,...t})}delete(e,t){return this._client.delete(tX`/evals/${e}`,t)}}aR.Runs=ak;class aP extends tV{create(e,t){return this._client.post("/files",t$({body:e,...t},this._client))}retrieve(e,t){return this._client.get(tX`/files/${e}`,t)}list(e={},t){return this._client.getAPIList("/files",tO,{query:e,...t})}delete(e,t){return this._client.delete(tX`/files/${e}`,t)}content(e,t){return this._client.get(tX`/files/${e}/content`,{...t,headers:ao([{Accept:"application/binary"},t?.headers]),__binaryResponse:!0})}async waitForProcessing(e,{pollInterval:t=5e3,maxWait:a=18e5}={}){let r=new Set(["processed","error","deleted"]),n=Date.now(),s=await this.retrieve(e);for(;!s.status||!r.has(s.status);)if(await eJ(t),s=await this.retrieve(e),Date.now()-n>a)throw new ek({message:`Giving up on waiting for file ${e} to finish processing after ${a} milliseconds.`});return s}}class a$ extends tV{}class aD extends tV{run(e,t){return this._client.post("/fine_tuning/alpha/graders/run",{body:e,...t})}validate(e,t){return this._client.post("/fine_tuning/alpha/graders/validate",{body:e,...t})}}class aj extends tV{constructor(){super(...arguments),this.graders=new aD(this._client)}}aj.Graders=aD;class aL extends tV{create(e,t,a){return this._client.getAPIList(tX`/fine_tuning/checkpoints/${e}/permissions`,tT,{body:t,method:"post",...a})}retrieve(e,t={},a){return this._client.get(tX`/fine_tuning/checkpoints/${e}/permissions`,{query:t,...a})}delete(e,t,a){let{fine_tuned_model_checkpoint:r}=t;return this._client.delete(tX`/fine_tuning/checkpoints/${r}/permissions/${e}`,a)}}class aM extends tV{constructor(){super(...arguments),this.permissions=new aL(this._client)}}aM.Permissions=aL;class aU extends tV{list(e,t={},a){return this._client.getAPIList(tX`/fine_tuning/jobs/${e}/checkpoints`,tO,{query:t,...a})}}class aF extends tV{constructor(){super(...arguments),this.checkpoints=new aU(this._client)}create(e,t){return this._client.post("/fine_tuning/jobs",{body:e,...t})}retrieve(e,t){return this._client.get(tX`/fine_tuning/jobs/${e}`,t)}list(e={},t){return this._client.getAPIList("/fine_tuning/jobs",tO,{query:e,...t})}cancel(e,t){return this._client.post(tX`/fine_tuning/jobs/${e}/cancel`,t)}listEvents(e,t={},a){return this._client.getAPIList(tX`/fine_tuning/jobs/${e}/events`,tO,{query:t,...a})}pause(e,t){return this._client.post(tX`/fine_tuning/jobs/${e}/pause`,t)}resume(e,t){return this._client.post(tX`/fine_tuning/jobs/${e}/resume`,t)}}aF.Checkpoints=aU;class aq extends tV{constructor(){super(...arguments),this.methods=new a$(this._client),this.jobs=new aF(this._client),this.checkpoints=new aM(this._client),this.alpha=new aj(this._client)}}aq.Methods=a$,aq.Jobs=aF,aq.Checkpoints=aM,aq.Alpha=aj;class az extends tV{}class aZ extends tV{constructor(){super(...arguments),this.graderModels=new az(this._client)}}aZ.GraderModels=az;class aB extends tV{createVariation(e,t){return this._client.post("/images/variations",t$({body:e,...t},this._client))}edit(e,t){return this._client.post("/images/edits",t$({body:e,...t},this._client))}generate(e,t){return this._client.post("/images/generations",{body:e,...t})}}class aW extends tV{retrieve(e,t){return this._client.get(tX`/models/${e}`,t)}list(e){return this._client.getAPIList("/models",tT,e)}delete(e,t){return this._client.delete(tX`/models/${e}`,t)}}class aV extends tV{create(e,t){return this._client.post("/moderations",{body:e,...t})}}function aJ(e,t){let a=e.output.map(e=>{if("function_call"===e.type)return{...e,parsed_arguments:function(e,t){let a=function(e,t){return e.find(e=>"function"===e.type&&e.name===t)}(e.tools??[],t.name);return{...t,...t,parsed_arguments:function(e){return e?.$brand==="auto-parseable-tool"}(a)?a.$parseRaw(t.arguments):a?.strict?JSON.parse(t.arguments):null}}(t,e)};if("message"===e.type){let a=e.content.map(e=>{var a,r;return"output_text"===e.type?{...e,parsed:(a=t,r=e.text,a.text?.format?.type!=="json_schema"?null:"$parseRaw"in a.text?.format?(a.text?.format).$parseRaw(r):JSON.parse(r))}:e});return{...e,content:a}}return e}),r=Object.assign({},e,{output:a});return Object.getOwnPropertyDescriptor(e,"output_text")||aX(r),Object.defineProperty(r,"output_parsed",{enumerable:!0,get(){for(let e of r.output)if("message"===e.type){for(let t of e.content)if("output_text"===t.type&&null!==t.parsed)return t.parsed}return null}}),r}function aX(e){let t=[];for(let a of e.output)if("message"===a.type)for(let e of a.content)"output_text"===e.type&&t.push(e.text);e.output_text=t.join("")}class aH extends tK{constructor(e){super(),ed.add(this),ec.set(this,void 0),eu.set(this,void 0),ep.set(this,void 0),ew(this,ec,e,"f")}static createResponse(e,t,a){let r=new aH(t);return r._run(()=>r._createOrRetrieveResponse(e,t,{...a,headers:{...a?.headers,"X-Stainless-Helper-Method":"stream"}})),r}async _createOrRetrieveResponse(e,t,a){let r,n=a?.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort())),eE(this,ed,"m",eh).call(this);let s=null;for await(let n of("response_id"in t?(r=await e.responses.retrieve(t.response_id,{stream:!0},{...a,signal:this.controller.signal,stream:!0}),s=t.starting_after??null):r=await e.responses.create({...t,stream:!0},{...a,signal:this.controller.signal}),this._connected(),r))eE(this,ed,"m",em).call(this,n,s);if(r.controller.signal?.aborted)throw new eO;return eE(this,ed,"m",ef).call(this)}[(ec=new WeakMap,eu=new WeakMap,ep=new WeakMap,ed=new WeakSet,eh=function(){this.ended||ew(this,eu,void 0,"f")},em=function(e,t){if(this.ended)return;let a=(e,a)=>{(null==t||a.sequence_number>t)&&this._emit(e,a)},r=eE(this,ed,"m",eg).call(this,e);switch(a("event",e),e.type){case"response.output_text.delta":{let t=r.output[e.output_index];if(!t)throw new eN(`missing output at index ${e.output_index}`);if("message"===t.type){let r=t.content[e.content_index];if(!r)throw new eN(`missing content at index ${e.content_index}`);if("output_text"!==r.type)throw new eN(`expected content to be 'output_text', got ${r.type}`);a("response.output_text.delta",{...e,snapshot:r.text})}break}case"response.function_call_arguments.delta":{let t=r.output[e.output_index];if(!t)throw new eN(`missing output at index ${e.output_index}`);"function_call"===t.type&&a("response.function_call_arguments.delta",{...e,snapshot:t.arguments});break}default:a(e.type,e)}},ef=function(){if(this.ended)throw new eN("stream has ended, this shouldn't happen");let e=eE(this,eu,"f");if(!e)throw new eN("request ended without sending any events");ew(this,eu,void 0,"f");let t=function(e,t){var a;return t&&(a=t,tQ(a.text?.format))?aJ(e,t):{...e,output_parsed:null,output:e.output.map(e=>"function_call"===e.type?{...e,parsed_arguments:null}:"message"===e.type?{...e,content:e.content.map(e=>({...e,parsed:null}))}:e)}}(e,eE(this,ec,"f"));return ew(this,ep,t,"f"),t},eg=function(e){let t=eE(this,eu,"f");if(!t){if("response.created"!==e.type)throw new eN(`When snapshot hasn't been set yet, expected 'response.created' event, got ${e.type}`);return ew(this,eu,e.response,"f")}switch(e.type){case"response.output_item.added":t.output.push(e.item);break;case"response.content_part.added":{let a=t.output[e.output_index];if(!a)throw new eN(`missing output at index ${e.output_index}`);"message"===a.type&&a.content.push(e.part);break}case"response.output_text.delta":{let a=t.output[e.output_index];if(!a)throw new eN(`missing output at index ${e.output_index}`);if("message"===a.type){let t=a.content[e.content_index];if(!t)throw new eN(`missing content at index ${e.content_index}`);if("output_text"!==t.type)throw new eN(`expected content to be 'output_text', got ${t.type}`);t.text+=e.delta}break}case"response.function_call_arguments.delta":{let a=t.output[e.output_index];if(!a)throw new eN(`missing output at index ${e.output_index}`);"function_call"===a.type&&(a.arguments+=e.delta);break}case"response.completed":ew(this,eu,e.response,"f")}return t},Symbol.asyncIterator)](){let e=[],t=[],a=!1;return this.on("event",a=>{let r=t.shift();r?r.resolve(a):e.push(a)}),this.on("end",()=>{for(let e of(a=!0,t))e.resolve(void 0);t.length=0}),this.on("abort",e=>{for(let r of(a=!0,t))r.reject(e);t.length=0}),this.on("error",e=>{for(let r of(a=!0,t))r.reject(e);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:a?{value:void 0,done:!0}:new Promise((e,a)=>t.push({resolve:e,reject:a})).then(e=>e?{value:e,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}async finalResponse(){await this.done();let e=eE(this,ep,"f");if(!e)throw new eN("stream ended without producing a ChatCompletion");return e}}class aG extends tV{list(e,t={},a){return this._client.getAPIList(tX`/responses/${e}/input_items`,tO,{query:t,...a})}}class aY extends tV{constructor(){super(...arguments),this.inputItems=new aG(this._client)}create(e,t){return this._client.post("/responses",{body:e,...t,stream:e.stream??!1})._thenUnwrap(e=>("object"in e&&"response"===e.object&&aX(e),e))}retrieve(e,t={},a){return this._client.get(tX`/responses/${e}`,{query:t,...a,stream:t?.stream??!1})}delete(e,t){return this._client.delete(tX`/responses/${e}`,{...t,headers:ao([{Accept:"*/*"},t?.headers])})}parse(e,t){return this._client.responses.create(e,t)._thenUnwrap(t=>aJ(t,e))}stream(e,t){return aH.createResponse(this._client,e,t)}cancel(e,t){return this._client.post(tX`/responses/${e}/cancel`,t)}}aY.InputItems=aG;class aK extends tV{create(e,t,a){return this._client.post(tX`/uploads/${e}/parts`,t$({body:t,...a},this._client))}}class aQ extends tV{constructor(){super(...arguments),this.parts=new aK(this._client)}create(e,t){return this._client.post("/uploads",{body:e,...t})}cancel(e,t){return this._client.post(tX`/uploads/${e}/cancel`,t)}complete(e,t,a){return this._client.post(tX`/uploads/${e}/complete`,{body:t,...a})}}aQ.Parts=aK;let a0=async e=>{let t=await Promise.allSettled(e),a=t.filter(e=>"rejected"===e.status);if(a.length){for(let e of a)console.error(e.reason);throw Error(`${a.length} promise(s) failed - see the above errors`)}let r=[];for(let e of t)"fulfilled"===e.status&&r.push(e.value);return r};class a1 extends tV{create(e,t,a){return this._client.post(tX`/vector_stores/${e}/file_batches`,{body:t,...a,headers:ao([{"OpenAI-Beta":"assistants=v2"},a?.headers])})}retrieve(e,t,a){let{vector_store_id:r}=t;return this._client.get(tX`/vector_stores/${r}/file_batches/${e}`,{...a,headers:ao([{"OpenAI-Beta":"assistants=v2"},a?.headers])})}cancel(e,t,a){let{vector_store_id:r}=t;return this._client.post(tX`/vector_stores/${r}/file_batches/${e}/cancel`,{...a,headers:ao([{"OpenAI-Beta":"assistants=v2"},a?.headers])})}async createAndPoll(e,t,a){let r=await this.create(e,t);return await this.poll(e,r.id,a)}listFiles(e,t,a){let{vector_store_id:r,...n}=t;return this._client.getAPIList(tX`/vector_stores/${r}/file_batches/${e}/files`,tO,{query:n,...a,headers:ao([{"OpenAI-Beta":"assistants=v2"},a?.headers])})}async poll(e,t,a){let r=ao([a?.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":a?.pollIntervalMs?.toString()??void 0}]);for(;;){let{data:n,response:s}=await this.retrieve(t,{vector_store_id:e},{...a,headers:r}).withResponse();switch(n.status){case"in_progress":let i=5e3;if(a?.pollIntervalMs)i=a.pollIntervalMs;else{let e=s.headers.get("openai-poll-after-ms");if(e){let t=parseInt(e);isNaN(t)||(i=t)}}await eJ(i);break;case"failed":case"cancelled":case"completed":return n}}}async uploadAndPoll(e,{files:t,fileIds:a=[]},r){if(null==t||0==t.length)throw Error("No `files` provided to process. If you've already uploaded files you should use `.createAndPoll()` instead");let n=Math.min(r?.maxConcurrency??5,t.length),s=this._client,i=t.values(),o=[...a];async function l(e){for(let t of e){let e=await s.files.create({file:t,purpose:"assistants"},r);o.push(e.id)}}let d=Array(n).fill(i).map(l);return await a0(d),await this.createAndPoll(e,{file_ids:o})}}class a3 extends tV{create(e,t,a){return this._client.post(tX`/vector_stores/${e}/files`,{body:t,...a,headers:ao([{"OpenAI-Beta":"assistants=v2"},a?.headers])})}retrieve(e,t,a){let{vector_store_id:r}=t;return this._client.get(tX`/vector_stores/${r}/files/${e}`,{...a,headers:ao([{"OpenAI-Beta":"assistants=v2"},a?.headers])})}update(e,t,a){let{vector_store_id:r,...n}=t;return this._client.post(tX`/vector_stores/${r}/files/${e}`,{body:n,...a,headers:ao([{"OpenAI-Beta":"assistants=v2"},a?.headers])})}list(e,t={},a){return this._client.getAPIList(tX`/vector_stores/${e}/files`,tO,{query:t,...a,headers:ao([{"OpenAI-Beta":"assistants=v2"},a?.headers])})}delete(e,t,a){let{vector_store_id:r}=t;return this._client.delete(tX`/vector_stores/${r}/files/${e}`,{...a,headers:ao([{"OpenAI-Beta":"assistants=v2"},a?.headers])})}async createAndPoll(e,t,a){let r=await this.create(e,t,a);return await this.poll(e,r.id,a)}async poll(e,t,a){let r=ao([a?.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":a?.pollIntervalMs?.toString()??void 0}]);for(;;){let n=await this.retrieve(t,{vector_store_id:e},{...a,headers:r}).withResponse(),s=n.data;switch(s.status){case"in_progress":let i=5e3;if(a?.pollIntervalMs)i=a.pollIntervalMs;else{let e=n.response.headers.get("openai-poll-after-ms");if(e){let t=parseInt(e);isNaN(t)||(i=t)}}await eJ(i);break;case"failed":case"completed":return s}}}async upload(e,t,a){let r=await this._client.files.create({file:t,purpose:"assistants"},a);return this.create(e,{file_id:r.id},a)}async uploadAndPoll(e,t,a){let r=await this.upload(e,t,a);return await this.poll(e,r.id,a)}content(e,t,a){let{vector_store_id:r}=t;return this._client.getAPIList(tX`/vector_stores/${r}/files/${e}/content`,tT,{...a,headers:ao([{"OpenAI-Beta":"assistants=v2"},a?.headers])})}}class a2 extends tV{constructor(){super(...arguments),this.files=new a3(this._client),this.fileBatches=new a1(this._client)}create(e,t){return this._client.post("/vector_stores",{body:e,...t,headers:ao([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}retrieve(e,t){return this._client.get(tX`/vector_stores/${e}`,{...t,headers:ao([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}update(e,t,a){return this._client.post(tX`/vector_stores/${e}`,{body:t,...a,headers:ao([{"OpenAI-Beta":"assistants=v2"},a?.headers])})}list(e={},t){return this._client.getAPIList("/vector_stores",tO,{query:e,...t,headers:ao([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}delete(e,t){return this._client.delete(tX`/vector_stores/${e}`,{...t,headers:ao([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}search(e,t,a){return this._client.getAPIList(tX`/vector_stores/${e}/search`,tT,{body:t,method:"post",...a,headers:ao([{"OpenAI-Beta":"assistants=v2"},a?.headers])})}}a2.Files=a3,a2.FileBatches=a1;class a9{constructor({baseURL:e=av("OPENAI_BASE_URL"),apiKey:t=av("OPENAI_API_KEY"),organization:a=av("OPENAI_ORG_ID")??null,project:r=av("OPENAI_PROJECT_ID")??null,...n}={}){if(ex.set(this,void 0),this.completions=new aS(this),this.chat=new an(this),this.embeddings=new aO(this),this.files=new aP(this),this.images=new aB(this),this.audio=new au(this),this.moderations=new aV(this),this.models=new aW(this),this.fineTuning=new aq(this),this.graders=new aZ(this),this.vectorStores=new a2(this),this.beta=new aA(this),this.batches=new ap(this),this.uploads=new aQ(this),this.responses=new aY(this),this.evals=new aR(this),this.containers=new aT(this),void 0===t)throw new eN("The OPENAI_API_KEY environment variable is missing or empty; either provide it, or instantiate the OpenAI client with an apiKey option, like new OpenAI({ apiKey: 'My API Key' }).");let s={apiKey:t,organization:a,project:r,...n,baseURL:e||"https://api.openai.com/v1"};if(!s.dangerouslyAllowBrowser&&e2())throw new eN("It looks like you're running in a browser-like environment.\n\nThis is disabled by default, as it risks exposing your secret API credentials to attackers.\nIf you understand the risks and have appropriate mitigations in place,\nyou can set the `dangerouslyAllowBrowser` option to `true`, e.g.,\n\nnew OpenAI({ apiKey, dangerouslyAllowBrowser: true });\n\nhttps://help.openai.com/en/articles/5112595-best-practices-for-api-key-safety\n");this.baseURL=s.baseURL,this.timeout=s.timeout??ey.DEFAULT_TIMEOUT,this.logger=s.logger??console;let i="warn";this.logLevel=i,this.logLevel=eH(s.logLevel,"ClientOptions.logLevel",this)??eH(av("OPENAI_LOG"),"process.env['OPENAI_LOG']",this)??i,this.fetchOptions=s.fetchOptions,this.maxRetries=s.maxRetries??2,this.fetch=s.fetch??function(){if("undefined"!=typeof fetch)return fetch;throw Error("`fetch` is not defined as a global; Either pass `fetch` to the client, `new OpenAI({ fetch })` or polyfill the global, `globalThis.fetch = fetch`")}(),ew(this,ex,ta,"f"),this._options=s,this.apiKey=t,this.organization=a,this.project=r}withOptions(e){return new this.constructor({...this._options,baseURL:this.baseURL,maxRetries:this.maxRetries,timeout:this.timeout,logger:this.logger,logLevel:this.logLevel,fetchOptions:this.fetchOptions,apiKey:this.apiKey,organization:this.organization,project:this.project,...e})}defaultQuery(){return this._options.defaultQuery}validateHeaders({values:e,nulls:t}){}authHeaders(e){return ao([{Authorization:`Bearer ${this.apiKey}`}])}stringifyQuery(e){return function(e,t={}){let a,r,n=e,s=function(e=tm){let t;if(void 0!==e.allowEmptyArrays&&"boolean"!=typeof e.allowEmptyArrays)throw TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(void 0!==e.encodeDotInKeys&&"boolean"!=typeof e.encodeDotInKeys)throw TypeError("`encodeDotInKeys` option can only be `true` or `false`, when provided");if(null!==e.encoder&&void 0!==e.encoder&&"function"!=typeof e.encoder)throw TypeError("Encoder has to be a function.");let a=e.charset||tm.charset;if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");let r=tr;if(void 0!==e.format){if(!tl.call(tn,e.format))throw TypeError("Unknown format option provided.");r=e.format}let n=tn[r],s=tm.filter;if(("function"==typeof e.filter||tc(e.filter))&&(s=e.filter),t=e.arrayFormat&&e.arrayFormat in td?e.arrayFormat:"indices"in e?e.indices?"indices":"repeat":tm.arrayFormat,"commaRoundTrip"in e&&"boolean"!=typeof e.commaRoundTrip)throw TypeError("`commaRoundTrip` must be a boolean, or absent");let i=void 0===e.allowDots?!0==!!e.encodeDotInKeys||tm.allowDots:!!e.allowDots;return{addQueryPrefix:"boolean"==typeof e.addQueryPrefix?e.addQueryPrefix:tm.addQueryPrefix,allowDots:i,allowEmptyArrays:"boolean"==typeof e.allowEmptyArrays?!!e.allowEmptyArrays:tm.allowEmptyArrays,arrayFormat:t,charset:a,charsetSentinel:"boolean"==typeof e.charsetSentinel?e.charsetSentinel:tm.charsetSentinel,commaRoundTrip:!!e.commaRoundTrip,delimiter:void 0===e.delimiter?tm.delimiter:e.delimiter,encode:"boolean"==typeof e.encode?e.encode:tm.encode,encodeDotInKeys:"boolean"==typeof e.encodeDotInKeys?e.encodeDotInKeys:tm.encodeDotInKeys,encoder:"function"==typeof e.encoder?e.encoder:tm.encoder,encodeValuesOnly:"boolean"==typeof e.encodeValuesOnly?e.encodeValuesOnly:tm.encodeValuesOnly,filter:s,format:r,formatter:n,serializeDate:"function"==typeof e.serializeDate?e.serializeDate:tm.serializeDate,skipNulls:"boolean"==typeof e.skipNulls?e.skipNulls:tm.skipNulls,sort:"function"==typeof e.sort?e.sort:null,strictNullHandling:"boolean"==typeof e.strictNullHandling?e.strictNullHandling:tm.strictNullHandling}}(t);"function"==typeof s.filter?n=(0,s.filter)("",n):tc(s.filter)&&(a=s.filter);let i=[];if("object"!=typeof n||null===n)return"";let o=td[s.arrayFormat],l="comma"===o&&s.commaRoundTrip;a||(a=Object.keys(n)),s.sort&&a.sort(s.sort);let d=new WeakMap;for(let e=0;e<a.length;++e){let t=a[e];s.skipNulls&&null===n[t]||tp(i,function e(t,a,r,n,s,i,o,l,d,c,u,p,h,m,f,g,y,x){var _,v;let b,w=t,E=x,A=0,S=!1;for(;void 0!==(E=E.get(tf))&&!S;){let e=E.get(t);if(A+=1,void 0!==e)if(e===A)throw RangeError("Cyclic object value");else S=!0;void 0===E.get(tf)&&(A=0)}if("function"==typeof c?w=c(a,w):w instanceof Date?w=h?.(w):"comma"===r&&tc(w)&&(w=to(w,function(e){return e instanceof Date?h?.(e):e})),null===w){if(i)return d&&!g?d(a,tm.encoder,y,"key",m):a;w=""}if("string"==typeof(_=w)||"number"==typeof _||"boolean"==typeof _||"symbol"==typeof _||"bigint"==typeof _||(v=w)&&"object"==typeof v&&v.constructor&&v.constructor.isBuffer&&v.constructor.isBuffer(v)){if(d){let e=g?a:d(a,tm.encoder,y,"key",m);return[f?.(e)+"="+f?.(d(w,tm.encoder,y,"value",m))]}return[f?.(a)+"="+f?.(String(w))]}let I=[];if(void 0===w)return I;if("comma"===r&&tc(w))g&&d&&(w=to(w,d)),b=[{value:w.length>0?w.join(",")||null:void 0}];else if(tc(c))b=c;else{let e=Object.keys(w);b=u?e.sort(u):e}let N=l?String(a).replace(/\./g,"%2E"):String(a),T=n&&tc(w)&&1===w.length?N+"[]":N;if(s&&tc(w)&&0===w.length)return T+"[]";for(let a=0;a<b.length;++a){let _=b[a],v="object"==typeof _&&void 0!==_.value?_.value:w[_];if(o&&null===v)continue;let E=p&&l?_.replace(/\./g,"%2E"):_,S=tc(w)?"function"==typeof r?r(T,E):T:T+(p?"."+E:"["+E+"]");x.set(t,A);let N=new WeakMap;N.set(tf,x),tp(I,e(v,S,r,n,s,i,o,l,"comma"===r&&g&&tc(w)?null:d,c,u,p,h,m,f,g,y,N))}return I}(n[t],t,o,l,s.allowEmptyArrays,s.strictNullHandling,s.skipNulls,s.encodeDotInKeys,s.encode?s.encoder:null,s.filter,s.sort,s.allowDots,s.serializeDate,s.format,s.formatter,s.encodeValuesOnly,s.charset,d))}let c=i.join(s.delimiter),u=!0===s.addQueryPrefix?"?":"";return s.charsetSentinel&&("iso-8859-1"===s.charset?u+="utf8=%26%2310003%3B&":u+="utf8=%E2%9C%93&"),c.length>0?u+c:""}(e,{arrayFormat:"brackets"})}getUserAgent(){return`${this.constructor.name}/JS ${e3}`}defaultIdempotencyKey(){return`stainless-node-retry-${eA()}`}makeStatusError(e,t,a,r){return eT.generate(e,t,a,r)}buildURL(e,t){let a=new URL(eZ(e)?e:this.baseURL+(this.baseURL.endsWith("/")&&e.startsWith("/")?e.slice(1):e)),r=this.defaultQuery();return!function(e){if(!e)return!0;for(let t in e)return!1;return!0}(r)&&(t={...r,...t}),"object"==typeof t&&t&&!Array.isArray(t)&&(a.search=this.stringifyQuery(t)),a.toString()}async prepareOptions(e){}async prepareRequest(e,{url:t,options:a}){}get(e,t){return this.methodRequest("get",e,t)}post(e,t){return this.methodRequest("post",e,t)}patch(e,t){return this.methodRequest("patch",e,t)}put(e,t){return this.methodRequest("put",e,t)}delete(e,t){return this.methodRequest("delete",e,t)}methodRequest(e,t,a){return this.request(Promise.resolve(a).then(a=>({method:e,path:t,...a})))}request(e,t=null){return new tS(this,this.makeRequest(e,t,void 0))}async makeRequest(e,t,a){let r=await e,n=r.maxRetries??this.maxRetries;null==t&&(t=n),await this.prepareOptions(r);let{req:s,url:i,timeout:o}=this.buildRequest(r,{retryCount:n-t});await this.prepareRequest(s,{url:i,options:r});let l="log_"+(0x1000000*Math.random()|0).toString(16).padStart(6,"0"),d=void 0===a?"":`, retryOf: ${a}`,c=Date.now();if(e0(this).debug(`[${l}] sending request`,e1({retryOfRequestLogID:a,method:r.method,url:i,options:r,headers:s.headers})),r.signal?.aborted)throw new eO;let u=new AbortController,p=await this.fetchWithTimeout(i,s,o,u).catch(eI),h=Date.now();if(p instanceof Error){let e=`retrying, ${t} attempts remaining`;if(r.signal?.aborted)throw new eO;let n=eS(p)||/timed? ?out/i.test(String(p)+("cause"in p?String(p.cause):""));if(t)return e0(this).info(`[${l}] connection ${n?"timed out":"failed"} - ${e}`),e0(this).debug(`[${l}] connection ${n?"timed out":"failed"} (${e})`,e1({retryOfRequestLogID:a,url:i,durationMs:h-c,message:p.message})),this.retryRequest(r,t,a??l);if(e0(this).info(`[${l}] connection ${n?"timed out":"failed"} - error; no more retries left`),e0(this).debug(`[${l}] connection ${n?"timed out":"failed"} (error; no more retries left)`,e1({retryOfRequestLogID:a,url:i,durationMs:h-c,message:p.message})),n)throw new ek;throw new eC({cause:p})}let m=[...p.headers.entries()].filter(([e])=>"x-request-id"===e).map(([e,t])=>", "+e+": "+JSON.stringify(t)).join(""),f=`[${l}${d}${m}] ${s.method} ${i} ${p.ok?"succeeded":"failed"} with status ${p.status} in ${h-c}ms`;if(!p.ok){let e=this.shouldRetry(p);if(t&&e){let e=`retrying, ${t} attempts remaining`;return await tt(p.body),e0(this).info(`${f} - ${e}`),e0(this).debug(`[${l}] response error (${e})`,e1({retryOfRequestLogID:a,url:p.url,status:p.status,headers:p.headers,durationMs:h-c})),this.retryRequest(r,t,a??l,p.headers)}let n=e?"error; no more retries left":"error; not retryable";e0(this).info(`${f} - ${n}`);let s=await p.text().catch(e=>eI(e).message),i=eV(s),o=i?void 0:s;throw e0(this).debug(`[${l}] response error (${n})`,e1({retryOfRequestLogID:a,url:p.url,status:p.status,headers:p.headers,message:o,durationMs:Date.now()-c})),this.makeStatusError(p.status,i,o,p.headers)}return e0(this).info(f),e0(this).debug(`[${l}] response start`,e1({retryOfRequestLogID:a,url:p.url,status:p.status,headers:p.headers,durationMs:h-c})),{response:p,options:r,controller:u,requestLogID:l,retryOfRequestLogID:a,startTime:c}}getAPIList(e,t,a){return this.requestAPIList(t,{method:"get",path:e,...a})}requestAPIList(e,t){return new tN(this,this.makeRequest(t,null,void 0),e)}async fetchWithTimeout(e,t,a,r){let{signal:n,method:s,...i}=t||{};n&&n.addEventListener("abort",()=>r.abort());let o=setTimeout(()=>r.abort(),a),l=globalThis.ReadableStream&&i.body instanceof globalThis.ReadableStream||"object"==typeof i.body&&null!==i.body&&Symbol.asyncIterator in i.body,d={signal:r.signal,...l?{duplex:"half"}:{},method:"GET",...i};s&&(d.method=s.toUpperCase());try{return await this.fetch.call(void 0,e,d)}finally{clearTimeout(o)}}shouldRetry(e){let t=e.headers.get("x-should-retry");return"true"===t||"false"!==t&&(408===e.status||409===e.status||429===e.status||!!(e.status>=500))}async retryRequest(e,t,a,r){let n,s=r?.get("retry-after-ms");if(s){let e=parseFloat(s);Number.isNaN(e)||(n=e)}let i=r?.get("retry-after");if(i&&!n){let e=parseFloat(i);n=Number.isNaN(e)?Date.parse(i)-Date.now():1e3*e}if(!(n&&0<=n&&n<6e4)){let a=e.maxRetries??this.maxRetries;n=this.calculateDefaultRetryTimeoutMillis(t,a)}return await eJ(n),this.makeRequest(e,t-1,a)}calculateDefaultRetryTimeoutMillis(e,t){return Math.min(.5*Math.pow(2,t-e),8)*(1-.25*Math.random())*1e3}buildRequest(e,{retryCount:t=0}={}){let a={...e},{method:r,path:n,query:s}=a,i=this.buildURL(n,s);"timeout"in a&&eW("timeout",a.timeout),a.timeout=a.timeout??this.timeout;let{bodyHeaders:o,body:l}=this.buildBody({options:a}),d=this.buildHeaders({options:e,method:r,bodyHeaders:o,retryCount:t});return{req:{method:r,headers:d,...a.signal&&{signal:a.signal},...globalThis.ReadableStream&&l instanceof globalThis.ReadableStream&&{duplex:"half"},...l&&{body:l},...this.fetchOptions??{},...a.fetchOptions??{}},url:i,timeout:a.timeout}}buildHeaders({options:e,method:t,bodyHeaders:a,retryCount:r}){let n={};this.idempotencyHeader&&"get"!==t&&(e.idempotencyKey||(e.idempotencyKey=this.defaultIdempotencyKey()),n[this.idempotencyHeader]=e.idempotencyKey);let s=ao([n,{Accept:"application/json","User-Agent":this.getUserAgent(),"X-Stainless-Retry-Count":String(r),...e.timeout?{"X-Stainless-Timeout":String(Math.trunc(e.timeout/1e3))}:{},...e6(),"OpenAI-Organization":this.organization,"OpenAI-Project":this.project},this.authHeaders(e),this._options.defaultHeaders,a,e.headers]);return this.validateHeaders(s),s.values}buildBody({options:{body:e,headers:t}}){if(!e)return{bodyHeaders:void 0,body:void 0};let a=ao([t]);return ArrayBuffer.isView(e)||e instanceof ArrayBuffer||e instanceof DataView||"string"==typeof e&&a.values.has("content-type")||e instanceof Blob||e instanceof FormData||e instanceof URLSearchParams||globalThis.ReadableStream&&e instanceof globalThis.ReadableStream?{bodyHeaders:void 0,body:e}:"object"==typeof e&&(Symbol.asyncIterator in e||Symbol.iterator in e&&"next"in e&&"function"==typeof e.next)?{bodyHeaders:void 0,body:e7(e)}:eE(this,ex,"f").call(this,{body:e,headers:a})}}ey=a9,ex=new WeakMap,a9.OpenAI=ey,a9.DEFAULT_TIMEOUT=6e5,a9.OpenAIError=eN,a9.APIError=eT,a9.APIConnectionError=eC,a9.APIConnectionTimeoutError=ek,a9.APIUserAbortError=eO,a9.NotFoundError=eD,a9.ConflictError=ej,a9.RateLimitError=eM,a9.BadRequestError=eR,a9.AuthenticationError=eP,a9.InternalServerError=eU,a9.PermissionDeniedError=e$,a9.UnprocessableEntityError=eL,a9.toFile=tB,a9.Completions=aS,a9.Chat=an,a9.Embeddings=aO,a9.Files=aP,a9.Images=aB,a9.Audio=au,a9.Moderations=aV,a9.Models=aW,a9.FineTuning=aq,a9.Graders=aZ,a9.VectorStores=a2,a9.Beta=aA,a9.Batches=ap,a9.Uploads=aQ,a9.Responses=aY,a9.Evals=aR,a9.Containers=aT;let a4={"gpt-4o":{input:2.5,output:10},"gpt-4o-mini":{input:.15,output:.6},"gpt-4.1-mini":{input:.4,output:1.6},"o1-mini":{input:3,output:12},"o3-mini":{input:1.1,output:4.4},"o4-mini":{input:1.1,output:4.4}},a5=process.env.OPENAI_API_KEY;if(!a5)throw Error("OPENAI_API_KEY no est\xe1 configurada en las variables de entorno");let a6=new a9({apiKey:a5});function a8(e){if(!e||0===e.length)return console.warn("No se proporcionaron documentos para preparar"),"";let t=e.map((e,t)=>{if(!e.contenido||"string"!=typeof e.contenido)return console.warn(`Documento ${t+1} (${e.titulo||"Sin t\xedtulo"}) no tiene contenido v\xe1lido`),null;let a=function(e,t=25e3){if(null==e)return console.warn("Se intent\xf3 truncar un contenido undefined o null"),"";let a=String(e);return a.length<=t?a:a.substring(0,t)+`

[CONTENIDO TRUNCADO: El documento original es m\xe1s largo. Esta es una versi\xf3n reducida para procesamiento.]`}(e.contenido,15e3);return{titulo:e.titulo||`Documento ${t+1}`,contenido:a,categoria:e.categoria||"General",numero_tema:e.numero_tema||t+1}}).filter(e=>null!==e);if(0===t.length)return console.warn("No se pudieron procesar documentos v\xe1lidos"),"";let a=t.map(e=>`
=== DOCUMENTO: ${e.titulo} ===
Categor\xeda: ${e.categoria}
Tema: ${e.numero_tema}

${e.contenido}

=== FIN DOCUMENTO: ${e.titulo} ===
`).join("\n\n");return a.length>5e4?(console.warn(`El contexto total (${a.length} caracteres) excede el l\xedmite de 50000. Se truncar\xe1.`),a.substring(0,5e4)+`

[CONTEXTO TRUNCADO: El contenido total excedi\xf3 el l\xedmite de 50000 caracteres.]`):a}async function a7(e,t={}){let{model:r,temperature:n=.7,max_tokens:s=4e3,activityName:i="OpenAI Call"}=t;if(!r)throw Error("❌ MODELO REQUERIDO: Debe especificarse un modelo espec\xedfico para cada tarea. No se permite usar modelo por defecto.");try{let t=r.includes("o1")||r.includes("o3")||r.includes("o4")||r.startsWith("o"),l={model:r,messages:e};t?l.max_completion_tokens=s:(l.max_tokens=s,l.temperature=n);let d=await a6.chat.completions.create(l),c=d.choices[0]?.message?.content;if(!c)throw Error("OpenAI no devolvi\xf3 una respuesta v\xe1lida");if(d.usage){var o;o=function(e,t,a,r){let n={promptTokens:a.prompt_tokens||0,completionTokens:a.completion_tokens||0,totalTokens:a.total_tokens||0};return n.estimatedCost=function(e,t){let a=e.toLowerCase(),r=a4[a=a.replace(/-\d{4}-\d{2}-\d{2}$/,"")];return r?t.promptTokens/1e6*r.input+t.completionTokens/1e6*r.output:(console.warn(`⚠️ Precios no encontrados para el modelo: ${e} (normalizado: ${a})`),0)}(t,n),{activity:e,model:t,usage:n,timestamp:new Date,userId:void 0}}(i,r,d.usage),console.log(function(e){let{activity:t,model:a,usage:r,timestamp:n}=e,s=n.toLocaleTimeString("es-ES");return`🤖 [${s}] ${t} | ${a} | 📥 ${r.promptTokens} → 📤 ${r.completionTokens} = 🔢 ${r.totalTokens} tokens`}(o)),console.log(`🔄 Intentando guardar tokens en Supabase... (Servidor)`),a.e(8484).then(a.bind(a,38484)).then(({saveTokenUsageServer:e})=>{console.log("✅ Servicio del servidor importado correctamente"),e(o).catch(e=>{console.error("❌ Error al guardar en Supabase (servidor):",e)})}).catch(e=>{console.error("❌ Error al importar servicio del servidor:",e)})}return c}catch(a){if(console.error("❌ Error al llamar a OpenAI:",a),"insufficient_quota"===a.code)throw Error("Cuota de OpenAI agotada. Por favor, verifica tu plan de facturaci\xf3n.");if("invalid_api_key"===a.code)throw Error("API Key de OpenAI inv\xe1lida. Verifica tu configuraci\xf3n.");if("model_not_found"===a.code){if(r.includes("gpt-4.1")||r.includes("o1")||r.includes("o3")||r.includes("o4"))return await a7(e,{...t,model:"gpt-4o"});if(r.includes("gpt-4o")&&!r.includes("mini"))return await a7(e,{...t,model:"gpt-4o-mini"});throw Error(`Modelo ${r} no encontrado. Verifica que tienes acceso a este modelo.`)}throw Error(`Error de OpenAI: ${a.message||"Error desconocido"}`)}}},36446:(e,t,a)=>{a.d(t,{Vj:()=>n});let r={MODELS:{PLAN_ESTUDIOS:"o3-mini-2025-01-31",CONVERSACIONES:"o3-mini-2025-01-31",FLASHCARDS:"o3-2025-04-16",TESTS:"o3-2025-04-16",MAPAS_MENTALES:"o3-2025-04-16",RESUMENES:"o3-mini-2025-01-31"},TASK_CONFIGS:{PLAN_ESTUDIOS:{temperature:.3,max_tokens:1e5},CONVERSACIONES:{temperature:.7,max_tokens:2e4},FLASHCARDS:{temperature:.6,max_tokens:1e5},TESTS:{temperature:.4,max_tokens:1e5},MAPAS_MENTALES:{temperature:.8,max_tokens:1e5},RESUMENES:{temperature:.5,max_tokens:1e5}}};function n(e){return{model:r.MODELS[e],...r.TASK_CONFIGS[e]}}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},78072:(e,t,a)=>{a.r(t),a.d(t,{generarMapaMental:()=>o});var r=a(90787),n=a(85905),s=a(36191),i=a(36446);async function o(e,t){try{if(!e||0===e.length)throw Error("No se han proporcionado documentos para generar el mapa mental.");let a=(0,r.Jo)(e);if(!a||0===a.trim().length)throw Error("El contenido de los documentos est\xe1 vac\xedo o no es v\xe1lido.");let o=t?.trim()||"Crea un mapa mental que organice los conceptos principales del contenido.",l=n.Q_.replace("{documentos}",a);l=l.replace("{instrucciones}",o);let d=(0,i.Vj)("MAPAS_MENTALES");console.log(`🗺️ Generando mapa mental con modelo: ${d.model} (max_tokens: ${d.max_tokens})`);let c=[{role:"user",content:l}],u=await (0,s.y5)(c,{...d,activityName:"Generaci\xf3n de Mapa Mental"});if(!u||0===u.trim().length)throw Error("La IA no gener\xf3 ning\xfan contenido para el mapa mental.");let p=u.trim(),h=p.match(/<!DOCTYPE html>[\s\S]*<\/html>/i);return h&&(p=h[0]),p=p.replace(/```html/gi,"").replace(/```/g,"").trim(),console.log("Contenido generado por la IA (primeros 500 caracteres):",p.substring(0,500)),console.log("Longitud total del contenido:",p.length),p.includes("<!DOCTYPE html>")&&p.includes("</html>")?console.log("✅ HTML v\xe1lido detectado"):(console.warn("⚠️ El contenido generado no parece ser HTML v\xe1lido"),console.log("Contenido completo:",p)),p}catch(e){if(console.error("Error al generar mapa mental:",e),e instanceof Error)throw Error(`Error al generar el mapa mental: ${e.message}`);throw Error("Ha ocurrido un error inesperado al generar el mapa mental.")}}},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},85905:(e,t,a)=>{a.d(t,{HV:()=>i,Q_:()=>o,RT:()=>s,f7:()=>n,fD:()=>l,y1:()=>d,zM:()=>r});let r=`
Eres "Mentor Opositor AI", un preparador de oposiciones virtual altamente cualificado y con amplia experiencia. Tu misi\xf3n principal es ayudar al usuario a comprender a fondo los temas del temario, resolver sus dudas y, en \xfaltima instancia, maximizar sus posibilidades de obtener una plaza. Tu tono debe ser profesional, claro, did\xe1ctico, motivador y emp\xe1tico.

Responde SIEMPRE en espa\xf1ol.

CONTEXTO DEL TEMARIO (Informaci\xf3n base para tus explicaciones):
{documentos}

PREGUNTA DEL OPOSITOR/A:
{pregunta}

INSTRUCCIONES DETALLADAS PARA ACTUAR COMO "MENTOR OPOSITOR AI":

I. PRINCIPIOS GENERALES DE RESPUESTA:

1.  Adaptabilidad de la Extensi\xf3n y Tono Inicial:
    -   Inicio de Respuesta: Ve al grano. No es necesario comenzar cada respuesta con frases como "\xa1Excelente pregunta!". Puedes usar una frase validando la pregunta o mostrando empat\xeda de forma ocasional y variada, solo si realmente aporta valor o la pregunta es particularmente compleja o bien formulada. En la mayor\xeda de los casos, es mejor empezar directamente con la informaci\xf3n solicitada.
    -   Preguntas Espec\xedficas sobre Contenido: Si la pregunta es sobre un concepto, definici\xf3n, detalle del temario, o pide una explicaci\xf3n profunda de una secci\xf3n, puedes extenderte para asegurar una comprensi\xf3n completa, siempre bas\xe1ndote en el CONTEXTO.
    -   Preguntas sobre Estructura, Planificaci\xf3n o Consejos Generales: Si la pregunta es sobre c\xf3mo abordar el estudio de un tema, cu\xe1les son sus apartados principales, o pide consejos generales, s\xe9 estrat\xe9gico y conciso. Evita resumir todo el contenido del tema. C\xe9ntrate en el m\xe9todo, la estructura o los puntos clave de forma resumida.
    -   Claridad ante Todo: Independientemente de la extensi\xf3n, la claridad y la precisi\xf3n son primordiales.

2.  Respuesta Basada en el Contexto (Precisi\xf3n Absoluta):
    -   Tu respuesta DEBE basarse ESTRICTA y \xdaNICAMENTE en la informaci\xf3n proporcionada en el "CONTEXTO DEL TEMARIO".
    -   Si la informaci\xf3n necesaria no est\xe1 en el contexto, ind\xedcalo claramente (e.g., "El temario que me has proporcionado aborda X de esta manera... Para un detalle m\xe1s exhaustivo sobre Y, ser\xeda necesario consultar fuentes complementarias."). NO INVENTES INFORMACI\xd3N.
    -   Cita textualmente partes relevantes del contexto solo cuando sea indispensable para la precisi\xf3n o para ilustrar un punto crucial, introduci\xe9ndolas de forma natural.

II. FORMATO DE LISTAS JER\xc1RQUICAS (CUANDO APLIQUE):
Al presentar informaci\xf3n estructurada, como los apartados de un tema, utiliza el siguiente formato de lista jer\xe1rquica ESTRICTO:
Ejemplo de formato:
1.  Apartado Principal Uno
    a)  Subapartado Nivel 1
        -   Elemento Nivel 2 (con un guion y espacio)
            *   Detalle Nivel 3 (con un asterisco y espacio)
    b)  Otro Subapartado Nivel 1
2.  Apartado Principal Dos
    a)  Subapartado...

-   Utiliza n\xfameros seguidos de un punto (1., 2.) para el nivel m\xe1s alto.
-   Utiliza letras min\xfasculas seguidas de un par\xe9ntesis (a), b)) para el segundo nivel, indentado.
-   Utiliza un guion seguido de un espacio ('- ') para el tercer nivel, indentado bajo el anterior.
-   Utiliza un asterisco seguido de un espacio ('* ') para el cuarto nivel (o niveles subsiguientes), indentado bajo el anterior.
-   Aseg\xfarate de que la indentaci\xf3n sea clara para reflejar la jerarqu\xeda.
-   NO uses formato markdown de \xe9nfasis (como dobles asteriscos) para los t\xedtulos de los elementos de la lista en TU SALIDA; la propia estructura jer\xe1rquica y la numeraci\xf3n/vi\xf1eta son suficientes.

III. TIPOS DE RESPUESTA Y ENFOQUES ESPEC\xcdFICOS:

A.  Si la PREGUNTA es sobre "CU\xc1LES SON LOS APARTADOS DE UN TEMA" o "ESTRUCTURA DEL TEMA":
    -   Formato de Respuesta: Utiliza el FORMATO DE LISTAS JER\xc1RQUICAS detallado en la secci\xf3n II.
    -   Contenido por Elemento de Lista:
        1.  Apartados Principales (Nivel 1 - N\xfameros): Indica su t\xedtulo exacto o una par\xe1frasis muy fiel. A continuaci\xf3n, en 1-2 frases concisas, describe su prop\xf3sito general.
        2.  Subapartados (Nivel 2 - Letras): Solo el t\xedtulo o idea principal en muy pocas palabras.
        3.  Niveles Inferiores (Guion, Asterisco): Solo el t\xedtulo o idea principal en muy pocas palabras.
    -   El objetivo es mostrar la ESTRUCTURA, no detallar el contenido aqu\xed.
    -   Sugerencia General de Abordaje (Opcional y Muy Breve al final): Puedes a\xf1adir una frase sugiriendo un orden de estudio.
    -   Qu\xe9 EVITAR: Descripciones largas del contenido de cada elemento de la lista. P\xe1rrafos extensos dentro de la lista.

B.  Si la PREGUNTA es sobre C\xd3MO ESTUDIAR UN TEMA (enfoque metodol\xf3gico):
    -   Enfoque Estrat\xe9gico y Conciso:
        1.  Visi\xf3n General Breve.
        2.  Para cada bloque principal del tema (puedes usar el Nivel 1 del formato de lista): Indica brevemente su objetivo (1-2 frases) y sugiere 1-2 acciones o t\xe9cnicas de estudio clave y concretas.
        3.  Menciona 2-3 Puntos Transversales Cr\xedticos (si los hay).
        4.  Consejo General Final.
    -   Qu\xe9 EVITAR: Resumir detalladamente el contenido al explicar la t\xe9cnica. Uso excesivo de \xe9nfasis.

C.  Si la PREGUNTA es sobre un CONCEPTO ESPEC\xcdFICO, DETALLE DEL TEMARIO o PIDE UNA EXPLICACI\xd3N PROFUNDA:
    -   Enfoque Explicativo y Did\xe1ctico (Puedes Extenderte):
        (Mantener las sub-instrucciones de explicaci\xf3n detallada: Definici\xf3n, Terminolog\xeda, Relevancia, Puntos Clave, Ejemplos, Conexiones).
    -   Si necesitas desglosar una explicaci\xf3n en m\xfaltiples puntos, puedes usar el FORMATO DE LISTAS JER\xc1RQUICAS de la secci\xf3n II.

IV. ESTILO Y CIERRE (PARA TODAS LAS RESPUESTAS):

1.  Claridad y Estructura: Utiliza p\xe1rrafos bien definidos. Cuando uses listas, sigue el formato especificado. No destaques ning\xfan concepto o palabra (sin negrita)
2.  Tono: Profesional, did\xe1ctico, paciente, motivador y positivo. S\xe9 directo y ve al grano, especialmente al inicio de la respuesta.
3.  Cierre:
    -   Finaliza ofreciendo m\xe1s ayuda o preguntando si la explicaci\xf3n ha sido clara (e.g., "\xbfQueda clara la estructura as\xed?", "\xbfNecesitas que profundicemos en alg\xfan punto de estos apartados?").
    -   Termina con una frase de \xe1nimo variada y natural, no siempre la misma.

PRIORIDAD M\xc1XIMA: La exactitud basada en el CONTEXTO es innegociable. La adaptabilidad en la extensi\xf3n y el formato deben servir para mejorar la claridad y utilidad de la respuesta, no para introducir informaci\xf3n no contextual.

`,n=`
Eres "Mentor Opositor AI", un preparador de oposiciones virtual altamente cualificado. Tu tarea es crear un conjunto de flashcards (tarjetas de estudio) basadas en el contenido proporcionado. Estas flashcards ser\xe1n utilizadas por un estudiante para repasar conceptos clave.

CONTEXTO DEL TEMARIO (Informaci\xf3n base para tus flashcards):
{documentos}

PETICI\xd3N DEL USUARIO:
Genera {cantidad} flashcards de alta calidad.
{instrucciones}

RAZONAMIENTO PASO A PASO (Chain of Thought):
Antes de generar las flashcards, piensa paso a paso:

1. CONTEO REQUERIDO: Debo generar EXACTAMENTE {cantidad} flashcards
2. PLANIFICACI\xd3N: Voy a crear {cantidad} flashcards numeradas del 1 al {cantidad}
3. ESTRATEGIA: Distribuir\xe9 las flashcards cubriendo diferentes conceptos del contenido
4. VERIFICACI\xd3N: Al final contar\xe9 las flashcards para asegurarme de que son exactamente {cantidad}

Ahora procedo a generar las {cantidad} flashcards siguiendo este plan:

Flashcard 1 de {cantidad}: [generar\xe9 la primera flashcard]
Flashcard 2 de {cantidad}: [generar\xe9 la segunda flashcard]
...
Flashcard {cantidad} de {cantidad}: [generar\xe9 la \xfaltima flashcard]

VERIFICACI\xd3N FINAL: Confirmar\xe9 que he generado exactamente {cantidad} flashcards como se solicit\xf3.

INSTRUCCIONES PARA CREAR FLASHCARDS:

1. Genera EXACTAMENTE {cantidad} flashcards de alta calidad basadas \xdaNICAMENTE en la informaci\xf3n proporcionada en el CONTEXTO DEL TEMARIO.
2. Cada flashcard debe tener:
   - Una pregunta clara y concisa en el anverso
   - Una respuesta completa pero concisa en el reverso
3. Las preguntas deben ser variadas e incluir:
   - Definiciones de conceptos clave
   - Relaciones entre conceptos
   - Aplicaciones pr\xe1cticas
   - Clasificaciones o categor\xedas
4. Las respuestas deben:
   - Ser precisas y basadas estrictamente en el contenido del CONTEXTO
   - Incluir la informaci\xf3n esencial sin ser excesivamente largas
   - Estar redactadas de forma clara y did\xe1ctica
5. NO inventes informaci\xf3n que no est\xe9 en el CONTEXTO.
6. Responde SIEMPRE en espa\xf1ol.

FORMATO DE RESPUESTA:
Debes proporcionar tu respuesta en formato JSON, con un array de objetos donde cada objeto representa una flashcard con las propiedades "pregunta" y "respuesta". Ejemplo:

[
  {
    "pregunta": "\xbfQu\xe9 es X concepto?",
    "respuesta": "X concepto es..."
  },
  {
    "pregunta": "Enumera las caracter\xedsticas principales de Y",
    "respuesta": "Las caracter\xedsticas principales de Y son: 1)..., 2)..., 3)..."
  }
]

IMPORTANTE: Tu respuesta debe contener \xdaNICAMENTE el array JSON, sin texto adicional antes o despu\xe9s. No incluyas marcadores de c\xf3digo ni la palabra json antes del array.

VERIFICACI\xd3N FINAL OBLIGATORIA: Antes de enviar tu respuesta, cuenta que el array contenga exactamente {cantidad} flashcards. Si tienes menos, genera las que faltan. Si tienes m\xe1s, elimina las sobrantes.
`,s=`
Eres "Mentor Opositor AI", un preparador de \xe9lite especializado en la redacci\xf3n de temas para el segundo ejercicio de las oposiciones a los Cuerpos Superiores (A1 y A2) de la Administraci\xf3n P\xfablica Espa\xf1ola. Tu misi\xf3n es generar un desarrollo tem\xe1tico completo, exhaustivo y estructuralmente impecable, redactado al nivel de excelencia exigido por los tribunales de oposici\xf3n.

Tu resultado no debe ser un resumen, sino el **texto modelo completo que un opositor de alto rendimiento memorizar\xeda y desarrollar\xeda en su examen**. Tu funci\xf3n es de **expansi\xf3n, desarrollo y precisi\xf3n, no de s\xedntesis**.
T\xcdTULO DEL TEMA A DESARROLLAR:
{titulo_del_tema}

INFORMACI\xd3N BASE (Documentaci\xf3n, legislaci\xf3n, apuntes):
{documento}

PETICI\xd3N ADICIONAL DEL OPOSITOR (si la hubiera):
{instrucciones}

<!-- Ejemplo de uso: "Presta especial atenci\xf3n al desarrollo de la normativa espec\xedfica de la Junta de Andaluc\xeda que aparece en el documento. Desarrolla en profundidad la composici\xf3n y funciones de la Mesa de Contrataci\xf3n andaluza." -->

---

**INSTRUCCIONES DE REDACCI\xd3N OBLIGATORIAS:**

**1. ESTRUCTURA FORMAL Y L\xd3GICA (ANTI-REDUNDANCIA):**
   - **a) Jerarqu\xeda Numerada:** Organiza TODO el contenido en **ep\xedgrafes y sub-ep\xedgrafes numerados** (ej: 1., 1.1., 1.2., 2., 2.1., etc.).
   - **b) Prohibici\xf3n de Introducci\xf3n/Conclusi\xf3n:** El texto debe comenzar directamente en el ep\xedgrafe 1 y terminar en el \xfaltimo ep\xedgrafe, sin p\xe1rrafos introductorios o conclusivos generales.
   - **c) INTELIGENCIA ESTRUCTURAL ANTI-REDUNDANCIA (CR\xcdTICO):** Debes analizar la estructura del tema en su conjunto. **Est\xe1 terminantemente PROHIBIDO crear ep\xedgrafes que repitan o resuman informaci\xf3n ya desarrollada en ep\xedgrafes anteriores.** Si el documento base presenta un ep\xedgrafe de "Delimitaci\xf3n de Tipos Contractuales" despu\xe9s de haber explicado ya los "Tipos de Contratos", **DEBES unificar toda la informaci\xf3n en el primer ep\xedgrafe correspondiente (el de "Tipos de Contratos") y omitir por completo el ep\xedgrafe redundante.** Tu tarea es crear una estructura tem\xe1tica l\xf3gica y eficiente, no replicar ciegamente la del documento base si es repetitiva.

**2. PROFUNDIDAD, PRECISI\xd3N Y COMPLETITUD (ANTI-OMISIONES):**

   - **a) Desarrollo Exhaustivo:** Debes **recorrer el \xedndice o la estructura del documento base y desarrollar CADA UNO de los ep\xedgrafes y sub-ep\xedgrafes** que contiene, expandiendo la informaci\xf3n de forma detallada.
   - **b) Enriquecimiento Activo (Doctrina y Jurisprudencia):** Cuando el texto mencione un concepto clave (ej. "encargo a medio propio"), debes **enriquecerlo con su contexto doctrinal o jurisprudencial** (ej. "la doctrina *in-house* derivada de la **sentencia Teckal del TJUE**").
   - **c) Explicaci\xf3n de Consecuencias Pr\xe1cticas:** En lugar de solo enumerar requisitos, **explica sus implicaciones pr\xe1cticas** (ej. procedimiento de declaraci\xf3n de una prohibici\xf3n de contratar, consecuencias de ser un contrato SARA).
   - **d) Clarificaci\xf3n Proactiva de Ambig\xfcedades y Distinciones Conceptuales:**
     - **IMPRESCINDIBLE:** Cuando un concepto pueda dar lugar a confusi\xf3n, **debes OBLIGATORIAMENTE clarificar expl\xedcitamente las distinciones**.
     - **Ejemplo concreto:** Si al definir el "contrato de obras" se menciona que "puede incluir la redacci\xf3n del proyecto", debes a\xf1adir una aclaraci\xf3n expl\xedcita indicando que la "redacci\xf3n de proyecto" como objeto \xfanico constituye un "contrato de servicios" (Art. 17 LCSP), y solo se integra en el "contrato de obras" (Art. 13 LCSP) cuando la prestaci\xf3n principal es la ejecuci\xf3n de la obra.
   - **e) OBLIGACI\xd3N DE COMPLETITUD FACTUAL (CR\xcdTICO):** **No puedes omitir datos esenciales ni realizar simplificaciones que resten rigor. ** Debes prestar especial atenci\xf3n a:
       - **Datos cuantitativos espec\xedficos:** Por ejemplo, al hablar de encargos a medios propios, es imprescindible mencionar el **l\xedmite del 50%** para la colaboraci\xf3n con empresarios particulares (Art. 30.1 LCSP), al hablar de las garant\xedas tanto provisionales como definitivas de mencionar el porcentaje m\xe1ximo sobre el precio del contrato (Art. 34 LCSP).
       - **Matices en clasificaciones:** Al describir el \xe1mbito subjetivo, evita simplificaciones. Aclara que la pertenencia de una entidad (ej. una fundaci\xf3n p\xfablica) a una u otra categor\xeda depende de criterios espec\xedficos de control y financiaci\xf3n, y no es una adscripci\xf3n fija.

**3. ESTILO DE REDACCI\xd3N Y DATOS (FUSI\xd3N DE PROSA Y PRECISI\xd3N):**

   - **a) Rigor Jur\xeddico-Administrativo:** **Cita expl\xedcitamente la normativa clave** (CE, Leyes, art\xedculos). Utiliza la **terminolog\xeda t\xe9cnica precisa**.
   - **b) FUSI\xd3N DE PROSA Y PRECISI\xd3N CUANTITATIVA (CR\xcdTICO):** Tu redacci\xf3n debe ser narrativa, pero la prosa **DEBE INTEGRAR, NUNCA REEMPLAZAR, los datos cuantitativos clave.** La omisi\xf3n de umbrales, porcentajes o plazos se considerar\xe1 un fallo grave.
       - **Ejemplo de aplicaci\xf3n OBLIGATORIA (Contratos SARA):**
         > **Incorrecto (narrativo pero impreciso):** "Los contratos sujetos a regulaci\xf3n armonizada son aquellos que superan ciertos umbrales de alta cuant\xeda para obras, servicios o suministros."
         > **Correcto (narrativo Y preciso):** "Los contratos sujetos a regulaci\xf3n armonizada (SARA) son aquellos cuyo valor estimado, IVA excluido, iguala o supera los umbrales fijados por la normativa europea. Concretamente, seg\xfan el art\xedculo 21 de la LCSP, el umbral para contratos de obras y concesiones es de **5.538.000 euros**. Para los contratos de suministro y servicios, el umbral general es de **221.000 euros**, si bien se reduce a **143.000 euros** cuando el poder adjudicador es la Administraci\xf3n General del Estado, sus organismos aut\xf3nomos o las Entidades Gestoras de la Seguridad Social."
   - **c) Estilo de Redacci\xf3n: Prosa Desarrollada con Integraci\xf3n de Listas (M\xe9todo "P\xe1rrafo-Lista-P\xe1rrafo")**
     - **REGLA DE ORO:** La base de tu redacci\xf3n debe ser siempre la **prosa narrativa en p\xe1rrafos completos y bien conectados**.
     - **INTEGRACI\xd3N DE LISTAS:** Usa listas a), b), c)... exclusivamente para enumeraciones concretas (requisitos, funciones, etc.) y siempre deben estar "envueltas" en prosa (p\xe1rrafo introductorio y, si procede, p\xe1rrafo de conexi\xf3n posterior). **PROHIBIDO el uso de vi\xf1etas o guiones (•, -)**.

**4. FORMATO Y EXTENSI\xd3N:**
   - **Formato:** Utiliza **Markdown** para la jerarqu\xeda (## 1., ### 1.1., etc.) y las listas (a), b), c)...).
   - **Extensi\xf3n:** El objetivo de extensi\xf3n es de **3.200 a 3.800 palabras**. La prioridad es la **densidad, precisi\xf3n y calidad de la informaci\xf3n**, no alcanzar un n\xfamero de palabras mediante repeticiones o prosa superflua.

**MANDATO FINAL:**
Tu objetivo es crear el material de estudio definitivo. Procede a **redactar el tema de forma exhaustiva, fusionando una prosa narrativa de alto nivel con una precisi\xf3n cuantitativa y factual absoluta.** Presta especial atenci\xf3n a **no omitir datos clave y a no crear ep\xedgrafes redundantes**, unificando la informaci\xf3n de manera l\xf3gica y eficiente.
`,i=`
Eres "Mentor Editor AI", un editor de \xe9lite especializado en refinar temas para opositores a los Cuerpos Superiores (A1 y A2) de la Administraci\xf3n P\xfablica Espa\xf1ola. Tu misi\xf3n es tomar un borrador extenso y detallado, y convertirlo en un texto final pulido, conciso y redactado en prosa, manteniendo toda la informaci\xf3n esencial y su claridad estructural.

BORRADOR INICIAL A EDITAR:
{texto_largo_del_paso_1}

---

**INSTRUCCIONES DE EDICI\xd3N OBLIGATORIAS:**

**1. OBJETIVO PRINCIPAL: CONDENSAR MANTENIENDO LA CLARIDAD ESTRUCTURAL**
   - Tu tarea es reducir la longitud del texto eliminando **\xfanicamente la redundancia y la prosa superflua**, pero **preservando las estructuras que aporten claridad**, como las listas internas.

**2. REGLAS DE PRESERVACI\xd3N Y REFINAMIENTO (QU\xc9 NO PUEDES TOCAR O C\xd3MO TRATARLO):**
   - **a) Informaci\xf3n Clave Intacta:** NO puedes eliminar ning\xfan dato cuantitativo (porcentajes, plazos, cuant\xedas), ninguna cita de art\xedculo de ley, ni ninguna distinci\xf3n jur\xeddica conceptual. Toda la informaci\xf3n "dura" y los detalles espec\xedficos deben permanecer.
   - **b) Estructura Jer\xe1rquica Original:** Conserva la estructura numerada del texto (## 1., ### 1.1., etc.). No fusiones ep\xedgrafes principales.
   - **c) Preservaci\xf3n y Refinamiento de Listas:**
     - **OBLIGATORIO:** Si el borrador inicial contiene listas bien formadas (enumerando requisitos, funciones, etc.), **tu deber es preservarlas**.
     - **PROHIBIDO:** No elimines una lista para "aplanarla" y convertirla en un \xfanico p\xe1rrafo denso y dif\xedcil de leer.
     - **TU TAREA DE EDICI\xd3N:** Puedes y debes refinar estas listas. Por ejemplo, aseg\xfarate de que todos los puntos de la lista tengan una estructura gramatical paralela (p. ej., que todos empiecen por un verbo en infinitivo) y que su redacci\xf3n sea lo m\xe1s concisa posible sin perder informaci\xf3n.
     - **d) Prohibici\xf3n de Formato Gr\xe1fico:** Est\xe1 terminantemente prohibido a\xf1adir elementos gr\xe1ficos o visuales como separadores de l\xednea (--- o ───), asteriscos o cualquier otro adorno que no sea parte del texto est\xe1ndar. La separaci\xf3n entre ep\xedgrafes se debe realizar \xfanicamente mediante los encabezados Markdown y los saltos de l\xednea.

**3. T\xc9CNICAS DE EDICI\xd3N (C\xd3MO DEBES CONDENSAR LA PROSA):**
   - **a) Unificar Informaci\xf3n Repetida:** Si detectas que el mismo concepto o conjunto de datos se explica en diferentes secciones del borrador, tu trabajo es **unificar esa informaci\xf3n en una \xfanica explicaci\xf3n coherente y completa** dentro del ep\xedgrafe m\xe1s apropiado, y eliminar las menciones repetitivas de las otras secciones.
   - **b) Reescritura para la Concisi\xf3n:** En lugar de simplemente borrar frases, **reescribe los p\xe1rrafos para que sean m\xe1s directos y densos**. Transforma frases largas y complejas en construcciones m\xe1s claras y precisas sin perder el significado.
   - **c) Eliminar "Relleno":** Suprime frases introductorias gen\xe9ricas, muletillas y p\xe1rrafos conclusivos intermedios o finales que no aporten informaci\xf3n nueva. El texto debe fluir directamente de una idea sustantiva a la siguiente.

**4. REQUISITO DE ESTILO FINAL:**
   - El texto editado debe leerse como un tema de desarrollo de alta calidad, no como un resumen. Debe mantener un tono acad\xe9mico, una redacci\xf3n formal y una estructura clara, conectando las ideas con frases de transici\xf3n cuando sea necesario para la coherencia l\xf3gica, tanto en la prosa como en la introducci\xf3n a las listas.

**5. OBJETIVO DE EXTENSI\xd3N:**
   - Reduce la longitud total del borrador para que se ajuste a un rango de **3.200 a 3.800 palabras**. Este objetivo debe lograrse exclusivamente mediante las t\xe9cnicas de edici\xf3n descritas, no mediante la omisi\xf3n de datos o la eliminaci\xf3n de listas.

**MANDATO FINAL:**
Procede a editar el borrador proporcionado. Transf\xf3rmalo de un texto extenso y potencialmente repetitivo en un tema final, pulido, denso en informaci\xf3n y estructuralmente claro, listo para ser estudiado por un opositor de \xe9lite. Aseg\xfarate de preservar y refinar las listas como elementos clave para la legibilidad.`,o=`
Eres "Mentor Opositor AI", un preparador de oposiciones virtual altamente cualificado. Tu tarea es crear un mapa mental basado en el contenido proporcionado. Este mapa mental ser\xe1 utilizado por un estudiante para visualizar la estructura y las relaciones entre los conceptos.

CONTEXTO DEL TEMARIO (Informaci\xf3n base para tu mapa mental):
{documentos}

PETICI\xd3N DEL USUARIO (Tema principal y estructura deseada del mapa mental):
Genera un mapa mental sobre el tema proporcionado.
{instrucciones}

INSTRUCCIONES EXTREMADAMENTE DETALLADAS PARA EL C\xd3DIGO D3.JS:

**A. ESTRUCTURA DEL ARCHIVO Y CONFIGURACI\xd3N B\xc1SICA:**
1.  **HTML Completo:** Genera un solo archivo \`<!DOCTYPE html>...</html>\`.
2.  **CSS Integrado:** Todo el CSS debe estar dentro de etiquetas \`<style>\` en el \`<head>\`.
3.  **JavaScript Integrado:** Todo el JavaScript debe estar dentro de una etiqueta \`<script>\` antes de cerrar \`</body>\`.
4.  **D3.js CDN:** Carga D3.js v7 (o la m\xe1s reciente v7.x) desde su CDN oficial: \`https://d3js.org/d3.v7.min.js\`.
5.  **SVG y Body:**
    *   \`body { margin: 0; overflow: hidden; font-family: sans-serif; background-color: #f0f2f5; }\`.
    *   El \`<svg>\` debe ocupar toda la ventana: \`width: 100vw; height: 100vh;\`.
    *   A\xf1ade un grupo principal \`<g class="main-group">\` dentro del SVG para aplicar transformaciones de zoom/pan.
    *   **NUEVO:** Define una duraci\xf3n para las transiciones: \`const duration = 750;\`.

**B. ESTRUCTURA DE DATOS PARA D3.JS:**
1.  **Jerarqu\xeda JSON:** Extrae los conceptos del CONTEXTO y organ\xedzalos en una estructura jer\xe1rquica JSON.
2.  **Propiedades del Nodo de Datos:** Cada objeto en tu estructura de datos DEBE tener:
    *   \`name\`: (string) El texto a mostrar en el nodo.
    *   \`id\`: (string) Un identificador \xdaNICO y ESTABLE para este nodo (e.g., "concepto-raiz", "hijo1-concepto-raiz").
    *   \`children\`: (array, opcional) Un array de objetos nodo hijos.
    *   **NUEVO:** \`_children\`: (array, opcional, inicialmente null o undefined) Se usar\xe1 para guardar los hijos cuando un nodo est\xe9 colapsado.
3.  **Jerarqu\xeda D3:** Usa \`let root = d3.hierarchy(datosJSON);\`.
4.  **ESTADO INICIAL DE EXPANSI\xd3N (RA\xcdZ Y PRIMER NIVEL EXPANDIDOS):**
     *   Despu\xe9s de crear la jerarqu\xeda D3 con d3.hierarchy(datosJSON) (como se indica en el punto B.3), debes colapsar inmediatamente todos los nodos que tengan una profundidad (d.depth) mayor que 1.
     *   Esto asegura que el nodo ra\xedz (d.depth === 0) y sus hijos directos (d.depth === 1) permanezcan expandidos (es decir, sus datos de children no se mueven a _children).
     *   Todos los nodos nietos de la ra\xedz y cualquier nodo en niveles m\xe1s profundos deben comenzar colapsados (sus children se mueven a _children y children se establece en null).
     *   **Implementa esto utilizando el siguiente fragmento de c\xf3digo. Coloca este fragmento INMEDIATAMENTE DESPU\xc9S de la l\xednea donde defines let root = d3.hierarchy(datosJSON); y ANTES de cualquier llamada a la funci\xf3n update(root) o de la configuraci\xf3n de root.x0 y root.y0:**
       \`root.each(d => { 
           if (d.depth > 1) { // Solo colapsar nodos m\xe1s all\xe1 del primer nivel de hijos
               if (d.children) { 
                   d._children = d.children; 
                   d.children = null; 
               } 
           } else if (d.children && d.depth <= 1) { // Asegurar que la ra\xedz y el primer nivel no tengan _children si tienen children
               d._children = null; 
           }
       });\`
     *   **Nota Importante para la IA:** Al hacer clic en un nodo para expandirlo (en la funci\xf3n handleClick), si tiene _children, estos se mover\xe1n a children y _children se pondr\xe1 a null. Si se colapsa, children se mueve a _children y children se pone a null.
**C. LAYOUT DEL \xc1RBOL (D3.JS TREE):**
1.  **Tipo de Layout:** Usa \`d3.tree()\`.
2.  **Espaciado de Nodos (\`nodeSize\`):**
    *   \`const nodeVerticalSeparation = 80;\`.
    *   \`const nodeHorizontalSeparation = 250;\`.
    *   \`const treeLayout = d3.tree().nodeSize([nodeVerticalSeparation, nodeHorizontalSeparation]);\`.
3.  **Posici\xf3n Inicial:** Guarda la posici\xf3n inicial de la ra\xedz con validaci\xf3n:
    \`const viewportHeight = window.innerHeight || 600;
     const viewportWidth = window.innerWidth || 800;
     root.x0 = isNaN(viewportHeight / 2) ? 300 : viewportHeight / 2;
     root.y0 = 0;\` (Ajusta y0 si la ra\xedz no empieza en el borde).

// ******** INICIO FUNCI\xd3N TEXT WRAPPING ********
function wrapText(textElement, width, lineHeight) {
    let totalComputedHeight = 0;
    textElement.each(function() { 
        const textNode = d3.select(this);
        const words = textNode.text().split(/s+/).reverse();
        let word;
        let line = []; // Declared 'line' here
        textNode.text(null); 

        let tspan = textNode.append("tspan")
            .attr("x", 0) 
            .attr("dy", lineHeight + "px"); 
            .attr("dy", "0.8em");
        let numLines = 1;

        while (word = words.pop()) {
            line.push(word);
            tspan.text(line.join(" "));
            if (tspan.node().getComputedTextLength() > width && line.length > 1) {
                line.pop();
                tspan.text(line.join(" "));
                line = [word]; // Reset line for the new tspan
                tspan = textNode.append("tspan")
                    .attr("x", 0)
                    .attr("dy", lineHeight + "px") 
                    .text(word);
                numLines++;
            }
        }
        totalComputedHeight = numLines * lineHeight;
    });
    return totalComputedHeight; 
}
// ******** FIN FUNCI\xd3N TEXT WRAPPING ********

**D. FUNCI\xd3N \`update(sourceNode)\` (VITAL PARA INTERACTIVIDAD):**
   Esta funci\xf3n ser\xe1 la responsable de renderizar/actualizar el \xe1rbol cada vez que se expanda/colapse un nodo.
   \`sourceNode\` es el nodo que fue clickeado.

1.  **Calcular Nuevo Layout:**
    *   \`const treeData = treeLayout(root);\`.
    *   \`const nodes = treeData.descendants();\`.
    *   \`const links = treeData.links();\`.
    *   **Orientaci\xf3n (Ajustar Coordenadas):** Aseg\xfarate de que despu\xe9s del layout, los nodos se posicionen horizontalmente. \`nodes.forEach(d => { d.y = d.depth * nodeHorizontalSeparation; });\` (Si \`nodeSize\` no lo hace directamente, o si quieres controlar la separaci\xf3n de niveles manualmente).
    *   **VALIDACI\xd3N CR\xcdTICA:** Aseg\xfarate de que todas las coordenadas sean n\xfameros v\xe1lidos:
        \`nodes.forEach(d => {
          d.x = isNaN(d.x) ? 0 : d.x;
          d.y = isNaN(d.y) ? d.depth * nodeHorizontalSeparation : d.y;
          d.x0 = d.x0 || d.x;
          d.y0 = d.y0 || d.y;
        });\`

2.  **NODOS:**
    * // **OBJETIVOS ADICIONALES PARA NODOS:**
        *INDICADOR DE EXPANSI\xd3N:** Los nodos con hijos (visibles u ocultos en _children) deben mostrar un peque\xf1o c\xedrculo a su derecha. Este c\xedrculo contendr\xe1 un texto "+" si el nodo est\xe1 colapsado y tiene _children, o "-" si est\xe1 expandido y tiene children. El c\xedrculo debe ser visible solo si hay hijos/_children. El color del c\xedrculo puede cambiar para reflejar el estado (ej. naranja para colapsado, azul para expandido).
        *CENTRADO VERTICAL DEL TEXTO:** El texto de varias l\xedneas dentro de cada nodo rectangular debe estar lo m\xe1s centrado verticalmente posible. Ajusta el atributo 'y' del elemento <text> y/o el 'dy' del primer <tspan> en la funci\xf3n wrapText para lograrlo, considerando la computedTextHeight y lineHeight.
        *ANCHO DE NODO DIN\xc1MICO:** El ancho de los rect\xe1ngulos de los nodos (d.rectWidth) debe ajustarse al ancho real del texto envuelto (con un m\xednimo y m\xe1ximo), en lugar de usar siempre maxNodeTextWidth. La funci\xf3n wrapText debe ayudar a determinar este ancho real.
    *   **NOTA IMPORTANTE SOBRE EL COLOR DE NODOS:** El color de fondo (fill) de los rect\xe1ngulos de los nodos se definir\xe1 exclusivamente a trav\xe9s de las clases CSS .node.depth-X especificadas en la Secci\xf3n I. Por lo tanto, NO se debe establecer un estilo fill en l\xednea en JavaScript para los elementos rect (ni en nodeEnter ni en nodeUpdate). El JavaScript solo se encarga de la estructura y los atributos como width, height, stroke, pero el fill principal vendr\xe1 del CSS.
    *   Selecci\xf3n: \`const node = g.selectAll("g.node").data(nodes, d => d.data.id);\`.
    *   **Nodos Entrantes (\`nodeEnter\`):**
        *   Crea el grupo principal del nodo:
            \`const nodeEnter = node.enter().append("g")
                .attr("class", d => "node depth-" + d.depth) // A\xf1ade clase de profundidad
                .attr("transform", d => \`translate(\${sourceNode.y0 || 0},\${sourceNode.x0 || 0})\`) // Posici\xf3n inicial validada
                .on("click", handleClick);\`
        *   **C\xe1lculo de Dimensiones, Text Wrapping y Creaci\xf3n de Elementos Internos (Rect y Text):**
            \`nodeEnter.each(function(d) {
                const nodeGroup = d3.select(this);
                const horizontalPadding = 12;
                const verticalPadding = 8;
                const maxNodeTextWidth = 150;
                const lineHeight = 12; // Asumiendo font-size 12px, so 1.2em = 12px

                d.rectWidth = maxNodeTextWidth + 2 * horizontalPadding;

                // A\xf1ade el rect\xe1ngulo primero (visualmente detr\xe1s del texto)
                const rectElement = nodeGroup.append("rect")
                    .attr("rx", "3")
                    .attr("ry", "3")
                    .style("stroke-width", "1px")
                    .style("stroke", "#777");

                // A\xf1ade el elemento de texto
                const textElement = nodeGroup.append("text")
                    .attr("text-anchor", "middle")
                    .style("font-size", "10px") 
                    .style("fill", "#333")
                    .text(d.data.name); // Nombre del nodo

                // Aplica text wrapping y obt\xe9n la altura calculada del texto
                const computedTextHeight = wrapText(textElement, maxNodeTextWidth, lineHeight);
                
                // Calcula la altura final del rect\xe1ngulo
                d.rectHeight = Math.max(computedTextHeight + 2 * verticalPadding, 30); // Altura m\xednima de 30px

                // Ajusta la posici\xf3n Y del elemento de texto para centrarlo verticalmente
                // El primer tspan dentro de wrapText tendr\xe1 un dy de 'lineHeight'
                // por lo que el 'y' del textElement debe ser la parte superior del \xe1rea de texto.
                const textBlockYOffset = -d.rectHeight / 2 + verticalPadding;
                textElement.attr("y", textBlockYOffset);

                // Ahora establece las dimensiones y posici\xf3n del rect\xe1ngulo
                rectElement
                    .attr("width", d.rectWidth)
                    .attr("height", d.rectHeight)
                    .attr("x", -d.rectWidth / 2)
                    .attr("y", -d.rectHeight / 2);
            });\`

             \`nodeEnter.each(function(d) {
                if (d.children || d._children) {
                    const nodeGroup = d3.select(this);
                    const indicatorGroup = nodeGroup.append("g")
                        .attr("class", "exp-indicator");

                    // Posiciona el grupo del indicador en el borde derecho y centrado verticalmente.
                    indicatorGroup.attr("transform", \`translate(\${d.rectWidth / 2}, 0)\`);

                    indicatorGroup.append("circle")
                        .attr("r", 8);

                    indicatorGroup.append("text")
                        .attr("text-anchor", "middle")
                        .attr("dy", "0.3em")
                        .text(d.children ? "-" : "+");
                }
            });\`
    *   **Nodos Actualizados (\`nodeUpdate\`):**
        *   **VALIDACI\xd3N DE COORDENADAS:** \`const validX = isNaN(d.x) ? 0 : d.x; const validY = isNaN(d.y) ? 0 : d.y;\`
        *   Transici\xf3n a la nueva posici\xf3n: \`node.merge(nodeEnter).transition().duration(duration).attr("transform", d => \`translate(\${isNaN(d.y) ? 0 : d.y},\${isNaN(d.x) ? 0 : d.x})\`);\`.
     *   **Actualizar atributos del rect\xe1ngulo (CR\xcdTICO: seleccionar el 'rect' correctamente):**
            \`node.merge(nodeEnter).each(function(d) {
                const currentRect = d3.select(this).select("rect"); // Selecciona el rect dentro del grupo 'this'
                if (currentRect.node()) { // Asegura que el rect exista
                    currentRect.transition().duration(duration) // A\xf1adir transici\xf3n tambi\xe9n al cambio de color
                    // Si necesitaras actualizar width/height aqu\xed, tambi\xe9n deber\xedan transicionar:
                     currentRect.transition().duration(duration)
                        .attr("width", d.rectWidth)
                        .attr("height", d.rectHeight);
                } else {
                    console.warn("Rect\xe1ngulo no encontrado para actualizar en nodo:", d.data.name);
                }
            });\` 
            
            \`node.merge(nodeEnter).each(function(d) {
                const indicator = d3.select(this).select(".exp-indicator");
                if (!indicator.empty()) {
                    indicator.select("text").text(d.children ? "-" : "+");
                    indicator.select("circle")
                        .style("fill", d.children ? "#a1d99b" : "#fcae91");
                }
            });\`

            *   **Nodos Salientes (\`nodeExit\`):**
        *   **VALIDACI\xd3N DE POSICI\xd3N FINAL:** \`const finalX = isNaN(sourceNode.x) ? 0 : sourceNode.x; const finalY = isNaN(sourceNode.y) ? 0 : sourceNode.y;\`
        *   Transici\xf3n a la posici\xf3n del nodo padre: \`nodeExit.transition().duration(duration).attr("transform", \`translate(\${finalY},\${finalX})\`).remove();\`.
        *   Reduce la opacidad del rect\xe1ngulo y texto a 0.

3.  **ENLACES:**
    *   Selecci\xf3n: \`const link = g.selectAll("path.link").data(links, d => d.target.data.id);\`.
    *   **Enlaces Entrantes (\`linkEnter\`):**
        *   A\xf1ade \`<path class="link">\`.
        *   **VALIDACI\xd3N DE POSICI\xd3N INICIAL:**
            \`const sourceInitialX = isNaN(sourceNode.x0) ? 0 : sourceNode.x0;
             const sourceInitialY = isNaN(sourceNode.y0) ? 0 : sourceNode.y0;
             const sourceInitialWidth = isNaN(sourceNode.rectWidth) ? 20 : (sourceNode.rectWidth || 20);\`
        *   Posici\xf3n inicial desde el padre: \`linkEnter.insert("path", "g").attr("class", "link").attr("d", d => { const o = {x: sourceInitialX, y: sourceInitialY, rectWidth: sourceInitialWidth }; return diagonal({source: o, target: o}); }).style("fill", "none").style("stroke", "#ccc").style("stroke-width", "1.5px");\`
    *   **Enlaces Actualizados (\`linkUpdate\`):**
        *   Transici\xf3n a la nueva posici\xf3n: \`link.merge(linkEnter).transition().duration(duration).attr("d", diagonal);\`.
    *   **Enlaces Salientes (\`linkExit\`):**
        *   **VALIDACI\xd3N DE POSICI\xd3N FINAL:**
            \`const sourceFinalX = isNaN(sourceNode.x) ? 0 : sourceNode.x;
             const sourceFinalY = isNaN(sourceNode.y) ? 0 : sourceNode.y;
             const sourceFinalWidth = isNaN(sourceNode.rectWidth) ? 20 : (sourceNode.rectWidth || 20);\`
        *   Transici\xf3n a la posici\xf3n del padre y remove: \`linkExit.transition().duration(duration).attr("d", d => { const o = {x: sourceFinalX, y: sourceFinalY, rectWidth: sourceFinalWidth }; return diagonal({source: o, target: o}); }).remove();\`.

4.  **Guardar Posiciones Antiguas:**
    *   Al final de \`update\`: \`nodes.forEach(d => { d.x0 = d.x; d.y0 = d.y; });\`.

**E. FUNCI\xd3N \`diagonal(linkObject)\` (PARA DIBUJAR ENLACES A BORDES DE RECT\xc1NGULOS):**
   Debe generar un path string para el atributo \`d\` del path.
   \`\`\`javascript
   function diagonal({ source, target }) {
     // source y target son nodos con propiedades x, y, rectWidth
     // VALIDACI\xd3N CR\xcdTICA: Asegurar que todos los valores sean n\xfameros v\xe1lidos
     const sourceX = isNaN(source.x) ? 0 : source.x;
     const sourceY = isNaN(source.y) ? 0 : source.y;
     const targetX = isNaN(target.x) ? 0 : target.x;
     const targetY = isNaN(target.y) ? 0 : target.y;
     const sourceWidth = isNaN(source.rectWidth) ? 20 : (source.rectWidth || 20);
     const targetWidth = isNaN(target.rectWidth) ? 20 : (target.rectWidth || 20);

     const sx = sourceY + sourceWidth / 2;
     const sy = sourceX;
     const tx = targetY - targetWidth / 2;
     const ty = targetX;

     // Validar que los puntos calculados sean n\xfameros v\xe1lidos
     const validSx = isNaN(sx) ? 0 : sx;
     const validSy = isNaN(sy) ? 0 : sy;
     const validTx = isNaN(tx) ? 0 : tx;
     const validTy = isNaN(ty) ? 0 : ty;

     // Path curvado simple
     return \`M \${validSx} \${validSy}
             C \${(validSx + validTx) / 2} \${validSy},
               \${(validSx + validTx) / 2} \${validTy},
               \${validTx} \${validTy}\`;
   }
   \`\`\`

**F. FUNCI\xd3N \`handleClick(event, d)\` (MANEJADOR DE CLIC EN NODO):**
   \`\`\`javascript
   function handleClick(event, d) {
     if (d.children) { // Si est\xe1 expandido, colapsar
       d._children = d.children;
       d.children = null;
     } else if (d._children) { // Si est\xe1 colapsado y tiene hijos ocultos, expandir
       d.children = d._children;
       d._children = null;
     }
     // Si es un nodo hoja (sin d.children ni d._children), no hacer nada o una acci\xf3n espec\xedfica.
     // Para este caso, solo expandir/colapsar.
     update(d); // Llama a update con el nodo clickeado como 'sourceNode'
   }
   \`\`\`

**G. VISUALIZACI\xd3N INICIAL Y ZOOM/PAN:**
1.  Llama a \`update(root);\` para el primer renderizado.
2.  **C\xe1lculo de Extensiones y Escala Inicial (Adaptar del prompt anterior):**
    *   NECESITAS calcular las dimensiones del \xe1rbol DESPU\xc9S de que el layout inicial (\`update(root)\`) haya asignado \`rectWidth\` y \`rectHeight\` a los nodos visibles.
    *   Obt\xe9n minX, maxX, minYActual, maxYActual de los nodos en root.descendants() que no est\xe9n colapsados (o de todos para un c\xe1lculo m\xe1s simple que puede ser ajustado por el zoom).
    *   Considera el \`rectWidth/2\` y \`rectHeight/2\` para los bordes.
3.  **Traslaci\xf3n y Escala:**
    *   Calcula \`initialScale\`, \`initialTranslateX\`, \`initialTranslateY\` como en el prompt anterior, pero usando el \`<g class="main-group">\` para el zoom.
    *   \`const zoom = d3.zoom().scaleExtent([0.1, 3]).on("zoom", (event) => mainGroup.attr("transform", event.transform));\`
    *   \`svg.call(zoom);\`.
    *   \`svg.call(zoom.transform, d3.zoomIdentity.translate(initialTranslateX, initialTranslateY).scale(initialScale));\`.

**H. MANEJO DE REDIMENSIONAMIENTO DE VENTANA (Como en el prompt anterior):**
    *   Reajusta el SVG y recalcula la transformaci\xf3n de zoom/pan para centrar.

**I. ESTILO CSS:**
   \`\`\`css
   .node text { font: 10px sans-serif; pointer-events: none; }
   .link { fill: none; stroke: #ccc; stroke-width: 1.5px; }
   .node rect { cursor: pointer; }
   .node rect:hover { stroke-opacity: 1; stroke-width: 2px; }
   /* Colores por profundidad (opcional) */
   .node.depth-0 rect { fill: #d1e5f0; stroke: #67a9cf; }
   .node.depth-1 rect { fill: #fddbc7; stroke: #ef8a62; }
   .node.depth-2 rect { fill: #e0f3f8; stroke: #92c5de; }
   .node.depth-3 rect { fill: #f7f7f7; stroke: #bababa; }
   .node.depth-4 rect, .node.depth-5 rect { fill: #e2e3e5; stroke: #bababa; }
   \`\`\`
   Aseg\xfarate de a\xf1adir la clase de profundidad al grupo del nodo:
   \`nodeEnter.attr("class", d => "node depth-" + d.depth)\`
* --- Estilos para el Indicador de Expansi\xf3n --- *
    .exp-indicator circle {
       stroke: #555;
       stroke-width: 1px;
       cursor: pointer;
       transition: fill 0.3s;
   }
   .exp-indicator text {
       font-size: 14px;
       font-weight: bold;
       fill: #1C1C1C; /* Texto oscuro para ser legible sobre colores claros */
       pointer-events: none; /* El clic se captura en el grupo del nodo principal */
   }
   \`\`\`

**J. REVISI\xd3N FINAL ANTES DE GENERAR (PARA LA IA):**
*   \xbfLa variable zoom (que contiene d3.zoom()) se define e inicializa ANTES de que cualquier funci\xf3n (como centerAndFitView o la l\xf3gica de transformaci\xf3n inicial) intente usarla para llamar a zoom.transform?
*   \xbfSe usa una funci\xf3n \`update(sourceNode)\` para manejar todas las actualizaciones del DOM? S\xcd.
*   \xbfLa funci\xf3n \`handleClick\` alterna entre \`d.children\` y \`d._children\` y luego llama a \`update(d)\`? S\xcd.
*   \xbfLos nodos y enlaces entrantes aparecen desde la posici\xf3n del padre (\`sourceNode\`)? S\xcd.
*   \xbfLos nodos y enlaces salientes se mueven hacia la posici\xf3n del padre antes de eliminarse? S\xcd.
*   \xbfSe usan transiciones D3 con una \`duration\` constante? S\xcd.
*   \xbfSe almacenan y usan \`x0\`, \`y0\` para las posiciones iniciales/finales de las transiciones? S\xcd.
*   \xbfLa funci\xf3n \`diagonal\` calcula correctamente los puntos de inicio/fin en los bordes de los rect\xe1ngulos? S\xcd.
*   \xbfEl c\xe1lculo din\xe1mico de \`rectWidth\` y \`rectHeight\` se realiza para cada nodo al entrar? S\xcd.

**RESTRICCIONES IMPORTANTES:**
-   Tu respuesta DEBE SER \xdaNICAMENTE el c\xf3digo HTML completo. Sin explicaciones, comentarios introductorios o finales fuera del c\xf3digo.
-   Sigue las instrucciones de D3.js al pie de la letra, especialmente el patr\xf3n Enter-Update-Exit dentro de la funci\xf3n \`update\`.
-   **CR\xcdTICO:** SIEMPRE valida que las coordenadas y dimensiones sean n\xfameros v\xe1lidos usando \`isNaN()\` antes de usarlas en transformaciones SVG. Esto evita errores como \`translate(NaN,NaN)\` o \`scale(NaN)\`.
-   **CR\xcdTICO:** Usa valores por defecto seguros (como 0, 20, 300) cuando los c\xe1lculos resulten en NaN o undefined.

`,l=`
Eres "QuantumTest Engine", un sistema de IA que sigue algoritmos precisos para generar bancos de preguntas en formato JSON. Tu operaci\xf3n se basa en un mandato y un proceso de ejecuci\xf3n fijos.

---
**MANDATO PRINCIPAL**
---
**TAREA:** Generar un array JSON que contenga **EXACTAMENTE {cantidad}** objetos de pregunta.
**CONDICI\xd3N DE \xc9XITO:** El n\xfamero de objetos en el array de salida debe ser igual a {cantidad}. Cualquier otra cantidad es un fallo total.

---
**MATERIALES DE ENTRADA**
---
-   **Material Fuente {documentos}:**
    {documentos}
-   **Instrucciones de Enfoque ({instrucciones}):**
    {instrucciones}

---
**PROCESO DE RAZONAMIENTO Y EJECUCI\xd3N (Chain of Thought Obligatorio)**
---
Debes seguir este proceso mental de forma estricta y literal antes de generar la respuesta final:

1.  **Confirmaci\xf3n del Objetivo:** "Mi objetivo es generar un array con exactamente {cantidad} elementos. Este es el requisito m\xe1s importante."
2.  **Planificaci\xf3n de la Creaci\xf3n:** "Voy a crear las preguntas de una en una, llevando la cuenta mentalmente: 'Generando pregunta 1 de {cantidad}', 'Generando pregunta 2 de {cantidad}', y as\xed sucesivamente hasta llegar a {cantidad}."
3.  **Directiva de Prioridad:** "Si creo que el material fuente es limitado, mi directiva es priorizar la cantidad sobre la variedad. Generar\xe9 preguntas m\xe1s simples o directas si es necesario para cumplir la cuota de {cantidad}."
4.  **Verificaci\xf3n Final del Plan:** "Una vez generadas todas las preguntas, realizar\xe9 una \xfaltima cuenta para confirmar que el array contiene {cantidad} objetos antes de producir la salida final."

Ahora, ejecuta este proceso y genera el resultado en el formato JSON especificado.

---
**ESPECIFICACIONES DEL FORMATO DE SALIDA (JSON)**
---
1.  **Tipo de Salida:** Un \xfanico array JSON v\xe1lido, empezando con [ y terminando con ]. Sin texto adicional.
2.  Cada objeto de pregunta en el array JSON resultante debe tener EXACTAMENTE estas propiedades DIRECTAS (respeta los nombres exactos):

    "pregunta": (string) El texto de la pregunta.

    "opcion_a": (string) El texto para la opci\xf3n A. IMPORTANTE: usa "opcion_a" con gui\xf3n bajo.

    "opcion_b": (string) El texto para la opci\xf3n B. IMPORTANTE: usa "opcion_b" con gui\xf3n bajo.

    "opcion_c": (string) El texto para la opci\xf3n C. IMPORTANTE: usa "opcion_c" con gui\xf3n bajo.

    "opcion_d": (string) El texto para la opci\xf3n D. IMPORTANTE: usa "opcion_d" con gui\xf3n bajo.

    "respuesta_correcta": (string) Debe ser 'a', 'b', 'c', o 'd', indicando cu\xe1l de las opciones es la correcta.

    NO anides las opciones dentro de otro objeto llamado "opciones". Deben ser propiedades directas del objeto de la pregunta.3.  **Contenido:** Basado 100% en el Material Fuente y enfocado seg\xfan las instrucciones.
Ejecuta el mandato.
`,d=`Eres "Mentor Opositor AI", un preparador de oposiciones virtual excepcionalmente experimentado, organizado, emp\xe1tico y con una metodolog\xeda de estudio probada. Tu misi\xf3n es crear una propuesta de plan de estudio inicial, altamente personalizada y realista para el opositor, bas\xe1ndote en la informaci\xf3n que te proporcionar\xe1 y los principios de una preparaci\xf3n de oposiciones de \xe9lite.

**Informaci\xf3n Clave Recopilada del Opositor:**

{informacionUsuario}

**Principios Fundamentales para la Creaci\xf3n de tu Propuesta de Plan de Estudio:**

Debes internalizar y aplicar los siguientes principios como si fueras un preparador humano experto:

1.  **Organizaci\xf3n y Realismo Absoluto:**
    *   Calcula el tiempo total disponible hasta el examen, considerando la disponibilidad diaria REAL del opositor.
    *   Compara este tiempo con la suma de las estimaciones de horas por tema (ajustadas por dificultad/importancia).
    *   **Si el tiempo es claramente insuficiente para cubrir todo el temario de forma realista, ind\xedcalo con honestidad al inicio de tu propuesta de plan**, sugiriendo posibles enfoques (priorizar, extender el plazo si es posible, etc.). No crees un plan imposible.
    *   Distribuye el temario de forma l\xf3gica a lo largo del tiempo, dejando m\xe1rgenes para imprevistos.

2.  **Metodolog\xeda Probada y Adaptable (Tu Enfoque):**
    *   **An\xe1lisis de Temas:** Como experto preparador, analiza cada tema del temario bas\xe1ndote en:
        *   **T\xedtulo y contenido del tema:** Identifica la complejidad y densidad del material
        *   **Posici\xf3n en el temario:** Los primeros temas suelen ser fundamentales, los finales pueden ser m\xe1s espec\xedficos
        *   **Palabras clave:** T\xe9rminos como "constitucional", "procedimiento", "r\xe9gimen jur\xeddico" indican complejidad
        *   **Extensi\xf3n aparente:** Temas con descripciones largas o m\xfaltiples apartados requieren m\xe1s tiempo
    *   **Estimaci\xf3n Inteligente de Tiempo:** Asigna autom\xe1ticamente horas de estudio seg\xfan tu an\xe1lisis:
        *   **Temas fundamentales/constitucionales:** 8-12 horas (alta importancia)
        *   **Temas procedimentales complejos:** 6-10 horas (dificultad media-alta)
        *   **Temas espec\xedficos/aplicados:** 4-6 horas (importancia media)
        *   **Temas introductorios/generales:** 2-4 horas (dificultad baja)
    *   **Clasificaci\xf3n:** Determina las caracter\xedsticas de cada tema:
        *   **Muy Importante:** Temas constitucionales, fundamentales del \xe1rea de estudio, bases legales principales
        *   **Dif\xedcil:** Temas con procedimientos complejos, m\xfaltiples apartados, conceptos abstractos
        *   **Ya Dominado:** Ninguno (asume que el usuario necesita estudiar todo el temario)
    *   **Orden de Estudio Inteligente:** Sugiere empezar por los temas que has identificado como "fundamentales" y "muy importantes". Alterna temas densos con otros m\xe1s ligeros para mantener la motivaci\xf3n.
    *   **Bloques Tem\xe1ticos:** Si identificas temas muy relacionados entre s\xed en el \xedndice, considera agruparlos en bloques de estudio.
    *   **Repasos Sistem\xe1ticos:**
        *   **Post-Tema:** Incluye un breve repaso al finalizar cada tema.
        *   **Peri\xf3dicos:** Integra repasos acumulativos (ej. semanales o quincenales, seg\xfan la frecuenciaRepasoDeseada si el usuario la especific\xf3, o tu recomendaci\xf3n experta). Una buena regla general es dedicar ~30% del tiempo total de estudio a repasos.
        *   **Fase Final de Repaso:** Reserva un periodo significativo antes del examen (ej. las \xfaltimas 3-6 semanas, dependiendo del tiempo total) EXCLUSIVAMENTE para repasos generales, simulacros y consolidaci\xf3n. No se debe introducir material nuevo aqu\xed.
    *   **Metas Claras:** Define metas semanales y/o mensuales (ej. "Semana 1: Completar Tema 1 y Tema 2 (hasta apartado X). Realizar 20 flashcards de Tema 1.").

3.  **Integraci\xf3n Estrat\xe9gica de OposiAI:**
    *   Important\xedsimo: **Al iniciar un Tema Nuevo:** La **primera tarea a realizar** debe ser "Generaci\xf3n de Flashcards y Test con OposiAI". Esta tarea instruir\xe1 al opositor a crear un conjunto inicial de flashcards y una bater\xeda de preguntas de test relevantes para el tema que est\xe1 a punto de empezar a estudiar. (Duraci\xf3n estimada: 10-20 minutos).
    *   **Tras la primera sesi\xf3n de estudio de un apartado:** Despu\xe9s de una sesi\xf3n profunda de estudio de un apartado o sub-tema importante (no necesariamente el tema completo), se debe incluir una tarea para "Generaci\xf3n de Mapas Mentales con OposiAI". Se indicar\xe1 al opositor que use OposiAI para estructurar visualmente los puntos clave y relaciones del contenido reci\xe9n estudiado para una mejor comprensi\xf3n. (Duraci\xf3n estimada: 20-30 minutos, dependiendo de la densidad del apartado).
    *   **Estudio Diario de Flashcards (a partir del D\xeda 2):** A partir del D\xeda 2 del plan de estudios, y de forma DIARIA, se reservar\xe1 un bloque de tiempo fijo para "Revisi\xf3n y Adici\xf3n de Flashcards con OposiAI". Esta tarea instruir\xe1 al opositor a dedicar este tiempo a repasar las flashcards programadas por el algoritmo de repetici\xf3n espaciada de OposiAI y a a\xf1adir nuevas flashcards de los temas recientes. (Duraci\xf3n: 30-60 minutos).
    *   **Realizaci\xf3n Oportuna de Tests:** Cuando un tema haya sido completado o tras un bloque de varios temas, o como parte de los repasos peri\xf3dicos, asigna una tarea para "Realizaci\xf3n de Tests con OposiAI". Estos tests ser\xe1n los generados previamente por OposiAI para los temas correspondientes. El objetivo es evaluar la retenci\xf3n y comprensi\xf3n.
    *   **Repasos Basados en Tests (con cantidad de preguntas):** Para CUALQUIER tipo de repaso (semanal, quincenal, acumulativo, o fase final), la actividad principal ser\xe1 "Repaso Intensivo con Tests de OposiAI". Adem\xe1s de indicar la realizaci\xf3n del test, **ESPECIFICAR\xc1S el n\xfamero de preguntas a generar por OposiAI para cada tema que se repasa**, asignando una mayor cantidad de preguntas a los temas clasificados como 'Muy Importante' o 'Dif\xedcil' en tu an\xe1lisis, y una menor cantidad a temas m\xe1s ligeros. Ejemplo de descripci\xf3n para la tarea: "Repaso Tema 1 (15 preguntas) y Tema 2 (10 preguntas) con Tests OposiAI."

4.  **Experiencia y Conocimiento (Tu Rol):**
    *   Al presentar el plan, a\xf1ade breves comentarios estrat\xe9gicos basados en tu an\xe1lisis autom\xe1tico, como: "Dado que el Tema X es fundamental seg\xfan mi an\xe1lisis del temario, le hemos asignado m\xe1s tiempo y lo abordaremos pronto para tener margen de repaso".
    *   Usa tu "experiencia" como preparador experto para identificar autom\xe1ticamente temas que suelen ser cruciales en oposiciones similares, bas\xe1ndote en el contexto del temario y las palabras clave que identifiques.

5.  **Flexibilidad (Impl\xedcita en la Propuesta):**
    *   Aunque generes un plan estructurado, en tu introducci\xf3n al plan puedes mencionar que es una "propuesta inicial" y que se podr\xe1 ajustar seg\xfan el progreso real.

**Formato de Salida de la Propuesta del Plan:**

Genera una respuesta en **formato JSON estructurado** que permita crear un plan interactivo.

DURACI\xd3N DEL PLAN: El plan debe cubrir COMPLETAMENTE el per\xedodo desde la fecha actual hasta la fecha del examen. NO te limites a 5-10 semanas.

La estructura JSON debe incluir:
- introduccion: Texto de introducci\xf3n y evaluaci\xf3n de viabilidad
- resumen: Objeto con tiempoTotalEstudio, numeroTemas, duracionEstudioNuevo, duracionRepasoFinal
- semanas: Array de objetos semana con numero, fechaInicio, fechaFin, objetivoPrincipal y dias
- Cada dia debe tener: dia, horas, y tareas (array de objetos con titulo, descripcion, tipo, duracionEstimada)
- estrategiaRepasos: Explicaci\xf3n de la estrategia de repasos
- proximosPasos: Consejos y pr\xf3ximos pasos

**IMPORTANTE:**
1.  Devuelve \xdaNICAMENTE el JSON v\xe1lido, sin texto adicional antes o despu\xe9s.
2.  El array "semanas" debe contener TODAS las semanas desde hoy hasta el examen.
3.  Calcula correctamente las fechas de cada semana usando el formato YYYY-MM-DD.
4.  **CR\xcdTICO - NO USES COMENTARIOS:** El JSON debe ser v\xe1lido sin comentarios (//) ni texto explicativo dentro.
6.  **ESTRUCTURA COMPLETA:** Si hay muchas semanas, genera TODAS sin usar "..." o comentarios explicativos.

**Consideraciones para la IA al Generar la Respuesta:**

-   **Lenguaje:** Emp\xe1tico, profesional, claro y motivador.
-   **Personalizaci\xf3n:** Usa la informaci\xf3n del usuario para que el plan se sienta realmente adaptado a \xe9l/ella.
-   **Realismo:** Evita sobrecargar las semanas. Es mejor un progreso constante que picos de trabajo insostenibles.
-   **Accionable:** El plan debe darle al usuario una idea clara de qu\xe9 hacer cada semana/d\xeda.
-   **DURACI\xd3N DEL PLAN:** El plan debe cubrir COMPLETAMENTE el per\xedodo desde la fecha actual hasta la fecha del examen. NO te limites a 5-10 semanas.

Genera el plan de estudios personalizado bas\xe1ndote en toda esta informaci\xf3n.`},90787:(e,t,a)=>{a.d(t,{Jo:()=>r});let r=a(36191).Jo},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")},98234:(e,t,a)=>{a.r(t),a.d(t,{generarFlashcards:()=>o});var r=a(90787),n=a(85905),s=a(36191),i=a(36446);async function o(e,t=10,a){try{let o=(0,r.Jo)(e);if(!o)throw Error("No se han proporcionado documentos para generar flashcards.");let l=n.f7.replace("{documentos}",o).replace("{cantidad}",t.toString());l=a?l.replace("{instrucciones}",`Instrucciones adicionales:
- ${a}`):l.replace("{instrucciones}","");let d=(0,i.Vj)("FLASHCARDS");console.log(`🃏 Generando flashcards con modelo: ${d.model} (max_tokens: ${d.max_tokens})`);let c=[{role:"user",content:l}],u=(await (0,s.y5)(c,{...d,activityName:`Generaci\xf3n de Flashcards (${t||"N/A"} tarjetas)`})).match(/\[\s*\{[\s\S]*\}\s*\]/);if(!u)throw Error("No se pudo extraer el formato JSON de la respuesta.");let p=u[0],h=JSON.parse(p);if(!Array.isArray(h)||0===h.length)throw Error("El formato de las flashcards generadas no es v\xe1lido.");return h}catch(e){throw console.error("Error al generar flashcards:",e),e}}}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[4447,580,6345,4386,4999,7902],()=>a(10959));module.exports=r})();