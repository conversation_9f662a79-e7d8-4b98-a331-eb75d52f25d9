(()=>{var e={};e.id=9890,e.ids=[9890],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8814:(e,r,t)=>{"use strict";t.d(r,{IE:()=>s,Nu:()=>n,qo:()=>a,t4:()=>o});let a={free:{id:"free",name:"Plan Gratis",price:0,limits:{documents:1,mindMapsForTrial:2,testsForTrial:10,flashcardsForTrial:10,tokensForTrial:5e4,features:["document_upload","test_generation","flashcard_generation","mind_map_generation"]},features:["document_upload","test_generation","flashcard_generation","mind_map_generation"],restrictedFeatures:["study_planning","ai_tutor_chat","summary_a1_a2"]},usuario:{id:"usuario",name:"Plan Usuario",price:1e3,limits:{documents:-1,mindMapsPerWeek:-1,testsPerWeek:-1,flashcardsPerWeek:-1,monthlyTokens:1e6,features:["document_upload","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation"]},features:["document_upload","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation"],restrictedFeatures:["study_planning","summary_a1_a2"]},pro:{id:"pro",name:"Plan Pro",price:1500,limits:{documents:-1,mindMapsPerWeek:-1,testsPerWeek:-1,flashcardsPerWeek:-1,monthlyTokens:1e6,features:["document_upload","study_planning","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation","summary_a1_a2"]},features:["document_upload","study_planning","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation","summary_a1_a2"],restrictedFeatures:[]}};function s(e){return a[e]||null}function o(e){let r=s(e);return r?"free"===e?r.limits.tokensForTrial||5e4:r.limits.monthlyTokens||1e6:5e4}function n(e,r){let t=s(e);return!(!t||t.restrictedFeatures.includes(r))&&t.features.includes(r)}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},28354:e=>{"use strict";e.exports=require("util")},29023:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>_,routeModule:()=>c,serverHooks:()=>m,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>p});var a={};t.r(a),t.d(a,{POST:()=>l});var s=t(96559),o=t(48088),n=t(37719),i=t(32190),u=t(61730);async function l(e){try{console.log("Stripe API called");let r=await e.json();console.log("Request body:",r);let{planId:t,email:a,customerName:s,userId:o,registrationData:n}=r;if(!t||!a)return console.log("Missing planId or email"),i.NextResponse.json({error:"Plan ID y email son requeridos"},{status:400});if(console.log("Validating plan:",t),!(0,u.aN)(t))return console.log("Invalid plan ID:",t),i.NextResponse.json({error:"Plan ID no v\xe1lido"},{status:400});let l=(0,u.Md)(t);if(console.log("Plan found:",l),!l)return console.log("Plan not found"),i.NextResponse.json({error:"Plan no encontrado"},{status:404});if("free"===t)return console.log("Free plan does not require payment"),i.NextResponse.json({error:"El plan gratuito no requiere pago"},{status:400});console.log("Creating checkout session for:",{planId:t,email:a,customerName:s});let c=l.stripePriceId;if(!c)return console.log("No price ID configured for plan:",t),i.NextResponse.json({error:"Precio no configurado para este plan"},{status:500});console.log("Using price ID:",c);let d="pro"===t||"usuario"===t,p=d?"subscription":"payment";console.log("Payment mode:",p,"for plan:",t);let m={payment_method_types:["card"],line_items:[{price:c,quantity:1}],mode:p,customer_email:a,client_reference_id:t,metadata:{planId:t,customerEmail:a,customerName:s||"",userId:o||"",registrationData:n?JSON.stringify(n):"",createdAt:new Date().toISOString(),source:"oposiai_website",autoActivate:"true"},success_url:`${u.L6.success}?session_id={CHECKOUT_SESSION_ID}&plan=${t}`,cancel_url:`${u.L6.cancel}?plan=${t}`,automatic_tax:{enabled:!0},billing_address_collection:"required",allow_promotion_codes:!0};if(d&&(m.subscription_data={metadata:{planId:t,customerEmail:a,customerName:s||"",userId:o||"",registrationData:n?JSON.stringify(n):"",source:"oposiai_website",autoActivate:"true"}}),!u._4)return console.error("Stripe not initialized"),i.NextResponse.json({error:"Error de configuraci\xf3n de Stripe"},{status:500});let _=await u._4.checkout.sessions.create(m);return console.log("Checkout session created:",_.id),i.NextResponse.json({sessionId:_.id,url:_.url})}catch(r){console.error("Error creating checkout session:",r),console.error("Error stack:",r instanceof Error?r.stack:"No stack trace");let e="Error al crear la sesi\xf3n de pago";return r instanceof Error&&console.error("Error message:",e=r.message),i.NextResponse.json({error:e,details:r instanceof Error?r.message:"Unknown error"},{status:500})}}let c=new s.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/stripe/create-checkout-session/route",pathname:"/api/stripe/create-checkout-session",filename:"route",bundlePath:"app/api/stripe/create-checkout-session/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\stripe\\create-checkout-session\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:d,workUnitAsyncStorage:p,serverHooks:m}=c;function _(){return(0,n.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:p})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},61730:(e,r,t)=>{"use strict";t.d(r,{L6:()=>u,Md:()=>n,aN:()=>i,_4:()=>l});var a=t(97877),s=t(8814);let o={free:{id:"free",name:"Plan Gratis",price:0,stripeProductId:null,stripePriceId:null,features:["Incluye:","• Uso de la plataforma solo durante 5 d\xedas","• Subida de documentos: m\xe1ximo 1 documento","• Generador de test: m\xe1ximo 10 preguntas test","• Generador de flashcards: m\xe1ximo 10 tarjetas flashcard","• Generador de mapas mentales: m\xe1ximo 2 mapas mentales","No incluye:","• Planificaci\xf3n de estudios","• Habla con tu preparador IA","• Res\xfamenes A2 y A1"],limits:s.qo.free.limits,planConfig:s.qo.free},usuario:{id:"usuario",name:"Plan Usuario",price:1e3,stripeProductId:"prod_SR65BdKdek1OXd",stripePriceId:"price_1Rae5807kFn3sIXhRf3adX1n",features:["Incluye:","• Uso de la plataforma durante el mes (una vez finalizado no podr\xe1 volver a acceder hasta renovaci\xf3n)","• Subida de documentos","• Habla con tu preparador IA *","• Generador de test *","• Generador de flashcards *","• Generador de mapas mentales *","• * Para las tareas en las que se haga uso de IA, el l\xedmite mensual ser\xe1 de 1.000.000 de tokens, podr\xe1n ampliarse durante el mes mediante pago","No incluye:","• Planificaci\xf3n de estudios","• Res\xfamenes A2 y A1"],limits:s.qo.usuario.limits,planConfig:s.qo.usuario},pro:{id:"pro",name:"Plan Pro",price:1500,stripeProductId:"prod_SR66U2G7bVJqu3",stripePriceId:"price_1Rae3U07kFn3sIXhkvSuJco1",features:["Incluye:","• Uso de la plataforma durante el mes (una vez finalizado no podr\xe1 volver a acceder hasta renovaci\xf3n)","• Subida de documentos","• Planificaci\xf3n de estudios mediante IA*","• Habla con tu preparador IA *","• Generador de test *","• Generador de flashcards *","• Generador de mapas mentales *","• Generaci\xf3n de Res\xfamenes para A2 y A1","• * Para las tareas en las que se haga uso de IA, el l\xedmite mensual ser\xe1 de 1.000.000 de tokens, podr\xe1n ampliarse durante el mes mediante pago"],limits:s.qo.pro.limits,planConfig:s.qo.pro}};function n(e){return o[e]||null}function i(e){return e in o}let u={success:"http://localhost:3000/thank-you",cancel:"http://localhost:3000/upgrade-plan",webhook:"http://localhost:3000/api/stripe/webhook"},l=new a.A(process.env.STRIPE_SECRET_KEY,{apiVersion:"2025-05-28.basil",typescript:!0})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4447,580,7877],()=>t(29023));module.exports=a})();