(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4466],{453:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>eM});var t=a(5155),r=a(2115),l=a(5695),n=a(6766),i=a(351),o=a(3737),c=a(2973),d=a(8260);function m(e){let{onSelectConversation:s,conversacionActualId:a,onConversationDeleted:l}=e,[n,o]=(0,r.useState)([]),[m,u]=(0,r.useState)(!0),[x,h]=(0,r.useState)(""),[g,p]=(0,r.useState)(null),[b,f]=(0,r.useState)(null),[j,y]=(0,r.useState)(""),[N,v]=(0,r.useState)(null),w=async()=>{u(!0);try{let e=await (0,c.sj)();o(e),h("")}catch(e){console.error("Error al cargar conversaciones:",e),h("No se pudieron cargar las conversaciones")}finally{u(!1)}};(0,r.useEffect)(()=>{w()},[]),(0,r.useEffect)(()=>{a&&w()},[a]);let E=e=>{let s=new Date(e),a=Math.floor((new Date().getTime()-s.getTime())/864e5);return 0===a?s.toLocaleTimeString("es-ES",{hour:"2-digit",minute:"2-digit"}):1===a?"Ayer":a<7?"Hace ".concat(a," d\xedas"):s.toLocaleDateString("es-ES",{day:"2-digit",month:"2-digit",year:"2-digit"})},k=e=>e.titulo?e.titulo:"Conversaci\xf3n del ".concat(E(e.creado_en)),C=async e=>{let s;if(!g){p(e);try{s=d.oR.loading("Eliminando conversaci\xf3n..."),await (0,c.sq)(e)?(d.oR.success("Conversaci\xf3n eliminada exitosamente",{id:s}),o(s=>s.filter(s=>s.id!==e)),e===a&&(null==l||l())):d.oR.error("Error al eliminar la conversaci\xf3n",{id:s})}catch(e){console.error("Error al eliminar conversaci\xf3n:",e),d.oR.error("Error al eliminar la conversaci\xf3n",{id:s})}finally{p(null),v(null)}}},S=e=>{f(e.id),y(k(e))},T=()=>{f(null),y("")},A=async e=>{let s;if(!j.trim())return void d.oR.error("El t\xedtulo no puede estar vac\xedo");try{s=d.oR.loading("Actualizando t\xedtulo..."),await (0,c.fW)(e,j.trim())?(d.oR.success("T\xedtulo actualizado exitosamente",{id:s}),o(s=>s.map(s=>s.id===e?{...s,titulo:j.trim()}:s)),f(null),y("")):d.oR.error("Error al actualizar el t\xedtulo",{id:s})}catch(e){console.error("Error al actualizar t\xedtulo:",e),d.oR.error("Error al actualizar el t\xedtulo",{id:s})}};return(0,t.jsxs)("div",{className:"w-64 bg-gray-50 border border-gray-200 rounded-lg flex flex-col h-full",children:[(0,t.jsxs)("div",{className:"p-4 border-b border-gray-200",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Conversaciones"}),(0,t.jsxs)("p",{className:"text-sm text-gray-500 mt-1",children:[n.length," conversaci\xf3n",1!==n.length?"es":""]})]}),(0,t.jsx)("div",{className:"flex-1 overflow-y-auto",children:m?(0,t.jsxs)("div",{className:"flex flex-col items-center justify-center py-8",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"}),(0,t.jsx)("p",{className:"text-sm text-gray-500 mt-2",children:"Cargando conversaciones..."})]}):x?(0,t.jsx)("div",{className:"p-4",children:(0,t.jsx)("div",{className:"text-red-500 text-sm",children:x})}):0===n.length?(0,t.jsx)("div",{className:"p-4",children:(0,t.jsx)("div",{className:"text-gray-500 text-sm text-center",children:"No hay conversaciones guardadas"})}):(0,t.jsx)("div",{className:"divide-y divide-gray-200",children:n.map(e=>(0,t.jsxs)("div",{className:"p-4 hover:bg-gray-100 transition-colors ".concat(a===e.id?"bg-blue-50 border-r-2 border-blue-500":""),children:[(0,t.jsx)("div",{className:"flex items-start justify-between mb-2",children:(0,t.jsx)("div",{className:"flex-1 min-w-0",children:b===e.id?(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("input",{type:"text",value:j,onChange:e=>y(e.target.value),className:"flex-1 text-sm font-medium bg-white border border-gray-300 rounded px-2 py-1",onKeyDown:s=>{"Enter"===s.key?A(e.id):"Escape"===s.key&&T()},autoFocus:!0}),(0,t.jsx)("button",{onClick:()=>A(e.id),className:"text-green-600 hover:text-green-800",children:(0,t.jsx)(i.YrT,{size:16})}),(0,t.jsx)("button",{onClick:T,className:"text-gray-600 hover:text-gray-800",children:(0,t.jsx)(i.yGN,{size:16})})]}):(0,t.jsx)("h4",{className:"font-medium text-gray-800 truncate cursor-pointer hover:text-blue-600",onClick:()=>s(e.id),title:k(e),children:k(e)})})}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("div",{className:"text-xs text-gray-500",children:E(e.actualizado_en)}),b!==e.id&&(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)("button",{onClick:()=>S(e),className:"p-1 text-gray-400 hover:text-blue-600 transition-colors",title:"Renombrar conversaci\xf3n",children:(0,t.jsx)(i.Pj4,{size:14})}),N===e.id?(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)("button",{onClick:()=>C(e.id),disabled:g===e.id,className:"p-1 text-red-600 hover:text-red-800 transition-colors",title:"Confirmar eliminaci\xf3n",children:(0,t.jsx)(i.YrT,{size:14})}),(0,t.jsx)("button",{onClick:()=>v(null),className:"p-1 text-gray-400 hover:text-gray-600 transition-colors",title:"Cancelar",children:(0,t.jsx)(i.yGN,{size:14})})]}):(0,t.jsx)("button",{onClick:()=>v(e.id),disabled:g===e.id,className:"p-1 text-gray-400 hover:text-red-600 transition-colors",title:"Eliminar conversaci\xf3n",children:(0,t.jsx)(i.IXo,{size:14})})]})]}),a===e.id&&(0,t.jsx)("div",{className:"text-xs text-blue-600 mt-1 font-medium",children:"Conversaci\xf3n actual"})]},e.id))})})]})}var u=a(2177),x=a(221),h=a(8781),g=a(4092),p=a(1544),b=a(5967);function f(e){let{documentosSeleccionados:s}=e,[a,l]=(0,r.useState)([]),[n,i]=(0,r.useState)(!1),[o,d]=(0,r.useState)(""),[f,j]=(0,r.useState)(null),[y,N]=(0,r.useState)(!1),v=(0,r.useRef)(null),{user:w}=(0,g.A)(),[E,k]=(0,r.useState)(null),{register:C,handleSubmit:S,formState:{errors:T},reset:A,setValue:R}=(0,u.mN)({resolver:(0,x.u)(h.oS),defaultValues:{pregunta:"",documentos:s}});(0,r.useEffect)(()=>{R("documentos",s.map(e=>({...e,categoria:e.categoria||null,numero_tema:void 0!==e.numero_tema&&null!==e.numero_tema?"string"==typeof e.numero_tema?parseInt(e.numero_tema,10):e.numero_tema:void 0,id:e.id||void 0,creado_en:e.creado_en||void 0,actualizado_en:e.actualizado_en||void 0,user_id:e.user_id||void 0,tipo_original:e.tipo_original||void 0})))},[s,R]),(0,r.useEffect)(()=>{(async()=>{w&&k(await (0,p.qk)("ai_tutor_chat")?"paid":"free")})()},[w]),(0,r.useEffect)(()=>{let e=async()=>{try{let e=await (0,c.Sl)();e?(j(e.id),await _(e.id)):(l([]),j(null))}catch(e){console.warn("No se pudo cargar la conversaci\xf3n activa (esto es normal para usuarios nuevos):",e),l([]),j(null)}};"paid"===E&&e()},[E]),(0,r.useEffect)(()=>{v.current&&(v.current.scrollTop=v.current.scrollHeight)},[a]);let _=async e=>{try{i(!0),await (0,c.vW)(e);let s=(await (0,c.C9)(e)).map(e=>({id:e.id,tipo:e.tipo,contenido:e.contenido,timestamp:new Date(e.timestamp)}));l(s),j(e),d("")}catch(e){console.error("Error al cargar la conversaci\xf3n:",e),d("No se pudo cargar la conversaci\xf3n")}finally{i(!1)}},z=async(e,s)=>{try{N(!0);let a=s||f;if(a){let s=await (0,c.Sl)();return s&&s.id===a||await (0,c.vW)(a),await (0,c.QE)({conversacion_id:a,tipo:e.tipo,contenido:e.contenido}),a}if("usuario"===e.tipo){let s="Conversaci\xf3n: ".concat(e.contenido.substring(0,50)).concat(e.contenido.length>50?"...":""),a=await (0,c.Yp)(s,!0);if(!a)throw Error("No se pudo crear la conversaci\xf3n");return j(a),await (0,c.QE)({conversacion_id:a,tipo:e.tipo,contenido:e.contenido}),a}throw console.error("❌ ERROR CR\xcdTICO: Intentando guardar mensaje de IA sin conversaci\xf3n activa"),Error("No se puede guardar un mensaje de IA sin una conversaci\xf3n activa")}catch(e){return console.error("Error al guardar el mensaje:",e),null}finally{N(!1)}},D=async()=>{try{i(!0),await (0,c.CM)(),l([]),j(null),d("")}catch(e){console.error("Error al iniciar nueva conversaci\xf3n:",e)}finally{i(!1)}},P=async e=>{i(!0),d("");let t={tipo:"usuario",contenido:e.pregunta,timestamp:new Date};l(e=>[...e,t]),i(!0),d(""),A({pregunta:"",documentos:s});let r=null;try{r=await z(t);let s=await fetch("/api/ai",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({pregunta:t.contenido,documentos:e.documentos})}),n=await s.json(),i="";if(i=n.result?"string"==typeof n.result?n.result:JSON.stringify(n.result):n.error?"string"==typeof n.error?n.error:JSON.stringify(n.error):"Error desconocido al obtener respuesta de la IA.",!r)throw console.error("❌ ERROR: No se pudo obtener el ID de conversaci\xf3n para guardar la respuesta de la IA"),Error("No se pudo guardar la respuesta: conversaci\xf3n no encontrada");f!==r&&j(r);let o={tipo:"ia",contenido:i,timestamp:new Date};if(l(e=>[...e,o]),await z(o,r),0===a.length&&r){let s="Conversaci\xf3n: ".concat(e.pregunta.substring(0,50)).concat(e.pregunta.length>50?"...":"");await (0,c.fW)(r,s)}}catch(a){console.error("Error al obtener respuesta:",a);let e="Lo siento, ha ocurrido un error al procesar tu pregunta. Por favor, int\xe9ntalo de nuevo.";a instanceof Error&&(e=a.message.includes("API key")?"Error de configuraci\xf3n: La clave de API de Gemini no est\xe1 configurada correctamente.":a.message.includes("network")||a.message.includes("fetch")?"Error de conexi\xf3n: No se pudo conectar con el servicio de IA. Verifica tu conexi\xf3n a internet.":a.message.includes("quota")||a.message.includes("limit")?"Se ha alcanzado el l\xedmite de uso del servicio de IA. Int\xe9ntalo m\xe1s tarde.":"Error: ".concat(a.message)),d(e);let s={tipo:"ia",contenido:e,timestamp:new Date};l(e=>[...e,s]);try{let e=r||f;e&&await z(s,e)}catch(e){console.error("Error al guardar mensaje de error en DB:",e)}}finally{i(!1)}},I=e=>e.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"});return"free"===E?(0,t.jsx)("div",{className:"mt-6",children:(0,t.jsx)(b.A,{feature:"ai_tutor_chat",benefits:["Chat ilimitado con IA especializada","Respuestas personalizadas a tus documentos","Historial completo de conversaciones","Explicaciones detalladas y ejemplos"],className:"h-[600px]"})}):(0,t.jsxs)("div",{className:"mt-6 flex h-[600px] gap-6",children:[(0,t.jsxs)("div",{className:"flex-1 flex flex-col",children:[(0,t.jsx)("div",{className:"flex justify-start mb-4",children:(0,t.jsxs)("button",{type:"button",onClick:D,className:"bg-green-500 hover:bg-green-600 text-white font-semibold py-2 px-4 rounded inline-flex items-center",disabled:n,children:[(0,t.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-2",viewBox:"0 0 20 20",fill:"currentColor",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z",clipRule:"evenodd"})}),"Nueva conversaci\xf3n"]})}),(0,t.jsx)("div",{ref:v,className:"flex-grow overflow-y-auto mb-4 p-4 border rounded-lg bg-gray-50",style:{height:"calc(100% - 180px)"},children:0===a.length?(0,t.jsx)("div",{className:"flex items-center justify-center h-full text-gray-500",children:(0,t.jsx)("p",{children:"Selecciona documentos y haz una pregunta para comenzar la conversaci\xf3n."})}):(0,t.jsxs)("div",{className:"space-y-4",children:[a.map((e,s)=>(0,t.jsx)("div",{className:"flex ".concat("usuario"===e.tipo?"justify-end":"justify-start"),children:(0,t.jsxs)("div",{className:"max-w-[80%] p-3 rounded-lg ".concat("usuario"===e.tipo?"bg-blue-500 text-white rounded-br-none":"bg-white border border-gray-300 rounded-bl-none"),children:[(0,t.jsx)("div",{className:"whitespace-pre-wrap",children:e.contenido}),(0,t.jsx)("div",{className:"text-xs mt-1 text-right ".concat("usuario"===e.tipo?"text-blue-100":"text-gray-500"),children:I(e.timestamp)})]})},e.id||s)),n&&(0,t.jsx)("div",{className:"flex justify-start",children:(0,t.jsx)("div",{className:"bg-white p-3 rounded-lg border border-gray-300 rounded-bl-none",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"animate-bounce h-2 w-2 bg-gray-500 rounded-full"}),(0,t.jsx)("div",{className:"animate-bounce h-2 w-2 bg-gray-500 rounded-full",style:{animationDelay:"0.2s"}}),(0,t.jsx)("div",{className:"animate-bounce h-2 w-2 bg-gray-500 rounded-full",style:{animationDelay:"0.4s"}})]})})}),y&&(0,t.jsx)("div",{className:"text-xs text-gray-500 text-center py-1",children:"Guardando conversaci\xf3n..."})]})}),(0,t.jsxs)("form",{onSubmit:S(P),className:"mt-auto",children:[o&&(0,t.jsx)("div",{className:"text-red-500 text-sm mb-2",children:o}),(0,t.jsx)("div",{className:"text-xs text-gray-600 mb-2",children:s.length>0?(0,t.jsxs)("span",{className:"text-green-600",children:["✓ ",s.length," documento",1!==s.length?"s":""," seleccionado",1!==s.length?"s":""]}):(0,t.jsx)("span",{className:"text-red-600",children:"⚠ No hay documentos seleccionados. Selecciona al menos uno para hacer preguntas."})}),(0,t.jsxs)("div",{className:"flex items-end space-x-2",children:[(0,t.jsxs)("div",{className:"flex-grow",children:[(0,t.jsx)("textarea",{id:"pregunta",className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",rows:2,...C("pregunta"),placeholder:"Escribe tu pregunta sobre los documentos seleccionados...",disabled:n,onKeyDown:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),S(P)())}}),T.pregunta&&(0,t.jsx)("p",{className:"text-red-500 text-xs mt-1",children:T.pregunta.message}),(0,t.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Presiona Enter para enviar, Shift+Enter para nueva l\xednea"})]}),(0,t.jsx)("button",{type:"submit",className:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-full h-10 w-10 flex items-center justify-center focus:outline-none focus:shadow-outline disabled:opacity-50 disabled:cursor-not-allowed",disabled:n||0===s.length,title:0===s.length?"Selecciona al menos un documento para hacer una pregunta":"Enviar pregunta",children:n?(0,t.jsxs)("svg",{className:"animate-spin h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,t.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,t.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 008-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}):(0,t.jsx)("svg",{className:"h-5 w-5 text-white",fill:"currentColor",viewBox:"0 0 20 20",children:(0,t.jsx)("path",{d:"M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z"})})})]})]})]}),(0,t.jsx)(m,{onSelectConversation:_,conversacionActualId:f,onConversationDeleted:()=>{l([]),j(null),d("")}})]})}var j=a(6874),y=a.n(j);function N(e){let{className:s=""}=e,[a,l]=(0,r.useState)(null),[n,o]=(0,r.useState)(!0);(0,r.useEffect)(()=>{c()},[]);let c=async()=>{try{o(!0);let e=await fetch("/api/user/plan");if(e.ok){let s=await e.json();if("free"===s.plan){let e=await fetch("/api/auth/free-account-status");if(e.ok){let s=await e.json();if(s.success&&s.isFreeAccount&&s.status){let e=s.status.usageCount||{},a=s.status.limits||{},t="number"==typeof e.documents?e.documents:0,r="number"==typeof a.documents?a.documents:1;l({current:t,limit:r,remaining:Math.max(0,r-t),isAtLimit:t>=r,plan:"free"})}else l(null)}else console.error("Error fetching free account status:",e.status),l(null)}else l({current:0,limit:-1,remaining:-1,isAtLimit:!1,plan:s.plan||"paid"})}else l(null)}catch(e){console.error("Error cargando estado de l\xedmites:",e),l(null)}finally{o(!1)}};if(n)return(0,t.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-500 ".concat(s),children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-gray-400"}),(0,t.jsx)("span",{children:"Cargando l\xedmites..."})]});if(!a)return null;if("paid"===a.plan||-1===a.limit)return(0,t.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-green-600 ".concat(s),children:[(0,t.jsx)(i.jH2,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:"Documentos ilimitados"})]});let d=a.current/a.limit*100;return(0,t.jsxs)("div",{className:"space-y-2 ".concat(s),children:[(0,t.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(i.jH2,{className:"w-4 h-4 text-gray-500"}),(0,t.jsxs)("span",{className:"text-gray-700",children:["Documentos: ",a.current,"/",a.limit]})]}),a.isAtLimit&&(0,t.jsxs)("div",{className:"flex items-center space-x-1 text-red-600",children:[(0,t.jsx)(i.y3G,{className:"w-4 h-4"}),(0,t.jsx)("span",{className:"text-xs font-medium",children:"L\xedmite alcanzado"})]})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"h-2 rounded-full transition-all duration-300 ".concat(a.isAtLimit?"bg-red-500":d>80?"bg-yellow-500":"bg-green-500"),style:{width:"".concat(Math.min(d,100),"%")}})}),a.isAtLimit?(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3",children:(0,t.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,t.jsx)(i.y3G,{className:"w-5 h-5 text-red-600 mt-0.5 flex-shrink-0"}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("p",{className:"text-sm text-red-800 font-medium",children:"L\xedmite de documentos alcanzado"}),(0,t.jsxs)("p",{className:"text-xs text-red-700 mt-1",children:["Has alcanzado el l\xedmite de ",a.limit," documento(s) para el plan gratuito."]}),(0,t.jsxs)(y(),{href:"/upgrade-plan",className:"inline-flex items-center mt-2 px-3 py-1 bg-red-600 text-white text-xs font-medium rounded hover:bg-red-700 transition-colors",children:[(0,t.jsx)(i.ei4,{className:"w-3 h-3 mr-1"}),"Actualizar Plan"]})]})]})}):0===a.remaining?(0,t.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-3",children:(0,t.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,t.jsx)(i.y3G,{className:"w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0"}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("p",{className:"text-sm text-yellow-800 font-medium",children:"\xdaltimo documento disponible"}),(0,t.jsx)("p",{className:"text-xs text-yellow-700 mt-1",children:"Este es tu \xfaltimo documento disponible en el plan gratuito."})]})]})}):(0,t.jsxs)("div",{className:"text-xs text-gray-600",children:["Te quedan ",a.remaining," documento(s) disponible(s)"]})]})}function v(e){let{onSuccess:s}=e,[a,l]=(0,r.useState)(""),[n,i]=(0,r.useState)(""),[o,m]=(0,r.useState)(""),[u,x]=(0,r.useState)(""),[h,g]=(0,r.useState)(null),p=(0,r.useRef)(null),[b,f]=(0,r.useState)(!1),[j,y]=(0,r.useState)({texto:"",tipo:""}),v=async e=>{let t;if(e.preventDefault(),f(!0),y({texto:"",tipo:""}),h){t=d.Ay.loading("Subiendo ".concat(h.name,"..."));let e=new FormData;e.append("file",h),e.append("titulo",a),o&&e.append("categoria",o),u&&e.append("numero_tema",u);try{let a=await fetch("/api/document/upload",{method:"POST",body:e});if(a.ok){let e=await a.json();d.Ay.success('Documento "'.concat(h.name,'" subido y procesado con ID: ').concat(e.documentId,"."),{id:t}),l(""),i(""),m(""),x(""),g(null),p.current&&(p.current.value=""),s&&s()}else{let e=await a.json();403===a.status&&e.needsUpgrade?d.Ay.error("".concat(e.error,": ").concat(e.reason),{id:t,duration:6e3}):d.Ay.error("Error al subir archivo: ".concat(e.error||a.statusText),{id:t})}}catch(e){console.error("Error en la subida del archivo:",e),d.Ay.error("Error de conexi\xf3n o inesperado al subir el archivo.",{id:t})}finally{f(!1)}}else{if(!a.trim()||!n.trim()){y({texto:"El t\xedtulo y el contenido son obligatorios si no se selecciona un archivo.",tipo:"error"}),f(!1);return}t=d.Ay.loading("Guardando documento manualmente...");try{let e={titulo:a,contenido:n,categoria:o||void 0,numero_tema:u?parseInt(u):void 0};await (0,c.hE)(e)?(d.Ay.success("Documento guardado manualmente correctamente.",{id:t}),l(""),i(""),m(""),x(""),s&&s()):d.Ay.error("Error al guardar el documento manualmente.",{id:t})}catch(e){console.error("Error al guardar documento manualmente:",e),d.Ay.error("Ha ocurrido un error al guardar el documento manualmente.",{id:t})}finally{f(!1)}}};return(0,t.jsxs)("div",{className:"bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4",children:[(0,t.jsx)("h2",{className:"text-xl font-bold mb-4",children:"Subir nuevo documento"}),(0,t.jsx)(N,{className:"mb-6"}),(0,t.jsxs)("form",{onSubmit:v,className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"titulo",className:"block text-gray-700 text-sm font-bold mb-2",children:"T\xedtulo:"}),(0,t.jsx)("input",{type:"text",id:"titulo",className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",value:a,onChange:e=>l(e.target.value),placeholder:"T\xedtulo del documento (se autocompleta con el nombre del archivo)",disabled:b,required:!0})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"categoria",className:"block text-gray-700 text-sm font-bold mb-2",children:"Categor\xeda:"}),(0,t.jsxs)("select",{id:"categoria",className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",value:o,onChange:e=>m(e.target.value),disabled:b,children:[(0,t.jsx)("option",{value:"",children:"Seleccionar categor\xeda"}),(0,t.jsx)("option",{value:"tema",children:"Tema"}),(0,t.jsx)("option",{value:"anexo",children:"Anexo"}),(0,t.jsx)("option",{value:"resumen",children:"Resumen"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"numeroTema",className:"block text-gray-700 text-sm font-bold mb-2",children:"N\xfamero de tema:"}),(0,t.jsx)("input",{type:"number",id:"numeroTema",className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",value:u,onChange:e=>x(e.target.value),placeholder:"Opcional",min:"1",disabled:b})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"contenido",className:"block text-gray-700 text-sm font-bold mb-2",children:"Contenido (manual o previsualizaci\xf3n de .txt):"}),(0,t.jsx)("textarea",{id:"contenido",className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",rows:10,value:n,onChange:e=>i(e.target.value),placeholder:"Escribe o pega el contenido aqu\xed, o selecciona un archivo .txt para previsualizarlo. Para PDFs, el contenido se extraer\xe1 autom\xe1ticamente.",disabled:b})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"archivo",className:"block text-gray-700 text-sm font-bold mb-2",children:"O sube un archivo (.txt o .pdf):"}),(0,t.jsx)("input",{type:"file",id:"archivo",ref:p,className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",onChange:e=>{var s;let a=null==(s=e.target.files)?void 0:s[0];if(y({texto:"",tipo:""}),!a)return void g(null);if(a.size>5242880){d.Ay.error("El archivo es demasiado grande. El tama\xf1o m\xe1ximo es ".concat(5,"MB.")),p.current&&(p.current.value=""),g(null);return}if(g(a),l(a.name),"text/plain"===a.type){let e=new FileReader;e.onload=e=>{var s;(null==(s=e.target)?void 0:s.result)?(i(e.target.result),d.Ay.success("Archivo TXT le\xeddo y listo para vista previa.")):(d.Ay.error("Error al leer el archivo TXT."),i(""))},e.onerror=()=>{d.Ay.error("Error al leer el archivo TXT."),i("")},e.readAsText(a)}else"application/pdf"===a.type?(i("El contenido se extraer\xe1 del PDF al guardar. Puedes editar el t\xedtulo si es necesario."),d.Ay.success("Archivo PDF seleccionado. El contenido se procesar\xe1 en el servidor.")):(i("Este tipo de archivo no tiene previsualizaci\xf3n. El contenido se procesar\xe1 en el servidor si es compatible."),(0,d.Ay)("Archivo ".concat(a.name," seleccionado. El tipo no es previsualizable.")))},accept:".txt,.pdf",disabled:b}),(0,t.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:["Solo archivos .txt o .pdf. M\xe1ximo ",5,"MB."]})]}),j.texto&&(0,t.jsx)("div",{className:"p-3 rounded ".concat("error"===j.tipo?"bg-red-100 text-red-700":"bg-green-100 text-green-700"),children:j.texto}),(0,t.jsx)("div",{children:(0,t.jsx)("button",{type:"submit",className:"bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",disabled:b,children:b?"Guardando...":"Guardar documento"})})]})]})}var w=a(9679);let E=()=>{let{addTask:e,updateTask:s,getTasksByType:a}=(0,w.M)(),t=(0,r.useCallback)(async(a,t,r)=>{let{peticion:l,contextos:n,cantidad:i,onComplete:o,onError:c}=r,d=e({type:a,title:l.length>50?"".concat(l.substring(0,50),"..."):l});try{s(d,{status:"processing"}),console.log("\uD83D\uDE80 Iniciando generaci\xf3n de ".concat(a,":"),{action:t,peticion:l,contextos:null==n?void 0:n.length,cantidad:i});let e=await fetch("/api/ai",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:t,peticion:l,contextos:n,cantidad:i})});if(console.log("\uD83D\uDCE1 Respuesta recibida para ".concat(a,":"),e.status,e.ok),!e.ok){let s=await e.text();throw console.error("❌ Error en la API para ".concat(a,":"),e.status,s),Error("Error en la API: ".concat(e.status," - ").concat(s))}let r=await e.json();console.log("✅ Resultado obtenido para ".concat(a,":"),r);let{result:c}=r;return s(d,{status:"completed",result:c,progress:100}),o&&setTimeout(()=>o(c),0),d}catch(a){let e=a instanceof Error?a.message:"Error desconocido";throw s(d,{status:"error",error:e}),c&&setTimeout(()=>c(e),0),a}},[e,s]),l=(0,r.useCallback)(async e=>t("mapa-mental","generarMapaMental",e),[t]),n=(0,r.useCallback)(async e=>t("test","generarTest",e),[t]),i=(0,r.useCallback)(async e=>t("flashcards","generarFlashcards",e),[t]),o=(0,r.useCallback)(async e=>{let{documento:s,instrucciones:a,onComplete:r,onError:l}=e,n={action:"generarResumen",peticion:"".concat(s.titulo,"|").concat(s.categoria||"","|").concat(s.numero_tema||"","|").concat(a),contextos:[s.contenido]};return t("resumen","generarResumen",{peticion:n.peticion,contextos:n.contextos,onComplete:r,onError:l})},[t]),c=(0,r.useCallback)(async a=>{let{temarioId:t,onComplete:r,onError:l}=a,n=e({type:"plan-estudios",title:"Generando plan de estudios personalizado"});try{s(n,{status:"processing"});let e=await fetch("/api/ai",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"generarPlanEstudios",peticion:t,contextos:[]})});if(!e.ok)throw Error("Error en la API: ".concat(e.status));let{result:a}=await e.json();return s(n,{status:"completed",result:a,progress:100}),r&&setTimeout(()=>r(a),0),n}catch(a){let e=a instanceof Error?a.message:"Error desconocido";throw s(n,{status:"error",error:e}),l&&setTimeout(()=>l(e),0),a}},[e,s]);return{generateMapaMental:l,generateTest:n,generateFlashcards:i,generatePlanEstudios:c,generateResumen:o,isGenerating:(0,r.useCallback)(e=>a(e).some(e=>"pending"===e.status||"processing"===e.status),[a]),getActiveTask:(0,r.useCallback)(e=>a(e).find(e=>"pending"===e.status||"processing"===e.status),[a])}},k=e=>{let{taskType:s,onResult:a,onError:t}=e,{tasks:l}=(0,w.M)(),[n,i]=(0,r.useState)(new Set),o=(0,r.useRef)(a),c=(0,r.useRef)(t);return o.current=a,c.current=t,(0,r.useEffect)(()=>{let e=l.filter(e=>e.type===s&&"completed"===e.status&&!n.has(e.id)&&e.result),a=l.filter(e=>e.type===s&&"error"===e.status&&!n.has(e.id)&&e.error);if(e.length>0){let s=e[e.length-1];i(e=>{let a=new Set(e);return a.add(s.id),a}),o.current&&setTimeout(()=>{var e;null==(e=o.current)||e.call(o,s.result)},0)}if(a.length>0){let e=a[a.length-1];i(s=>{let a=new Set(s);return a.add(e.id),a}),c.current&&setTimeout(()=>{var s;null==(s=c.current)||s.call(c,e.error)},0)}},[l,s,n]),{resetProcessed:(0,r.useCallback)(()=>{i(new Set)},[])}};function C(e){let{className:s=""}=e,[a,l]=(0,r.useState)(!1);return(0,t.jsxs)("div",{className:"relative ".concat(s),children:[(0,t.jsxs)("button",{onClick:()=>l(!a),className:"inline-flex items-center px-3 py-1 text-sm text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-md transition-colors",title:"Ayuda sobre mapas mentales",children:[(0,t.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-1",viewBox:"0 0 20 20",fill:"currentColor",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z",clipRule:"evenodd"})}),"\xbfC\xf3mo usar?"]}),a&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-40",onClick:()=>l(!1)}),(0,t.jsx)("div",{className:"fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 sm:w-96 bg-white border border-gray-200 rounded-lg shadow-xl z-50 p-4 max-h-80 sm:max-h-96 overflow-y-auto",children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-800",children:"Gu\xeda de Mapas Mentales"}),(0,t.jsx)("button",{onClick:()=>l(!1),className:"text-gray-400 hover:text-gray-600",children:(0,t.jsx)("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,t.jsxs)("div",{className:"space-y-2 text-sm text-gray-600",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium text-gray-700 mb-1",children:"\uD83D\uDCDD Generar"}),(0,t.jsx)("p",{className:"text-xs",children:"Describe el mapa mental basado en tus documentos."})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium text-gray-700 mb-1",children:"\uD83D\uDD0D Vista Previa"}),(0,t.jsx)("p",{className:"text-xs",children:"Revisa el resultado antes de expandir."})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium text-gray-700 mb-1",children:"\uD83D\uDDA5️ Pantalla Completa"}),(0,t.jsx)("p",{className:"text-xs",children:"Bot\xf3n azul para mejor visualizaci\xf3n."})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium text-gray-700 mb-1",children:"⌨️ Controles"}),(0,t.jsxs)("ul",{className:"list-disc list-inside space-y-0.5 ml-2 text-xs",children:[(0,t.jsxs)("li",{children:[(0,t.jsx)("kbd",{className:"px-1 py-0.5 bg-gray-100 rounded text-xs",children:"ESC"})," para salir"]}),(0,t.jsx)("li",{children:"Clic fuera para cerrar"}),(0,t.jsx)("li",{children:"Zoom y pan en el mapa"}),(0,t.jsx)("li",{children:"Clic en nodos para expandir"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium text-gray-700 mb-1",children:"\uD83D\uDCBE Descargar"}),(0,t.jsx)("p",{className:"text-xs",children:"Guarda como archivo HTML interactivo."})]})]}),(0,t.jsx)("div",{className:"pt-2 border-t border-gray-100",children:(0,t.jsxs)("p",{className:"text-xs text-gray-500",children:["\uD83D\uDCA1 ",(0,t.jsx)("strong",{children:"Consejo:"})," Los mapas son interactivos con zoom y navegaci\xf3n."]})})]})})]})]})}function S(){let{validateAndExecute:e}=function(){let{isFreeAccount:e,status:s,canPerformAction:a}=function(){let{user:e,isLoading:s}=(0,g.A)(),[a,t]=(0,r.useState)({isFreeAccount:!1,status:null,alerts:[],usageWarnings:[],recommendations:[],upgradeUrl:"/upgrade-plan",loading:!0,error:null}),l=(0,r.useCallback)(async()=>{if(!e||s)return void t(e=>({...e,loading:!1}));try{t(e=>({...e,loading:!0,error:null}));let e=await fetch("/api/auth/free-account-status"),s=await e.json();if(!e.ok){if(404===e.status)return void t({isFreeAccount:!1,status:null,alerts:[],usageWarnings:[],recommendations:[],upgradeUrl:"/upgrade-plan",loading:!1,error:null});throw Error(s.error||"Error obteniendo estado")}t({isFreeAccount:s.isFreeAccount,status:s.status,alerts:s.alerts||[],usageWarnings:s.usageWarnings||[],recommendations:s.recommendations||[],upgradeUrl:s.upgradeUrl||"/upgrade-plan",loading:!1,error:null})}catch(e){console.error("Error obteniendo estado de cuenta gratuita:",e),t(s=>({...s,loading:!1,error:e instanceof Error?e.message:"Error desconocido"}))}},[e,s]),n=(0,r.useCallback)(function(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;return!a.isFreeAccount||!a.status||!!a.status.isActive&&(a.status.usage[e]||0)+s<=(a.status.limits[e]||0)},[a]),i=(0,r.useCallback)(async()=>{await l()},[l]);return(0,r.useEffect)(()=>{l()},[l]),{...a,refresh:i,canPerformAction:n}}(),t=(0,r.useCallback)(async function(t){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,l=arguments.length>2?arguments[2]:void 0;if(!e)try{let e=await l();return{success:!0,result:e}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Error desconocido"}}if(!a(t,r)){let e=(null==s?void 0:s.usage[t])||0,a=(null==s?void 0:s.limits[t])||0;return(null==s?void 0:s.isActive)?{success:!1,error:"Has alcanzado el l\xedmite de ".concat(t," (").concat(e,"/").concat(a,"). Actualiza tu plan para continuar.")}:{success:!1,error:"Tu cuenta gratuita ha expirado. Actualiza tu plan para continuar."}}try{let e=await l();return{success:!0,result:e}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Error desconocido"}}},[e,s,a]);return{isFreeAccount:e,status:s,validateAndExecute:t,canPerformAction:a}}();return{executeWithGuard:async function(s,a){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;return e(s,t,a)}}}function T(){let[e,s]=(0,r.useState)("free"),[a,t]=(0,r.useState)(!0),[l,n]=(0,r.useState)(null),{user:i}=(0,g.A)();return(0,r.useEffect)(()=>{(async()=>{if(!i){s("free"),t(!1);return}try{t(!0),n(null);let e=await fetch("/api/user/plan");if(!e.ok)throw Error("Error obteniendo plan del usuario");let a=await e.json();s(a.plan||"free")}catch(e){console.error("Error fetching user plan:",e),n(e instanceof Error?e.message:"Error desconocido"),s("free")}finally{t(!1)}})()},[i]),{plan:e,isLoading:a,error:l}}function A(e){let{documentosSeleccionados:s}=e,[a,l]=(0,r.useState)(null),[n,o]=(0,r.useState)(!1),[c,m]=(0,r.useState)(!1),{generateMapaMental:b,isGenerating:f,getActiveTask:j}=E(),{getTask:N}=(0,w.M)(),{executeWithGuard:v}=S(),{user:A}=(0,g.A)(),{plan:R,isLoading:_}=T(),z=j("mapa-mental"),D=f("mapa-mental");k({taskType:"mapa-mental",onResult:e=>{l(e),d.oR.success("\xa1Mapa mental generado exitosamente!")},onError:e=>{d.oR.error("Error al generar mapa mental: ".concat(e))}});let{register:P,handleSubmit:I,formState:{errors:F}}=(0,u.mN)({resolver:(0,x.u)(h.MZ),defaultValues:{peticion:""}});(0,r.useEffect)(()=>{let e=e=>{"Escape"===e.key&&n&&o(!1)};return n?(document.addEventListener("keydown",e),document.body.style.overflow="hidden"):document.body.style.overflow="unset",()=>{document.removeEventListener("keydown",e),document.body.style.overflow="unset"}},[n]);let L=async e=>{let a=s.map(e=>e.contenido);console.log("\uD83D\uDDFA️ Iniciando generaci\xf3n de mapa mental:",{peticion:e.peticion,documentos:s.length,contextosLength:a.length});let t=await v("mindMaps",async()=>{let s=await b({peticion:e.peticion,contextos:a});return console.log("✅ Tarea de mapa mental creada:",s),s},1);t.success?d.oR.success("Generaci\xf3n iniciada en segundo plano. Puedes continuar usando la aplicaci\xf3n.",{duration:4e3}):(console.error("❌ Error al generar mapa mental:",t.error),d.oR.error(t.error||"Error al iniciar la generaci\xf3n del mapa mental"))},M=()=>{if(!a)return;let e=new Blob([a],{type:"text/html"}),s=URL.createObjectURL(e),t=document.createElement("a");t.href=s,t.download="mapa-mental.html",document.body.appendChild(t),t.click(),setTimeout(()=>{document.body.removeChild(t),URL.revokeObjectURL(s)},0)},G=()=>{o(!1)};return(0,t.jsxs)("div",{className:"mt-8 border-t pt-8",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,t.jsx)("h2",{className:"text-xl font-bold",children:"Generador de Mapas Mentales"}),(0,t.jsx)(C,{})]}),!_&&"free"===R&&(0,t.jsx)("div",{className:"mb-6 p-4 bg-purple-50 border border-purple-200 rounded-lg",children:(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)(i.F5$,{className:"w-5 h-5 text-purple-600 mt-0.5"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium text-purple-900",children:"L\xedmites del Plan Gratuito"}),(0,t.jsxs)("p",{className:"text-sm text-purple-700 mt-1",children:["M\xe1ximo ",p.qo.free.limits.mindMapsForTrial," mapas mentales durante el per\xedodo de prueba. Para generar mapas mentales ilimitados,",(0,t.jsx)(y(),{href:"/upgrade-plan",className:"font-medium underline hover:text-purple-800 ml-1",children:"actualiza tu plan"}),"."]})]})]})}),(0,t.jsxs)("form",{onSubmit:I(L),className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"peticion",className:"block text-gray-700 text-sm font-bold mb-2",children:"Describe el mapa mental que deseas generar:"}),(0,t.jsx)("input",{id:"peticion",type:"text",...P("peticion"),disabled:D,className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",placeholder:"Ej: Genera un mapa mental sobre los conceptos principales del tema 1"}),F.peticion&&(0,t.jsx)("span",{className:"text-red-500 text-sm",children:F.peticion.message}),(0,t.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"La IA generar\xe1 un mapa mental basado en los documentos seleccionados y tu petici\xf3n."})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("button",{type:"submit",className:"bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline disabled:opacity-50",disabled:D||0===s.length,children:D?"Generando en segundo plano...":"Generar Mapa Mental"}),z&&(0,t.jsxs)("div",{className:"text-sm text-blue-600 bg-blue-50 px-3 py-2 rounded-lg",children:[(0,t.jsx)("span",{className:"font-medium",children:"Generando:"})," ",z.title]})]})]}),D&&(0,t.jsx)("div",{className:"mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg",children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-blue-800 font-medium",children:"Generando mapa mental en segundo plano"}),(0,t.jsx)("p",{className:"text-blue-600 text-sm",children:"Puedes continuar usando otras funciones de la aplicaci\xf3n"})]})]})}),a&&(0,t.jsxs)("div",{className:"mt-6",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Vista previa:"}),(0,t.jsx)("div",{className:"bg-gray-100 p-4 rounded-lg border overflow-hidden",style:{maxHeight:"500px"},children:(0,t.jsx)("iframe",{srcDoc:a,title:"Vista previa del mapa mental",className:"w-full h-96 border-0",sandbox:"allow-scripts allow-same-origin"})}),(0,t.jsxs)("div",{className:"flex justify-between items-center mt-2",children:[(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"Vista previa limitada. Usa pantalla completa o descarga para mejor experiencia."}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)("button",{type:"button",onClick:()=>{o(!0)},className:"bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline flex items-center",children:[(0,t.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-2",viewBox:"0 0 20 20",fill:"currentColor",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M3 4a1 1 0 011-1h4a1 1 0 010 2H6.414l2.293 2.293a1 1 0 11-1.414 1.414L5 6.414V8a1 1 0 01-2 0V4zm9 1a1 1 0 010-2h4a1 1 0 011 1v4a1 1 0 01-2 0V6.414l-2.293 2.293a1 1 0 11-1.414-1.414L13.586 5H12zm-9 7a1 1 0 012 0v1.586l2.293-2.293a1 1 0 111.414 1.414L6.414 15H8a1 1 0 010 2H4a1 1 0 01-1-1v-4zm13-1a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 010-2h1.586l-2.293-2.293a1 1 0 111.414-1.414L15 13.586V12a1 1 0 011-1z",clipRule:"evenodd"})}),"Pantalla Completa"]}),(0,t.jsxs)("button",{type:"button",onClick:M,className:"bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline flex items-center",children:[(0,t.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-2",viewBox:"0 0 20 20",fill:"currentColor",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z",clipRule:"evenodd"})}),"Descargar Mapa Mental"]})]})]})]}),n&&a&&(0,t.jsxs)("div",{className:"fixed inset-0 z-50 bg-black bg-opacity-90 flex items-center justify-center animate-fadeIn",children:[(0,t.jsxs)("div",{className:"relative w-full h-full max-w-none max-h-none p-4 animate-scaleIn",children:[(0,t.jsxs)("div",{className:"absolute top-4 left-4 right-4 z-10 flex justify-between items-center bg-white bg-opacity-90 backdrop-blur-sm rounded-lg px-4 py-2 shadow-lg",children:[(0,t.jsx)("div",{className:"flex items-center space-x-4",children:(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-800",children:"Mapa Mental - Vista Completa"})}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)("button",{onClick:M,className:"bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm flex items-center",title:"Descargar mapa mental",children:[(0,t.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-1",viewBox:"0 0 20 20",fill:"currentColor",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z",clipRule:"evenodd"})}),"Descargar"]}),(0,t.jsxs)("button",{onClick:G,className:"bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm flex items-center",title:"Cerrar pantalla completa",children:[(0,t.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-1",viewBox:"0 0 20 20",fill:"currentColor",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})}),"Cerrar"]})]})]}),(0,t.jsx)("div",{className:"w-full h-full pt-16 pb-4",children:(0,t.jsx)("iframe",{srcDoc:a,title:"Mapa mental en pantalla completa",className:"w-full h-full border-0 rounded-lg shadow-2xl bg-white",sandbox:"allow-scripts allow-same-origin"})})]}),(0,t.jsx)("div",{className:"absolute inset-0 -z-10",onClick:G,"aria-label":"Cerrar pantalla completa"})]})]})}function R(e){let{documentosSeleccionados:s}=e,[a,l]=(0,r.useState)(""),[n,o]=(0,r.useState)(""),[m,b]=(0,r.useState)([]),[f,j]=(0,r.useState)(!1),[N,v]=(0,r.useState)(0),[C,A]=(0,r.useState)(!1),[R,_]=(0,r.useState)([]),[z,D]=(0,r.useState)("nueva"),[P,I]=(0,r.useState)(!1),[F,L]=(0,r.useState)(""),{generateFlashcards:M,isGenerating:G,getActiveTask:O}=E(),{getTask:q}=(0,w.M)(),{executeWithGuard:H}=S(),{user:V}=(0,g.A)(),{plan:U,isLoading:B}=T();O("flashcards");let $=G("flashcards");k({taskType:"flashcards",onResult:e=>{b(e),d.oR.success("\xa1Flashcards generadas exitosamente!")},onError:e=>{d.oR.error("Error al generar flashcards: ".concat(e))}});let{register:Y,handleSubmit:W,formState:{errors:K}}=(0,u.mN)({resolver:(0,x.u)(h.oO),defaultValues:{peticion:"",cantidad:10}});(0,r.useEffect)(()=>{X()},[]);let X=async()=>{I(!0);try{let e=await (0,c.oE)();_(e)}catch(e){console.error("Error al cargar colecciones:",e),d.oR.error("No se pudieron cargar las colecciones existentes.")}finally{I(!1)}},J=async e=>{let t=s.map(e=>e.contenido);b([]),j(!1);let r=await H("flashcards",async()=>(await M({peticion:e.peticion,contextos:t,cantidad:e.cantidad}),a||l("Flashcards: ".concat(e.peticion.substring(0,50)).concat(e.peticion.length>50?"...":"")),!0),e.cantidad);r.success?d.oR.success("Generaci\xf3n iniciada en segundo plano. Puedes continuar usando la aplicaci\xf3n.",{duration:4e3}):d.oR.error(r.error||"Error al iniciar la generaci\xf3n de flashcards")},Z=async()=>{if(0===m.length)return void L("No hay flashcards para guardar");if("nueva"===z&&!a.trim())return void L("Por favor, proporciona un t\xedtulo para la nueva colecci\xf3n");if("nueva"!==z&&""===z)return void L("Por favor, selecciona una colecci\xf3n existente");L("");try{let e;if("nueva"===z){if(!(e=await (0,c.qJ)(a,n)))throw Error("No se pudo crear la colecci\xf3n")}else e=z;let s=m.map(s=>({coleccion_id:e,pregunta:s.pregunta,respuesta:s.respuesta}));if(!await (0,c.yK)(s))throw Error("No se pudieron guardar las flashcards");j(!0),"nueva"===z&&await X()}catch(e){console.error("Error al guardar las flashcards:",e),L("Ha ocurrido un error al guardar las flashcards. Por favor, int\xe9ntalo de nuevo.")}},Q=()=>{A(!C)};return(0,t.jsxs)("div",{className:"mt-8 border-t pt-8",children:[(0,t.jsx)("h2",{className:"text-xl font-bold mb-4",children:"Generador de Flashcards"}),!B&&"free"===U&&(0,t.jsx)("div",{className:"mb-6 p-4 bg-orange-50 border border-orange-200 rounded-lg",children:(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)(i.F5$,{className:"w-5 h-5 text-orange-600 mt-0.5"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium text-orange-900",children:"L\xedmites del Plan Gratuito"}),(0,t.jsxs)("p",{className:"text-sm text-orange-700 mt-1",children:["M\xe1ximo ",p.qo.free.limits.flashcardsForTrial," flashcards durante el per\xedodo de prueba. Para generar flashcards ilimitadas,",(0,t.jsx)(y(),{href:"/upgrade-plan",className:"font-medium underline hover:text-orange-800 ml-1",children:"actualiza tu plan"}),"."]})]})]})}),(0,t.jsxs)("form",{onSubmit:W(J),className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"peticion",className:"block text-gray-700 text-sm font-bold mb-2",children:"Describe las flashcards que deseas generar:"}),(0,t.jsx)("textarea",{id:"peticion",className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",rows:3,...Y("peticion"),placeholder:"Ej: Genera flashcards sobre los conceptos principales del tema 1",disabled:$}),K.peticion&&(0,t.jsx)("span",{className:"text-red-500 text-sm",children:K.peticion.message}),(0,t.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"La IA generar\xe1 flashcards basadas en los documentos seleccionados y tu petici\xf3n."})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"cantidad",className:"block text-gray-700 text-sm font-bold mb-2",children:"N\xfamero de flashcards:"}),(0,t.jsx)("input",{id:"cantidad",type:"number",min:"1",max:"30",className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",...Y("cantidad",{valueAsNumber:!0}),disabled:$}),K.cantidad&&(0,t.jsx)("span",{className:"text-red-500 text-sm",children:K.cantidad.message}),(0,t.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"Especifica cu\xe1ntas flashcards quieres generar (entre 1 y 30)."})]}),F&&(0,t.jsx)("div",{className:"text-red-500 text-sm",children:F}),(0,t.jsx)("div",{children:(0,t.jsx)("button",{type:"submit",className:"bg-orange-500 hover:bg-orange-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",disabled:$||0===s.length,children:$?"Generando...":"Generar Flashcards"})})]}),$&&(0,t.jsxs)("div",{className:"mt-4 text-center",children:[(0,t.jsx)("p",{className:"text-gray-600",children:"Generando flashcards, por favor espera..."}),(0,t.jsx)("div",{className:"mt-2 flex justify-center",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"})})]}),m.length>0&&(0,t.jsxs)("div",{className:"mt-6",children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold mb-4",children:["Flashcards generadas (",m.length,")"]}),!f&&(0,t.jsxs)("div",{className:"bg-gray-100 p-4 rounded-lg mb-6",children:[(0,t.jsx)("h4",{className:"font-medium mb-2",children:"Guardar flashcards"}),(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsx)("label",{htmlFor:"tipoColeccion",className:"block text-sm font-medium text-gray-700 mb-1",children:"\xbfD\xf3nde quieres guardar estas flashcards?"}),(0,t.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("input",{type:"radio",id:"nuevaColeccion",name:"tipoColeccion",value:"nueva",checked:"nueva"===z,onChange:()=>D("nueva"),className:"mr-2",disabled:$}),(0,t.jsx)("label",{htmlFor:"nuevaColeccion",className:"text-sm text-gray-700",children:"Crear nueva colecci\xf3n"})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("input",{type:"radio",id:"coleccionExistente",name:"tipoColeccion",value:"existente",checked:"nueva"!==z,onChange:()=>{R.length>0?D(R[0].id):D("")},className:"mr-2",disabled:$||0===R.length}),(0,t.jsxs)("label",{htmlFor:"coleccionExistente",className:"text-sm text-gray-700",children:["A\xf1adir a una colecci\xf3n existente",0===R.length&&(0,t.jsx)("span",{className:"text-gray-500 ml-2",children:"(No hay colecciones disponibles)"})]})]})]})]}),"nueva"===z&&(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"tituloColeccion",className:"block text-sm font-medium text-gray-700 mb-1",children:"T\xedtulo de la nueva colecci\xf3n:"}),(0,t.jsx)("input",{type:"text",id:"tituloColeccion",className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",value:a,onChange:e=>l(e.target.value),disabled:$})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"descripcionColeccion",className:"block text-sm font-medium text-gray-700 mb-1",children:"Descripci\xf3n (opcional):"}),(0,t.jsx)("textarea",{id:"descripcionColeccion",className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",rows:2,value:n,onChange:e=>o(e.target.value),disabled:$})]})]}),"nueva"!==z&&R.length>0&&(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"coleccionExistenteSelect",className:"block text-sm font-medium text-gray-700 mb-1",children:"Selecciona una colecci\xf3n:"}),(0,t.jsx)("select",{id:"coleccionExistenteSelect",className:"shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",value:z,onChange:e=>D(e.target.value),disabled:$,children:R.map(e=>(0,t.jsx)("option",{value:e.id,children:e.titulo},e.id))})]}),(0,t.jsx)("div",{className:"mt-4",children:(0,t.jsx)("button",{type:"button",onClick:Z,className:"bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",disabled:!1,children:"Guardar flashcards"})})]}),f&&(0,t.jsxs)("div",{className:"bg-green-100 text-green-800 p-4 rounded-lg mb-6",children:[(0,t.jsx)("p",{className:"font-medium",children:"nueva"===z?"\xa1Nueva colecci\xf3n creada correctamente!":"\xa1Flashcards a\xf1adidas a la colecci\xf3n correctamente!"}),(0,t.jsxs)("p",{className:"text-sm mt-1",children:["Puedes acceder a ","nueva"===z?"ella":"las flashcards",' desde la secci\xf3n de "Mis Flashcards".']})]}),(0,t.jsxs)("div",{className:"bg-white border rounded-lg shadow-md p-6 mb-4",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,t.jsx)("button",{onClick:()=>{N>0&&(v(N-1),A(!1))},disabled:0===N,className:"p-2 rounded-full ".concat(0===N?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-200"),children:(0,t.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})})}),(0,t.jsxs)("span",{className:"text-gray-600",children:[N+1," de ",m.length]}),(0,t.jsx)("button",{onClick:()=>{N<m.length-1&&(v(N+1),A(!1))},disabled:N===m.length-1,className:"p-2 rounded-full ".concat(N===m.length-1?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-200"),children:(0,t.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})})]}),(0,t.jsx)("div",{className:"min-h-[200px] flex items-center justify-center cursor-pointer",onClick:Q,children:(0,t.jsx)("div",{className:"text-center p-4 w-full",children:C?(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-semibold text-lg mb-2",children:m[N].pregunta}),(0,t.jsx)("div",{className:"border-t pt-4 text-left whitespace-pre-wrap",children:m[N].respuesta})]}):(0,t.jsx)("div",{className:"font-semibold text-lg",children:m[N].pregunta})})}),(0,t.jsx)("div",{className:"mt-4 text-center",children:(0,t.jsx)("button",{onClick:Q,className:"bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:C?"Ocultar respuesta":"Mostrar respuesta"})})]}),(0,t.jsxs)("div",{className:"mt-6",children:[(0,t.jsx)("h4",{className:"font-medium mb-2",children:"Todas las flashcards:"}),(0,t.jsx)("div",{className:"space-y-2",children:m.map((e,s)=>(0,t.jsx)("div",{className:"p-3 border rounded-lg cursor-pointer hover:bg-gray-50 ".concat(s===N?"border-blue-500 bg-blue-50":""),onClick:()=>{v(s),A(!1)},children:(0,t.jsx)("p",{className:"font-medium",children:e.pregunta})},s))})]})]})]})}var _=a(1153),z=a(6317),D=a(1881);async function P(){try{let{user:e}=await (0,D.iF)();if(!e)return console.error("No hay usuario autenticado"),[];let{data:s,error:a}=await z.N.from("resumenes").select("*").eq("user_id",e.id).order("creado_en",{ascending:!1});if(a)return console.error("Error al obtener res\xfamenes:",a),[];return s||[]}catch(e){return console.error("Error al obtener res\xfamenes:",e),[]}}async function I(e){try{let{user:s}=await (0,D.iF)();if(!s)return console.log("No hay usuario autenticado para verificar resumen"),!1;console.log("Verificando si existe resumen para documento ".concat(e," y usuario ").concat(s.id));let{data:a,error:t}=await z.N.from("resumenes").select("id").eq("user_id",s.id).eq("documento_id",e).maybeSingle();if(t)return console.error("Error al verificar resumen existente:",t),!1;let r=!!a;return console.log("Resultado verificaci\xf3n resumen: ".concat(r?"existe":"no existe")),r}catch(e){return console.error("Error al verificar resumen existente:",e),!1}}async function F(e,s,a,t){try{let{user:r}=await (0,D.iF)();if(!r)return console.error("No hay usuario autenticado para guardar resumen"),null;if(console.log("Intentando guardar resumen para documento ".concat(e," y usuario ").concat(r.id)),await I(e))return console.error("Ya existe un resumen para este documento"),null;let l={user_id:r.id,documento_id:e,titulo:s.trim(),contenido:a.trim(),instrucciones:(null==t?void 0:t.trim())||null};console.log("Datos del resumen a insertar:",{...l,contenido:"".concat(l.contenido.substring(0,100),"...")});let{data:n,error:i}=await z.N.from("resumenes").insert([l]).select().single();if(i)return console.error("Error al guardar resumen en Supabase:",i),null;if(!(null==n?void 0:n.id))return console.error("No se recibi\xf3 ID del resumen guardado"),null;return console.log("Resumen guardado exitosamente con ID: ".concat(n.id)),n.id}catch(e){return console.error("Error al guardar resumen:",e),null}}async function L(e){try{let{user:s}=await (0,D.iF)();if(!s)return console.error("No hay usuario autenticado"),!1;let{error:a}=await z.N.from("resumenes").delete().eq("id",e).eq("user_id",s.id);if(a)return console.error("Error al eliminar resumen:",a),!1;return!0}catch(e){return console.error("Error al eliminar resumen:",e),!1}}async function M(e,s){try{let{user:a}=await (0,D.iF)();if(!a)return console.error("No hay usuario autenticado para guardar resumen editado"),!1;console.log("Guardando versi\xf3n editada del resumen ".concat(e));let{error:t}=await z.N.from("resumenes").update({contenido_editado:s.trim(),editado:!0,fecha_edicion:new Date().toISOString(),actualizado_en:new Date().toISOString()}).eq("id",e).eq("user_id",a.id);if(t)return console.error("Error al guardar resumen editado en Supabase:",t),!1;return console.log("✅ Resumen editado guardado exitosamente"),!0}catch(e){return console.error("Error al guardar resumen editado:",e),!1}}let G=_.z.object({instrucciones:_.z.string().min(10,"Las instrucciones deben tener al menos 10 caracteres")});function O(e){let{documentosSeleccionados:s,onSummaryGenerated:a}=e,[l,n]=(0,r.useState)(null),[i,o]=(0,r.useState)(0),{generateResumen:c,isGenerating:m,getActiveTask:h}=E(),{register:g,handleSubmit:p,formState:{errors:b},reset:f}=(0,u.mN)({resolver:(0,x.u)(G),defaultValues:{instrucciones:"Crea un resumen completo y estructurado del tema, organizando los conceptos principales de manera clara y did\xe1ctica."}}),j=e=>e&&e.contenido?Math.max(15,Math.min(120,Math.ceil(e.contenido.split(/\s+/).length/100))):30,y=1===s.length,N=s[0],v=N?N?N.titulo&&0!==N.titulo.trim().length?N.contenido&&0!==N.contenido.trim().length?N.contenido.trim().length<50?{valido:!1,error:"El contenido del documento es demasiado corto para generar un resumen \xfatil"}:{valido:!0}:{valido:!1,error:"El documento debe tener contenido"}:{valido:!1,error:"El documento debe tener un t\xedtulo"}:{valido:!1,error:"No se ha proporcionado ning\xfan documento"}:{valido:!1,error:"No hay documento seleccionado"},w=h("resumen"),k=m("resumen");(0,r.useEffect)(()=>{(null==w?void 0:w.status)==="completed"&&w.result?(n(w.result),o(0)):(null==w?void 0:w.status)==="error"&&o(0)},[w]);let C=async e=>{if(!y||!N||!v.valido)return void d.oR.error("No se puede generar el resumen. Verifica que tengas exactamente un documento seleccionado.");try{if(console.log("\uD83D\uDE80 Iniciando generaci\xf3n de resumen..."),o(j(N)),console.log("\uD83D\uDD0D Verificando si ya existe resumen..."),await I(N.id)){console.log("⚠️ Ya existe un resumen para este documento"),d.oR.error("Ya existe un resumen para este documento. Solo se permite un resumen por tema.");return}console.log("✅ No existe resumen previo, continuando..."),await c({documento:N,instrucciones:e.instrucciones,onComplete:async s=>{console.log("✅ Resumen generado, guardando en Supabase...");let t=await F(N.id,"Resumen: ".concat(N.titulo),s,e.instrucciones);t?(console.log("✅ Resumen guardado exitosamente con ID:",t),null==a||a(t),f()):(console.error("❌ Error al guardar el resumen - no se recibi\xf3 ID"),d.oR.error("Error al guardar el resumen en la base de datos"))},onError:e=>{console.error("❌ Error en generaci\xf3n de resumen:",e),o(0)}})}catch(s){console.error("Error al iniciar generaci\xf3n de resumen:",s);let e=s instanceof Error?s.message:"Error al generar el resumen";d.oR.error(e),o(0)}};return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,t.jsx)("h3",{className:"font-semibold text-blue-900 mb-2",children:"\uD83D\uDCC4 Generaci\xf3n de Res\xfamenes"}),y?v.valido?(0,t.jsxs)("div",{className:"text-blue-700",children:[(0,t.jsx)("p",{className:"font-medium",children:"✅ Documento seleccionado:"}),(0,t.jsxs)("p",{className:"text-sm mt-1",children:[(0,t.jsx)("strong",{children:N.titulo}),N.numero_tema&&" (Tema ".concat(N.numero_tema,")")]}),(0,t.jsxs)("p",{className:"text-xs mt-1 text-blue-600",children:["Contenido: ~",N.contenido.split(/\s+/).length," palabras"]})]}):(0,t.jsxs)("div",{className:"text-red-700",children:[(0,t.jsx)("p",{className:"font-medium",children:"⚠️ Documento no v\xe1lido"}),(0,t.jsx)("p",{className:"text-sm mt-1",children:v.error})]}):(0,t.jsxs)("div",{className:"text-red-700",children:[(0,t.jsx)("p",{className:"font-medium",children:"⚠️ Selecci\xf3n incorrecta"}),(0,t.jsx)("p",{className:"text-sm mt-1",children:0===s.length?"Debes seleccionar exactamente un documento para generar un resumen.":"Tienes ".concat(s.length," documentos seleccionados. Solo se permite generar un resumen por tema.")})]})]}),y&&v.valido&&(0,t.jsxs)("form",{onSubmit:p(C),className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"instrucciones",className:"block text-gray-700 text-sm font-bold mb-2",children:"Instrucciones para el resumen:"}),(0,t.jsx)("textarea",{id:"instrucciones",...g("instrucciones"),disabled:k,rows:4,className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline resize-vertical",placeholder:"Describe c\xf3mo quieres que sea el resumen..."}),b.instrucciones&&(0,t.jsx)("span",{className:"text-red-500 text-sm",children:b.instrucciones.message}),(0,t.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"Especifica el enfoque, nivel de detalle, o aspectos particulares que quieres que incluya el resumen."})]}),(0,t.jsx)("button",{type:"submit",disabled:k,className:"bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline flex items-center",children:k?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,t.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,t.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Generando resumen...",i>0&&" (~".concat(i,"s)")]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-2",viewBox:"0 0 20 20",fill:"currentColor",children:[(0,t.jsx)("path",{fillRule:"evenodd",d:"M4 4a2 2 0 012-2h8a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 0v12h8V4H6z",clipRule:"evenodd"}),(0,t.jsx)("path",{fillRule:"evenodd",d:"M8 6a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1zM8 9a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1zM8 12a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z",clipRule:"evenodd"})]}),"Generar Resumen"]})}),k&&(0,t.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-3",children:(0,t.jsxs)("p",{className:"text-yellow-800 text-sm",children:[(0,t.jsx)("strong",{children:"⏳ Generando resumen..."}),(0,t.jsx)("br",{}),"La IA est\xe1 analizando el contenido y creando un resumen estructurado. Este proceso puede tardar ",i>0?"aproximadamente ".concat(i," segundos"):"unos momentos",".",(0,t.jsx)("br",{}),(0,t.jsx)("em",{children:"Puedes navegar a otras pesta\xf1as mientras se genera."})]})})]}),l&&(0,t.jsxs)("div",{className:"mt-6",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-3",children:"\uD83D\uDCCB Resumen Generado"}),(0,t.jsx)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4 max-h-96 overflow-y-auto",children:(0,t.jsx)("div",{className:"prose prose-sm max-w-none",dangerouslySetInnerHTML:{__html:l.replace(/\n/g,"<br />")}})}),(0,t.jsx)("p",{className:"text-sm text-gray-500 mt-2",children:"✅ Resumen guardado exitosamente. Puedes acceder a \xe9l desde la secci\xf3n de res\xfamenes."})]})]})}function q(e){let{refreshTrigger:s}=e,[a,l]=(0,r.useState)([]),[n,o]=(0,r.useState)(!0),[c,m]=(0,r.useState)(null),[u,x]=(0,r.useState)(!1),[h,g]=(0,r.useState)(null),[p,b]=(0,r.useState)(!1),[f,j]=(0,r.useState)("editada"),y=async()=>{try{o(!0);let e=await P();l(e)}catch(e){console.error("Error al cargar res\xfamenes:",e),d.oR.error("Error al cargar los res\xfamenes")}finally{o(!1)}};(0,r.useEffect)(()=>{y()},[s]);let N=async(e,s)=>{if(confirm('\xbfEst\xe1s seguro de que quieres eliminar el resumen "'.concat(s,'"?')))try{await L(e)?(d.oR.success("Resumen eliminado exitosamente"),await y()):d.oR.error("Error al eliminar el resumen")}catch(e){console.error("Error al eliminar resumen:",e),d.oR.error("Error al eliminar el resumen")}},v=e=>{m(e),e.editado&&e.contenido_editado?j("editada"):j("original"),x(!0)},w=async e=>{if(confirm('\xbfEst\xe1s seguro de que quieres editar el resumen "'.concat(e.titulo,'"? Esta acci\xf3n crear\xe1 una versi\xf3n condensada del resumen original.')))try{g(e.id),d.oR.loading("Editando resumen con IA...",{id:"editing-summary"});let s=e.contenido_editado||e.contenido,a=await fetch("/api/ai",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"editarResumen",contextos:[s]})});if(!a.ok)throw Error("Error en la respuesta del servidor");let t=await a.json();if(!t.result)throw Error("No se recibi\xf3 resultado de la edici\xf3n");if(await M(e.id,t.result))d.oR.success("Resumen editado exitosamente",{id:"editing-summary"}),await y();else throw Error("Error al guardar el resumen editado")}catch(e){console.error("Error al editar resumen:",e),d.oR.error("Error al editar el resumen",{id:"editing-summary"})}finally{g(null)}},E=e=>{try{let s,a="";u&&c&&c.id===e.id?(s="editada"===f&&c.contenido_editado?c.contenido_editado:c.contenido,a="editada"===f&&c.contenido_editado?" (Versi\xf3n Editada)":""):(s=e.contenido_editado||e.contenido,a=e.contenido_editado?" (Versi\xf3n Editada)":"");let t=function(e,s,a){let t=(e=e.replace(/\r\n/g,"\n").replace(/\r/g,"\n")).split(/\n\s*\n/),r="";for(let e of t){if(!(e=e.trim()))continue;let s=e.replace(/^### (.*$)/gim,"<h3>$1</h3>").replace(/^## (.*$)/gim,"<h2>$1</h2>").replace(/^# (.*$)/gim,"<h1>$1</h1>").replace(/\*\*(.*?)\*\*/g,"<strong>$1</strong>").replace(/\*(.*?)\*/g,"<em>$1</em>").replace(/^> (.*$)/gim,"<blockquote>$1</blockquote>").replace(/^\- (.*$)/gim,"<li>$1</li>").replace(/^\* (.*$)/gim,"<li>$1</li>").replace(/^\+ (.*$)/gim,"<li>$1</li>").replace(/\n/g," ");s.includes("<li>")?r+=s=s.replace(/(<li>.*?<\/li>)/g,e=>"<ul>".concat(e,"</ul>")):s.startsWith("<h1>")||s.startsWith("<h2>")||s.startsWith("<h3>")||s.startsWith("<blockquote>")?r+=s:r+="<p>".concat(s,"</p>")}let l="";if(s&&(l+="<h1>".concat(s,"</h1>")),a){if(l+='<div class="metadata">',a.createdAt){let e=new Date(a.createdAt).toLocaleDateString("es-ES",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"});l+="<p><strong>Fecha de creaci\xf3n:</strong> ".concat(e,"</p>")}a.author&&(l+="<p><strong>Autor:</strong> ".concat(a.author,"</p>")),a.instructions&&(l+="<p><strong>Instrucciones utilizadas:</strong> ".concat(a.instructions,"</p>")),l+="</div>"}return l+=r}(s,e.titulo+a,{createdAt:e.creado_en,instructions:e.instrucciones||void 0,author:"OposiAI"}),r=window.open("","_blank");r&&(r.document.write("\n          <html>\n            <head>\n              <title>".concat(e.titulo,"</title>\n              <style>\n                * { box-sizing: border-box; }\n                body {\n                  font-family: Arial, sans-serif;\n                  margin: 20mm;\n                  line-height: 1.6;\n                  color: #333;\n                  font-size: 12px;\n                }\n                h1 {\n                  color: #2563eb;\n                  border-bottom: 2px solid #2563eb;\n                  padding-bottom: 10px;\n                  margin: 0 0 20px 0;\n                  font-size: 24px;\n                  text-align: left;\n                }\n                h2 {\n                  color: #1e40af;\n                  border-bottom: 1px solid #cbd5e1;\n                  padding-bottom: 5px;\n                  margin: 25px 0 15px 0;\n                  font-size: 20px;\n                  text-align: left;\n                }\n                h3 {\n                  color: #1e40af;\n                  margin: 20px 0 10px 0;\n                  font-size: 16px;\n                  text-align: left;\n                }\n                p {\n                  margin: 12px 0;\n                  text-align: justify;\n                  text-justify: inter-word;\n                  hyphens: auto;\n                  word-wrap: break-word;\n                }\n                strong {\n                  color: #1e40af;\n                  font-weight: bold;\n                }\n                em {\n                  font-style: italic;\n                  color: #64748b;\n                }\n                ul, ol {\n                  margin: 12px 0;\n                  padding-left: 20px;\n                }\n                li {\n                  margin: 6px 0;\n                  text-align: justify;\n                }\n                blockquote {\n                  margin: 15px 0;\n                  padding: 12px 15px;\n                  background-color: #f8fafc;\n                  border-left: 4px solid #2563eb;\n                  font-style: italic;\n                  text-align: justify;\n                }\n                .metadata {\n                  font-size: 11px;\n                  color: #64748b;\n                  margin-bottom: 25px;\n                  padding: 12px;\n                  background-color: #f8fafc;\n                  border-radius: 5px;\n                  border: 1px solid #e2e8f0;\n                }\n                .metadata p {\n                  margin: 4px 0;\n                  text-align: left;\n                }\n                @media print {\n                  body {\n                    margin: 20mm;\n                    font-size: 12px;\n                  }\n                  .metadata {\n                    background-color: #f9f9f9;\n                    border: 1px solid #ddd;\n                  }\n                  blockquote {\n                    background-color: #f9f9f9;\n                  }\n                }\n              </style>\n            </head>\n            <body>\n              ").concat(t,"\n            </body>\n          </html>\n        ")),r.document.close(),r.focus(),r.print())}catch(e){console.error("Error al imprimir:",e),d.oR.error("Error al preparar la impresi\xf3n")}},k=e=>new Date(e).toLocaleDateString("es-ES",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"});return n?(0,t.jsxs)("div",{className:"flex justify-center items-center py-8",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),(0,t.jsx)("span",{className:"ml-2 text-gray-600",children:"Cargando res\xfamenes..."})]}):(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-800",children:"\uD83D\uDCDA Mis Res\xfamenes"}),(0,t.jsxs)("button",{onClick:y,className:"text-blue-600 hover:text-blue-800 text-sm flex items-center",children:[(0,t.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-1",viewBox:"0 0 20 20",fill:"currentColor",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z",clipRule:"evenodd"})}),"Actualizar"]})]}),(0,t.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)(i.Pj4,{className:"h-5 w-5 text-blue-600 mt-0.5"})}),(0,t.jsxs)("div",{className:"ml-3",children:[(0,t.jsx)("h4",{className:"text-sm font-medium text-blue-800",children:"✨ Funci\xf3n de Edici\xf3n con IA"}),(0,t.jsx)("p",{className:"text-sm text-blue-700 mt-1",children:'Puedes usar la IA para condensar y refinar tus res\xfamenes. La edici\xf3n mantiene toda la informaci\xf3n esencial mientras elimina redundancias, creando un texto m\xe1s conciso y estructurado (3.200-3.800 palabras). El resumen original se conserva siempre. Para acceder a ambos, haz clic en "Ver" en el resumen.'})]})]})}),0===a.length?(0,t.jsxs)("div",{className:"text-center py-8 bg-gray-50 rounded-lg border border-gray-200",children:[(0,t.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-12 w-12 text-gray-400 mx-auto mb-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),(0,t.jsx)("p",{className:"text-gray-600 mb-2",children:"No tienes res\xfamenes creados"}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"Selecciona un documento y genera tu primer resumen para empezar a estudiar de manera m\xe1s eficiente."})]}):(0,t.jsx)("div",{className:"grid gap-4",children:a.map(e=>(0,t.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow",children:(0,t.jsxs)("div",{className:"flex justify-between items-start",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,t.jsx)("h4",{className:"font-medium text-gray-900",children:e.titulo}),e.editado&&(0,t.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800",children:"✨ Editado"})]}),(0,t.jsxs)("p",{className:"text-sm text-gray-600 mb-2",children:["Creado: ",k(e.creado_en),e.editado&&e.fecha_edicion&&(0,t.jsxs)("span",{className:"ml-2 text-green-600",children:["• Editado: ",k(e.fecha_edicion)]})]}),e.instrucciones&&(0,t.jsxs)("p",{className:"text-xs text-gray-500 mb-2 italic",children:['"',e.instrucciones.substring(0,100),e.instrucciones.length>100?"...":"",'"']}),(0,t.jsxs)("p",{className:"text-xs text-gray-400",children:["Contenido original: ~",e.contenido.split(/\s+/).length," palabras",e.contenido_editado&&(0,t.jsxs)("span",{className:"ml-2 text-green-600",children:["• Versi\xf3n editada: ~",e.contenido_editado.split(/\s+/).length," palabras"]})]})]}),(0,t.jsxs)("div",{className:"flex flex-wrap gap-2 ml-4",children:[(0,t.jsxs)("button",{onClick:()=>v(e),className:"flex items-center gap-1 bg-blue-600 hover:bg-blue-700 text-white text-xs px-3 py-1 rounded focus:outline-none focus:shadow-outline transition-colors",title:"Ver resumen completo",children:[(0,t.jsx)(i.Vap,{className:"w-3 h-3"}),"Ver"]}),(0,t.jsxs)("button",{onClick:()=>w(e),disabled:h===e.id,className:"flex items-center gap-1 bg-green-600 hover:bg-green-700 disabled:bg-green-400 text-white text-xs px-3 py-1 rounded focus:outline-none focus:shadow-outline transition-colors",title:"Editar resumen con IA",children:[(0,t.jsx)(i.Pj4,{className:"w-3 h-3"}),h===e.id?"Editando...":"Editar"]}),(0,t.jsxs)("button",{onClick:()=>E(e),className:"flex items-center gap-1 bg-gray-600 hover:bg-gray-700 text-white text-xs px-3 py-1 rounded focus:outline-none focus:shadow-outline transition-colors",title:"Imprimir resumen",children:[(0,t.jsx)(i.Mvz,{className:"w-3 h-3"}),"Imprimir"]}),(0,t.jsxs)("button",{onClick:()=>N(e.id,e.titulo),className:"flex items-center gap-1 bg-red-600 hover:bg-red-700 text-white text-xs px-3 py-1 rounded focus:outline-none focus:shadow-outline transition-colors",title:"Eliminar resumen",children:[(0,t.jsx)(i.IXo,{className:"w-3 h-3"}),"Eliminar"]})]})]})},e.id))}),u&&c&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-40",onClick:()=>x(!1)}),(0,t.jsxs)("div",{className:"fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-11/12 max-w-4xl max-h-5/6 bg-white rounded-lg shadow-xl z-50 overflow-hidden",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center p-4 border-b border-gray-200",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:c.titulo}),(0,t.jsx)("button",{onClick:()=>x(!1),className:"text-gray-400 hover:text-gray-600",children:(0,t.jsx)("svg",{className:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),c.editado&&c.contenido_editado&&(0,t.jsxs)("div",{className:"p-4 border-b border-gray-200 flex space-x-2",children:[(0,t.jsx)("button",{onClick:()=>j("editada"),className:"px-3 py-1 rounded text-xs font-medium transition-colors\n                    ".concat("editada"===f?"bg-green-600 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"),title:"Versi\xf3n concisa refinada por IA",children:"✨ Versi\xf3n Editada"}),(0,t.jsx)("button",{onClick:()=>j("original"),className:"px-3 py-1 rounded text-xs font-medium transition-colors\n                    ".concat("original"===f?"bg-blue-600 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"),title:"Primer borrador generado por IA",children:"\uD83D\uDCDC Versi\xf3n Original"})]}),(0,t.jsxs)("div",{className:"p-4 overflow-y-auto max-h-96",children:[(0,t.jsxs)("div",{className:"mb-4 text-sm text-gray-600",children:[(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Creado:"})," ",k(c.creado_en)]}),c.editado&&c.fecha_edicion&&(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Editado:"})," ",k(c.fecha_edicion)]}),c.instrucciones&&(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Instrucciones:"})," ",c.instrucciones]}),c.editado&&c.contenido_editado&&(0,t.jsx)("div",{className:"mt-2 p-2 bg-gray-100 border border-gray-200 rounded text-xs",children:"editada"===f?(0,t.jsx)("p",{className:"text-green-800 font-medium",children:"✨ Mostrando versi\xf3n editada por IA (condensada y refinada)."}):(0,t.jsx)("p",{className:"text-blue-800 font-medium",children:"\uD83D\uDCDC Mostrando versi\xf3n original."})})]}),(0,t.jsx)("div",{className:"prose prose-sm max-w-none",dangerouslySetInnerHTML:{__html:("editada"===f&&c.contenido_editado?c.contenido_editado:c.contenido).replace(/\*\*(.*?)\*\*/g,"<strong>$1</strong>").replace(/\*(.*?)\*/g,"<em>$1</em>").replace(/^### (.*$)/gim,"<h3>$1</h3>").replace(/^## (.*$)/gim,"<h2>$1</h2>").replace(/^# (.*$)/gim,"<h1>$1</h1>").replace(/^> (.*$)/gim,"<blockquote>$1</blockquote>").replace(/^\- (.*$)/gim,"<li>$1</li>").replace(/\n/g,"<br />")}})]}),(0,t.jsxs)("div",{className:"p-4 border-t border-gray-200 flex justify-between items-center",children:[(0,t.jsx)("div",{className:"flex gap-2",children:(0,t.jsxs)("button",{onClick:()=>E(c),className:"flex items-center gap-2 bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded focus:outline-none focus:shadow-outline transition-colors",children:[(0,t.jsx)(i.Mvz,{className:"w-4 h-4"}),"Imprimir Versi\xf3n Actual"]})}),(0,t.jsx)("button",{onClick:()=>x(!1),className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded focus:outline-none focus:shadow-outline transition-colors",children:"Cerrar"})]})]})]})]})}function H(e){let{onDocumentDeleted:s}=e,[a,l]=(0,r.useState)([]),[n,o]=(0,r.useState)(!0),[m,u]=(0,r.useState)(null),[x,h]=(0,r.useState)(null),g=async()=>{o(!0);try{let e=await (0,c.R1)();l(e)}catch(e){console.error("Error al cargar documentos:",e),d.Ay.error("Error al cargar documentos")}finally{o(!1)}};(0,r.useEffect)(()=>{g()},[]);let p=async e=>{let a;u(e);try{a=d.Ay.loading("Eliminando documento..."),await (0,c.Q3)(e)?(d.Ay.success("Documento eliminado exitosamente",{id:a}),l(s=>s.filter(s=>s.id!==e)),null==s||s()):d.Ay.error("Error al eliminar el documento",{id:a})}catch(e){console.error("Error al eliminar documento:",e),d.Ay.error("Error al eliminar el documento",{id:a})}finally{u(null),h(null)}},b=e=>new Date(e).toLocaleDateString("es-ES",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"});return n?(0,t.jsxs)("div",{className:"flex items-center justify-center p-8",children:[(0,t.jsx)(i.jTZ,{className:"animate-spin text-blue-500 mr-2"}),(0,t.jsx)("span",{children:"Cargando documentos..."})]}):0===a.length?(0,t.jsxs)("div",{className:"text-center p-8 text-gray-500",children:[(0,t.jsx)(i.jH2,{className:"mx-auto text-4xl mb-4"}),(0,t.jsx)("p",{children:"No hay documentos subidos a\xfan."}),(0,t.jsx)("p",{className:"text-sm",children:"Sube tu primer documento para comenzar."})]}):(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:["Gestionar Documentos (",a.length,")"]}),(0,t.jsx)("div",{className:"space-y-3",children:a.map(e=>(0,t.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow",children:(0,t.jsxs)("div",{className:"flex items-start justify-between",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center mb-2",children:[(0,t.jsx)(i.jH2,{className:"text-blue-500 mr-2 flex-shrink-0"}),(0,t.jsx)("h4",{className:"font-medium text-gray-900 truncate",children:e.titulo}),e.numero_tema&&(0,t.jsxs)("span",{className:"ml-2 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full",children:["Tema ",e.numero_tema]}),e.categoria&&(0,t.jsx)("span",{className:"ml-2 px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full",children:e.categoria})]}),(0,t.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,t.jsxs)("p",{children:["Subido: ",b(e.creado_en)]}),(0,t.jsxs)("p",{children:["Caracteres: ",e.contenido.length.toLocaleString()]}),e.tipo_original&&(0,t.jsxs)("p",{children:["Tipo: ",e.tipo_original.toUpperCase()]})]})]}),(0,t.jsx)("button",{onClick:()=>h(e.id),disabled:m===e.id,className:"ml-4 p-2 text-red-500 hover:bg-red-50 rounded-lg transition-colors disabled:opacity-50",title:"Eliminar documento",children:m===e.id?(0,t.jsx)(i.jTZ,{className:"animate-spin"}):(0,t.jsx)(i.IXo,{})})]})},e.id))}),x&&(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-md w-full mx-4",children:[(0,t.jsxs)("div",{className:"flex items-center mb-4",children:[(0,t.jsx)(i.eHT,{className:"text-red-500 mr-3"}),(0,t.jsx)("h3",{className:"text-lg font-semibold",children:"Confirmar eliminaci\xf3n"})]}),(0,t.jsx)("p",{className:"text-gray-600 mb-6",children:"\xbfEst\xe1s seguro de que quieres eliminar este documento? Esta acci\xf3n no se puede deshacer."}),(0,t.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,t.jsx)("button",{onClick:()=>h(null),className:"px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors",children:"Cancelar"}),(0,t.jsx)("button",{onClick:()=>p(x),className:"px-4 py-2 bg-red-500 text-white hover:bg-red-600 rounded-lg transition-colors",children:"Eliminar"})]})]})})]})}var V=a(3796),U=a(4794);let B=e=>{let{colecciones:s,coleccionSeleccionada:a,onSeleccionarColeccion:r,onEliminarColeccion:l,isLoading:n,deletingId:o}=e;if(n)return(0,t.jsx)("div",{className:"flex justify-center items-center h-40",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"})});if(0===s.length)return(0,t.jsxs)("div",{className:"text-center p-8 border-2 border-dashed border-gray-300 rounded-lg",children:[(0,t.jsx)(i._Y7,{className:"mx-auto text-6xl text-gray-400 mb-4"}),(0,t.jsx)("p",{className:"text-gray-500 text-lg",children:"No hay colecciones de flashcards disponibles."}),(0,t.jsx)("p",{className:"text-sm text-gray-400 mt-1",children:"Crea una nueva colecci\xf3n para empezar a estudiar."})]});let c=(e,s)=>{e.stopPropagation(),l(s)};return(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6",children:s.map(e=>(0,t.jsxs)("div",{className:"border rounded-lg p-4 cursor-pointer transition-colors flex flex-col justify-between ".concat((null==a?void 0:a.id)===e.id?"border-orange-500 bg-orange-50 shadow-lg":"border-gray-200 hover:bg-gray-50"),onClick:()=>r(e),children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-lg mb-2",children:e.titulo}),e.descripcion&&(0,t.jsx)("p",{className:"text-sm text-gray-600 mb-2 break-words",children:e.descripcion}),(0,t.jsxs)("div",{className:"flex justify-between items-center mb-1",children:[(0,t.jsxs)("p",{className:"text-sm text-gray-500",children:["Flashcards: ","number"==typeof e.numero_flashcards?e.numero_flashcards:"N/A"]}),"number"==typeof e.pendientes_hoy&&e.pendientes_hoy>0&&(0,t.jsxs)("span",{className:"text-xs px-2 py-1 bg-orange-100 text-orange-800 rounded-full",children:[e.pendientes_hoy," para hoy"]})]}),(0,t.jsxs)("p",{className:"text-xs text-gray-400",children:["Creada: ",new Date(e.creado_en).toLocaleDateString("es-ES")]})]}),(0,t.jsxs)("div",{className:"flex justify-between items-center mt-4",children:[(0,t.jsx)("button",{onClick:s=>{s.stopPropagation(),r(e)},className:"bg-orange-500 hover:bg-orange-600 text-white font-semibold py-2 px-4 rounded text-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-opacity-50",children:"Estudiar"}),(0,t.jsxs)("button",{onClick:s=>c(s,e.id),disabled:o===e.id,className:"bg-red-500 hover:bg-red-600 text-white font-semibold py-2 px-4 rounded text-sm flex items-center focus:outline-none focus:ring-2 focus:ring-red-400 focus:ring-opacity-50 disabled:opacity-50",title:"Eliminar colecci\xf3n",children:[o===e.id?(0,t.jsx)(i.jTZ,{size:14,className:"animate-spin mr-2"}):(0,t.jsx)(i.IXo,{size:14,className:"mr-2"}),"Eliminar"]})]})]},e.id))})},$=e=>{let{coleccion:s,flashcards:a,estadisticas:r,isLoading:l,onStartStudy:n,onShowStudyOptions:o,onShowStatistics:c,onEditFlashcard:d,onDeleteFlashcard:m,deletingFlashcardId:u}=e;return(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-xl font-semibold mb-4",children:s.titulo}),r&&(0,t.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg mb-6",children:[(0,t.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Estad\xedsticas"}),(0,t.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-2 text-sm",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"font-semibold text-blue-600",children:r.total}),(0,t.jsx)("div",{className:"text-gray-500",children:"Total"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"font-semibold text-orange-600",children:r.paraHoy}),(0,t.jsx)("div",{className:"text-gray-500",children:"Para hoy"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"font-semibold text-gray-600",children:r.nuevas}),(0,t.jsx)("div",{className:"text-gray-500",children:"Nuevas"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"font-semibold text-yellow-600",children:r.aprendiendo}),(0,t.jsx)("div",{className:"text-gray-500",children:"Aprendiendo"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"font-semibold text-green-600",children:r.aprendidas}),(0,t.jsx)("div",{className:"text-gray-500",children:"Aprendidas"})]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 mb-6",children:[(0,t.jsx)("button",{onClick:n,className:"bg-orange-500 hover:bg-orange-600 text-white font-bold py-3 px-4 rounded-lg focus:outline-none focus:shadow-outline transition-colors",children:"Estudiar (".concat(r?r.paraHoy:0," para hoy)")}),(0,t.jsx)("button",{onClick:o,className:"bg-purple-500 hover:bg-purple-600 text-white font-bold py-3 px-4 rounded-lg focus:outline-none focus:shadow-outline transition-colors",children:"Opciones de estudio"}),(0,t.jsx)("button",{onClick:c,className:"bg-blue-500 hover:bg-blue-600 text-white font-bold py-3 px-4 rounded-lg focus:outline-none focus:shadow-outline transition-colors",children:"Ver estad\xedsticas"})]}),l?(0,t.jsx)("div",{className:"flex justify-center items-center h-40",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"})}):0===a.length?(0,t.jsx)("div",{className:"text-center p-4",children:(0,t.jsx)("p",{className:"text-gray-500",children:"No hay flashcards en esta colecci\xf3n."})}):(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:a.map((e,s)=>(0,t.jsxs)("div",{className:"p-3 border rounded-lg ".concat(e.debeEstudiar?"border-orange-300 bg-orange-50":"border-gray-200"),children:[(0,t.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,t.jsx)("p",{className:"font-medium flex-1 pr-2",children:e.pregunta}),(0,t.jsxs)("div",{className:"flex items-center space-x-1 flex-shrink-0",children:[d&&(0,t.jsx)("button",{onClick:()=>d(e),className:"p-1 text-blue-600 hover:text-blue-800 hover:bg-blue-100 rounded",title:"Editar flashcard",children:(0,t.jsx)(i.WXf,{size:14})}),m&&(0,t.jsx)("button",{onClick:()=>m(e.id),disabled:u===e.id,className:"p-1 text-red-600 hover:text-red-800 hover:bg-red-100 rounded disabled:opacity-50",title:"Eliminar flashcard",children:u===e.id?(0,t.jsx)("div",{className:"animate-spin rounded-full h-3.5 w-3.5 border-b-2 border-red-600"}):(0,t.jsx)(i.IXo,{size:14})})]})]}),(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("p",{className:"text-xs text-gray-500",children:["Tarjeta ",s+1]}),e.progreso&&(0,t.jsx)("span",{className:"text-xs px-2 py-1 rounded-full ".concat("nuevo"===e.progreso.estado?"bg-blue-100 text-blue-800":"aprendiendo"===e.progreso.estado?"bg-yellow-100 text-yellow-800":"repasando"===e.progreso.estado?"bg-orange-100 text-orange-800":"bg-green-100 text-green-800"),children:e.progreso.estado})]})]},e.id))})]})},Y=e=>{let{isOpen:s,onClose:a,onSelectStudyType:r,estadisticas:l,isLoading:n=!1}=e,o=[{tipo:"dificiles",label:"M\xe1s dif\xedciles",descripcion:"Tarjetas que has marcado como dif\xedciles m\xe1s frecuentemente",icon:(0,t.jsx)(i.lrG,{className:"text-red-600"}),color:"red-600",bgColor:"bg-red-100",hoverBgColor:"hover:bg-red-200"},{tipo:"aleatorias",label:"Aleatorias",descripcion:"Selecci\xf3n aleatoria de tarjetas de la colecci\xf3n",icon:(0,t.jsx)(i.jTZ,{className:"text-purple-600"}),color:"purple-600",bgColor:"bg-purple-100",hoverBgColor:"hover:bg-purple-200"},{tipo:"no-recientes",label:"No estudiadas recientemente",descripcion:"Tarjetas que no has revisado en mucho tiempo",icon:(0,t.jsx)(i.Ohp,{className:"text-orange-600"}),color:"orange-600",bgColor:"bg-orange-100",hoverBgColor:"hover:bg-orange-200"},{tipo:"nuevas",label:"Nuevas",descripcion:"Tarjetas que nunca has estudiado",icon:(0,t.jsx)(i.D1A,{className:"text-green-600"}),color:"green-600",bgColor:"bg-green-100",hoverBgColor:"hover:bg-green-200"},{tipo:"aprendiendo",label:"En aprendizaje",descripcion:"Tarjetas que est\xe1s aprendiendo actualmente",icon:(0,t.jsx)(i.TwU,{className:"text-yellow-600"}),color:"yellow-600",bgColor:"bg-yellow-100",hoverBgColor:"hover:bg-yellow-200"}];return s?(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold",children:"Opciones de Estudio"}),(0,t.jsx)("button",{onClick:a,className:"text-gray-500 hover:text-gray-700",children:(0,t.jsx)(i.yGN,{size:24})})]}),(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("p",{className:"text-gray-600 mb-3",children:"Elige el tipo de estudio que prefieras. Cada opci\xf3n te permitir\xe1 enfocar tu aprendizaje de manera diferente:"}),(0,t.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-3",children:(0,t.jsxs)("p",{className:"text-sm text-blue-800 font-medium",children:["ℹ️ Importante: Estos estudios adicionales son complementarios y ",(0,t.jsx)("strong",{children:"no afectan al algoritmo de repetici\xf3n espaciada"}),'. Para el estudio oficial que cuenta para tu progreso, usa el bot\xf3n "Estudiar" principal.']})})]}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:o.map(e=>(0,t.jsx)("button",{onClick:()=>r(e.tipo),className:"p-4 border rounded-lg text-left transition-all duration-200 ".concat(e.hoverBgColor," ").concat(e.bgColor," border-gray-200 hover:border-gray-300"),disabled:n,children:(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)("div",{className:"flex-shrink-0 mt-1",children:e.icon}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("h4",{className:"font-medium text-".concat(e.color," mb-1"),children:e.label}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:e.descripcion})]})]})},e.tipo))}),(0,t.jsx)("div",{className:"mt-6 flex justify-end space-x-3",children:(0,t.jsx)("button",{onClick:a,className:"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",children:"Cancelar"})})]})}):null};var W=a(1013),K=a(1934);let X=e=>{var s,a;let{flashcards:l,activeIndex:n,respondiendo:o,onRespuesta:c,onNavigate:d,onVolver:m,onReiniciarProgreso:u,onVerHistorial:x}=e,h=l[n],[g,p]=(0,r.useState)(!1);(0,r.useEffect)(()=>{p(!1)},[n]);let b=e=>{!(e&&e.target.closest("button"))&&(o||p(e=>!e))},f=(e,s)=>{e.stopPropagation(),o||c(s)},j=(e,s)=>{e.stopPropagation(),o||s()};return h?(0,t.jsxs)("div",{className:"flex flex-col items-center w-full",children:[(0,t.jsxs)("div",{className:"w-full flex justify-between items-center mb-4 px-2",children:[(0,t.jsxs)("button",{onClick:m,className:"flex items-center text-sm text-gray-600 hover:text-gray-900 p-2 rounded-md hover:bg-gray-100 transition-colors",disabled:o,children:[(0,t.jsx)(W.NEn,{className:"mr-1"})," Volver"]}),(0,t.jsxs)("div",{className:"text-sm text-gray-500",children:[n+1," / ",l.length]})]}),(0,t.jsx)("div",{className:"w-full max-w-2xl mx-auto",children:(0,t.jsx)("div",{className:"relative w-full h-[24rem] sm:h-[28rem] perspective-1000",onClick:()=>b(),children:(0,t.jsxs)(K.P.div,{className:"absolute w-full h-full transform-style-3d",animate:{rotateY:180*!!g},transition:{duration:.6},children:[(0,t.jsxs)("div",{className:"absolute w-full h-full backface-hidden bg-white rounded-xl shadow-xl border border-gray-200 p-6 flex flex-col justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"text-xs text-gray-400 mb-2",children:[(null==(s=h.progreso)?void 0:s.estado)&&(0,t.jsx)("span",{className:"px-2 py-0.5 rounded-full font-medium ".concat("nuevo"===h.progreso.estado?"bg-blue-100 text-blue-700":"aprendiendo"===h.progreso.estado?"bg-yellow-100 text-yellow-700":"repasando"===h.progreso.estado?"bg-orange-100 text-orange-700":"bg-green-100 text-green-700"),children:h.progreso.estado.charAt(0).toUpperCase()+h.progreso.estado.slice(1)}),!(null==(a=h.progreso)?void 0:a.estado)&&(0,t.jsx)("span",{className:"bg-blue-100 text-blue-700 px-2 py-0.5 rounded-full font-medium text-xs",children:"Nuevo"})]}),(0,t.jsx)("div",{className:"flex-grow flex items-center justify-center min-h-[150px] sm:min-h-[200px]",children:(0,t.jsx)("h3",{className:"text-lg sm:text-xl font-semibold text-center text-gray-800 break-words",children:h.pregunta})})]}),(0,t.jsx)("div",{className:"text-center mt-4",children:(0,t.jsx)("button",{onClick:e=>{e.stopPropagation(),b()},className:"bg-blue-500 hover:bg-blue-600 text-white font-semibold py-2 px-6 rounded-lg text-sm transition-colors",disabled:o,children:"Mostrar respuesta"})})]}),(0,t.jsxs)("div",{className:"absolute w-full h-full backface-hidden bg-white rounded-xl shadow-xl border border-gray-200 p-6 flex flex-col justify-between rotate-y-180",children:[(0,t.jsx)("div",{className:"flex-grow flex items-center justify-center min-h-[150px] sm:min-h-[200px] overflow-y-auto",children:(0,t.jsx)("p",{className:"text-base sm:text-lg text-center text-gray-700 whitespace-pre-wrap break-words transform-none",children:h.respuesta})}),(0,t.jsxs)("div",{className:"mt-4 space-y-3",children:[(0,t.jsx)("p",{className:"text-center text-sm font-medium text-gray-600",children:"\xbfQu\xe9 tal te ha resultado?"}),(0,t.jsxs)("div",{className:"flex justify-around space-x-2 sm:space-x-3",children:[(0,t.jsxs)("button",{onClick:e=>f(e,"dificil"),disabled:o,className:"flex-1 flex flex-col items-center p-3 rounded-lg bg-red-50 hover:bg-red-100 text-red-600 transition-colors disabled:opacity-50",children:[(0,t.jsx)(i.rxb,{className:"mb-1 text-xl"})," ",(0,t.jsx)("span",{className:"text-xs font-medium",children:"Dif\xedcil"})]}),(0,t.jsxs)("button",{onClick:e=>f(e,"normal"),disabled:o,className:"flex-1 flex flex-col items-center p-3 rounded-lg bg-yellow-50 hover:bg-yellow-100 text-yellow-600 transition-colors disabled:opacity-50",children:[(0,t.jsx)(i.YrT,{className:"mb-1 text-xl"})," ",(0,t.jsx)("span",{className:"text-xs font-medium",children:"Normal"})]}),(0,t.jsxs)("button",{onClick:e=>f(e,"facil"),disabled:o,className:"flex-1 flex flex-col items-center p-3 rounded-lg bg-green-50 hover:bg-green-100 text-green-600 transition-colors disabled:opacity-50",children:[(0,t.jsx)(i.Ydy,{className:"mb-1 text-xl"})," ",(0,t.jsx)("span",{className:"text-xs font-medium",children:"F\xe1cil"})]})]}),(u||x)&&(0,t.jsxs)("div",{className:"flex justify-center space-x-4 pt-2 text-xs",children:[u&&(0,t.jsxs)("button",{onClick:e=>j(e,()=>u(h.id)),disabled:o,className:"text-gray-500 hover:text-gray-700 underline flex items-center",children:[(0,t.jsx)(i.VI6,{size:12,className:"mr-1"})," Reiniciar"]}),x&&(0,t.jsxs)("button",{onClick:e=>j(e,()=>x(h.id)),disabled:o,className:"text-blue-500 hover:text-blue-700 underline flex items-center",children:[(0,t.jsx)(i.lrG,{size:12,className:"mr-1"})," Ver Historial"]})]})]})]})]})})}),(0,t.jsxs)("div",{className:"w-full max-w-2xl mx-auto flex justify-between mt-6 px-2",children:[(0,t.jsxs)("button",{onClick:()=>d("prev"),disabled:0===n||o,className:"flex items-center text-sm p-2 rounded-md transition-colors ".concat(0===n||o?"text-gray-400 cursor-not-allowed":"text-gray-600 hover:text-gray-900 hover:bg-gray-100"),children:[(0,t.jsx)(W.NEn,{className:"mr-1"})," Anterior"]}),(0,t.jsxs)("button",{onClick:()=>d("next"),disabled:n===l.length-1||o,className:"flex items-center text-sm p-2 rounded-md transition-colors ".concat(n===l.length-1||o?"text-gray-400 cursor-not-allowed":"text-gray-600 hover:text-gray-900 hover:bg-gray-100"),children:["Siguiente ",(0,t.jsx)(W.Lqc,{className:"ml-1"})]})]})]}):(0,t.jsxs)("div",{className:"flex flex-col items-center justify-center h-96 text-gray-500",children:["Cargando tarjeta...",(0,t.jsxs)("button",{onClick:m,className:"mt-4 flex items-center text-blue-600 hover:text-blue-800",children:[(0,t.jsx)(W.NEn,{className:"mr-1"})," Volver"]})]})};function J(e){var s;let{flashcard:a,isOpen:l,onClose:n,onSave:o}=e,[m,u]=(0,r.useState)(""),[x,h]=(0,r.useState)(""),[g,p]=(0,r.useState)(!1);(0,r.useEffect)(()=>{a&&(u(a.pregunta),h(a.respuesta))},[a]);let b=async()=>{let e;if(!m.trim()||!x.trim())return void d.Ay.error("La pregunta y respuesta no pueden estar vac\xedas");p(!0);try{if(e=d.Ay.loading("Guardando cambios..."),await (0,c.xq)(a.id,m.trim(),x.trim())){d.Ay.success("Flashcard actualizada exitosamente",{id:e});let s={...a,pregunta:m.trim(),respuesta:x.trim()};o(s),n()}else d.Ay.error("Error al actualizar la flashcard",{id:e})}catch(s){console.error("Error al actualizar flashcard:",s),d.Ay.error("Error al actualizar la flashcard",{id:e})}finally{p(!1)}},f=()=>{u(a.pregunta),h(a.respuesta),n()};return l?(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Editar Flashcard"}),(0,t.jsx)("button",{onClick:f,className:"text-gray-400 hover:text-gray-600 transition-colors",disabled:g,children:(0,t.jsx)(i.yGN,{size:24})})]}),(0,t.jsxs)("div",{className:"p-6 space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Pregunta"}),(0,t.jsx)("textarea",{value:m,onChange:e=>u(e.target.value),className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none",rows:4,placeholder:"Escribe la pregunta aqu\xed...",disabled:g})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Respuesta"}),(0,t.jsx)("textarea",{value:x,onChange:e=>h(e.target.value),className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none",rows:6,placeholder:"Escribe la respuesta aqu\xed...",disabled:g})]}),(null==(s=a.progreso)?void 0:s.estado)&&(0,t.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,t.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"Estado actual"}),(0,t.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-600",children:[(0,t.jsx)("span",{className:"px-2 py-1 rounded-full text-xs ".concat("nuevo"===a.progreso.estado?"bg-blue-100 text-blue-800":"aprendiendo"===a.progreso.estado?"bg-yellow-100 text-yellow-800":"repasando"===a.progreso.estado?"bg-orange-100 text-orange-800":"bg-green-100 text-green-800"),children:a.progreso.estado}),(0,t.jsxs)("span",{children:["Repeticiones: ",a.progreso.repeticiones]}),(0,t.jsxs)("span",{children:["Intervalo: ",a.progreso.intervalo," d\xedas"]})]})]})]}),(0,t.jsxs)("div",{className:"flex justify-end space-x-3 p-6 border-t border-gray-200",children:[(0,t.jsx)("button",{onClick:f,className:"px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors",disabled:g,children:"Cancelar"}),(0,t.jsx)("button",{onClick:b,disabled:g||!m.trim()||!x.trim(),className:"px-4 py-2 bg-blue-500 text-white hover:bg-blue-600 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center",children:g?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(i.TwU,{className:"animate-spin mr-2"}),"Guardando..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(i.Bc_,{className:"mr-2"}),"Guardar cambios"]})})]})]})}):null}let Z=e=>{let{onClose:s}=e,[a,l]=(0,r.useState)(null),[n,o]=(0,r.useState)(!0),[d,m]=(0,r.useState)("");(0,r.useEffect)(()=>{u()},[]);let u=async()=>{try{o(!0);let e=await (0,c.oE)();if(0===e.length)return void l({totalColecciones:0,totalFlashcards:0,totalNuevas:0,totalAprendiendo:0,totalRepasando:0,totalAprendidas:0,totalParaHoy:0,coleccionesConMasActividad:[]});let s=await Promise.all(e.map(async e=>{let s=await (0,c.yV)(e.id);return{coleccion:e,estadisticas:s}})),a={totalColecciones:e.length,totalFlashcards:s.reduce((e,s)=>e+s.estadisticas.total,0),totalNuevas:s.reduce((e,s)=>e+s.estadisticas.nuevas,0),totalAprendiendo:s.reduce((e,s)=>e+s.estadisticas.aprendiendo,0),totalRepasando:s.reduce((e,s)=>e+s.estadisticas.repasando,0),totalAprendidas:s.reduce((e,s)=>e+s.estadisticas.aprendidas,0),totalParaHoy:s.reduce((e,s)=>e+s.estadisticas.paraHoy,0),coleccionesConMasActividad:s.map(e=>({id:e.coleccion.id,titulo:e.coleccion.titulo,totalRevisiones:e.estadisticas.total,paraHoy:e.estadisticas.paraHoy})).sort((e,s)=>s.paraHoy-e.paraHoy).slice(0,5)};l(a)}catch(e){console.error("Error al cargar estad\xedsticas generales:",e),m("No se pudieron cargar las estad\xedsticas generales")}finally{o(!1)}};return n?(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,t.jsx)("div",{className:"bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto",children:(0,t.jsx)("div",{className:"flex justify-center items-center h-40",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"})})})}):d?(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,t.jsx)("h2",{className:"text-xl font-bold",children:"Error"}),(0,t.jsx)("button",{onClick:s,className:"text-gray-500 hover:text-gray-700",children:(0,t.jsx)(i.yGN,{size:24})})]}),(0,t.jsx)("div",{className:"text-red-500",children:d})]})}):a?(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold",children:"Estad\xedsticas Generales de Flashcards"}),(0,t.jsx)("button",{onClick:s,className:"text-gray-500 hover:text-gray-700 transition-colors",children:(0,t.jsx)(i.yGN,{size:24})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6",children:[(0,t.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,t.jsxs)("div",{className:"flex items-center mb-2",children:[(0,t.jsx)(i.D1A,{className:"text-blue-600 mr-2 text-xl"}),(0,t.jsx)("h4",{className:"font-semibold",children:"Total Colecciones"})]}),(0,t.jsx)("p",{className:"text-3xl font-bold text-blue-700",children:a.totalColecciones})]}),(0,t.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:[(0,t.jsxs)("div",{className:"flex items-center mb-2",children:[(0,t.jsx)(i.x_j,{className:"text-green-600 mr-2 text-xl"}),(0,t.jsx)("h4",{className:"font-semibold",children:"Total Flashcards"})]}),(0,t.jsx)("p",{className:"text-3xl font-bold text-green-700",children:a.totalFlashcards})]}),(0,t.jsxs)("div",{className:"bg-orange-50 border border-orange-200 rounded-lg p-4",children:[(0,t.jsxs)("div",{className:"flex items-center mb-2",children:[(0,t.jsx)(i.Ohp,{className:"text-orange-600 mr-2 text-xl"}),(0,t.jsx)("h4",{className:"font-semibold",children:"Para Estudiar Hoy"})]}),(0,t.jsx)("p",{className:"text-3xl font-bold text-orange-700",children:a.totalParaHoy})]}),(0,t.jsxs)("div",{className:"bg-purple-50 border border-purple-200 rounded-lg p-4",children:[(0,t.jsxs)("div",{className:"flex items-center mb-2",children:[(0,t.jsx)(i.TPq,{className:"text-purple-600 mr-2 text-xl"}),(0,t.jsx)("h4",{className:"font-semibold",children:"Aprendidas"})]}),(0,t.jsx)("p",{className:"text-3xl font-bold text-purple-700",children:a.totalAprendidas})]})]}),(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("h4",{className:"text-lg font-semibold mb-3",children:"Distribuci\xf3n por Estado"}),(0,t.jsx)("div",{className:"bg-gray-50 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:a.totalNuevas}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Nuevas"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-yellow-600",children:a.totalAprendiendo}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Aprendiendo"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-orange-600",children:a.totalRepasando}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Repasando"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-green-600",children:a.totalAprendidas}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Aprendidas"})]})]})})]}),a.coleccionesConMasActividad.length>0&&(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("h4",{className:"text-lg font-semibold mb-3",children:"Colecciones con M\xe1s Actividad"}),(0,t.jsx)("div",{className:"space-y-3",children:a.coleccionesConMasActividad.map((e,s)=>(0,t.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-3",children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsxs)("div",{className:"text-lg font-bold text-blue-600 mr-3",children:["#",s+1]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium text-gray-900",children:e.titulo}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:[e.totalRevisiones," flashcards total"]})]})]}),(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsx)("div",{className:"text-lg font-bold text-orange-600",children:e.paraHoy}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"para hoy"})]})]})},e.id))})]}),(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("h4",{className:"text-lg font-semibold mb-3",children:"Resumen de Progreso"}),(0,t.jsx)("div",{className:"bg-gradient-to-r from-blue-50 to-green-50 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 text-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"text-2xl font-bold text-blue-600",children:[a.totalFlashcards>0?Math.round(a.totalAprendidas/a.totalFlashcards*100):0,"%"]}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Progreso General"})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"text-2xl font-bold text-green-600",children:[a.totalFlashcards>0?Math.round((a.totalAprendidas+a.totalRepasando)/a.totalFlashcards*100):0,"%"]}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"En Proceso"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-orange-600",children:a.totalParaHoy}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Pendientes Hoy"})]})]})})]}),(0,t.jsx)("div",{className:"flex justify-end",children:(0,t.jsx)("button",{onClick:s,className:"bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Cerrar"})})]})}):null};function Q(e){let{coleccionId:s,onClose:a}=e,[l,n]=(0,r.useState)(null),[i,o]=(0,r.useState)(!0),[d,m]=(0,r.useState)(""),[u,x]=(0,r.useState)("general");(0,r.useEffect)(()=>{(async()=>{o(!0);try{let e=await (0,c.wU)(s);n(e)}catch(e){console.error("Error al cargar estad\xedsticas:",e),m("No se pudieron cargar las estad\xedsticas detalladas")}finally{o(!1)}})()},[s]);let h=e=>new Date(e).toLocaleDateString("es-ES",{day:"2-digit",month:"2-digit",year:"numeric"}),g=(e,s)=>0===s?0:Math.round(e/s*100);return(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] flex flex-col",children:[(0,t.jsxs)("div",{className:"p-4 border-b flex justify-between items-center",children:[(0,t.jsx)("h2",{className:"text-xl font-bold",children:"Estad\xedsticas detalladas de estudio"}),(0,t.jsx)("button",{onClick:a,className:"text-gray-500 hover:text-gray-700",children:(0,t.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,t.jsx)("div",{className:"border-b",children:(0,t.jsxs)("div",{className:"flex",children:[(0,t.jsx)("button",{onClick:()=>x("general"),className:"px-4 py-2 font-medium ".concat("general"===u?"border-b-2 border-orange-500 text-orange-600":"text-gray-600 hover:text-gray-800"),children:"General"}),(0,t.jsx)("button",{onClick:()=>x("progreso"),className:"px-4 py-2 font-medium ".concat("progreso"===u?"border-b-2 border-orange-500 text-orange-600":"text-gray-600 hover:text-gray-800"),children:"Progreso"}),(0,t.jsx)("button",{onClick:()=>x("dificiles"),className:"px-4 py-2 font-medium ".concat("dificiles"===u?"border-b-2 border-orange-500 text-orange-600":"text-gray-600 hover:text-gray-800"),children:"Tarjetas dif\xedciles"})]})}),(0,t.jsx)("div",{className:"p-4 overflow-y-auto flex-grow",children:i?(0,t.jsx)("div",{className:"flex justify-center items-center h-40",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"})}):d?(0,t.jsx)("div",{className:"text-red-500 text-center py-4",children:d}):l?(0,t.jsxs)(t.Fragment,{children:["general"===u&&(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Sesiones de estudio"}),(0,t.jsx)("p",{className:"text-3xl font-bold text-orange-600",children:l.totalSesiones})]}),(0,t.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Total de revisiones"}),(0,t.jsx)("p",{className:"text-3xl font-bold text-orange-600",children:l.totalRevisiones})]}),(0,t.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Promedio por sesi\xf3n"}),(0,t.jsx)("p",{className:"text-3xl font-bold text-orange-600",children:l.totalSesiones>0?Math.round(l.totalRevisiones/l.totalSesiones):0})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-3",children:"Distribuci\xf3n de respuestas"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex justify-between mb-1",children:[(0,t.jsx)("span",{className:"text-sm font-medium",children:"Dif\xedcil"}),(0,t.jsxs)("span",{className:"text-sm font-medium",children:[l.distribucionDificultad.dificil," (",g(l.distribucionDificultad.dificil,l.totalRevisiones),"%)"]})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2.5",children:(0,t.jsx)("div",{className:"bg-red-500 h-2.5 rounded-full",style:{width:"".concat(g(l.distribucionDificultad.dificil,l.totalRevisiones),"%")}})})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex justify-between mb-1",children:[(0,t.jsx)("span",{className:"text-sm font-medium",children:"Normal"}),(0,t.jsxs)("span",{className:"text-sm font-medium",children:[l.distribucionDificultad.normal," (",g(l.distribucionDificultad.normal,l.totalRevisiones),"%)"]})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2.5",children:(0,t.jsx)("div",{className:"bg-yellow-500 h-2.5 rounded-full",style:{width:"".concat(g(l.distribucionDificultad.normal,l.totalRevisiones),"%")}})})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex justify-between mb-1",children:[(0,t.jsx)("span",{className:"text-sm font-medium",children:"F\xe1cil"}),(0,t.jsxs)("span",{className:"text-sm font-medium",children:[l.distribucionDificultad.facil," (",g(l.distribucionDificultad.facil,l.totalRevisiones),"%)"]})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2.5",children:(0,t.jsx)("div",{className:"bg-green-500 h-2.5 rounded-full",style:{width:"".concat(g(l.distribucionDificultad.facil,l.totalRevisiones),"%")}})})]})]})]})]}),"progreso"===u&&(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-3",children:"Progreso a lo largo del tiempo"}),0===l.progresoTiempo.length?(0,t.jsx)("div",{className:"text-gray-500 text-center py-4",children:"No hay datos de progreso disponibles"}):(0,t.jsx)("div",{className:"overflow-x-auto",children:(0,t.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,t.jsx)("thead",{className:"bg-gray-50",children:(0,t.jsxs)("tr",{children:[(0,t.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Fecha"}),(0,t.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Nuevas"}),(0,t.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Aprendiendo"}),(0,t.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Repasando"}),(0,t.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Aprendidas"})]})}),(0,t.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:l.progresoTiempo.map((e,s)=>(0,t.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:h(e.fecha)}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(0,t.jsx)("span",{className:"px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800",children:e.nuevas})}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(0,t.jsx)("span",{className:"px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800",children:e.aprendiendo})}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(0,t.jsx)("span",{className:"px-2 py-1 text-xs font-semibold rounded-full bg-orange-100 text-orange-800",children:e.repasando})}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(0,t.jsx)("span",{className:"px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800",children:e.aprendidas})})]},s))})]})})]}),"dificiles"===u&&(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-3",children:"Tarjetas m\xe1s dif\xedciles"}),0===l.tarjetasMasDificiles.length?(0,t.jsx)("div",{className:"text-gray-500 text-center py-4",children:"No hay datos suficientes para determinar las tarjetas m\xe1s dif\xedciles"}):(0,t.jsx)("div",{className:"space-y-4",children:l.tarjetasMasDificiles.map(e=>(0,t.jsxs)("div",{className:"border rounded-lg p-4 hover:bg-gray-50",children:[(0,t.jsx)("p",{className:"font-medium mb-2",children:e.pregunta}),(0,t.jsxs)("div",{className:"flex space-x-4 text-sm",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("span",{className:"inline-block w-3 h-3 rounded-full bg-red-500 mr-1"}),(0,t.jsxs)("span",{children:["Dif\xedcil: ",e.dificil]})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("span",{className:"inline-block w-3 h-3 rounded-full bg-yellow-500 mr-1"}),(0,t.jsxs)("span",{children:["Normal: ",e.normal]})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("span",{className:"inline-block w-3 h-3 rounded-full bg-green-500 mr-1"}),(0,t.jsxs)("span",{children:["F\xe1cil: ",e.facil]})]})]}),(0,t.jsx)("div",{className:"mt-2",children:(0,t.jsxs)("div",{className:"w-full bg-gray-200 rounded-full h-1.5 flex",children:[(0,t.jsx)("div",{className:"bg-red-500 h-1.5 rounded-l-full",style:{width:"".concat(g(e.dificil,e.totalRevisiones),"%")}}),(0,t.jsx)("div",{className:"bg-yellow-500 h-1.5",style:{width:"".concat(g(e.normal,e.totalRevisiones),"%")}}),(0,t.jsx)("div",{className:"bg-green-500 h-1.5 rounded-r-full",style:{width:"".concat(g(e.facil,e.totalRevisiones),"%")}})]})})]},e.id))})]})]}):(0,t.jsx)("div",{className:"text-gray-500 text-center py-4",children:"No hay datos estad\xedsticos disponibles"})})]})})}function ee(){let[e,s]=(0,r.useState)([]),[a,l]=(0,r.useState)(null),[n,o]=(0,r.useState)([]),[c,d]=(0,r.useState)(!0),[m,u]=(0,r.useState)(!0),[x,h]=(0,r.useState)(""),[g,p]=(0,r.useState)(0),[b,f]=(0,r.useState)(null),[j,y]=(0,r.useState)(!1),[N,v]=(0,r.useState)(null),[w,E]=(0,r.useState)(!1),[k,C]=(0,r.useState)(!1),[S,T]=(0,r.useState)(!1),[A,R]=(0,r.useState)(!1),[_,z]=(0,r.useState)(!1),[D,P]=(0,r.useState)(null),[I,F]=(0,r.useState)(null);(0,r.useEffect)(()=>{(async()=>{u(!0);try{let e=await (0,V.oE)();s(e)}catch(e){console.error("Error al cargar colecciones:",e),h("No se pudieron cargar las colecciones de flashcards")}finally{u(!1)}})()},[]);let L=async e=>{d(!0),h(""),l(e),p(0),E(!1);try{let s=[...await (0,V.Og)(e.id)].sort((e,s)=>e.debeEstudiar&&!s.debeEstudiar?-1:!e.debeEstudiar&&s.debeEstudiar?1:0);o(s);let a=await (0,U.yV)(e.id);f(a)}catch(e){console.error("Error al cargar flashcards:",e),h("No se pudieron cargar las flashcards de esta colecci\xf3n")}finally{d(!1)}},M=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"programadas";d(!0);try{if(a){let s=[];switch(e){case"programadas":default:s=(await (0,V.Og)(a.id)).filter(e=>e.debeEstudiar);break;case"dificiles":s=await (0,V.kO)(a.id,20);break;case"aleatorias":s=await (0,V._p)(a.id,20);break;case"no-recientes":s=await (0,V._W)(a.id,20);break;case"nuevas":s=await (0,V.Iv)(a.id,"nuevo",20);break;case"aprendiendo":s=await (0,V.Iv)(a.id,"aprendiendo",20);break;case"repasando":s=await (0,V.Iv)(a.id,"repasando",20);break;case"aprendidas":s=await (0,V.Iv)(a.id,"aprendido",20)}let t=await (0,U.yV)(a.id);if(f(t),0===s.length)if("programadas"===e)return void alert('No hay flashcards programadas para estudiar hoy. Puedes usar "Opciones de estudio" para elegir otro tipo de repaso.');else return void alert("No hay flashcards disponibles para el tipo de estudio seleccionado.");o(s),E(!0),p(0),C(!1)}}catch(e){console.error("Error al iniciar modo estudio:",e)}finally{d(!1)}},G=async()=>{if(E(!1),a)try{let e=[...await (0,V.Og)(a.id)].sort((e,s)=>e.debeEstudiar&&!s.debeEstudiar?-1:!e.debeEstudiar&&s.debeEstudiar?1:0);o(e);let s=await (0,U.yV)(a.id);f(s)}catch(e){console.error("Error al recargar datos:",e)}},O=async e=>{if(n[g]){y(!0);try{if(!await (0,V.yf)(n[g].id,e))throw Error("Error al registrar la respuesta");g>=n.length-1?(alert("\xa1Has completado la sesi\xf3n de estudio!"),await G()):p(g+1)}catch(e){console.error("Error al actualizar progreso:",e),h("Error al guardar tu respuesta. Por favor, int\xe9ntalo de nuevo.")}finally{y(!1)}}},q=async e=>{if(confirm("\xbfEst\xe1s seguro de que quieres eliminar esta colecci\xf3n? Esta acci\xf3n no se puede deshacer.")){v(e);try{if(await (0,V.as)(e)){let t=await (0,V.oE)();s(t),(null==a?void 0:a.id)===e&&(l(null),o([]),f(null))}else h("No se pudo eliminar la colecci\xf3n")}catch(e){console.error("Error al eliminar colecci\xf3n:",e),h("Error al eliminar la colecci\xf3n")}finally{v(null)}}},H=async e=>{if(confirm("\xbfEst\xe1s seguro de que quieres eliminar esta flashcard? Esta acci\xf3n no se puede deshacer.")){F(e);try{if(await (0,V.QU)(e)){if(a){let e=[...await (0,V.Og)(a.id)].sort((e,s)=>e.debeEstudiar&&!s.debeEstudiar?-1:!e.debeEstudiar&&s.debeEstudiar?1:0);o(e);let s=await (0,U.yV)(a.id);f(s)}}else h("No se pudo eliminar la flashcard")}catch(e){console.error("Error al eliminar flashcard:",e),h("Error al eliminar la flashcard")}finally{F(null)}}},W=async e=>{o(s=>s.map(s=>s.id===e.id?e:s))};return(0,t.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[x&&(0,t.jsx)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4",children:x}),w?(0,t.jsx)(X,{flashcards:n,activeIndex:g,respondiendo:j,onRespuesta:O,onNavigate:e=>{"next"===e&&g<n.length-1?p(g+1):"prev"===e&&g>0&&p(g-1)},onVolver:G}):(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold",children:"Mis Flashcards"}),(0,t.jsxs)("button",{onClick:()=>R(!0),className:"bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline flex items-center",children:[(0,t.jsx)(i.vQY,{className:"mr-2"})," Estad\xedsticas Generales"]})]}),a?(0,t.jsxs)("div",{children:[(0,t.jsx)("button",{onClick:()=>l(null),className:"mb-4 text-blue-600 hover:text-blue-800 flex items-center",children:"← Volver a mis colecciones"}),(0,t.jsx)($,{coleccion:a,flashcards:n,estadisticas:b,isLoading:c,onStartStudy:()=>M("programadas"),onShowStudyOptions:()=>C(!0),onShowStatistics:()=>T(!0),onEditFlashcard:e=>{P(e),z(!0)},onDeleteFlashcard:H,deletingFlashcardId:I})]}):(0,t.jsx)(B,{colecciones:e,coleccionSeleccionada:a,onSeleccionarColeccion:L,onEliminarColeccion:q,isLoading:m,deletingId:N})]}),(0,t.jsx)(Y,{isOpen:k,onClose:()=>C(!1),onSelectStudyType:M,estadisticas:b,isLoading:c}),S&&a&&(0,t.jsx)(Q,{coleccionId:a.id,onClose:()=>T(!1)}),A&&(0,t.jsx)(Z,{onClose:()=>R(!1)}),D&&(0,t.jsx)(J,{flashcard:D,isOpen:_,onClose:()=>{z(!1),P(null)},onSave:W})]})}function es(e){var s;let{documentosSeleccionados:a}=e,[l,n]=(0,r.useState)(""),[o,m]=(0,r.useState)(""),[b,f]=(0,r.useState)([]),[j,N]=(0,r.useState)(!1),[v,C]=(0,r.useState)(0),[A,R]=(0,r.useState)(!1),[_,z]=(0,r.useState)([]),[D,P]=(0,r.useState)("nuevo"),[I,F]=(0,r.useState)(!1),[L,M]=(0,r.useState)(""),{generateTest:G,isGenerating:O,getActiveTask:q}=E(),{getTask:H}=(0,w.M)(),{executeWithGuard:V}=S(),{user:U}=(0,g.A)(),{plan:B,isLoading:$}=T();q("test");let Y=O("test");k({taskType:"test",onResult:e=>{f(e),d.oR.success("\xa1Test generado exitosamente!")},onError:e=>{d.oR.error("Error al generar test: ".concat(e))}});let{register:W,handleSubmit:K,formState:{errors:X}}=(0,u.mN)({resolver:(0,x.u)(h.GS),defaultValues:{peticion:"",cantidad:10}});(0,r.useEffect)(()=>{J()},[]);let J=async()=>{F(!0);try{let e=await (0,c.Lx)(),s=[];for(let a of e){let e=await (0,c.Kj)(a.id);s.push({...a,numPreguntas:e})}z(s)}catch(e){console.error("Error al cargar tests:",e),d.oR.error("No se pudieron cargar los tests existentes.")}finally{F(!1)}},Z=async e=>{let s=a.map(e=>e.contenido);f([]),N(!1);let t=await V("tests",async()=>(await G({peticion:e.peticion,contextos:s,cantidad:e.cantidad}),l||n("Test: ".concat(e.peticion.substring(0,50)).concat(e.peticion.length>50?"...":"")),!0),e.cantidad);t.success?d.oR.success("Generaci\xf3n iniciada en segundo plano. Puedes continuar usando la aplicaci\xf3n.",{duration:4e3}):d.oR.error(t.error||"Error al iniciar la generaci\xf3n del test")},Q=async()=>{if(0===b.length)return void M("No hay preguntas para guardar");if("nuevo"===D&&!l.trim())return void M("Por favor, proporciona un t\xedtulo para el nuevo test");if("nuevo"!==D&&""===D)return void M("Por favor, selecciona un test existente");M("");try{let e;if("nuevo"===D){if(!(e=await (0,c._4)(l,o,a.map(e=>e.id))))throw Error("No se pudo crear el test")}else e=D;let s=b.map(s=>({test_id:e,pregunta:s.pregunta,opcion_a:s.opciones.a,opcion_b:s.opciones.b,opcion_c:s.opciones.c,opcion_d:s.opciones.d,respuesta_correcta:s.respuesta_correcta}));if(!await (0,c.OA)(s))throw Error("No se pudieron guardar las preguntas");N(!0),"nuevo"===D&&await J(),setTimeout(()=>{f([]),N(!1),n(""),m(""),P("nuevo"),C(0),R(!1),d.oR.success("Test guardado correctamente. Puedes generar uno nuevo.")},3e3)}catch(e){console.error("Error al guardar las preguntas:",e),M("Ha ocurrido un error al guardar las preguntas. Por favor, int\xe9ntalo de nuevo.")}};return(0,t.jsxs)("div",{className:"mt-8 border-t pt-8",children:[(0,t.jsx)("h2",{className:"text-xl font-bold mb-4",children:"Generador de Tests"}),!$&&"free"===B&&(0,t.jsx)("div",{className:"mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg",children:(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)(i.F5$,{className:"w-5 h-5 text-blue-600 mt-0.5"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium text-blue-900",children:"L\xedmites del Plan Gratuito"}),(0,t.jsxs)("p",{className:"text-sm text-blue-700 mt-1",children:["M\xe1ximo ",p.qo.free.limits.testsForTrial," preguntas de test durante el per\xedodo de prueba. Para generar tests ilimitados,",(0,t.jsx)(y(),{href:"/upgrade-plan",className:"font-medium underline hover:text-blue-800 ml-1",children:"actualiza tu plan"}),"."]})]})]})}),(0,t.jsxs)("form",{onSubmit:K(Z),className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"peticion",className:"block text-gray-700 text-sm font-bold mb-2",children:"Describe el test que deseas generar:"}),(0,t.jsx)("textarea",{id:"peticion",className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",rows:3,...W("peticion"),placeholder:"Ej: Genera un test sobre los conceptos principales del tema 1",disabled:Y}),X.peticion&&(0,t.jsx)("span",{className:"text-red-500 text-sm",children:X.peticion.message}),(0,t.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"La IA generar\xe1 preguntas de test basadas en los documentos seleccionados y tu petici\xf3n."})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"cantidad",className:"block text-gray-700 text-sm font-bold mb-2",children:"N\xfamero de preguntas:"}),(0,t.jsx)("input",{id:"cantidad",type:"number",min:"1",max:"50",className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",...W("cantidad",{valueAsNumber:!0}),disabled:Y}),X.cantidad&&(0,t.jsx)("span",{className:"text-red-500 text-sm",children:X.cantidad.message}),(0,t.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"Especifica cu\xe1ntas preguntas quieres generar (entre 1 y 50)."})]}),L&&(0,t.jsx)("div",{className:"text-red-500 text-sm",children:L}),(0,t.jsx)("div",{children:(0,t.jsx)("button",{type:"submit",className:"bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",disabled:Y||0===a.length,children:Y?"Generando...":"Generar Test"})})]}),Y&&(0,t.jsxs)("div",{className:"mt-4 text-center",children:[(0,t.jsx)("p",{className:"text-gray-600",children:"Generando test, por favor espera..."}),(0,t.jsx)("div",{className:"mt-2 flex justify-center",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"})})]}),b.length>0&&(0,t.jsxs)("div",{className:"mt-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold",children:["Test generado (",b.length," preguntas)"]}),(0,t.jsx)("button",{onClick:()=>{f([]),N(!1),n(""),m(""),P("nuevo"),C(0),R(!1)},className:"bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline text-sm",title:"Limpiar test generado",children:"Limpiar"})]}),!j&&(0,t.jsxs)("div",{className:"bg-gray-100 p-4 rounded-lg mb-6",children:[(0,t.jsx)("h4",{className:"font-medium mb-2",children:"Guardar preguntas de test"}),(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsx)("label",{htmlFor:"tipoTest",className:"block text-sm font-medium text-gray-700 mb-1",children:"\xbfD\xf3nde quieres guardar estas preguntas?"}),(0,t.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("input",{type:"radio",id:"nuevoTest",name:"tipoTest",value:"nuevo",checked:"nuevo"===D,onChange:()=>P("nuevo"),className:"mr-2",disabled:Y}),(0,t.jsx)("label",{htmlFor:"nuevoTest",className:"text-sm text-gray-700",children:"Crear nuevo test"})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("input",{type:"radio",id:"testExistente",name:"tipoTest",value:"existente",checked:"nuevo"!==D,onChange:()=>{_.length>0?P(_[0].id):P("")},className:"mr-2",disabled:Y||0===_.length}),(0,t.jsxs)("label",{htmlFor:"testExistente",className:"text-sm text-gray-700",children:["A\xf1adir a un test existente",0===_.length&&(0,t.jsx)("span",{className:"text-gray-500 ml-2",children:"(No hay tests disponibles)"})]})]})]})]}),"nuevo"===D&&(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"tituloTest",className:"block text-sm font-medium text-gray-700 mb-1",children:"T\xedtulo del nuevo test:"}),(0,t.jsx)("input",{type:"text",id:"tituloTest",className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",value:l,onChange:e=>n(e.target.value),disabled:Y})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"descripcionTest",className:"block text-sm font-medium text-gray-700 mb-1",children:"Descripci\xf3n (opcional):"}),(0,t.jsx)("textarea",{id:"descripcionTest",className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",rows:2,value:o,onChange:e=>m(e.target.value),disabled:Y})]})]}),"nuevo"!==D&&_.length>0&&(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"testExistenteSelect",className:"block text-sm font-medium text-gray-700 mb-1",children:"Selecciona un test:"}),(0,t.jsx)("select",{id:"testExistenteSelect",className:"shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",value:D,onChange:e=>P(e.target.value),disabled:Y,children:_.map(e=>(0,t.jsxs)("option",{value:e.id,children:[e.titulo," ",e.numPreguntas?"(".concat(e.numPreguntas," preguntas)"):""]},e.id))})]}),(0,t.jsx)("div",{className:"mt-4",children:(0,t.jsx)("button",{type:"button",onClick:Q,className:"bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",disabled:!1,children:"Guardar preguntas"})})]}),j&&(0,t.jsxs)("div",{className:"bg-green-100 text-green-800 p-4 rounded-lg mb-6",children:[(0,t.jsx)("p",{className:"font-medium",children:"nuevo"===D?"\xa1Nuevo test creado correctamente!":"\xa1Preguntas a\xf1adidas al test correctamente!"}),(0,t.jsxs)("p",{className:"text-sm mt-1",children:["Puedes acceder a ","nuevo"===D?"\xe9l":"las preguntas",' desde la secci\xf3n de "Mis Tests".']})]}),(0,t.jsxs)("div",{className:"bg-white border rounded-lg shadow-md p-6 mb-4",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,t.jsx)("button",{onClick:()=>{v>0&&(C(v-1),R(!1))},disabled:0===v,className:"p-2 rounded-full ".concat(0===v?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-200"),children:(0,t.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})})}),(0,t.jsxs)("span",{className:"text-gray-600",children:["Pregunta ",v+1," de ",b.length]}),(0,t.jsx)("button",{onClick:()=>{v<b.length-1&&(C(v+1),R(!1))},disabled:v===b.length-1,className:"p-2 rounded-full ".concat(v===b.length-1?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-200"),children:(0,t.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})})]}),(0,t.jsx)("div",{className:"min-h-[300px]",children:(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsx)("h4",{className:"font-semibold text-lg mb-4",children:null==(s=b[v])?void 0:s.pregunta}),(0,t.jsx)("div",{className:"space-y-3 mt-6",children:["a","b","c","d"].map(e=>{var s;return(0,t.jsx)("div",{className:"p-3 border rounded-lg ".concat(A&&b[v].respuesta_correcta===e?"bg-green-100 border-green-500":"hover:bg-gray-50"),children:(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)("div",{className:"flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center mr-2 ".concat(A&&b[v].respuesta_correcta===e?"bg-green-500 text-white":"bg-gray-200 text-gray-700"),children:e.toUpperCase()}),(0,t.jsx)("div",{className:"flex-grow",children:null==(s=b[v])?void 0:s.opciones[e]})]})},e)})})]})}),(0,t.jsx)("div",{className:"mt-4 text-center",children:(0,t.jsx)("button",{onClick:()=>{R(!A)},className:"bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:A?"Ocultar respuesta":"Mostrar respuesta"})})]}),(0,t.jsxs)("div",{className:"mt-6",children:[(0,t.jsx)("h4",{className:"font-medium mb-2",children:"Todas las preguntas:"}),(0,t.jsx)("div",{className:"space-y-2",children:b.map((e,s)=>(0,t.jsx)("div",{className:"p-3 border rounded-lg cursor-pointer hover:bg-gray-50 ".concat(s===v?"border-indigo-500 bg-indigo-50":""),onClick:()=>{C(s),R(!1)},children:(0,t.jsxs)("p",{className:"font-medium",children:[s+1,". ",e.pregunta]})},s))})]})]})]})}var ea=a(7616);let et=e=>{let{estadisticas:s,onClose:a}=e;return(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold",children:"Estad\xedsticas Generales de Tests"}),(0,t.jsx)("button",{onClick:a,className:"text-gray-500 hover:text-gray-700 transition-colors",children:(0,t.jsx)(i.yGN,{size:24})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6",children:[(0,t.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,t.jsxs)("div",{className:"flex items-center mb-2",children:[(0,t.jsx)(i.TPq,{className:"text-blue-600 mr-2 text-xl"}),(0,t.jsx)("h4",{className:"font-semibold",children:"Tests Realizados"})]}),(0,t.jsx)("p",{className:"text-3xl font-bold text-blue-700",children:s.totalTests})]}),(0,t.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:[(0,t.jsxs)("div",{className:"flex items-center mb-2",children:[(0,t.jsx)(i.YrT,{className:"text-green-600 mr-2 text-xl"}),(0,t.jsx)("h4",{className:"font-semibold",children:"Respuestas Correctas"})]}),(0,t.jsx)("p",{className:"text-3xl font-bold text-green-700",children:s.totalRespuestasCorrectas})]}),(0,t.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:[(0,t.jsxs)("div",{className:"flex items-center mb-2",children:[(0,t.jsx)(i.yGN,{className:"text-red-600 mr-2 text-xl"}),(0,t.jsx)("h4",{className:"font-semibold",children:"Respuestas Incorrectas"})]}),(0,t.jsx)("p",{className:"text-3xl font-bold text-red-700",children:s.totalRespuestasIncorrectas})]}),(0,t.jsxs)("div",{className:"bg-purple-50 border border-purple-200 rounded-lg p-4",children:[(0,t.jsxs)("div",{className:"flex items-center mb-2",children:[(0,t.jsx)(i.ARf,{className:"text-purple-600 mr-2 text-xl"}),(0,t.jsx)("h4",{className:"font-semibold",children:"Porcentaje de Acierto"})]}),(0,t.jsxs)("p",{className:"text-3xl font-bold text-purple-700",children:[s.porcentajeAcierto.toFixed(1),"%"]})]})]})]})})},er=e=>{let{estadisticas:s,testTitulo:a,onClose:r}=e;return(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,t.jsxs)("h2",{className:"text-2xl font-bold",children:["Estad\xedsticas del Test: ",a]}),(0,t.jsx)("button",{onClick:r,className:"text-gray-500 hover:text-gray-700 transition-colors",children:(0,t.jsx)(i.yGN,{size:24})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6",children:[(0,t.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,t.jsxs)("div",{className:"flex items-center mb-2",children:[(0,t.jsx)(i.Ohp,{className:"text-blue-600 mr-2 text-xl"}),(0,t.jsx)("h4",{className:"font-semibold",children:"Veces Realizado"})]}),(0,t.jsx)("p",{className:"text-3xl font-bold text-blue-700",children:s.fechasRealizacion.length})]}),(0,t.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:[(0,t.jsxs)("div",{className:"flex items-center mb-2",children:[(0,t.jsx)(i.YrT,{className:"text-green-600 mr-2 text-xl"}),(0,t.jsx)("h4",{className:"font-semibold",children:"Respuestas Correctas"})]}),(0,t.jsx)("p",{className:"text-3xl font-bold text-green-700",children:s.totalCorrectas})]}),(0,t.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:[(0,t.jsxs)("div",{className:"flex items-center mb-2",children:[(0,t.jsx)(i.yGN,{className:"text-red-600 mr-2 text-xl"}),(0,t.jsx)("h4",{className:"font-semibold",children:"Respuestas Incorrectas"})]}),(0,t.jsx)("p",{className:"text-3xl font-bold text-red-700",children:s.totalIncorrectas})]}),(0,t.jsxs)("div",{className:"bg-purple-50 border border-purple-200 rounded-lg p-4",children:[(0,t.jsxs)("div",{className:"flex items-center mb-2",children:[(0,t.jsx)(i.ARf,{className:"text-purple-600 mr-2 text-xl"}),(0,t.jsx)("h4",{className:"font-semibold",children:"Porcentaje de Acierto"})]}),(0,t.jsxs)("p",{className:"text-3xl font-bold text-purple-700",children:[s.porcentajeAcierto.toFixed(1),"%"]})]})]}),s.preguntasMasFalladas.length>0&&(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("h4",{className:"text-lg font-semibold mb-3",children:"Preguntas con M\xe1s Fallos"}),(0,t.jsx)("div",{className:"space-y-3",children:s.preguntasMasFalladas.map((e,s)=>(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3",children:(0,t.jsx)("div",{className:"flex justify-between items-start",children:(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsxs)("div",{className:"text-lg font-bold text-red-600 mr-3",children:["#",s+1]}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("p",{className:"font-medium text-gray-900",children:e.pregunta}),(0,t.jsxs)("div",{className:"flex items-center space-x-4 mt-2 text-sm",children:[(0,t.jsxs)("span",{className:"text-red-600",children:[(0,t.jsx)(i.yGN,{className:"inline mr-1"})," ",e.totalFallos," fallos"]}),(0,t.jsxs)("span",{className:"text-green-600",children:[(0,t.jsx)(i.YrT,{className:"inline mr-1"})," ",e.totalAciertos," aciertos"]})]})]})]})})},e.preguntaId))})]})]})})};function el(e){let{onIniciarRepaso:s,onCancelar:a}=e,[l,n]=(0,r.useState)([]),[o,c]=(0,r.useState)([]),[m,u]=(0,r.useState)(!1),[x,h]=(0,r.useState)(!1);(0,r.useEffect)(()=>{g()},[]);let g=async()=>{u(!0);try{let e=await (0,ea.Lx)(),s=[];for(let a of e){let e=await (0,ea.Kj)(a.id);e>0&&s.push({...a,numPreguntas:e})}n(s);let a=s.map(e=>({testId:e.id,cantidad:0,maxPreguntas:e.numPreguntas}));c(a)}catch(e){console.error("Error al cargar tests:",e),d.oR.error("No se pudieron cargar los tests.")}finally{u(!1)}},p=(e,s)=>{c(a=>a.map(a=>a.testId===e?{...a,cantidad:Math.max(0,Math.min(s,a.maxPreguntas))}:a))},b=async()=>{let e=o.filter(e=>e.cantidad>0);if(0===e.length)return void d.oR.error("Selecciona al menos una pregunta de alg\xfan test.");h(!0);try{let a=await (0,ea.HE)(e);if(0===a.length)return void d.oR.error("No se pudieron obtener preguntas para el repaso.");e.reduce((e,s)=>e+s.cantidad,0),d.oR.success("Test de repaso creado con ".concat(a.length," preguntas de ").concat(e.length," test(s)")),s(a,e)}catch(e){console.error("Error al generar test de repaso:",e),d.oR.error("Error al generar el test de repaso.")}finally{h(!1)}},f=o.reduce((e,s)=>e+s.cantidad,0),j=o.filter(e=>e.cantidad>0).length;return m?(0,t.jsxs)("div",{className:"flex justify-center items-center py-8",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"}),(0,t.jsx)("span",{className:"ml-2 text-gray-600",children:"Cargando tests..."})]}):0===l.length?(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)(i.NLe,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,t.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"No hay tests disponibles"}),(0,t.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Primero necesitas crear algunos tests con preguntas para poder hacer un repaso."}),(0,t.jsx)("button",{onClick:a,className:"mt-4 px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600",children:"Volver"})]}):(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-xl font-bold",children:"Configurar Test de Repaso"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Selecciona cu\xe1ntas preguntas quieres de cada test para crear tu repaso personalizado."})]}),(0,t.jsx)("button",{onClick:a,className:"p-2 text-gray-500 hover:text-gray-700",title:"Cancelar",children:(0,t.jsx)(i.yGN,{className:"h-6 w-6"})})]}),(0,t.jsxs)("div",{className:"flex justify-between items-center bg-gray-50 p-4 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)("button",{onClick:()=>{c(e=>e.map(e=>({...e,cantidad:e.maxPreguntas})))},className:"px-3 py-1 text-sm bg-indigo-100 text-indigo-700 rounded hover:bg-indigo-200",children:"Seleccionar todas"}),(0,t.jsx)("button",{onClick:()=>{c(e=>e.map(e=>({...e,cantidad:0})))},className:"px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200",children:"Limpiar todo"})]}),(0,t.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,t.jsx)("strong",{children:f})," preguntas de ",(0,t.jsx)("strong",{children:j})," test(s)"]})]}),(0,t.jsx)("div",{className:"space-y-4",children:l.map(e=>{let s=o.find(s=>s.testId===e.id);return s?(0,t.jsxs)("div",{className:"border rounded-lg p-4 bg-white",children:[(0,t.jsxs)("div",{className:"flex justify-between items-start mb-3",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("h3",{className:"font-medium text-gray-900",children:e.titulo}),e.descripcion&&(0,t.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:e.descripcion}),(0,t.jsxs)("p",{className:"text-xs text-gray-400 mt-1",children:["Creado: ",new Date(e.creado_en).toLocaleDateString()]})]}),(0,t.jsxs)("div",{className:"text-sm text-gray-500",children:[e.numPreguntas," pregunta",1!==e.numPreguntas?"s":""," disponible",1!==e.numPreguntas?"s":""]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Preguntas a incluir:"}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("input",{type:"range",min:"0",max:s.maxPreguntas,value:s.cantidad,onChange:s=>p(e.id,parseInt(s.target.value)),className:"flex-1"}),(0,t.jsx)("input",{type:"number",min:"0",max:s.maxPreguntas,value:s.cantidad,onChange:s=>p(e.id,parseInt(s.target.value)||0),className:"w-16 px-2 py-1 border rounded text-center text-sm"}),(0,t.jsxs)("span",{className:"text-sm text-gray-500",children:["/ ",s.maxPreguntas]})]})]})]},e.id):null})}),(0,t.jsx)("div",{className:"flex justify-center pt-4",children:(0,t.jsx)("button",{onClick:b,disabled:0===f||x,className:"flex items-center px-6 py-3 rounded-lg font-medium ".concat(0===f||x?"bg-gray-300 text-gray-500 cursor-not-allowed":"bg-indigo-600 text-white hover:bg-indigo-700"),children:x?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(i.jTZ,{className:"mr-2 h-5 w-5 animate-spin"}),"Generando repaso..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(i.aze,{className:"mr-2 h-5 w-5"}),"Iniciar Repaso (",f," preguntas)"]})})})]})}function en(e){let{preguntas:s,configuracion:a,onFinalizar:l,onCancelar:n}=e,[o,c]=(0,r.useState)(0),[d,m]=(0,r.useState)({}),[u,x]=(0,r.useState)(null),[h,g]=(0,r.useState)(Date.now()),[p,b]=(0,r.useState)({}),[f,j]=(0,r.useState)(!1),[y,N]=(0,r.useState)(null);(0,r.useEffect)(()=>{g(Date.now())},[]),(0,r.useEffect)(()=>{var e;x(d[null==(e=s[o])?void 0:e.id]||null)},[o,d,s]);let v=s[o],w=e=>{if(!f){if(x(e),h){let e=Date.now(),a=e-h;b(e=>({...e,[s[o].id]:a})),g(e)}m(a=>({...a,[s[o].id]:e}))}},E=()=>{j(!0);let e=0,a=0,t=0,r=0,n=[];s.forEach(s=>{let l=d[s.id],i=p[s.id]||0;l?"blank"===l?t++:l===s.respuesta_correcta?e++:a++:t++,n.push({preguntaId:s.id,respuestaSeleccionada:l||"blank",esCorrecta:l===s.respuesta_correcta,tiempoRespuesta:i}),r+=i});let i=e/s.length*100;N({correctas:e,incorrectas:a,enBlanco:t,porcentaje:i,tiempoTotal:r}),l({totalPreguntas:s.length,respuestasCorrectas:e,respuestasIncorrectas:a,respuestasEnBlanco:t,tiempoTotal:r,porcentajeAcierto:i,respuestas:n})},k=e=>f?e===v.respuesta_correcta?"bg-green-100 border-green-500 text-green-800":e===u&&e!==v.respuesta_correcta?"bg-red-100 border-red-500 text-red-800":"bg-gray-50":u===e?"bg-indigo-100 border-indigo-500":"hover:bg-gray-50 cursor-pointer",C=e=>f?e===v.respuesta_correcta?(0,t.jsx)(i.YrT,{className:"text-green-600"}):e===u&&e!==v.respuesta_correcta?(0,t.jsx)(i.yGN,{className:"text-red-600"}):null:null,S=(o+1)/s.length*100,T=Math.floor((Date.now()-h)/1e3);return(0,t.jsxs)("div",{className:"max-w-4xl mx-auto space-y-6",children:[(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-4",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,t.jsx)("h2",{className:"text-xl font-bold",children:"Test de Repaso"}),(0,t.jsxs)("div",{className:"flex items-center gap-4 text-sm text-gray-600",children:[(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(i.x_j,{className:"h-4 w-4"}),o+1," de ",s.length]}),!f&&(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(i.Ohp,{className:"h-4 w-4"}),Math.floor(T/60),":",(T%60).toString().padStart(2,"0")]})]})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"bg-indigo-600 h-2 rounded-full transition-all duration-300",style:{width:"".concat(S,"%")}})})]}),f&&y&&(0,t.jsxs)("div",{className:"bg-gray-50 border rounded-lg p-4",children:[(0,t.jsx)("h4",{className:"font-semibold text-lg mb-3",children:"Resultados del Test"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-5 gap-4",children:[(0,t.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-3",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-green-800",children:"Correctas"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-green-700",children:y.correctas})]}),(0,t.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-red-800",children:"Incorrectas"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-red-700",children:y.incorrectas})]}),(0,t.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-3",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-yellow-800",children:"En Blanco"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-yellow-700",children:y.enBlanco})]}),(0,t.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-3",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-blue-800",children:"Porcentaje"}),(0,t.jsxs)("p",{className:"text-2xl font-bold text-blue-700",children:[y.porcentaje.toFixed(1),"%"]})]}),(0,t.jsxs)("div",{className:"bg-purple-50 border border-purple-200 rounded-lg p-3",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-purple-800",children:"Tiempo Total"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-purple-700",children:(e=>{let s=Math.floor(e/6e4),a=Math.floor(e%6e4/1e3);return"".concat(s,":").concat(a.toString().padStart(2,"0"))})(y.tiempoTotal)})]})]}),(0,t.jsx)("div",{className:"mt-4 flex justify-center",children:(0,t.jsx)("button",{onClick:()=>{c(0),m({}),x(null),g(Date.now()),b({}),j(!1),N(null)},className:"bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded",children:"Realizar de nuevo"})})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:[(0,t.jsx)("h3",{className:"text-lg font-medium mb-6",children:v.pregunta}),(0,t.jsxs)("div",{className:"space-y-3",children:[["a","b","c","d"].map(e=>{let s=u===e;return(0,t.jsx)("div",{className:"p-3 border rounded-lg cursor-pointer transition-all ".concat(k(e)),onClick:()=>!f&&w(e),children:(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)("div",{className:"flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center mr-2 ".concat(f&&e===v.respuesta_correcta?"bg-green-500 text-white":f&&s&&e!==v.respuesta_correcta?"bg-red-500 text-white":s?"bg-indigo-500 text-white":"bg-gray-200 text-gray-700"),children:e.toUpperCase()}),(0,t.jsx)("div",{className:"flex-grow",children:v["opcion_".concat(e)]}),C(e)]})},e)}),!f&&(0,t.jsx)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:(0,t.jsxs)("div",{className:"p-3 border rounded-lg cursor-pointer flex items-center ".concat("blank"===u?"bg-yellow-50 border-yellow-300":"hover:bg-gray-50 border-gray-300"),onClick:()=>w("blank"),children:[(0,t.jsx)("input",{type:"checkbox",checked:"blank"===u,onChange:()=>w("blank"),className:"mr-3 h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300 rounded",onClick:e=>e.stopPropagation()}),(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Marque si quiere dejar en blanco"})]})})]})]}),(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("button",{onClick:()=>{o>0&&c(e=>e-1)},disabled:0===o,className:"flex items-center gap-2 px-4 py-2 rounded-lg ".concat(0===o?"bg-gray-100 text-gray-400 cursor-not-allowed":"bg-gray-200 text-gray-700 hover:bg-gray-300"),children:[(0,t.jsx)(i.irw,{className:"h-4 w-4"}),"Anterior"]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)("button",{onClick:n,className:"px-4 py-2 text-gray-600 hover:text-gray-800",children:"Cancelar"}),!f&&(0,t.jsxs)("button",{onClick:()=>{u||w("blank"),o<s.length-1?c(e=>e+1):E()},className:"bg-indigo-600 text-white py-2 px-4 rounded flex items-center hover:bg-indigo-700",children:[o===s.length-1?"Finalizar Test":"Siguiente",o<s.length-1&&(0,t.jsx)(i.fOo,{className:"ml-1"})]}),f&&o<s.length-1&&(0,t.jsxs)("button",{onClick:()=>c(e=>e+1),className:"text-indigo-600 hover:text-indigo-800 flex items-center",children:["Siguiente ",(0,t.jsx)(i.fOo,{className:"ml-1"})]})]})]}),!f&&!u&&(0,t.jsx)("div",{className:"text-center text-gray-500 text-sm",children:"Selecciona una respuesta para continuar"})]})}let ei=e=>{let{isOpen:s,onClose:a,test:l,onSave:n}=e,[o,c]=(0,r.useState)(""),[m,u]=(0,r.useState)(""),[x,h]=(0,r.useState)(!1);(0,r.useEffect)(()=>{s&&l&&(c(l.titulo),u(l.descripcion||""))},[s,l]);let g=async()=>{let e;if(!o.trim())return void d.oR.error("El t\xedtulo del test es obligatorio");h(!0);try{e=d.oR.loading("Actualizando test...");let s=await (0,ea.xD)(l.id,o.trim(),m.trim()||void 0);s?(d.oR.success("Test actualizado correctamente",{id:e}),n(s),a()):d.oR.error("Error al actualizar el test",{id:e})}catch(s){console.error("Error al actualizar test:",s),d.oR.error("Error inesperado al actualizar el test",{id:e})}finally{h(!1)}},p=()=>{x||a()};return s?(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",onKeyDown:e=>{"Escape"!==e.key||x||a()},role:"dialog","aria-modal":"true","aria-labelledby":"test-edit-title",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,t.jsx)("h3",{id:"test-edit-title",className:"text-lg font-semibold text-gray-900",children:"Editar Test"}),(0,t.jsx)("button",{onClick:p,className:"text-gray-400 hover:text-gray-600 transition-colors",disabled:x,"aria-label":"Cerrar modal",children:(0,t.jsx)(i.yGN,{size:24})})]}),(0,t.jsxs)("div",{className:"p-6 space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"test-titulo",className:"block text-sm font-medium text-gray-700 mb-2",children:"T\xedtulo del Test *"}),(0,t.jsx)("input",{id:"test-titulo",type:"text",value:o,onChange:e=>c(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Ingresa el t\xedtulo del test",disabled:x,maxLength:255,required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"test-descripcion",className:"block text-sm font-medium text-gray-700 mb-2",children:"Descripci\xf3n (opcional)"}),(0,t.jsx)("textarea",{id:"test-descripcion",value:m,onChange:e=>u(e.target.value),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-vertical",placeholder:"Describe brevemente el contenido del test",disabled:x,maxLength:1e3})]})]}),(0,t.jsxs)("div",{className:"flex items-center justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50",children:[(0,t.jsx)("button",{onClick:p,className:"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",disabled:x,children:"Cancelar"}),(0,t.jsxs)("button",{onClick:g,className:"px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2",disabled:x||!o.trim(),children:[(0,t.jsx)(i.Bc_,{size:16}),(0,t.jsx)("span",{children:x?"Guardando...":"Guardar"})]})]})]})}):null};function eo(){var e,s;let[a,l]=(0,r.useState)([]),[n,o]=(0,r.useState)(null),[m,u]=(0,r.useState)([]),[x,h]=(0,r.useState)(0),[g,p]=(0,r.useState)(null),[b,f]=(0,r.useState)({}),[j,y]=(0,r.useState)(!1),[N,v]=(0,r.useState)(null),[w,E]=(0,r.useState)(null),[k,C]=(0,r.useState)({}),[S,T]=(0,r.useState)(!0),[A,R]=(0,r.useState)(""),[_,z]=(0,r.useState)(null),[D,P]=(0,r.useState)(null),[I,F]=(0,r.useState)("lista"),[L,M]=(0,r.useState)([]),[G,O]=(0,r.useState)([]),[q,H]=(0,r.useState)(null),[V,U]=(0,r.useState)(!1),[B,$]=(0,r.useState)(null),[Y,W]=(0,r.useState)(!1),[K,X]=(0,r.useState)(null);(0,r.useEffect)(()=>{J()},[]),(0,r.useEffect)(()=>{n&&Z(n.id)},[n]),(0,r.useEffect)(()=>{m.length>0&&!j&&E(Date.now())},[m,j]);let J=async()=>{T(!0),R("");try{let e=await (0,c.Lx)(),s=await Promise.all(e.map(async e=>{try{let s=await (0,c.Kj)(e.id);return{...e,numero_preguntas:s}}catch(s){return console.error("Error al obtener conteo para test ".concat(e.id,":"),s),{...e,numero_preguntas:void 0}}}));l(s)}catch(e){console.error("Error al cargar tests:",e),R("No se pudieron cargar los tests. Por favor, int\xe9ntalo de nuevo.")}finally{T(!1)}},Z=async e=>{T(!0);try{let s=await (0,c.hg)(e);u(s),h(0),p(null),f({}),y(!1),v(null)}catch(e){console.error("Error al cargar preguntas:",e),R("No se pudieron cargar las preguntas del test. Por favor, int\xe9ntalo de nuevo.")}finally{T(!1)}},Q=e=>{if(!j){if(p(e),w){let e=Date.now(),s=e-w;C(e=>({...e,[m[x].id]:s})),E(e)}f(s=>({...s,[m[x].id]:e}))}},ee=async()=>{g||Q("blank"),x<m.length-1?(await es(),h(x+1),p(null)):(await es(),eo())},es=async()=>{if(!n)return;let e=m[x],s=g||"blank",a="blank"!==s&&s===e.respuesta_correcta;try{await (0,c.Gl)(n.id,e.id,"blank"===s?"x":s,a)}catch(e){console.error("Error al guardar estad\xedstica:",e)}},eo=()=>{y(!0);let e=0,s=0,a=0,t=0;m.forEach(r=>{let l=b[r.id];l?"blank"===l?a++:l===r.respuesta_correcta?e++:s++:a++,t+=k[r.id]||0});let r=e/m.length*100;v({correctas:e,incorrectas:s,enBlanco:a,porcentaje:r,tiempoTotal:t})},ec=async()=>{try{let e=await (0,c.oC)();z(e),F("estadisticas-generales")}catch(e){console.error("Error al cargar estad\xedsticas generales:",e),R("No se pudieron cargar las estad\xedsticas generales. Por favor, int\xe9ntalo de nuevo.")}},ed=async e=>{try{let s=await (0,c.dd)(e);P(s),F("estadisticas-test")}catch(e){console.error("Error al cargar estad\xedsticas del test:",e),R("No se pudieron cargar las estad\xedsticas del test. Por favor, int\xe9ntalo de nuevo.")}},em=e=>{H(e),U(!0)},eu=()=>{H(null),U(!1)},ex=e=>{$(e),W(!0)},eh=()=>{$(null),W(!1)},eg=async()=>{let e;if(B){X(B);try{e=d.oR.loading("Eliminando test..."),await (0,ea.aN)(B)?(d.oR.success("Test eliminado correctamente",{id:e}),l(e=>e.filter(e=>e.id!==B)),n&&n.id===B&&(o(null),F("lista")),eh()):d.oR.error("Error al eliminar el test",{id:e})}catch(s){console.error("Error al eliminar test:",s),d.oR.error("Error inesperado al eliminar el test",{id:e})}finally{X(null)}}};return(0,t.jsxs)("div",{className:"container mx-auto p-4",children:[A&&(0,t.jsx)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4",children:A}),(0,t.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold",children:"Mis Tests"}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)("button",{onClick:()=>{F("repaso-config")},className:"bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded flex items-center focus:outline-none focus:shadow-outline",children:[(0,t.jsx)(i.jTZ,{className:"mr-2"})," Test de Repaso"]}),(0,t.jsxs)("button",{onClick:ec,className:"bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded flex items-center focus:outline-none focus:shadow-outline",children:[(0,t.jsx)(i.vQY,{className:"mr-2"})," Estad\xedsticas Generales"]})]})]}),"lista"===I&&(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6",children:a.map(e=>(0,t.jsxs)("div",{className:"border rounded-lg p-4 cursor-pointer hover:bg-gray-50 transition-colors flex flex-col justify-between",onClick:()=>o(e),children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,t.jsx)("h3",{className:"font-semibold text-lg",children:e.titulo}),(0,t.jsx)("button",{onClick:s=>{s.stopPropagation(),em(e)},className:"p-1 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50",title:"Editar test",children:(0,t.jsx)(i.Pj4,{size:16})})]}),e.descripcion&&(0,t.jsx)("p",{className:"text-sm text-gray-600 mb-2 break-words",children:e.descripcion}),(0,t.jsxs)("p",{className:"text-sm text-gray-500 mb-1",children:["Preguntas: ","number"==typeof e.numero_preguntas?e.numero_preguntas:"Cargando..."]}),(0,t.jsxs)("p",{className:"text-xs text-gray-400",children:["Creado: ",new Date(e.creado_en).toLocaleDateString("es-ES")]})]}),(0,t.jsxs)("div",{className:"mt-4 space-y-2",children:[(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)("button",{onClick:s=>{s.stopPropagation(),o(e),F("test")},className:"flex-1 bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50",children:"Realizar Test"}),(0,t.jsxs)("button",{onClick:s=>{s.stopPropagation(),ed(e.id),F("estadisticas-test")},className:"flex-1 bg-gray-500 hover:bg-gray-600 text-white font-semibold py-2 px-4 rounded text-sm flex items-center justify-center focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-opacity-50",children:[(0,t.jsx)(i.eXT,{className:"mr-2"})," Estad\xedsticas"]})]}),(0,t.jsx)("button",{onClick:s=>{s.stopPropagation(),ex(e.id)},className:"w-full bg-red-500 hover:bg-red-600 text-white font-semibold py-2 px-4 rounded text-sm flex items-center justify-center focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-50 disabled:opacity-50 disabled:cursor-not-allowed",disabled:K===e.id,children:K===e.id?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Eliminando..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(i.IXo,{className:"mr-2",size:16}),"Eliminar"]})})]})]},e.id))}),"estadisticas-generales"===I&&_&&(0,t.jsx)(et,{estadisticas:{totalTests:_.totalTests,totalRespuestasCorrectas:_.totalRespuestasCorrectas,totalRespuestasIncorrectas:_.totalRespuestasIncorrectas,porcentajeAcierto:_.porcentajeAcierto},onClose:()=>F("lista")}),"estadisticas-test"===I&&D&&(0,t.jsx)(er,{estadisticas:{totalPreguntas:D.totalPreguntas,totalCorrectas:D.totalCorrectas,totalIncorrectas:D.totalIncorrectas,porcentajeAcierto:D.porcentajeAcierto,fechasRealizacion:D.fechasRealizacion,preguntasMasFalladas:D.preguntasMasFalladas},testTitulo:(null==(e=a.find(e=>e.id===D.testId))?void 0:e.titulo)||"Test",onClose:()=>F("lista")}),"repaso-config"===I&&(0,t.jsx)(el,{onIniciarRepaso:(e,s)=>{M(e),O(s),F("repaso-test")},onCancelar:()=>F("lista")}),"repaso-test"===I&&L.length>0&&(0,t.jsx)(en,{preguntas:L,configuracion:G,onFinalizar:e=>{console.log("Test de repaso finalizado:",e)},onCancelar:()=>F("lista")}),"test"===I&&n&&m.length>0&&(0,t.jsxs)("div",{className:"bg-white border rounded-lg shadow-md p-6 mt-4",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,t.jsx)("h3",{className:"text-xl font-bold",children:n.titulo}),(0,t.jsx)("button",{onClick:()=>F("lista"),className:"bg-gray-200 hover:bg-gray-300 text-gray-800 py-1 px-3 rounded",children:"Volver"})]}),j&&N&&(0,t.jsxs)("div",{className:"bg-gray-50 border rounded-lg p-4 mb-6",children:[(0,t.jsx)("h4",{className:"font-semibold text-lg mb-3",children:"Resultados del Test"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-5 gap-4",children:[(0,t.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-3",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-green-800",children:"Correctas"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-green-700",children:N.correctas})]}),(0,t.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-red-800",children:"Incorrectas"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-red-700",children:N.incorrectas})]}),(0,t.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-3",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-yellow-800",children:"En Blanco"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-yellow-700",children:N.enBlanco})]}),(0,t.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-3",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-blue-800",children:"Porcentaje"}),(0,t.jsxs)("p",{className:"text-2xl font-bold text-blue-700",children:[N.porcentaje.toFixed(1),"%"]})]}),(0,t.jsxs)("div",{className:"bg-purple-50 border border-purple-200 rounded-lg p-3",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-purple-800",children:"Tiempo Total"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-purple-700",children:(e=>{let s=Math.floor(e/1e3),a=Math.floor(s/60);return"".concat(a,":").concat((s%60).toString().padStart(2,"0"))})(N.tiempoTotal)})]})]}),(0,t.jsx)("div",{className:"mt-4 flex justify-center",children:(0,t.jsx)("button",{onClick:()=>{h(0),p(null),f({}),y(!1),v(null),E(Date.now()),C({})},className:"bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded",children:"Realizar de nuevo"})})]}),(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,t.jsxs)("span",{className:"text-gray-600",children:["Pregunta ",x+1," de ",m.length]}),!j&&(0,t.jsxs)("span",{className:"text-sm text-gray-500",children:[(0,t.jsx)(i.Ohp,{className:"inline mr-1"})," Tiempo por pregunta"]})]}),(0,t.jsx)("div",{className:"h-2 bg-gray-200 rounded-full mb-4",children:(0,t.jsx)("div",{className:"h-2 bg-indigo-600 rounded-full",style:{width:"".concat((x+1)/m.length*100,"%")}})})]}),(0,t.jsx)("div",{className:"min-h-[300px]",children:(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("h4",{className:"font-semibold text-lg mb-6",children:null==(s=m[x])?void 0:s.pregunta}),(0,t.jsxs)("div",{className:"space-y-3 mt-6",children:[["a","b","c","d"].map(e=>{var s;let a=j&&e===m[x].respuesta_correcta,r=j&&g===e&&e!==m[x].respuesta_correcta,l=g===e;return(0,t.jsx)("div",{className:"p-3 border rounded-lg cursor-pointer ".concat(a?"bg-green-100 border-green-500":r?"bg-red-100 border-red-500":l?"bg-indigo-100 border-indigo-500":"hover:bg-gray-50"),onClick:()=>!j&&Q(e),children:(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)("div",{className:"flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center mr-2 ".concat(a?"bg-green-500 text-white":r?"bg-red-500 text-white":l?"bg-indigo-500 text-white":"bg-gray-200 text-gray-700"),children:e.toUpperCase()}),(0,t.jsx)("div",{className:"flex-grow",children:null==(s=m[x])?void 0:s["opcion_".concat(e)]})]})},e)}),!j&&(0,t.jsx)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:(0,t.jsxs)("div",{className:"p-3 border rounded-lg cursor-pointer flex items-center ".concat("blank"===g?"bg-yellow-50 border-yellow-300":"hover:bg-gray-50 border-gray-300"),onClick:()=>Q("blank"),children:[(0,t.jsx)("input",{type:"checkbox",checked:"blank"===g,onChange:()=>Q("blank"),className:"mr-3 h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300 rounded",onClick:e=>e.stopPropagation()}),(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Marque si quiere dejar en blanco"})]})})]})]})}),(0,t.jsxs)("div",{className:"flex justify-between mt-6",children:[(0,t.jsxs)("button",{onClick:()=>{x>0&&(h(x-1),p(b[m[x-1].id]||null))},disabled:0===x,className:"flex items-center ".concat(0===x?"text-gray-400 cursor-not-allowed":"text-indigo-600 hover:text-indigo-800"),children:[(0,t.jsx)(i.irw,{className:"mr-1"})," Anterior"]}),!j&&(0,t.jsxs)("button",{onClick:ee,className:"bg-indigo-600 text-white py-2 px-4 rounded flex items-center hover:bg-indigo-700",children:[x===m.length-1?"Finalizar Test":"Siguiente",x<m.length-1&&(0,t.jsx)(i.fOo,{className:"ml-1"})]}),j&&x<m.length-1&&(0,t.jsxs)("button",{onClick:ee,className:"text-indigo-600 hover:text-indigo-800 flex items-center",children:["Siguiente ",(0,t.jsx)(i.fOo,{className:"ml-1"})]})]})]}),S&&(0,t.jsx)("div",{className:"flex justify-center items-center h-40",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"})}),!S&&0===a.length&&"lista"===I&&(0,t.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 text-yellow-800 p-4 rounded flex items-start",children:[(0,t.jsx)(i.eHT,{className:"mr-2 mt-1 flex-shrink-0"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium",children:"No hay tests disponibles"}),(0,t.jsx)("p",{className:"text-sm mt-1",children:'Genera nuevos tests desde la secci\xf3n "Generar Tests".'})]})]}),q&&(0,t.jsx)(ei,{isOpen:V,onClose:eu,test:q,onSave:e=>{l(s=>s.map(s=>s.id===e.id?e:s)),n&&n.id===e.id&&o(e),eu()}}),Y&&(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-md w-full mx-4",children:[(0,t.jsxs)("div",{className:"flex items-center mb-4",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)(i.IXo,{className:"w-6 h-6 text-red-600"})}),(0,t.jsx)("div",{className:"ml-3",children:(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Eliminar Test"})})]}),(0,t.jsx)("div",{className:"mb-4",children:(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"\xbfEst\xe1s seguro de que quieres eliminar este test? Esta acci\xf3n no se puede deshacer y se eliminar\xe1n todas las preguntas y estad\xedsticas asociadas."})}),(0,t.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,t.jsx)("button",{onClick:eh,className:"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",disabled:null!==K,children:"Cancelar"}),(0,t.jsx)("button",{onClick:eg,className:"px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center",disabled:null!==K,children:null!==K?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Eliminando..."]}):"Eliminar"})]})]})})]})}var ec=a(5307),ed=a(7634);let em=[{id:"a1_2019_junta",nombre:"Cuerpo Superior Facultativo - Inform\xe1tica (A1.2019)",descripcion:"Temario completo para oposiciones del Cuerpo Superior Facultativo, opci\xf3n Inform\xe1tica de la Junta de Andaluc\xeda",cuerpo:"CUERPO SUPERIOR FACULTATIVO, OPCI\xd3N INFORM\xc1TICA (A1.2019)",archivo:"a1_2019_junta.md"},{id:"c1_junta",nombre:"Cuerpo General de Administrativos (C1.1000)",descripcion:"Temario completo para oposiciones del Cuerpo General de Administrativos de la Junta de Andaluc\xeda",cuerpo:"CUERPO GENERAL DE ADMINISTRATIVOS (C1.1000)",archivo:"c1_junta.md"},{id:"c2_estado",nombre:"Cuerpo General Auxiliar del Estado (C2)",descripcion:"Temario para oposiciones del Cuerpo General Auxiliar del Estado",cuerpo:"CUERPO GENERAL AUXILIAR DEL ESTADO (C2)",archivo:"c2_estado.md"},{id:"c2_junta",nombre:"Cuerpo General Auxiliar - Junta de Andaluc\xeda (C2)",descripcion:"Temario para oposiciones del Cuerpo General Auxiliar de la Junta de Andaluc\xeda",cuerpo:"CUERPO GENERAL AUXILIAR - JUNTA DE ANDALUC\xcdA (C2)",archivo:"c2_junta.md"}];async function eu(e){try{let s=em.find(s=>s.id===e);if(!s)return console.error("Temario predefinido no encontrado:",e),null;let a=await fetch("/temarios/".concat(s.archivo));if(!a.ok)return console.error("Error al cargar archivo de temario:",a.status),null;let t=await a.text(),r=function(e){let s=[],a=e.split("\n");for(let e=0;e<a.length;e++){let t=a[e].trim(),r=t.match(/^Tema\s+(\d+)\.\s*(.+)$/);if(r||(r=t.match(/^(\d+)\.\s*(.+)$/)),r){let e=parseInt(r[1]),a=r[2].trim();a.length>10&&!a.match(/^[IVX]+\s*$/)&&s.push({numero:e,titulo:a,descripcion:a.length>100?a.substring(0,100)+"...":a})}}return s}(t);return{...s,temas:r}}catch(e){return console.error("Error al cargar temario predefinido:",e),null}}async function ex(e){try{let s=await eu(e);if(!s)return null;return{totalTemas:s.temas.length,tipoTemario:"Temario Completo Predefinido",cuerpo:s.cuerpo}}catch(e){return console.error("Error al obtener estad\xedsticas:",e),null}}let eh=e=>{let{onSeleccionar:s,onVolver:a}=e,[l,n]=(0,r.useState)([]),[o,c]=(0,r.useState)(""),[m,u]=(0,r.useState)(null),[x,h]=(0,r.useState)(null),[g,p]=(0,r.useState)({});(0,r.useEffect)(()=>{b()},[]),(0,r.useEffect)(()=>{n(function(e){if(!e.trim())return em;let s=e.toLowerCase();return em.filter(e=>e.nombre.toLowerCase().includes(s)||e.descripcion.toLowerCase().includes(s)||e.cuerpo.toLowerCase().includes(s))}(o))},[o]);let b=()=>{n(em),em.forEach(async e=>{let s=await ex(e.id);s&&p(a=>({...a,[e.id]:s}))})},f=async e=>{h(e);try{let a=await eu(e);a?(s(a),d.oR.success("Temario predefinido cargado exitosamente")):d.oR.error("Error al cargar el temario predefinido")}catch(e){console.error("Error al cargar temario:",e),d.oR.error("Error al cargar el temario predefinido")}finally{h(null)}},j=function(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:150;return e.length<=s?e:e.substring(0,s)+"..."};return(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 p-4",children:(0,t.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)("button",{onClick:a,className:"text-blue-600 hover:text-blue-700 text-sm font-medium mb-4 flex items-center",children:"← Volver a la selecci\xf3n"}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Seleccionar Temario Predefinido"}),(0,t.jsx)("p",{className:"text-lg text-gray-600 mb-2",children:"Elige uno de nuestros temarios oficiales predefinidos"}),(0,t.jsx)("p",{className:"text-gray-500",children:"Estos temarios est\xe1n basados en convocatorias oficiales y contienen todos los temas necesarios"})]})]}),(0,t.jsx)("div",{className:"mb-6",children:(0,t.jsxs)("div",{className:"relative max-w-md mx-auto",children:[(0,t.jsx)(i.CKj,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),(0,t.jsx)("input",{type:"text",placeholder:"Buscar por cuerpo, nivel o descripci\xf3n...",value:o,onChange:e=>c(e.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})}),(0,t.jsx)("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:l.map(e=>{let s=g[e.id],a=x===e.id;return(0,t.jsx)("div",{className:"bg-white rounded-xl shadow-sm border-2 transition-all duration-200 hover:shadow-md h-full flex flex-col ".concat(m===e.id?"border-blue-500 ring-2 ring-blue-200":"border-gray-200 hover:border-blue-300"),children:(0,t.jsxs)("div",{className:"p-6 flex flex-col h-full",children:[(0,t.jsxs)("div",{className:"flex-grow",children:[(0,t.jsx)("div",{className:"flex items-start justify-between mb-4",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-3",children:(0,t.jsx)(i.H9b,{className:"w-6 h-6 text-blue-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-900 text-sm leading-tight",children:e.nombre}),(0,t.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:e.cuerpo})]})]})}),(0,t.jsx)("p",{className:"text-sm text-gray-600 mb-4 leading-relaxed",children:j(e.descripcion)}),s&&(0,t.jsxs)("div",{className:"bg-gray-50 rounded-lg p-3 mb-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Total de temas:"}),(0,t.jsx)("span",{className:"font-semibold text-gray-900",children:s.totalTemas})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between text-sm mt-1",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Tipo:"}),(0,t.jsx)("span",{className:"text-xs bg-green-100 text-green-800 px-2 py-1 rounded",children:"Completo"})]})]})]}),(0,t.jsx)("button",{onClick:()=>f(e.id),disabled:a,className:"w-full py-3 px-4 rounded-lg font-medium transition-all duration-200 flex items-center justify-center ".concat(a?"bg-gray-100 text-gray-400 cursor-not-allowed":"bg-blue-600 text-white hover:bg-blue-700 active:bg-blue-800"),children:a?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(i.TwU,{className:"w-4 h-4 mr-2 animate-spin"}),"Cargando..."]}):(0,t.jsxs)(t.Fragment,{children:["Seleccionar Temario",(0,t.jsx)(i.dyV,{className:"w-4 h-4 ml-2"})]})})]})},e.id)})}),0===l.length&&(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)(i.S8s,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No se encontraron temarios"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Intenta con otros t\xe9rminos de b\xfasqueda o revisa la ortograf\xeda"})]}),(0,t.jsx)("div",{className:"mt-12 bg-blue-50 border border-blue-200 rounded-lg p-6",children:(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)(i.S8s,{className:"w-5 h-5 text-blue-600 mr-3 flex-shrink-0 mt-0.5"}),(0,t.jsxs)("div",{className:"text-blue-800",children:[(0,t.jsx)("h4",{className:"font-semibold mb-2",children:"Sobre los temarios predefinidos"}),(0,t.jsxs)("ul",{className:"text-sm space-y-1",children:[(0,t.jsx)("li",{children:"• Basados en convocatorias oficiales reales"}),(0,t.jsx)("li",{children:"• Incluyen todos los temas necesarios para la oposici\xf3n"}),(0,t.jsx)("li",{children:"• Optimizados para usar con las funciones de IA de la plataforma"}),(0,t.jsx)("li",{children:"• Se pueden personalizar despu\xe9s de la importaci\xf3n"})]})]})]})})]})})},eg=e=>{let{onComplete:s}=e,[a,l]=(0,r.useState)("seleccion"),[n,o]=(0,r.useState)(null),[c,m]=(0,r.useState)(""),[u,x]=(0,r.useState)(""),[h,g]=(0,r.useState)([{numero:1,titulo:"",descripcion:""}]),[p,b]=(0,r.useState)(null),[f,j]=(0,r.useState)(!1),y=e=>{o(e),"predefinido"===e?l("predefinidos"):l("configuracion")},N=e=>{h.length>1&&g(h.filter((s,a)=>a!==e).map((e,s)=>({...e,numero:s+1})))},v=(e,s,a)=>{let t=[...h];t[e]={...t[e],[s]:a},g(t)},w=()=>c.trim()?!h.some(e=>!e.titulo.trim())||(d.oR.error("Todos los temas deben tener un t\xedtulo"),!1):(d.oR.error("El t\xedtulo del temario es obligatorio"),!1),E=async()=>{j(!0);try{let e;if("predefinido"===n&&p)e=function(e){return{titulo:e.nombre,descripcion:"".concat(e.descripcion,"\n\nCuerpo: ").concat(e.cuerpo),tipo:"completo",temas:e.temas.map((e,s)=>({numero:e.numero,titulo:e.titulo,descripcion:e.descripcion,orden:s+1}))}}(p);else{if(!w()||!n)return;e={titulo:c,descripcion:u,tipo:n,temas:h.map((e,s)=>({numero:e.numero,titulo:e.titulo,descripcion:e.descripcion,orden:s+1}))}}let a=await (0,ed.r5)(e.titulo,e.descripcion,e.tipo);if(!a)return void d.oR.error("Error al crear el temario");if(!await (0,ed.sW)(a,e.temas))return void d.oR.error("Error al crear los temas");d.oR.success("\xa1Temario configurado exitosamente!"),s()}catch(e){console.error("Error al guardar temario:",e),d.oR.error("Error al configurar el temario")}finally{j(!1)}};return"seleccion"===a?(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center p-4",children:(0,t.jsxs)("div",{className:"max-w-4xl w-full",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"\xa1Bienvenido a OposiAI! \uD83C\uDF89"}),(0,t.jsx)("p",{className:"text-lg text-gray-600 mb-2",children:"Para comenzar, necesitamos configurar tu temario de estudio."}),(0,t.jsx)("p",{className:"text-gray-500",children:"Esto nos permitir\xe1 crear una planificaci\xf3n personalizada y hacer un seguimiento de tu progreso. El temario solo es el \xedndice de los temas necesarios para preparar la oposici\xf3n, los textos ser\xe1n a\xf1adidos por el opositor."})]}),(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-8 max-w-4xl mx-auto",children:[(0,t.jsx)("div",{className:"bg-white rounded-xl p-6 shadow-sm border-2 border-gray-200 hover:border-green-500 cursor-pointer transition-all duration-200 hover:shadow-md",onClick:()=>y("predefinido"),children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,t.jsx)(i.a4x,{className:"w-8 h-8 text-green-600"})}),(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"Temarios Predefinidos"}),(0,t.jsx)("p",{className:"text-gray-600 mb-4",children:"Selecciona un temario oficial ya configurado y listo para usar."}),(0,t.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-3 mb-4",children:(0,t.jsxs)("div",{className:"flex items-center text-green-800 text-sm",children:[(0,t.jsx)(i.YrT,{className:"w-4 h-4 mr-2 flex-shrink-0"}),(0,t.jsx)("span",{children:"Basados en convocatorias oficiales"})]})}),(0,t.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-3",children:(0,t.jsxs)("div",{className:"flex items-center text-green-800 text-sm",children:[(0,t.jsx)(i.YrT,{className:"w-4 h-4 mr-2 flex-shrink-0"}),(0,t.jsx)("span",{children:"Configuraci\xf3n instant\xe1nea"})]})}),(0,t.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-3 mb-4",children:(0,t.jsxs)("div",{className:"flex items-center text-green-800 text-sm",children:[(0,t.jsx)(i.YrT,{className:"w-4 h-4 mr-2 flex-shrink-0"}),(0,t.jsx)("span",{children:"La IA podr\xe1 crear una planificaci\xf3n completa y personalizada"})]})}),(0,t.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-3",children:(0,t.jsxs)("div",{className:"flex items-center text-green-800 text-sm",children:[(0,t.jsx)(i.YrT,{className:"w-4 h-4 mr-2 flex-shrink-0"}),(0,t.jsx)("span",{children:"Seguimiento detallado del progreso por temas"})]})})]})}),(0,t.jsx)("div",{className:"bg-white rounded-xl p-6 shadow-sm border-2 border-gray-200 hover:border-blue-500 cursor-pointer transition-all duration-200 hover:shadow-md",onClick:()=>y("completo"),children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,t.jsx)(i.H9b,{className:"w-8 h-8 text-blue-600"})}),(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"Temario Personalizado"}),(0,t.jsx)("p",{className:"text-gray-600 mb-4",children:"Configura todos los temas de tu oposici\xf3n de forma estructurada."}),(0,t.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-3 mb-4",children:(0,t.jsxs)("div",{className:"flex items-center text-green-800 text-sm",children:[(0,t.jsx)(i.YrT,{className:"w-4 h-4 mr-2 flex-shrink-0"}),(0,t.jsx)("span",{children:"La IA podr\xe1 crear una planificaci\xf3n completa y personalizada"})]})}),(0,t.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-3",children:(0,t.jsxs)("div",{className:"flex items-center text-green-800 text-sm",children:[(0,t.jsx)(i.YrT,{className:"w-4 h-4 mr-2 flex-shrink-0"}),(0,t.jsx)("span",{children:"Seguimiento detallado del progreso por temas"})]})})]})})]}),(0,t.jsx)("div",{className:"text-center mt-8",children:(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"Podr\xe1s modificar tu temario m\xe1s adelante desde la configuraci\xf3n"})})]})}):"predefinidos"===a?(0,t.jsx)(eh,{onSeleccionar:e=>{b(e),l("configuracion")},onVolver:()=>{l("seleccion"),o(null)}}):(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 p-4",children:(0,t.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,t.jsxs)("div",{className:"bg-white rounded-xl shadow-sm p-6",children:[(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("button",{onClick:()=>l("seleccion"),className:"text-blue-600 hover:text-blue-700 text-sm font-medium mb-4",children:"← Volver a la selecci\xf3n"}),(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"predefinido"===n?"Confirmar Temario Predefinido":"Configurar Temario Completo"})]}),"predefinido"===n&&p?(0,t.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-6 mb-6",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-green-900 mb-4",children:"Temario Seleccionado"}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-sm font-medium text-green-800",children:"Nombre:"}),(0,t.jsx)("p",{className:"text-green-700",children:p.nombre})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-sm font-medium text-green-800",children:"Cuerpo:"}),(0,t.jsx)("p",{className:"text-green-700",children:p.cuerpo})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-sm font-medium text-green-800",children:"Descripci\xf3n:"}),(0,t.jsx)("p",{className:"text-green-700",children:p.descripcion})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-sm font-medium text-green-800",children:"Total de temas:"}),(0,t.jsxs)("p",{className:"text-green-700",children:[p.temas.length," temas"]})]})]})]}):(0,t.jsxs)("div",{className:"space-y-4 mb-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"titulo",className:"block text-sm font-medium text-gray-700 mb-1",children:"T\xedtulo del temario *"}),(0,t.jsx)("input",{type:"text",id:"titulo",value:c,onChange:e=>m(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Ej: Oposiciones Auxiliar Administrativo 2024"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"descripcion",className:"block text-sm font-medium text-gray-700 mb-1",children:"Descripci\xf3n (opcional)"}),(0,t.jsx)("textarea",{id:"descripcion",value:u,onChange:e=>x(e.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Describe brevemente tu temario..."})]})]}),"predefinido"!==n&&(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Temas"}),(0,t.jsxs)("button",{onClick:()=>{let e=h.length+1;g([...h,{numero:e,titulo:"",descripcion:""}])},className:"bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded-md text-sm flex items-center",children:[(0,t.jsx)(i.GGD,{className:"w-4 h-4 mr-1"}),"A\xf1adir tema"]})]}),(0,t.jsx)("div",{className:"space-y-3",children:h.map((e,s)=>(0,t.jsxs)("div",{className:"flex items-start space-x-3 p-3 border border-gray-200 rounded-lg",children:[(0,t.jsxs)("div",{className:"w-16",children:[(0,t.jsx)("label",{className:"block text-xs text-gray-500 mb-1",children:"Tema"}),(0,t.jsx)("input",{type:"number",value:e.numero,onChange:e=>v(s,"numero",parseInt(e.target.value)||1),className:"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500",min:"1"})]}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("label",{className:"block text-xs text-gray-500 mb-1",children:"T\xedtulo *"}),(0,t.jsx)("input",{type:"text",value:e.titulo,onChange:e=>v(s,"titulo",e.target.value),className:"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500",placeholder:"T\xedtulo del tema"})]}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("label",{className:"block text-xs text-gray-500 mb-1",children:"Descripci\xf3n"}),(0,t.jsx)("input",{type:"text",value:e.descripcion,onChange:e=>v(s,"descripcion",e.target.value),className:"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500",placeholder:"Descripci\xf3n opcional"})]}),h.length>1&&(0,t.jsx)("button",{onClick:()=>N(s),className:"text-red-600 hover:text-red-700 p-1",title:"Eliminar tema",children:(0,t.jsx)(i.IXo,{className:"w-4 h-4"})})]},s))})]}),(0,t.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,t.jsx)("button",{onClick:()=>l("seleccion"),className:"px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors",disabled:f,children:"Cancelar"}),(0,t.jsx)("button",{onClick:E,disabled:f,className:"px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center",children:f?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Guardando..."]}):"Guardar temario"})]})]})})})},ep=e=>{let{onNavigateToTab:s}=e,{user:a}=(0,g.A)(),[l,n]=(0,r.useState)(null),[o,c]=(0,r.useState)([]),[d,m]=(0,r.useState)(!0),[u,x]=(0,r.useState)(!1);(0,r.useEffect)(()=>{h()},[]);let h=async()=>{m(!0);try{let[e,s,a]=await Promise.all([(0,ec.x)(),(0,ec.w)(5),(0,ed.yr)()]);n(e),c(s),a||x(!0)}catch(e){console.error("Error al cargar datos del dashboard:",e),n({totalDocumentos:0,totalColeccionesFlashcards:0,totalTests:0,totalFlashcards:0,flashcardsParaHoy:0,flashcardsNuevas:0,flashcardsAprendiendo:0,flashcardsRepasando:0,testsRealizados:0,porcentajeAcierto:0,coleccionesRecientes:[],testsRecientes:[]}),c([]),x(!0)}finally{m(!1)}};return u?(0,t.jsx)(eg,{onComplete:()=>{x(!1),h()}}):d?(0,t.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl p-6 text-white",children:[(0,t.jsxs)("h1",{className:"text-2xl font-bold mb-2",children:[(()=>{let e=new Date().getHours();return e<12?"Buenos d\xedas":e<18?"Buenas tardes":"Buenas noches"})(),", ",(()=>{var e;return(null==a||null==(e=a.email)?void 0:e.split("@")[0])||"Estudiante"})(),"! \uD83D\uDC4B"]}),(0,t.jsx)("p",{className:"text-blue-100",children:"\xbfListo para continuar con tu preparaci\xf3n? Aqu\xed tienes un resumen de tu progreso."})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,t.jsx)("div",{className:"bg-white rounded-lg p-4 shadow-sm border",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Documentos"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(null==l?void 0:l.totalDocumentos)||0})]}),(0,t.jsx)(i.jH2,{className:"h-8 w-8 text-blue-600"})]})}),(0,t.jsx)("div",{className:"bg-white rounded-lg p-4 shadow-sm border",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Colecciones"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(null==l?void 0:l.totalColeccionesFlashcards)||0})]}),(0,t.jsx)(i.H9b,{className:"h-8 w-8 text-emerald-600"})]})}),(0,t.jsx)("div",{className:"bg-white rounded-lg p-4 shadow-sm border",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Tests"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(null==l?void 0:l.totalTests)||0})]}),(0,t.jsx)(i.NLe,{className:"h-8 w-8 text-pink-600"})]})}),(0,t.jsx)("div",{className:"bg-white rounded-lg p-4 shadow-sm border",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Flashcards"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(null==l?void 0:l.totalFlashcards)||0})]}),(0,t.jsx)(i.x_j,{className:"h-8 w-8 text-orange-600"})]})})]}),(0,t.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-sm border",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,t.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:"Estudio de Hoy"}),(0,t.jsx)(i.wIk,{className:"h-6 w-6 text-gray-400"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsx)("div",{className:"bg-orange-50 rounded-lg p-4 border border-orange-200",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-orange-800",children:"Para Repasar Hoy"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-orange-600",children:(null==l?void 0:l.flashcardsParaHoy)||0})]}),(0,t.jsx)(i.Ohp,{className:"h-6 w-6 text-orange-600"})]})}),(0,t.jsx)("div",{className:"bg-blue-50 rounded-lg p-4 border border-blue-200",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-blue-800",children:"Nuevas"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-blue-600",children:(null==l?void 0:l.flashcardsNuevas)||0})]}),(0,t.jsx)(i.D1A,{className:"h-6 w-6 text-blue-600"})]})}),(0,t.jsx)("div",{className:"bg-green-50 rounded-lg p-4 border border-green-200",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-green-800",children:"% Acierto Tests"}),(0,t.jsxs)("p",{className:"text-2xl font-bold text-green-600",children:[(null==l?void 0:l.porcentajeAcierto.toFixed(1))||0,"%"]})]}),(0,t.jsx)(i.ARf,{className:"h-6 w-6 text-green-600"})]})})]}),l&&l.flashcardsParaHoy>0&&(0,t.jsx)("div",{className:"mt-4",children:(0,t.jsxs)("button",{onClick:()=>s("misFlashcards"),className:"bg-orange-600 hover:bg-orange-700 text-white font-semibold py-2 px-4 rounded-lg flex items-center transition-colors",children:[(0,t.jsx)(i.aze,{className:"mr-2"}),"Comenzar Estudio"]})})]}),o.length>0&&(0,t.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-sm border",children:[(0,t.jsx)("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"Pr\xf3ximas Flashcards"}),(0,t.jsx)("div",{className:"space-y-3",children:o.slice(0,3).map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("p",{className:"font-medium text-gray-900 truncate",children:e.pregunta}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:e.coleccionTitulo})]}),(0,t.jsx)("div",{className:"text-right",children:(0,t.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat("nuevo"===e.estado?"bg-blue-100 text-blue-800":"aprendiendo"===e.estado?"bg-yellow-100 text-yellow-800":"repasando"===e.estado?"bg-orange-100 text-orange-800":"bg-green-100 text-green-800"),children:e.estado})})]},e.id))}),o.length>3&&(0,t.jsx)("button",{onClick:()=>s("misFlashcards"),className:"mt-3 text-blue-600 hover:text-blue-800 text-sm font-medium",children:"Ver todas las flashcards pendientes"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-sm border",children:[(0,t.jsx)("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"Colecciones Recientes"}),(0,t.jsx)("div",{className:"space-y-3",children:null==l?void 0:l.coleccionesRecientes.map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("p",{className:"font-medium text-gray-900",children:e.titulo}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:["Creada: ",new Date(e.fechaCreacion).toLocaleDateString("es-ES")]})]}),(0,t.jsx)("div",{className:"text-right",children:(0,t.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800",children:[e.paraHoy," para hoy"]})})]},e.id))}),(0,t.jsx)("button",{onClick:()=>s("misFlashcards"),className:"mt-3 text-emerald-600 hover:text-emerald-800 text-sm font-medium",children:"Ver todas las colecciones"})]}),(0,t.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-sm border",children:[(0,t.jsx)("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"Tests Recientes"}),(0,t.jsx)("div",{className:"space-y-3",children:null==l?void 0:l.testsRecientes.map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("p",{className:"font-medium text-gray-900",children:e.titulo}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:["Creado: ",new Date(e.fechaCreacion).toLocaleDateString("es-ES")]})]}),(0,t.jsx)("div",{className:"text-right",children:(0,t.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-pink-100 text-pink-800",children:[e.numeroPreguntas," preguntas"]})})]},e.id))}),(0,t.jsx)("button",{onClick:()=>s("misTests"),className:"mt-3 text-pink-600 hover:text-pink-800 text-sm font-medium",children:"Ver todos los tests"})]})]}),(0,t.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-sm border",children:[(0,t.jsx)("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"Acciones R\xe1pidas"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,t.jsxs)("button",{onClick:()=>s("preguntas"),className:"flex items-center p-4 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors border border-blue-200",children:[(0,t.jsx)(i.jH2,{className:"h-6 w-6 text-blue-600 mr-3"}),(0,t.jsx)("span",{className:"font-medium text-blue-900",children:"Hacer Preguntas"})]}),(0,t.jsxs)("button",{onClick:()=>s("flashcards"),className:"flex items-center p-4 bg-orange-50 hover:bg-orange-100 rounded-lg transition-colors border border-orange-200",children:[(0,t.jsx)(i.GGD,{className:"h-6 w-6 text-orange-600 mr-3"}),(0,t.jsx)("span",{className:"font-medium text-orange-900",children:"Crear Flashcards"})]}),(0,t.jsxs)("button",{onClick:()=>s("tests"),className:"flex items-center p-4 bg-indigo-50 hover:bg-indigo-100 rounded-lg transition-colors border border-indigo-200",children:[(0,t.jsx)(i.NLe,{className:"h-6 w-6 text-indigo-600 mr-3"}),(0,t.jsx)("span",{className:"font-medium text-indigo-900",children:"Generar Tests"})]}),(0,t.jsxs)("button",{onClick:()=>s("mapas"),className:"flex items-center p-4 bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors border border-purple-200",children:[(0,t.jsx)(i.s_k,{className:"h-6 w-6 text-purple-600 mr-3"}),(0,t.jsx)("span",{className:"font-medium text-purple-900",children:"Mapas Mentales"})]}),(0,t.jsxs)("button",{onClick:()=>s("planEstudios"),className:"flex items-center p-4 bg-teal-50 hover:bg-teal-100 rounded-lg transition-colors border border-teal-200",children:[(0,t.jsx)(i.wIk,{className:"h-6 w-6 text-teal-600 mr-3"}),(0,t.jsx)("span",{className:"font-medium text-teal-900",children:"Plan de Estudios"})]})]})]})]})};var eb=a(3792),ef=a(2643);let ej=[{key:"lunes",label:"Lunes"},{key:"martes",label:"Martes"},{key:"miercoles",label:"Mi\xe9rcoles"},{key:"jueves",label:"Jueves"},{key:"viernes",label:"Viernes"},{key:"sabado",label:"S\xe1bado"},{key:"domingo",label:"Domingo"}],ey=e=>{let{temario:s,onComplete:a,onCancel:l,isEditing:n=!1}=e,[o,c]=(0,r.useState)(1),[m,u]=(0,r.useState)([]),[x,h]=(0,r.useState)(!0),[g,p]=(0,r.useState)(!1),[b,f]=(0,r.useState)({tiempoDiarioPromedio:2,tiempoPorDia:{},fechaExamen:"",fechaExamenAproximada:"",familiaridadGeneral:3,estimacionesTemas:{},preferenciasHorario:[],frecuenciaRepasos:"semanal"}),j=(0,r.useCallback)(async()=>{h(!0);try{let e=await (0,ed.cm)(s.id);if(u(e),n){let e=await (0,eb.u9)(s.id);e&&f({tiempoDiarioPromedio:e.tiempo_diario_promedio||2,tiempoPorDia:e.tiempo_por_dia||{},fechaExamen:e.fecha_examen||"",fechaExamenAproximada:e.fecha_examen_aproximada||"",familiaridadGeneral:e.familiaridad_general||3,estimacionesTemas:{},preferenciasHorario:e.preferencias_horario||[],frecuenciaRepasos:e.frecuencia_repasos||"semanal"})}}catch(e){console.error("Error al cargar datos:",e),d.oR.error(n?"Error al cargar la planificaci\xf3n existente":"Error al cargar los temas del temario")}finally{h(!1)}},[s.id,n]);(0,r.useEffect)(()=>{j()},[j]);let y=(e,s)=>{f(a=>({...a,[e]:s}))},N=(e,s)=>{f(a=>({...a,tiempoPorDia:{...a.tiempoPorDia,[e]:s}}))},v=e=>{f(s=>({...s,preferenciasHorario:s.preferenciasHorario.includes(e)?s.preferenciasHorario.filter(s=>s!==e):[...s.preferenciasHorario,e]}))},w=async()=>{p(!0);try{if(!await (0,eb.Pk)(s.id,{tiempo_diario_promedio:b.tiempoDiarioPromedio,tiempo_por_dia:b.tiempoPorDia,fecha_examen:b.fechaExamen||void 0,fecha_examen_aproximada:b.fechaExamenAproximada||void 0,familiaridad_general:b.familiaridadGeneral,preferencias_horario:b.preferenciasHorario,frecuencia_repasos:b.frecuenciaRepasos}))throw Error("Error al guardar la planificaci\xf3n");d.oR.success(n?"\xa1Planificaci\xf3n actualizada exitosamente!":"\xa1Planificaci\xf3n configurada exitosamente!"),a()}catch(e){console.error("Error al finalizar asistente:",e),d.oR.error(n?"Error al actualizar la planificaci\xf3n":"Error al guardar la planificaci\xf3n")}finally{p(!1)}};if(x)return(0,t.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})});let E=()=>(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)(i.Ohp,{className:"w-12 h-12 text-blue-600 mx-auto mb-4"}),(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Disponibilidad de Tiempo"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Para empezar, \xbfcu\xe1nto tiempo REAL estimas que puedes dedicar al estudio cada d\xeda?"}),(0,t.jsx)("p",{className:"text-sm text-blue-600 mt-2",children:"S\xe9 realista. Considera tu trabajo, familia y otros compromisos."})]}),(0,t.jsxs)("div",{className:"bg-gray-50 rounded-lg p-6",children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"Tiempo promedio diario (horas)"}),(0,t.jsx)("input",{type:"number",min:"0.5",max:"12",step:"0.5",value:b.tiempoDiarioPromedio,onChange:e=>y("tiempoDiarioPromedio",parseFloat(e.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,t.jsxs)("div",{className:"bg-gray-50 rounded-lg p-6",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Tiempo espec\xedfico por d\xeda (opcional)"}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:ej.map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700",children:e.label}),(0,t.jsx)("input",{type:"number",min:"0",max:"12",step:"0.5",value:b.tiempoPorDia[e.key]||"",onChange:s=>N(e.key,parseFloat(s.target.value)||0),className:"w-20 px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"0"})]},e.key))})]})]}),k=()=>(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)(i.wIk,{className:"w-12 h-12 text-blue-600 mx-auto mb-4"}),(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Fecha del Examen"}),(0,t.jsx)("p",{className:"text-gray-600",children:"\xbfCu\xe1l es la fecha (aproximada o exacta) de tu pr\xf3xima convocatoria o examen principal?"}),(0,t.jsx)("p",{className:"text-sm text-blue-600 mt-2",children:"Esto nos ayudar\xe1 a distribuir el temario en el tiempo disponible."})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"bg-gray-50 rounded-lg p-6",children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"Fecha exacta del examen"}),(0,t.jsx)("input",{type:"date",value:b.fechaExamen,onChange:e=>y("fechaExamen",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,t.jsxs)("div",{className:"bg-gray-50 rounded-lg p-6",children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"O fecha aproximada"}),(0,t.jsxs)("select",{value:b.fechaExamenAproximada,onChange:e=>y("fechaExamenAproximada",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,t.jsx)("option",{value:"",children:"Selecciona una opci\xf3n"}),(0,t.jsx)("option",{value:"1-3_meses",children:"En 1-3 meses"}),(0,t.jsx)("option",{value:"3-6_meses",children:"En 3-6 meses"}),(0,t.jsx)("option",{value:"6-12_meses",children:"En 6-12 meses"}),(0,t.jsx)("option",{value:"mas_12_meses",children:"M\xe1s de 12 meses"}),(0,t.jsx)("option",{value:"primavera_2025",children:"Primavera 2025"}),(0,t.jsx)("option",{value:"verano_2025",children:"Verano 2025"}),(0,t.jsx)("option",{value:"otono_2025",children:"Oto\xf1o 2025"}),(0,t.jsx)("option",{value:"invierno_2025",children:"Invierno 2025"})]})]})]})]}),C=()=>(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)(i.x_j,{className:"w-12 h-12 text-blue-600 mx-auto mb-4"}),(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Familiaridad con el Temario"}),(0,t.jsx)("p",{className:"text-gray-600",children:"En una escala del 1 al 5, \xbfc\xf3mo describir\xedas tu familiaridad general actual con el conjunto del temario?"})]}),(0,t.jsx)("div",{className:"bg-gray-50 rounded-lg p-6",children:(0,t.jsx)("div",{className:"grid grid-cols-5 gap-4",children:[1,2,3,4,5].map(e=>(0,t.jsxs)("button",{onClick:()=>y("familiaridadGeneral",e),className:"p-4 rounded-lg border-2 transition-all ".concat(b.familiaridadGeneral===e?"border-blue-500 bg-blue-50 text-blue-700":"border-gray-200 hover:border-gray-300"),children:[(0,t.jsx)("div",{className:"text-2xl font-bold mb-2",children:e}),(0,t.jsxs)("div",{className:"text-xs",children:[1===e&&"Muy poco",2===e&&"Poco",3===e&&"Moderado",4===e&&"Bastante",5===e&&"Muy familiarizado"]})]},e))})})]}),S=()=>(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)(i.usP,{className:"w-12 h-12 text-blue-600 mx-auto mb-4"}),(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Preferencias de Estudio"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Configura tus preferencias de horario y frecuencia de repasos."})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"bg-gray-50 rounded-lg p-6",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Preferencias de Horario"}),(0,t.jsx)("div",{className:"space-y-2",children:["ma\xf1ana","tarde","noche"].map(e=>(0,t.jsxs)("label",{className:"flex items-center",children:[(0,t.jsx)("input",{type:"checkbox",checked:b.preferenciasHorario.includes(e),onChange:()=>v(e),className:"mr-3"}),(0,t.jsx)("span",{className:"capitalize",children:e})]},e))})]}),(0,t.jsxs)("div",{className:"bg-gray-50 rounded-lg p-6",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Frecuencia de Repasos"}),(0,t.jsxs)("select",{value:b.frecuenciaRepasos,onChange:e=>y("frecuenciaRepasos",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,t.jsx)("option",{value:"semanal",children:"Semanal"}),(0,t.jsx)("option",{value:"quincenal",children:"Quincenal"}),(0,t.jsx)("option",{value:"mensual",children:"Mensual"})]}),(0,t.jsx)("p",{className:"text-sm text-gray-600 mt-2",children:"Los repasos son fundamentales para consolidar el aprendizaje a largo plazo."})]})]})]});return(0,t.jsxs)("div",{className:"max-w-4xl mx-auto p-6",children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:n?"Modificar Planificaci\xf3n IA":"Asistente de Planificaci\xf3n IA"}),(0,t.jsxs)("span",{className:"text-sm text-gray-500",children:["Paso ",o," de 4"]})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:"".concat(o/4*100,"%")}})})]}),(0,t.jsx)("div",{className:"bg-white rounded-xl shadow-sm border p-8 mb-6",children:(()=>{switch(o){case 1:return E();case 2:return k();case 3:return C();case 4:return S();default:return null}})()}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("button",{onClick:l,className:"px-6 py-2 text-gray-600 hover:text-gray-800 transition-colors",children:"Cancelar"}),(0,t.jsxs)("div",{className:"flex space-x-3",children:[o>1&&(0,t.jsxs)("button",{onClick:()=>{o>1&&c(o-1)},className:"flex items-center px-6 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors",children:[(0,t.jsx)(i.kRp,{className:"w-4 h-4 mr-2"}),"Anterior"]}),o<4?(0,t.jsxs)("button",{onClick:()=>{o<4&&c(o+1)},className:"flex items-center px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:["Siguiente",(0,t.jsx)(i.dyV,{className:"w-4 h-4 ml-2"})]}):(0,t.jsxs)("button",{onClick:w,disabled:g,className:"flex items-center px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50",children:[g?(0,t.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}):(0,t.jsx)(i.YrT,{className:"w-4 h-4 mr-2"}),n?"Actualizar":"Finalizar"]})]})]})]})},eN=e=>{let{isOpen:s,onClose:a,temario:l,onSave:n}=e,[o,c]=(0,r.useState)(""),[m,u]=(0,r.useState)(""),[x,h]=(0,r.useState)(!1);(0,r.useEffect)(()=>{s&&l&&(c(l.titulo),u(l.descripcion||""))},[s,l]);let g=async()=>{let e;if(!o.trim())return void d.oR.error("El t\xedtulo del temario es obligatorio");h(!0);try{if(e=d.oR.loading("Actualizando temario..."),await (0,ed.Se)(l.id,o.trim(),m.trim())){d.oR.success("Temario actualizado exitosamente",{id:e});let s={...l,titulo:o.trim(),descripcion:m.trim(),actualizado_en:new Date().toISOString()};n(s),a()}else d.oR.error("Error al actualizar el temario",{id:e})}catch(s){console.error("Error al actualizar temario:",s),d.oR.error("Error al actualizar el temario",{id:e})}finally{h(!1)}},p=()=>{c(l.titulo),u(l.descripcion||""),a()};return s?(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Editar Temario"}),(0,t.jsx)("button",{onClick:p,className:"text-gray-400 hover:text-gray-600 transition-colors",disabled:x,children:(0,t.jsx)(i.yGN,{size:24})})]}),(0,t.jsxs)("div",{className:"p-6 space-y-4",onKeyDown:e=>{"Escape"===e.key?p():"Enter"===e.key&&e.ctrlKey&&g()},children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"titulo",className:"block text-sm font-medium text-gray-700 mb-2",children:"T\xedtulo del temario *"}),(0,t.jsx)("input",{type:"text",id:"titulo",value:o,onChange:e=>c(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Ej: Oposiciones Auxiliar Administrativo 2024",disabled:x,autoFocus:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"descripcion",className:"block text-sm font-medium text-gray-700 mb-2",children:"Descripci\xf3n (opcional)"}),(0,t.jsx)("textarea",{id:"descripcion",value:m,onChange:e=>u(e.target.value),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none",placeholder:"Describe brevemente tu temario...",disabled:x})]}),(0,t.jsx)("div",{className:"bg-blue-50 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("div",{className:"w-2 h-2 bg-blue-400 rounded-full mt-2"})}),(0,t.jsx)("div",{className:"ml-3",children:(0,t.jsxs)("p",{className:"text-sm text-blue-800",children:[(0,t.jsx)("strong",{children:"Nota:"})," Solo puedes editar el t\xedtulo y la descripci\xf3n del temario. Para modificar los temas, utiliza las opciones individuales de cada tema."]})})]})})]}),(0,t.jsxs)("div",{className:"flex items-center justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50",children:[(0,t.jsx)("button",{onClick:p,className:"px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors",disabled:x,children:"Cancelar"}),(0,t.jsx)("button",{onClick:g,disabled:x||!o.trim(),className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:x?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Guardando..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(i.Bc_,{className:"w-4 h-4 mr-2"}),"Guardar cambios"]})})]}),(0,t.jsx)("div",{className:"px-6 pb-4",children:(0,t.jsxs)("p",{className:"text-xs text-gray-500",children:[(0,t.jsx)("strong",{children:"Atajos:"})," Esc para cancelar • Ctrl+Enter para guardar"]})})]})}):null},ev=e=>{let{isOpen:s,onClose:a,tema:l,onSave:n}=e,[o,c]=(0,r.useState)(""),[m,u]=(0,r.useState)(""),[x,h]=(0,r.useState)(!1);(0,r.useEffect)(()=>{s&&l&&(c(l.titulo),u(l.descripcion||""))},[s,l]);let g=async()=>{let e;if(!o.trim())return void d.oR.error("El t\xedtulo del tema es obligatorio");h(!0);try{if(e=d.oR.loading("Actualizando tema..."),await (0,ed.oS)(l.id,o.trim(),m.trim())){d.oR.success("Tema actualizado exitosamente",{id:e});let s={...l,titulo:o.trim(),descripcion:m.trim(),actualizado_en:new Date().toISOString()};n(s),a()}else d.oR.error("Error al actualizar el tema",{id:e})}catch(s){console.error("Error al actualizar tema:",s),d.oR.error("Error al actualizar el tema",{id:e})}finally{h(!1)}},p=()=>{c(l.titulo),u(l.descripcion||""),a()};return s?(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold text-gray-900",children:["Editar Tema ",l.numero]}),(0,t.jsx)("button",{onClick:p,className:"text-gray-400 hover:text-gray-600 transition-colors",disabled:x,children:(0,t.jsx)(i.yGN,{size:24})})]}),(0,t.jsxs)("div",{className:"p-6 space-y-4",onKeyDown:e=>{"Escape"===e.key?p():"Enter"===e.key&&e.ctrlKey&&g()},children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"titulo",className:"block text-sm font-medium text-gray-700 mb-2",children:"T\xedtulo del tema *"}),(0,t.jsx)("input",{type:"text",id:"titulo",value:o,onChange:e=>c(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Ej: Introducci\xf3n al Derecho Administrativo",disabled:x,autoFocus:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"descripcion",className:"block text-sm font-medium text-gray-700 mb-2",children:"Descripci\xf3n (opcional)"}),(0,t.jsx)("textarea",{id:"descripcion",value:m,onChange:e=>u(e.target.value),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none",placeholder:"Describe brevemente el contenido del tema...",disabled:x})]}),(0,t.jsx)("div",{className:"bg-blue-50 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("div",{className:"w-2 h-2 bg-blue-400 rounded-full mt-2"})}),(0,t.jsx)("div",{className:"ml-3",children:(0,t.jsxs)("p",{className:"text-sm text-blue-800",children:[(0,t.jsx)("strong",{children:"Nota:"})," El n\xfamero del tema y su estado de completado no se pueden modificar desde aqu\xed. Solo puedes editar el t\xedtulo y la descripci\xf3n."]})})]})})]}),(0,t.jsxs)("div",{className:"flex items-center justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50",children:[(0,t.jsx)("button",{onClick:p,className:"px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors",disabled:x,children:"Cancelar"}),(0,t.jsx)("button",{onClick:g,disabled:x||!o.trim(),className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:x?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Guardando..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(i.Bc_,{className:"w-4 h-4 mr-2"}),"Guardar cambios"]})})]}),(0,t.jsx)("div",{className:"px-6 pb-4",children:(0,t.jsxs)("p",{className:"text-xs text-gray-500",children:[(0,t.jsx)("strong",{children:"Atajos:"})," Esc para cancelar • Ctrl+Enter para guardar"]})})]})}):null},ew=e=>{let{tema:s,onEdit:a,onDelete:l,onToggleCompletado:n,isUpdating:o}=e,[c,m]=(0,r.useState)(!1),[u,x]=(0,r.useState)(!1),h=async()=>{let e;x(!0);try{e=d.oR.loading("Eliminando tema..."),await (0,ed.B$)(s.id)?(d.oR.success("Tema eliminado exitosamente",{id:e}),l(s.id)):d.oR.error("Error al eliminar el tema",{id:e})}catch(s){console.error("Error al eliminar tema:",s),d.oR.error("Error al eliminar el tema",{id:e})}finally{x(!1),m(!1)}};return c?(0,t.jsxs)("div",{className:"flex items-center space-x-2 bg-red-50 border border-red-200 rounded-lg p-3",children:[(0,t.jsx)(i.eHT,{className:"w-4 h-4 text-red-600 flex-shrink-0"}),(0,t.jsx)("div",{className:"flex-1 min-w-0",children:(0,t.jsxs)("p",{className:"text-sm text-red-800",children:['\xbfEliminar tema "',s.titulo,'"?']})}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("button",{onClick:()=>{m(!1)},className:"px-2 py-1 text-xs text-gray-600 bg-white border border-gray-300 rounded hover:bg-gray-50 transition-colors",disabled:u,children:"Cancelar"}),(0,t.jsx)("button",{onClick:h,disabled:u,className:"px-2 py-1 text-xs text-white bg-red-600 rounded hover:bg-red-700 transition-colors disabled:opacity-50 flex items-center",children:u?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-3 w-3 border-b border-white mr-1"}),"Eliminando..."]}):"Eliminar"})]})]}):(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("button",{onClick:()=>{a(s)},className:"p-2 text-gray-400 hover:text-blue-600 rounded-lg hover:bg-blue-50 transition-colors",title:"Editar tema",disabled:o,children:(0,t.jsx)(i.SG1,{className:"w-4 h-4"})}),(0,t.jsx)("button",{onClick:()=>{m(!0)},className:"p-2 text-gray-400 hover:text-red-600 rounded-lg hover:bg-red-50 transition-colors",title:"Eliminar tema",disabled:o,children:(0,t.jsx)(i.IXo,{className:"w-4 h-4"})}),(0,t.jsxs)("button",{onClick:()=>{n(s.id,s.completado)},disabled:o,className:"flex items-center px-3 py-1.5 rounded-md text-sm font-medium transition-colors ".concat(s.completado?"bg-gray-100 text-gray-700 hover:bg-gray-200":"bg-green-100 text-green-700 hover:bg-green-200"," disabled:opacity-50 disabled:cursor-not-allowed"),children:[o?(0,t.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-1"}):s.completado?(0,t.jsx)(i.Ohp,{className:"w-4 h-4 mr-1"}):(0,t.jsx)(i.YrT,{className:"w-4 h-4 mr-1"}),s.completado?"Marcar pendiente":"Marcar completado"]})]})},eE=()=>{let[e,s]=(0,r.useState)(null),[a,l]=(0,r.useState)([]),[n,o]=(0,r.useState)(null),[c,m]=(0,r.useState)(!0),[u,x]=(0,r.useState)(null),[h,g]=(0,r.useState)(!1),[p,b]=(0,r.useState)(!1),[f,j]=(0,r.useState)(!1),[y,N]=(0,r.useState)(!1),[v,w]=(0,r.useState)(null),[E,k]=(0,r.useState)(!1),[C,S]=(0,r.useState)(!1);(0,r.useEffect)(()=>{T()},[]);let T=async()=>{m(!0);try{let e=await (0,ed.jg)();if(e){s(e);let[a,t,r]=await Promise.all([(0,ed.cm)(e.id),(0,ed.Il)(e.id),(0,eb.vD)(e.id)]);l(a),o(t),g(r)}}catch(e){console.error("Error al cargar datos del temario:",e),d.oR.error("Error al cargar el temario")}finally{m(!1)}},A=async(s,t)=>{x(s);try{if(await (0,ed.cN)(s,!t)){if(l(a.map(e=>e.id===s?{...e,completado:!t,fecha_completado:t?void 0:new Date().toISOString()}:e)),e){let s=await (0,ed.Il)(e.id);o(s)}d.oR.success(t?"Tema marcado como pendiente":"Tema marcado como completado")}else d.oR.error("Error al actualizar el estado del tema")}catch(e){console.error("Error al actualizar tema:",e),d.oR.error("Error al actualizar el tema")}finally{x(null)}},R=e=>new Date(e).toLocaleDateString("es-ES",{day:"2-digit",month:"2-digit",year:"numeric"}),_=e=>{w(e),N(!0)},z=async s=>{l(a.filter(e=>e.id!==s)),e&&o(await (0,ed.Il)(e.id))},P=async()=>{let a;if(e){S(!0);try{a=d.oR.loading("Eliminando temario y desactivando plan de estudios...");let{user:t}=await (0,D.iF)();t&&await ef.N.from("planificacion_usuario").delete().eq("user_id",t.id).eq("temario_id",e.id),await ef.N.from("planes_estudios").update({activo:!1}).eq("temario_id",e.id),await (0,ed.xv)(e.id)?(d.oR.success("Temario eliminado y plan de estudios desactivado exitosamente",{id:a}),s(null),l([]),o(null),g(!1)):d.oR.error("Error al eliminar el temario",{id:a})}catch(e){console.error("Error al eliminar temario:",e),d.oR.error("Error al eliminar el temario",{id:a})}finally{S(!1),k(!1)}}};return c?(0,t.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):e?p?(0,t.jsx)(ey,{temario:e,onComplete:()=>{b(!1),g(!0),T()},onCancel:()=>{b(!1)},isEditing:h}):(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-sm border",children:[(0,t.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:e.titulo}),e.descripcion&&(0,t.jsx)("p",{className:"text-gray-600",children:e.descripcion}),(0,t.jsxs)("div",{className:"flex items-center mt-2 space-x-4",children:[(0,t.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat("completo"===e.tipo?"bg-green-100 text-green-800":"bg-orange-100 text-orange-800"),children:"completo"===e.tipo?"Temario Completo":"Temas Sueltos"}),(0,t.jsxs)("span",{className:"text-sm text-gray-500",children:["Creado el ",R(e.creado_en)]})]})]}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)("button",{onClick:()=>{j(!0)},className:"p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors",title:"Editar temario",children:(0,t.jsx)(i.SG1,{className:"w-5 h-5"})}),(0,t.jsx)("button",{onClick:()=>{k(!0)},className:"p-2 text-gray-400 hover:text-red-600 rounded-lg hover:bg-red-50 transition-colors",title:"Eliminar temario",children:(0,t.jsx)(i.IXo,{className:"w-5 h-5"})})]})]}),n&&(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsx)("div",{className:"bg-blue-50 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(i.H9b,{className:"w-5 h-5 text-blue-600 mr-2"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-blue-800",children:"Total Temas"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-blue-600",children:n.totalTemas})]})]})}),(0,t.jsx)("div",{className:"bg-green-50 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(i.YrT,{className:"w-5 h-5 text-green-600 mr-2"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-green-800",children:"Completados"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-green-600",children:n.temasCompletados})]})]})}),(0,t.jsx)("div",{className:"bg-purple-50 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(i.ARf,{className:"w-5 h-5 text-purple-600 mr-2"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-purple-800",children:"Progreso"}),(0,t.jsxs)("p",{className:"text-2xl font-bold text-purple-600",children:[n.porcentajeCompletado.toFixed(1),"%"]})]})]})})]}),n&&(0,t.jsxs)("div",{className:"mt-4",children:[(0,t.jsxs)("div",{className:"flex justify-between text-sm text-gray-600 mb-1",children:[(0,t.jsx)("span",{children:"Progreso del temario"}),(0,t.jsxs)("span",{children:[n.porcentajeCompletado.toFixed(1),"%"]})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:"".concat(n.porcentajeCompletado,"%")}})})]})]}),"completo"===e.tipo&&(0,t.jsx)("div",{className:"border rounded-xl p-6 ".concat(h?"bg-green-50 border-green-200":"bg-blue-50 border-blue-200"),children:(0,t.jsxs)("div",{className:"flex items-start justify-between",children:[(0,t.jsxs)("div",{className:"flex items-start",children:[h?(0,t.jsx)(i.YrT,{className:"w-6 h-6 text-green-600 mr-3 flex-shrink-0 mt-0.5"}):(0,t.jsx)(i.FrA,{className:"w-6 h-6 text-blue-600 mr-3 flex-shrink-0 mt-0.5"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-medium mb-2 ".concat(h?"text-green-900":"text-blue-900"),children:h?"Planificaci\xf3n Configurada":"Planificaci\xf3n Inteligente con IA"}),(0,t.jsx)("p",{className:"text-sm mb-3 ".concat(h?"text-green-800":"text-blue-800"),children:h?"Ya tienes configurada tu planificaci\xf3n de estudio personalizada. Pronto podr\xe1s ver tu calendario y seguimiento.":"Configura tu planificaci\xf3n personalizada con nuestro asistente inteligente:"}),!h&&(0,t.jsxs)("ul",{className:"text-blue-800 text-sm space-y-1",children:[(0,t.jsx)("li",{children:"• Planificaci\xf3n autom\xe1tica de estudio con IA"}),(0,t.jsx)("li",{children:"• Seguimiento de progreso personalizado"}),(0,t.jsx)("li",{children:"• Recomendaciones de orden de estudio"}),(0,t.jsx)("li",{children:"• Estimaci\xf3n de tiempos por tema"})]})]})]}),(0,t.jsx)("div",{className:"flex gap-2",children:h?(0,t.jsxs)("button",{onClick:()=>{b(!0)},className:"flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors",children:[(0,t.jsx)(i.VSk,{className:"w-4 h-4 mr-2"}),"Modificar Planificaci\xf3n"]}):(0,t.jsxs)("button",{onClick:()=>{b(!0)},className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,t.jsx)(i.FrA,{className:"w-4 h-4 mr-2"}),"Configurar Planificaci\xf3n"]})})]})}),(0,t.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border",children:[(0,t.jsxs)("div",{className:"p-6 border-b border-gray-200",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Temas del Temario"}),(0,t.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"Marca los temas como completados seg\xfan vayas estudi\xe1ndolos"})]}),(0,t.jsx)("div",{className:"divide-y divide-gray-200",children:a.map(e=>(0,t.jsx)("div",{className:"p-6 hover:bg-gray-50 transition-colors",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-start space-x-4 flex-1",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("span",{className:"inline-flex items-center justify-center w-8 h-8 rounded-full bg-blue-100 text-blue-800 text-sm font-medium",children:e.numero})}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsx)("h3",{className:"text-lg font-medium ".concat(e.completado?"text-gray-500 line-through":"text-gray-900"),children:e.titulo}),e.descripcion&&(0,t.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:e.descripcion}),e.fecha_completado&&(0,t.jsxs)("div",{className:"flex items-center mt-2 text-sm text-green-600",children:[(0,t.jsx)(i.YrT,{className:"w-4 h-4 mr-1"}),"Completado el ",R(e.fecha_completado)]})]})]}),(0,t.jsx)("div",{className:"flex items-center space-x-3",children:(0,t.jsx)(ew,{tema:e,onEdit:_,onDelete:z,onToggleCompletado:A,isUpdating:u===e.id})})]})},e.id))})]}),e&&(0,t.jsx)(eN,{isOpen:f,onClose:()=>{j(!1)},temario:e,onSave:e=>{s(e),j(!1)}}),v&&(0,t.jsx)(ev,{isOpen:y,onClose:()=>{N(!1),w(null)},tema:v,onSave:e=>{l(a.map(s=>s.id===e.id?e:s)),N(!1),w(null)}}),E&&(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-md w-full mx-4",children:[(0,t.jsxs)("div",{className:"flex items-center mb-4",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)(i.IXo,{className:"w-6 h-6 text-red-600"})}),(0,t.jsx)("div",{className:"ml-3",children:(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Eliminar Temario"})})]}),(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:"\xbfEst\xe1s seguro de que quieres eliminar este temario? Esta acci\xf3n:"}),(0,t.jsxs)("ul",{className:"text-sm text-red-600 space-y-1 ml-4",children:[(0,t.jsxs)("li",{children:['• Eliminar\xe1 permanentemente el temario "',(0,t.jsx)("strong",{children:null==e?void 0:e.titulo}),'"']}),(0,t.jsx)("li",{children:"• Eliminar\xe1 todos los temas asociados"}),(0,t.jsx)("li",{children:"• Eliminar\xe1 la planificaci\xf3n de estudios configurada"}),(0,t.jsx)("li",{children:"• Desactivar\xe1 los planes de estudios generados (se conservan en el historial)"})]}),(0,t.jsx)("p",{className:"text-sm text-gray-600 mt-3 font-medium",children:"Esta acci\xf3n no se puede deshacer."})]}),(0,t.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,t.jsx)("button",{onClick:()=>{k(!1)},disabled:C,className:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 disabled:opacity-50",children:"Cancelar"}),(0,t.jsx)("button",{onClick:P,disabled:C,className:"px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 flex items-center",children:C?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Eliminando..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(i.IXo,{className:"w-4 h-4 mr-2"}),"Eliminar Temario"]})})]})]})})]}):(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)(i.H9b,{className:"w-16 h-16 text-gray-400 mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No hay temario configurado"}),(0,t.jsx)("p",{className:"text-gray-500",children:"Configura tu temario desde el dashboard para comenzar."})]})};function ek(){let[e,s]=(0,r.useState)(null),[a,l]=(0,r.useState)(!1);return((0,r.useEffect)(()=>{let e=async()=>{let e={userAgent:navigator.userAgent,isMobile:/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent),hasLocalStorage:"undefined"!=typeof Storage,hasCookies:navigator.cookieEnabled,supabaseSession:!1,localStorageToken:!1,cookieToken:!1,timestamp:new Date().toISOString()};try{let{data:{session:s}}=await z.N.auth.getSession();e.supabaseSession=!!s}catch(e){console.warn("Error al verificar sesi\xf3n de Supabase:",e)}try{e.localStorageToken=!!localStorage.getItem("supabase.auth.token")}catch(e){console.warn("Error al verificar localStorage:",e)}try{e.cookieToken=document.cookie.includes("supabase.auth.token")}catch(e){console.warn("Error al verificar cookies:",e)}s(e)};e();let a=setInterval(e,5e3);return()=>clearInterval(a)},[]),e)?(0,t.jsxs)("div",{className:"fixed bottom-4 right-4 z-50",children:[(0,t.jsx)("button",{onClick:()=>l(!a),className:"bg-blue-500 text-white px-3 py-2 rounded-full text-sm shadow-lg hover:bg-blue-600 transition-colors",children:"\uD83D\uDC1B Debug"}),a&&(0,t.jsxs)("div",{className:"absolute bottom-12 right-0 bg-black bg-opacity-90 text-white p-4 rounded-lg shadow-xl max-w-sm text-xs",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,t.jsx)("h3",{className:"font-bold text-sm",children:"Debug Info"}),(0,t.jsx)("button",{onClick:()=>l(!1),className:"text-gray-300 hover:text-white",children:"✕"})]}),(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"M\xf3vil:"}),(0,t.jsx)("span",{className:e.isMobile?"text-green-400":"text-red-400",children:e.isMobile?"S\xed":"No"})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"LocalStorage:"}),(0,t.jsx)("span",{className:e.hasLocalStorage?"text-green-400":"text-red-400",children:e.hasLocalStorage?"S\xed":"No"})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"Cookies:"}),(0,t.jsx)("span",{className:e.hasCookies?"text-green-400":"text-red-400",children:e.hasCookies?"S\xed":"No"})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"Sesi\xf3n Supabase:"}),(0,t.jsx)("span",{className:e.supabaseSession?"text-green-400":"text-red-400",children:e.supabaseSession?"S\xed":"No"})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"Token localStorage:"}),(0,t.jsx)("span",{className:e.localStorageToken?"text-green-400":"text-red-400",children:e.localStorageToken?"S\xed":"No"})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"Token Cookie:"}),(0,t.jsx)("span",{className:e.cookieToken?"text-green-400":"text-red-400",children:e.cookieToken?"S\xed":"No"})]}),(0,t.jsx)("div",{className:"mt-2 pt-2 border-t border-gray-600",children:(0,t.jsxs)("div",{className:"text-gray-300 break-all",children:[(0,t.jsx)("strong",{children:"User Agent:"}),(0,t.jsx)("br",{}),e.userAgent.substring(0,100),"..."]})}),(0,t.jsxs)("div",{className:"text-gray-400 text-xs mt-1",children:["Actualizado: ",new Date(e.timestamp).toLocaleTimeString()]})]})]})]}):null}var eC=a(5759),eS=a(9509);function eT(){let[e,s]=(0,r.useState)(!1),[a,l]=(0,r.useState)({supabaseConnection:!1,userAuthenticated:!1,geminiApiKey:!1,conversationsCount:0,documentsCount:0,lastError:null}),[n,i]=(0,r.useState)(!1),{user:o,session:c}=(0,g.A)(),d=async()=>{try{i(!0);let e=await (0,eC.Yp)("Conversaci\xf3n de prueba",!1);e?(l(s=>({...s,lastError:"✅ Conversaci\xf3n de prueba creada exitosamente: ".concat(e)})),m()):l(e=>({...e,lastError:"❌ No se pudo crear la conversaci\xf3n de prueba"}))}catch(e){l(s=>({...s,lastError:"❌ Error al crear conversaci\xf3n de prueba: ".concat(e instanceof Error?e.message:"Unknown error")}))}finally{i(!1)}},m=(0,r.useCallback)(async()=>{i(!0);let e={supabaseConnection:!1,userAuthenticated:!1,geminiApiKey:!1,conversationsCount:0,documentsCount:0,lastError:null};try{e.userAuthenticated=!!o&&!!c;try{let{data:s,error:a}=await z.N.from("conversaciones").select("id").limit(1);if(a)e.lastError="Supabase error: ".concat(a.message);else{e.supabaseConnection=!0;let{count:s,error:a}=await z.N.from("conversaciones").select("*",{count:"exact",head:!0}).eq("user_id",o&&o.id?o.id:"");a?(e.conversationsCount=0,"406"===a.code?e.lastError="Error 406 al contar conversaciones - esto es normal para usuarios nuevos":e.lastError="Error al contar conversaciones: ".concat(a.message)):e.conversationsCount=s||0}}catch(s){e.lastError="Supabase connection error: ".concat(s instanceof Error?s.message:"Unknown error")}try{let{data:s,error:a}=await z.N.from("documentos").select("count",{count:"exact",head:!0});a||(e.documentsCount=(null==s?void 0:s.length)||0)}catch(e){console.error("Error checking documents:",e)}e.geminiApiKey=!!eS.env.OPENAI_API_KEY}catch(s){e.lastError="General error: ".concat(s instanceof Error?s.message:"Unknown error")}l(e),i(!1)},[o,c]);return((0,r.useEffect)(()=>{e&&m()},[e,m]),e)?(0,t.jsxs)("div",{className:"fixed bottom-4 right-4 bg-white border border-gray-300 rounded-lg shadow-lg p-4 w-80 z-50",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-3",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold",children:"Diagn\xf3stico del Sistema"}),(0,t.jsx)("button",{onClick:()=>s(!1),className:"text-gray-500 hover:text-gray-700",children:"✕"})]}),n?(0,t.jsxs)("div",{className:"text-center py-4",children:[(0,t.jsx)("div",{className:"animate-spin h-6 w-6 border-2 border-blue-500 border-t-transparent rounded-full mx-auto"}),(0,t.jsx)("p",{className:"mt-2 text-sm text-gray-600",children:"Ejecutando diagn\xf3sticos..."})]}):(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm",children:"Usuario autenticado:"}),(0,t.jsx)("span",{className:"text-sm font-semibold ".concat(a.userAuthenticated?"text-green-600":"text-red-600"),children:a.userAuthenticated?"✓":"✗"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm",children:"Conexi\xf3n Supabase:"}),(0,t.jsx)("span",{className:"text-sm font-semibold ".concat(a.supabaseConnection?"text-green-600":"text-red-600"),children:a.supabaseConnection?"✓":"✗"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm",children:"API Key Gemini:"}),(0,t.jsx)("span",{className:"text-sm font-semibold ".concat(a.geminiApiKey?"text-green-600":"text-red-600"),children:a.geminiApiKey?"✓":"✗"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm",children:"Conversaciones:"}),(0,t.jsx)("span",{className:"text-sm font-semibold text-blue-600",children:a.conversationsCount})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm",children:"Documentos:"}),(0,t.jsx)("span",{className:"text-sm font-semibold text-blue-600",children:a.documentsCount})]}),a.lastError&&(0,t.jsxs)("div",{className:"mt-3 p-2 bg-red-50 border border-red-200 rounded",children:[(0,t.jsx)("p",{className:"text-xs text-red-700 font-semibold",children:"\xdaltimo error:"}),(0,t.jsx)("p",{className:"text-xs text-red-600 mt-1",children:a.lastError})]}),(0,t.jsxs)("div",{className:"mt-4 space-y-2",children:[(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)("button",{onClick:m,className:"flex-1 bg-blue-500 hover:bg-blue-600 text-white text-xs py-2 px-3 rounded",disabled:n,children:"Actualizar"}),(0,t.jsx)("button",{onClick:()=>{console.log("Diagn\xf3sticos completos:",a),console.log("Usuario:",o),console.log("Sesi\xf3n:",c)},className:"flex-1 bg-gray-500 hover:bg-gray-600 text-white text-xs py-2 px-3 rounded",children:"Log Info"})]}),(0,t.jsx)("button",{onClick:d,className:"w-full bg-green-500 hover:bg-green-600 text-white text-xs py-2 px-3 rounded",disabled:n||!a.userAuthenticated,children:"Probar Crear Conversaci\xf3n"})]})]})]}):(0,t.jsx)("button",{onClick:()=>s(!0),className:"fixed bottom-4 right-4 bg-red-500 hover:bg-red-600 text-white p-2 rounded-full shadow-lg z-50",title:"Abrir panel de diagn\xf3stico",children:"\uD83D\uDD27"})}let eA=e=>{let{activeTab:s,onTabChange:a,children:l}=e,[n,o]=(0,r.useState)([]),[c,d]=(0,r.useState)(!1);(0,r.useEffect)(()=>{let e=localStorage.getItem("sidebarCollapsed");e&&d(JSON.parse(e))},[]),(0,r.useEffect)(()=>{localStorage.setItem("sidebarCollapsed",JSON.stringify(c))},[c]);let m=[{id:"dashboard",label:"Principal",icon:(0,t.jsx)(i.jTZ,{}),color:"bg-gradient-to-r from-blue-600 to-purple-600"},{id:"mi-temario-group",label:"Mi Temario",icon:(0,t.jsx)(i.H9b,{}),color:"bg-green-600",isGroup:!0,children:[{id:"temario",label:"Mi Temario",icon:(0,t.jsx)(i.H9b,{}),color:"bg-green-600"},{id:"gestionar",label:"Gestionar Documentos",icon:(0,t.jsx)(i.VSk,{}),color:"bg-gray-600"}]},{id:"planEstudios",label:"Mi Plan de Estudios",icon:(0,t.jsx)(i.wIk,{}),color:"bg-teal-600"},{id:"preguntas",label:"Habla con tu preparador",icon:(0,t.jsx)(i.mEP,{}),color:"bg-blue-600"},{id:"herramientas-group",label:"Herramientas de estudio",icon:(0,t.jsx)(i.x_j,{}),color:"bg-purple-600",isGroup:!0,children:[{id:"flashcards-group",label:"Flashcards",icon:(0,t.jsx)(i.lZI,{}),color:"bg-orange-500",isGroup:!0,children:[{id:"flashcards",label:"Generador de Flashcards",icon:(0,t.jsx)(i.GGD,{}),color:"bg-orange-500"},{id:"misFlashcards",label:"Mis Flashcards",icon:(0,t.jsx)(i.lZI,{}),color:"bg-emerald-600"}]},{id:"tests-group",label:"Tests",icon:(0,t.jsx)(i.NLe,{}),color:"bg-indigo-600",isGroup:!0,children:[{id:"tests",label:"Generador de Tests",icon:(0,t.jsx)(i.GGD,{}),color:"bg-indigo-600"},{id:"misTests",label:"Mis Tests",icon:(0,t.jsx)(i.NLe,{}),color:"bg-pink-600"}]}]},{id:"resumenes",label:"Res\xfamenes",icon:(0,t.jsx)(i.D1A,{}),color:"bg-green-600"},{id:"mapas",label:"Mapas Mentales",icon:(0,t.jsx)(i.s_k,{}),color:"bg-purple-600"}],u=e=>{o(s=>s.includes(e)?s.filter(s=>s!==e):[...s,e])},x=e=>n.includes(e),h=()=>{o([])},g=function(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,l=e.children&&e.children.length>0,n=s===e.id&&!e.isGroup,o=x(e.id);return c&&r>0?null:(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex items-center ".concat(c?"justify-center tooltip-hover":"justify-between"," px-").concat(c?"2":2+2*r," py-2 rounded-lg transition-all duration-300 cursor-pointer ").concat(n&&!l?"text-white ".concat(e.color," shadow-md"):"text-gray-600 hover:bg-gray-100 hover:text-gray-800"),onClick:()=>{c&&l?(d(!1),u(e.id)):l||e.isGroup?u(e.id):(h(),a(e.id))},title:c?e.label:void 0,"data-tooltip":c?e.label:void 0,"aria-label":e.label,children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("span",{className:"".concat(c?"":"mr-2"," text-sm"),children:e.icon}),!c&&(0,t.jsx)("span",{className:"text-sm font-medium",children:e.label})]}),l&&!c&&(0,t.jsx)("span",{className:"text-xs",children:o?(0,t.jsx)(i.fK4,{}):(0,t.jsx)(i.fOo,{})})]}),l&&o&&!c&&(0,t.jsx)("div",{className:"ml-2 mt-1 space-y-1",children:e.children.map(e=>g(e,r+1))})]},"".concat(e.id,"-").concat(r))};return(0,t.jsxs)("div",{className:"".concat(c?"w-16":"w-80"," flex-shrink-0 space-y-4 sidebar-transition"),children:[(0,t.jsxs)("div",{className:"bg-white rounded-xl shadow-sm p-4 sticky top-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[!c&&(0,t.jsx)("h2",{className:"text-sm font-semibold text-gray-500 uppercase tracking-wider px-2",children:"Men\xfa de Estudio"}),(0,t.jsx)("button",{onClick:()=>{d(e=>{let s=!e;return s&&o([]),s})},className:"p-1 rounded-lg hover:bg-gray-100 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",title:c?"Expandir men\xfa":"Colapsar men\xfa","aria-label":c?"Expandir men\xfa de navegaci\xf3n":"Colapsar men\xfa de navegaci\xf3n","aria-expanded":!c,children:c?(0,t.jsx)(i.ND1,{className:"w-4 h-4 text-gray-600"}):(0,t.jsx)(i.pM3,{className:"w-4 h-4 text-gray-600"})})]}),(0,t.jsx)("nav",{className:"space-y-1",children:m.map(e=>g(e))})]}),!c&&l]})},eR=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{tasks:s}=(0,w.M)(),[a,t]=(0,r.useState)(null),[l,n]=(0,r.useState)(!1);return(0,r.useEffect)(()=>{let r=s.filter(e=>"plan-estudios"===e.type);n(r.some(e=>"pending"===e.status||"processing"===e.status));let l=r.filter(e=>"completed"===e.status&&e.result).sort((e,s)=>new Date(s.createdAt).getTime()-new Date(e.createdAt).getTime());if(l.length>0){let s=l[0];t(s.result),e.onResult&&s.result!==a&&e.onResult(s.result)}let i=r.filter(e=>"error"===e.status).sort((e,s)=>new Date(s.createdAt).getTime()-new Date(e.createdAt).getTime());if(i.length>0&&e.onError){let s=i[0];e.onError(s.error||"Error desconocido")}},[s,e,a]),{latestResult:a,isLoading:l,hasResults:!!a}};var e_=a(2646),ez=a(572),eD=a(9509);class eP{static async logWebhookEvent(e){try{let s=e.success?"✅":"❌",a=new Date().toISOString();console.log("".concat(s," [WEBHOOK] ").concat(a),{eventType:e.eventType,eventId:e.eventId,success:e.success,processingTime:"".concat(e.processingTime,"ms"),message:e.message,...e.error&&{error:e.error},...e.data&&{data:e.data}}),await this.logToExternalService(e)}catch(e){console.error("Error logging webhook event:",e)}}static async logFeatureAccess(e,s,a,t){let r=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0,l=arguments.length>5?arguments[5]:void 0;try{console.log("".concat(a?"✅":"❌"," [FEATURE_ACCESS]"),{userId:e,feature:s,granted:a,plan:t,tokens:r,...l&&{reason:l}})}catch(e){console.error("Error logging feature access:",e)}}static async logPlanChange(e,s,a,t,r,l){try{console.log("\uD83D\uDD04 [PLAN_CHANGE]",{userId:e,oldPlan:s,newPlan:a,changedBy:t,reason:r,transactionId:l})}catch(e){console.error("Error logging plan change:",e)}}static async logCriticalError(e,s,a){try{let t={context:e,message:s.message,stack:s.stack,timestamp:new Date().toISOString(),additionalData:a};console.error("\uD83D\uDEA8 [CRITICAL_ERROR]",t),await this.sendCriticalAlert(t)}catch(e){console.error("Error logging critical error:",e)}}static logPerformanceMetrics(e,s,a,t){let r={operation:e,duration:"".concat(s,"ms"),success:a,timestamp:new Date().toISOString(),...t};console.log("\uD83D\uDCCA [PERFORMANCE]",r),this.sendMetrics(r)}static async logToExternalService(e){}static async sendCriticalAlert(e){}static sendMetrics(e){"true"===eD.env.ENABLE_METRICS_LOGGING&&console.log("\uD83D\uDCC8 [METRICS_EXPORT]",e)}static async getWebhookStats(){return arguments.length>0&&void 0!==arguments[0]&&arguments[0],{totalEvents:0,successRate:0,averageProcessingTime:0,errorsByType:{}}}}class eI{static async checkUserLimits(e){try{let{SupabaseAdminService:s}=await a.e(3883).then(a.bind(a,3883)),t=await s.getUserProfile(e);if(!t)return[];let r=[],l=await this.checkTokenLimits(t);l&&r.push(l);let n=await this.checkPlanLimits(t);return n&&r.push(n),r}catch(e){return console.error("Error checking user limits:",e),[]}}static async checkClientUserLimits(){try{let e=(0,z.U)(),{data:{user:s},error:a}=await e.auth.getUser();if(a||!s)return[];let{data:t,error:r}=await e.from("user_profiles").select("subscription_plan, payment_verified, monthly_token_limit, current_month_tokens, current_month, plan_expires_at").eq("user_id",s.id).single();if(r&&"PGRST116"!==r.code)return console.error("Error fetching profile for limits check:",r),[];if(!t)return[];let l=[],n=await this.checkTokenLimits(t);n&&l.push(n);let i=await this.checkPlanLimits(t);return i&&l.push(i),l}catch(e){return console.error("Error checking client user limits:",e),[]}}static async checkTokenLimits(e){let s=new Date().toISOString().slice(0,7)+"-01",a=e.current_month===s?e.current_month_tokens:0,t=a/e.monthly_token_limit*100,r="warning",l="",n=!1,i="";if(t>=100)r="exceeded",l="Has excedido tu l\xedmite mensual de tokens (".concat(a.toLocaleString(),"/").concat(e.monthly_token_limit.toLocaleString(),")"),n=!0,i="Actualiza tu plan para obtener m\xe1s tokens";else if(t>=90)r="limit_reached",l="Est\xe1s cerca de tu l\xedmite mensual de tokens (".concat(Math.round(t),"% usado)"),n=!0,i="Considera actualizar tu plan antes de alcanzar el l\xedmite";else{if(!(t>=75))return null;r="warning",l="Has usado ".concat(Math.round(t),"% de tus tokens mensuales"),n=!1,i="Monitorea tu uso para evitar alcanzar el l\xedmite"}let o=this.getUpgradeOptions(e.subscription_plan);return{type:"tokens",severity:r,current:a,limit:e.monthly_token_limit,percentage:Math.round(t),message:l,actionRequired:n,suggestedAction:i,upgradeOptions:o}}static async checkPlanLimits(e){if("free"!==e.subscription_plan&&!e.payment_verified)return{type:"plan",severity:"exceeded",current:0,limit:1,percentage:0,message:"Tu pago est\xe1 pendiente de verificaci\xf3n",actionRequired:!0,suggestedAction:"Completa el proceso de pago para activar tu plan",upgradeOptions:[]};if(e.plan_expires_at){let s=new Date(e.plan_expires_at),a=new Date,t=Math.ceil((s.getTime()-a.getTime())/864e5);if(t<=0)return{type:"plan",severity:"exceeded",current:0,limit:1,percentage:0,message:"Tu plan ha expirado",actionRequired:!0,suggestedAction:"Renueva tu suscripci\xf3n para continuar usando las funciones premium",upgradeOptions:this.getUpgradeOptions(e.subscription_plan)};if(t<=7)return{type:"plan",severity:"warning",current:t,limit:30,percentage:Math.round((30-t)/30*100),message:"Tu plan expira en ".concat(t," d\xeda").concat(1!==t?"s":""),actionRequired:!1,suggestedAction:"Renueva tu suscripci\xf3n para evitar la interrupci\xf3n del servicio",upgradeOptions:this.getUpgradeOptions(e.subscription_plan)}}return null}static getUpgradeOptions(e){let s=[];if("free"===e){let e=(0,p.IE)("usuario"),a=(0,p.IE)("pro");if(e){let a=e.limits.monthlyTokens||1e6;s.push({plan:"usuario",benefits:["Chat con preparador IA","".concat(a.toLocaleString()," tokens mensuales"),"Tests y flashcards ilimitados"],newLimit:a})}if(a){let e=a.limits.monthlyTokens||1e6;s.push({plan:"pro",benefits:["Todas las funciones del plan Usuario","Planificaci\xf3n de estudios con IA","Res\xfamenes A1 y A2","".concat(e.toLocaleString()," tokens mensuales")],newLimit:e})}}else if("usuario"===e){let e=(0,p.IE)("pro");if(e){let a=e.limits.monthlyTokens||1e6;s.push({plan:"pro",benefits:["Planificaci\xf3n de estudios con IA","Res\xfamenes A1 y A2","Funciones avanzadas","".concat(a.toLocaleString()," tokens mensuales")],newLimit:a})}}return s}static async createLimitNotification(e,s){let a={userId:e,type:"limit_".concat(s.type),severity:"exceeded"===s.severity?"error":"limit_reached"===s.severity?"warning":"info",title:this.getNotificationTitle(s),message:s.message,metadata:{limitType:s.type,current:s.current,limit:s.limit,percentage:s.percentage}};return s.actionRequired&&s.upgradeOptions&&s.upgradeOptions.length>0&&(a.actionUrl="/payment",a.actionText="Actualizar Plan"),await eP.logFeatureAccess(e,"limit_notification_".concat(s.type),!1,"system",0,"Limit notification: ".concat(s.severity)),a}static getNotificationTitle(e){switch(e.type){case"tokens":if("exceeded"===e.severity)return"L\xedmite de tokens excedido";if("limit_reached"===e.severity)return"L\xedmite de tokens casi alcanzado";return"Uso elevado de tokens";case"plan":if("exceeded"===e.severity)return"Plan expirado o pago pendiente";return"Plan pr\xf3ximo a expirar";default:return"L\xedmite alcanzado"}}static async isActionBlocked(e,s){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;try{let s=await this.checkUserLimits(e);if(a>0){let e=s.find(e=>"tokens"===e.type);if(e&&"exceeded"===e.severity)return{blocked:!0,reason:"L\xedmite mensual de tokens excedido",limitStatus:e};if(e&&e.current+a>e.limit)return{blocked:!0,reason:"Esta acci\xf3n requiere ".concat(a," tokens pero solo tienes ").concat(e.limit-e.current," disponibles"),limitStatus:e}}let t=s.find(e=>"plan"===e.type&&"exceeded"===e.severity);if(t)return{blocked:!0,reason:t.message,limitStatus:t};return{blocked:!1}}catch(e){return console.error("Error checking if action is blocked:",e),{blocked:!0,reason:"Error verificando l\xedmites"}}}static async isClientActionBlocked(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;try{let e=await this.checkClientUserLimits();if(s>0){let a=e.find(e=>"tokens"===e.type);if(a&&"exceeded"===a.severity)return{blocked:!0,reason:"L\xedmite mensual de tokens excedido",limitStatus:a};if(a&&a.current+s>a.limit)return{blocked:!0,reason:"Esta acci\xf3n requiere ".concat(s," tokens pero solo tienes ").concat(a.limit-a.current," disponibles"),limitStatus:a}}let a=e.find(e=>"plan"===e.type&&"exceeded"===e.severity);if(a)return{blocked:!0,reason:a.message,limitStatus:a};return{blocked:!1}}catch(e){return console.error("Error checking if client action is blocked:",e),{blocked:!0,reason:"Error verificando l\xedmites"}}}static async recordUsage(e,s,t){try{if(t>0){let{SupabaseAdminService:s}=await a.e(3883).then(a.bind(a,3883)),r=await s.getUserProfile(e);if(r){let a=new Date().toISOString().slice(0,7)+"-01",l=r.current_month===a?r.current_month_tokens:0;await s.upsertUserProfile({...r,current_month_tokens:l+t,current_month:a,updated_at:new Date().toISOString()}),console.log("✅ Tokens actualizados: +".concat(t," para usuario ").concat(e))}}await eP.logFeatureAccess(e,s,!0,"system",t,"Action completed successfully")}catch(e){console.error("Error recording usage:",e)}}static async recordClientUsage(e,s){try{let a=(0,z.U)(),{data:{user:t},error:r}=await a.auth.getUser();if(r||!t)return void console.warn("Cannot record usage: user not authenticated");if(s>0){let{data:e,error:r}=await a.from("user_profiles").select("subscription_plan, monthly_token_limit, current_month_tokens, current_month").eq("user_id",t.id).single();if(r)return void console.error("Error fetching profile for usage recording:",r);if(e){let r=new Date().toISOString().slice(0,7)+"-01",l=e.current_month===r?e.current_month_tokens:0,{error:n}=await a.from("user_profiles").update({current_month_tokens:l+s,current_month:r,updated_at:new Date().toISOString()}).eq("user_id",t.id);n?console.error("Error updating token usage:",n):console.log("✅ Tokens actualizados: +".concat(s," para usuario ").concat(t.id))}}await eP.logFeatureAccess(t.id,e,!0,"system",s,"Action completed successfully")}catch(e){console.error("Error recording client usage:",e)}}}function eF(e){let{used:s,limit:a,percentage:r,remaining:l}=e,n=r||0,i=e=>{let s=e||0;return s>=1e6?"".concat((s/1e6).toFixed(1),"M"):s>=1e3?"".concat((s/1e3).toFixed(1),"K"):s.toLocaleString()};return(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("h3",{className:"text-sm font-medium text-gray-700",children:"Uso de Tokens"}),(0,t.jsxs)("span",{className:"text-sm font-semibold ".concat(n<50?"text-green-600":n<80?"text-yellow-600":"text-red-600"),children:[n,"%"]})]}),(0,t.jsx)("div",{className:"w-full ".concat(n<50?"bg-green-100":n<80?"bg-yellow-100":"bg-red-100"," rounded-full h-3"),children:(0,t.jsx)("div",{className:"h-3 rounded-full transition-all duration-300 ".concat(n<50?"bg-green-500":n<80?"bg-yellow-500":"bg-red-500"),style:{width:"".concat(Math.min(n,100),"%")}})}),(0,t.jsxs)("div",{className:"flex items-center justify-between text-xs text-gray-600",children:[(0,t.jsxs)("span",{children:[(0,t.jsx)("strong",{children:i(s||0)})," usados"]}),(0,t.jsxs)("span",{children:[(0,t.jsx)("strong",{children:i(l||0)})," restantes"]})]}),(0,t.jsx)("div",{className:"text-center",children:(0,t.jsxs)("span",{className:"text-xs text-gray-500",children:["L\xedmite mensual: ",(0,t.jsx)("strong",{children:i(a||0)})," tokens"]})}),n>=80&&(0,t.jsx)("div",{className:"p-2 rounded-lg text-xs ".concat(n>=95?"bg-red-50 text-red-700 border border-red-200":"bg-yellow-50 text-yellow-700 border border-yellow-200"),children:n>=95?(0,t.jsx)("span",{children:"⚠️ L\xedmite casi alcanzado. Considera comprar m\xe1s tokens."}):(0,t.jsx)("span",{children:"⚠️ Te est\xe1s acercando al l\xedmite mensual de tokens."})})]})}function eL(e){let{isOpen:s,onClose:a,shouldRefreshOnOpen:l=!1}=e,n=function(){let[e,s]=(0,r.useState)({loading:!0,userPlan:null,tokenUsage:null,limits:[],paymentVerified:!1,error:null}),a=(0,r.useCallback)(async()=>{try{s(e=>({...e,loading:!0,error:null}));let e=(0,z.U)(),{data:{user:a},error:t}=await e.auth.getUser();if(t||!a)return void s(e=>({...e,loading:!1,error:"Usuario no autenticado"}));let{data:r,error:l}=await e.from("user_profiles").select("*").eq("user_id",a.id).single();if(l||!r)return void s(e=>({...e,loading:!1,error:"Perfil no encontrado"}));let n=new Date().toISOString().slice(0,7)+"-01",i=r.current_month===n&&r.current_month_tokens||0,o=r.monthly_token_limit||0,c=await eI.checkClientUserLimits();s({loading:!1,userPlan:r.subscription_plan,tokenUsage:{current:i,limit:o,percentage:o>0?Math.round(i/o*100):0,remaining:Math.max(0,o-i)},limits:c,paymentVerified:r.payment_verified||"free"===r.subscription_plan,error:null})}catch(e){console.error("Error loading plan limits:",e),s(e=>({...e,loading:!1,error:"Error cargando l\xedmites"}))}},[]);(0,r.useEffect)(()=>{a()},[a]);let t=(0,r.useCallback)(()=>{a()},[a]);return{...e,refresh:t}}();return s?(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,t.jsxs)("div",{className:"bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)(i.hht,{className:"w-6 h-6 text-blue-600"}),(0,t.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Estad\xedsticas de Uso de IA"})]}),(0,t.jsx)("button",{onClick:a,className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:(0,t.jsx)(i.yGN,{className:"w-5 h-5 text-gray-500"})})]}),(0,t.jsx)("div",{className:"p-6 space-y-6",children:n.loading?(0,t.jsxs)("div",{className:"flex justify-center items-center py-8",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),(0,t.jsx)("span",{className:"ml-2 text-gray-600",children:"Verificando plan..."})]}):"free"===n.userPlan?(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Estad\xedsticas de Tokens no disponibles"}),(0,t.jsx)("p",{className:"text-gray-600 mb-6",children:"Las estad\xedsticas detalladas est\xe1n disponibles para planes de pago."}),(0,t.jsx)("div",{children:(0,t.jsx)(y(),{href:"/",className:"inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors",children:"Ver Planes Disponibles"})})]}):n.tokenUsage&&n.tokenUsage.limit>0?(0,t.jsx)("div",{className:"mb-6",children:(0,t.jsx)(eF,{used:n.tokenUsage.current||0,limit:n.tokenUsage.limit||0,percentage:n.tokenUsage.percentage||0,remaining:n.tokenUsage.remaining||0})}):(0,t.jsx)("div",{className:"flex justify-center items-center py-8",children:(0,t.jsx)("p",{className:"text-gray-600",children:"No hay datos de uso disponibles."})})}),(0,t.jsx)("div",{className:"flex justify-end p-6 border-t border-gray-200",children:(0,t.jsx)("button",{onClick:a,className:"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors",children:"Cerrar"})})]})}):null}function eM(){var e;let[s,a]=(0,r.useState)([]),[c,m]=(0,r.useState)(!1),[u,x]=(0,r.useState)("dashboard"),[h,j]=(0,r.useState)(!1),[y,N]=(0,r.useState)(!1),[w,k]=(0,r.useState)(null),[C,S]=(0,r.useState)(null),[T,_]=(0,r.useState)(!1),[z,D]=(0,r.useState)(!1),[P,I]=(0,r.useState)(0),[F,L]=(0,r.useState)(!1),[M,G]=(0,r.useState)(!1),[V,U]=(0,r.useState)(!1),[B,$]=(0,r.useState)(!1),{cerrarSesion:Y,user:W,isLoading:K}=(0,g.A)(),X=(0,l.useRouter)(),J=(0,r.useRef)(null),{generatePlanEstudios:Z,isGenerating:Q}=E(),{latestResult:ea,isLoading:et}=eR({onResult:e=>{k(e),d.oR.success("\xa1Plan de estudios generado exitosamente!")},onError:e=>{d.oR.error("Error al generar plan: ".concat(e))}});if((0,r.useEffect)(()=>{K||W||X.push("/login")},[W,K,X]),(0,r.useEffect)(()=>{(async()=>{if(W)try{let e=await (0,ed.jg)();if(e){S(e.id);let s=await (0,eb.vD)(e.id);_(s);let a=await (0,e_.fF)(e.id);a&&a.plan_data?k(a.plan_data):k(null)}else S(null),_(!1),k(null)}catch(e){console.error("Error al cargar datos del temario:",e),S(null),_(!1),k(null)}})()},[W]),(0,r.useEffect)(()=>{ea&&k(ea)},[ea]),(0,r.useEffect)(()=>{(async()=>{if(W){let[e,s,a]=await Promise.all([(0,p.qk)("study_planning"),(0,p.qk)("ai_tutor_chat"),(0,p.qk)("summary_a1_a2")]);L(e),G(s),U(a)}})()},[W]),K||!W)return(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"}),(0,t.jsx)("p",{className:"mt-4 text-gray-600",children:"Cargando..."})]})});let er=async()=>{j(!0),N(!0);try{var e;await (null==(e=J.current)?void 0:e.recargarDocumentos())}catch(e){console.error("Error al recargar documentos:",e)}finally{N(!1)}setTimeout(()=>j(!1),5e3)},el=async()=>{try{var e;await (null==(e=J.current)?void 0:e.recargarDocumentos())}catch(e){console.error("Error al recargar documentos despu\xe9s de eliminar:",e)}},en=async()=>{await Y()},ei=async()=>{if(!C)return void d.oR.error("No se encontr\xf3 un temario configurado");if(!T)return void d.oR.error('Necesitas completar la configuraci\xf3n de planificaci\xf3n en "Mi Temario"');try{await Z({temarioId:C,onComplete:e=>{k(e)},onError:e=>{e.includes("planificaci\xf3n configurada")?d.oR.error('Necesitas completar la configuraci\xf3n de planificaci\xf3n en "Mi Temario"'):d.oR.error("Error al generar el plan de estudios. Int\xe9ntalo de nuevo.")}})}catch(e){console.error("Error al iniciar generaci\xf3n del plan:",e)}},ec=e=>{let s="# Plan de Estudios Personalizado\n\n";return s+="".concat(e.introduccion,"\n\n"),s+="## Resumen del Plan\n\n",s+="- **Tiempo total de estudio:** ".concat(e.resumen.tiempoTotalEstudio,"\n"),s+="- **N\xfamero de temas:** ".concat(e.resumen.numeroTemas,"\n"),s+="- **Duraci\xf3n estudio nuevo:** ".concat(e.resumen.duracionEstudioNuevo,"\n"),s+="- **Duraci\xf3n repaso final:** ".concat(e.resumen.duracionRepasoFinal,"\n\n"),s+="## Cronograma Semanal\n\n",e.semanas.forEach(e=>{s+="### Semana ".concat(e.numero," (").concat(e.fechaInicio," - ").concat(e.fechaFin,")\n\n"),s+="**Objetivo:** ".concat(e.objetivoPrincipal,"\n\n"),e.dias.forEach(e=>{s+="**".concat(e.dia," (").concat(e.horas,"h):**\n"),e.tareas.forEach(e=>{s+="- ".concat(e.titulo," (").concat(e.duracionEstimada,")\n"),e.descripcion&&(s+="  ".concat(e.descripcion,"\n"))}),s+="\n"})}),s+="## Estrategia de Repasos\n\n".concat(e.estrategiaRepasos,"\n\n"),s+="## Pr\xf3ximos Pasos\n\n".concat(e.proximosPasos,"\n")};return(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,t.jsx)("header",{className:"bg-white shadow-sm",children:(0,t.jsxs)("div",{className:"max-w-12xl mx-auto px-4 sm:px-6 lg:px-8 py-2",children:[" ",(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("div",{className:"flex items-center -ml-4",children:(0,t.jsx)("div",{className:"flex flex-col items-center",children:(0,t.jsx)(n.default,{src:"/logo2.png",alt:"OposiAI Logo",width:80,height:80,className:"h-20 w-20 object-contain"})})}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[W&&(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsxs)("div",{className:"text-sm text-gray-600",children:["Hola, ",null==(e=W.email)?void 0:e.split("@")[0]]}),(0,t.jsx)("button",{onClick:()=>X.push("/profile"),className:"inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",title:"Ver perfil",children:(0,t.jsx)(i.JXP,{className:"w-4 h-4"})})]}),(0,t.jsxs)("button",{onClick:()=>D(!0),className:"inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50",title:"Ver estad\xedsticas de uso de IA",children:[(0,t.jsx)(i.hht,{className:"w-4 h-4 mr-2"}),"Estad\xedsticas"]}),(0,t.jsxs)("button",{onClick:()=>m(!c),className:"inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700",children:[(0,t.jsx)(i.GGD,{className:"w-4 h-4 mr-2"}),"Nuevo documento"]}),(0,t.jsxs)("button",{onClick:en,className:"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:[(0,t.jsx)(i.QeK,{className:"mr-2"}),"Cerrar sesi\xf3n"]})]})]})]})}),(0,t.jsxs)("main",{className:"px-4 sm:px-6 lg:px-8 py-8",children:[c&&(0,t.jsx)("div",{className:"mb-8 transition-all duration-300 ease-in-out",children:(0,t.jsx)(v,{onSuccess:er})}),h&&(0,t.jsx)("div",{className:"mb-6 p-4 bg-green-50 text-green-800 rounded-lg border border-green-200",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(i.YrT,{className:"text-green-500 mr-2 flex-shrink-0"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium",children:"\xa1Documento subido exitosamente!"}),(0,t.jsx)("p",{className:"text-sm text-green-700 mt-1",children:y?(0,t.jsxs)("span",{className:"flex items-center",children:[(0,t.jsx)(i.jTZ,{className:"animate-spin mr-1"}),"Actualizando lista de documentos..."]}):"El documento ya est\xe1 disponible en los desplegables de selecci\xf3n."})]})]})}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-3 mb-4",children:[(0,t.jsxs)("div",{className:"flex items-center mb-2",children:[(0,t.jsx)(i.jH2,{className:"w-4 h-4 text-blue-600 mr-2"}),(0,t.jsx)("h2",{className:"text-base font-semibold text-gray-900",children:"Documentos Seleccionados"})]}),(0,t.jsx)("p",{className:"text-xs text-gray-600 mb-2",children:"Selecciona los documentos que quieres usar para generar contenido con IA."}),(0,t.jsx)(o.A,{ref:J,onSelectionChange:a}),s.length>0&&(0,t.jsxs)("div",{className:"mt-2 p-2 bg-blue-50 rounded-lg",children:[(0,t.jsxs)("p",{className:"text-xs text-blue-800 font-medium",children:[(0,t.jsx)("strong",{children:s.length})," documento",1!==s.length?"s":""," seleccionado",1!==s.length?"s":"","."]}),(0,t.jsx)("div",{className:"mt-1 flex flex-wrap gap-1",children:s.map(e=>(0,t.jsxs)("span",{className:"inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800",children:[e.numero_tema&&"Tema ".concat(e.numero_tema,": "),e.titulo]},e.id))})]})]}),(0,t.jsxs)("div",{className:"flex gap-6 mb-8",children:[(0,t.jsx)(eA,{activeTab:u,onTabChange:x}),(0,t.jsx)("div",{className:"flex-1",children:"dashboard"===u?(0,t.jsx)(ep,{onNavigateToTab:e=>{x(e)}}):(0,t.jsx)("div",{className:"bg-white rounded-xl shadow-sm overflow-hidden",children:(0,t.jsxs)("div",{className:"p-6",children:["temario"===u&&(0,t.jsx)(eE,{}),"planEstudios"===u&&(0,t.jsx)("div",{children:F?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,t.jsx)("h2",{className:"text-2xl font-semibold text-gray-900",children:"Mi Plan de Estudios"}),(0,t.jsx)("div",{className:"flex gap-2",children:w&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("button",{onClick:ei,disabled:et||Q("plan-estudios"),className:"flex items-center gap-2 px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:[(0,t.jsx)(i.jTZ,{className:"w-4 h-4 ".concat(et||Q("plan-estudios")?"animate-spin":"")}),"Regenerar Plan"]}),(0,t.jsxs)("button",{onClick:()=>{if(!w)return;let e=new Blob([ec(w)],{type:"text/markdown"}),s=URL.createObjectURL(e),a=document.createElement("a");a.href=s,a.download="plan-estudios-".concat(new Date().toISOString().split("T")[0],".md"),document.body.appendChild(a),a.click(),document.body.removeChild(a),URL.revokeObjectURL(s),d.oR.success("Plan descargado exitosamente")},className:"flex items-center gap-2 px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,t.jsx)(i.a4x,{className:"w-4 h-4"}),"Descargar"]}),(0,t.jsxs)("button",{onClick:()=>{if(!w)return;let e=ec(w),s=window.open("","_blank");s&&(s.document.write('\n        <html>\n          <head>\n            <title>Plan de Estudios Personalizado</title>\n            <style>\n              body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }\n              h1, h2, h3 { color: #333; }\n              h1 { border-bottom: 2px solid #333; padding-bottom: 10px; }\n              h2 { border-bottom: 1px solid #666; padding-bottom: 5px; }\n              ul, ol { margin-left: 20px; }\n              strong { color: #2563eb; }\n              @media print { body { margin: 0; } }\n            </style>\n          </head>\n          <body>\n            <div id="content"></div>\n            <script>\n              // Convertir markdown a HTML b\xe1sico para impresi\xf3n\n              const markdown = '.concat(JSON.stringify(e),";\n              const content = markdown\n                .replace(/^# (.*$)/gim, '<h1>$1</h1>')\n                .replace(/^## (.*$)/gim, '<h2>$1</h2>')\n                .replace(/^### (.*$)/gim, '<h3>$1</h3>')\n                .replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>')\n                .replace(/\\*(.*?)\\*/g, '<em>$1</em>')\n                .replace(/^- (.*$)/gim, '<li>$1</li>')\n                .replace(/(<li>.*<\\/li>)/s, '<ul>$1</ul>')\n                .replace(/\\n/g, '<br>');\n              document.getElementById('content').innerHTML = content;\n              window.print();\n            <\/script>\n          </body>\n        </html>\n      ")),s.document.close())},className:"flex items-center gap-2 px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,t.jsx)(i.Mvz,{className:"w-4 h-4"}),"Imprimir"]})]})})]}),et||Q("plan-estudios")?(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600 mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Generando tu plan personalizado"}),(0,t.jsx)("p",{className:"text-gray-600",children:"La IA est\xe1 analizando tu temario y configuraci\xf3n..."})]}):w&&C?(0,t.jsx)(ez.A,{plan:w,temarioId:C}):(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)("div",{className:"w-20 h-20 bg-teal-100 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,t.jsx)(i.wIk,{className:"w-10 h-10 text-teal-600"})}),(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Genera tu Plan de Estudios Personalizado"}),(0,t.jsx)("p",{className:"text-gray-600 mb-8 max-w-2xl mx-auto",children:"Crea un plan de estudios personalizado basado en tu temario y configuraci\xf3n de planificaci\xf3n"}),(0,t.jsxs)("button",{onClick:ei,disabled:!T,className:"inline-flex items-center px-6 py-3 bg-teal-600 text-white rounded-lg hover:bg-teal-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:[(0,t.jsx)(i.wIk,{className:"w-5 h-5 mr-3"}),"Generar Plan de Estudios"]}),!T&&(0,t.jsx)("p",{className:"text-sm text-red-600 mt-4",children:'Necesitas completar la configuraci\xf3n de planificaci\xf3n en "Mi Temario"'})]})]}):(0,t.jsx)(b.A,{feature:"study_planning",benefits:["Planes de estudio personalizados con IA","Cronogramas adaptativos a tu ritmo","Seguimiento autom\xe1tico de progreso","Recomendaciones inteligentes de repaso"],className:"min-h-[600px]"})}),"preguntas"===u&&(M?(0,t.jsx)(f,{documentosSeleccionados:s}):(0,t.jsx)(b.A,{feature:"ai_tutor_chat",benefits:["Chat ilimitado con IA especializada","Respuestas personalizadas a tus documentos","Historial completo de conversaciones","Explicaciones detalladas y ejemplos"],className:"min-h-[600px]"})),"mapas"===u&&(0,t.jsx)(A,{documentosSeleccionados:s}),"flashcards"===u&&(0,t.jsx)(R,{documentosSeleccionados:s}),"tests"===u&&(0,t.jsx)(es,{documentosSeleccionados:s}),"misTests"===u&&(0,t.jsx)(eo,{}),"misFlashcards"===u&&(0,t.jsx)(ee,{}),"resumenes"===u&&(V?(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)(O,{documentosSeleccionados:s,onSummaryGenerated:e=>{I(e=>e+1),d.oR.success("Resumen generado exitosamente")}}),(0,t.jsx)("hr",{className:"border-gray-200"}),(0,t.jsx)(q,{refreshTrigger:P})]}):(0,t.jsx)(b.A,{feature:"summary_a1_a2",benefits:["Res\xfamenes inteligentes con IA","Formato A1 y A2 optimizado","Edici\xf3n autom\xe1tica de contenido","Exportaci\xf3n a PDF de alta calidad"],className:"min-h-[600px]"})),"gestionar"===u&&(0,t.jsx)(H,{onDocumentDeleted:el})]})})})]})]}),(0,t.jsx)("footer",{className:"bg-white border-t border-gray-200 mt-12",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:(0,t.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center",children:[(0,t.jsx)("div",{className:"flex items-center",children:(0,t.jsxs)("span",{className:"text-gray-500 text-sm",children:["\xa9 ",new Date().getFullYear()," OposiAI - Asistente para Oposiciones"]})}),(0,t.jsx)("div",{className:"mt-4 md:mt-0",children:(0,t.jsxs)("nav",{className:"flex space-x-6",children:[(0,t.jsx)("a",{href:"#",className:"text-gray-500 hover:text-gray-700 text-sm",children:"T\xe9rminos"}),(0,t.jsx)("a",{href:"#",className:"text-gray-500 hover:text-gray-700 text-sm",children:"Privacidad"}),(0,t.jsx)("a",{href:"#",className:"text-gray-500 hover:text-gray-700 text-sm",children:"Contacto"})]})})]})})}),(0,t.jsx)(ek,{}),(0,t.jsx)(eT,{}),(0,t.jsx)(eL,{isOpen:z,onClose:()=>{D(!1),$(!1)},shouldRefreshOnOpen:B})]})}},5410:(e,s,a)=>{Promise.resolve().then(a.bind(a,453))},8781:(e,s,a)=>{"use strict";a.d(s,{E9:()=>c,GS:()=>n,MZ:()=>o,oO:()=>i,oS:()=>r});var t=a(1153);let r=t.z.object({pregunta:t.z.string().min(1,"La pregunta es obligatoria").max(500,"M\xe1ximo 500 caracteres"),documentos:t.z.array(t.z.object({id:t.z.string().optional(),titulo:t.z.string().min(1),contenido:t.z.string().min(1),categoria:t.z.string().optional().nullable(),numero_tema:t.z.union([t.z.number().int().positive(),t.z.string(),t.z.null(),t.z.undefined()]).optional(),creado_en:t.z.string().optional(),actualizado_en:t.z.string().optional(),user_id:t.z.string().optional(),tipo_original:t.z.string().optional()})).min(1,"Debes seleccionar al menos un documento")}),l=t.z.object({peticion:t.z.string().min(1,"La petici\xf3n es obligatoria").max(500,"M\xe1ximo 500 caracteres")}),n=t.z.object({peticion:t.z.string().min(1,"La petici\xf3n es obligatoria").max(500,"M\xe1ximo 500 caracteres"),cantidad:t.z.number().min(1,"M\xednimo 1 pregunta").max(50,"M\xe1ximo 50 preguntas").default(10)}),i=t.z.object({peticion:t.z.string().min(1,"La petici\xf3n es obligatoria").max(500,"M\xe1ximo 500 caracteres"),cantidad:t.z.number().min(1,"M\xednimo 1 flashcard").max(30,"M\xe1ximo 30 flashcards").default(10)}),o=l,c=t.z.object({email:t.z.string().min(1,"El email es obligatorio").email("Por favor, ingresa un email v\xe1lido").max(255,"El email es demasiado largo")});t.z.object({password:t.z.string().min(8,"La contrase\xf1a debe tener al menos 8 caracteres").max(128,"La contrase\xf1a es demasiado larga"),confirmPassword:t.z.string()}).refine(e=>e.password===e.confirmPassword,{message:"Las contrase\xf1as no coinciden",path:["confirmPassword"]})}},e=>{var s=s=>e(e.s=s);e.O(0,[844,512,7361,8260,6874,3898,861,6766,1934,4092,1869,8441,1684,7358],()=>s(5410)),_N_E=e.O()}]);