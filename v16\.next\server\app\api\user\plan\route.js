(()=>{var e={};e.id=643,e.ids=[643],e.modules={2507:(e,r,t)=>{"use strict";t.d(r,{createServerSupabaseClient:()=>o});var s=t(34386),i=t(44999);async function o(){let e=await (0,i.UL)();return(0,s.createServerClient)("https://fxnhpxjijinfuxxxplzj.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4",{auth:{persistSession:!1,autoRefreshToken:!1,detectSessionInUrl:!1},cookies:{getAll:()=>e.getAll(),setAll(r){try{r.filter(e=>!e.name.includes("auth-token")&&!e.name.includes("refresh-token")).forEach(({name:r,value:t,options:s})=>e.set(r,t,{...s,maxAge:void 0,expires:void 0}))}catch{}}}})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},52645:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>c,serverHooks:()=>x,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>d});var s={};t.r(s),t.d(s,{GET:()=>p});var i=t(96559),o=t(48088),n=t(37719),a=t(32190),u=t(2507);async function p(){try{let e=await (0,u.createServerSupabaseClient)(),{data:{user:r},error:t}=await e.auth.getUser();if(!r||t)return a.NextResponse.json({error:"No autorizado"},{status:401});let{data:s,error:i}=await e.from("user_profiles").select("subscription_plan").eq("user_id",r.id).single();if(i)return console.error("Error obteniendo perfil:",i),a.NextResponse.json({plan:"free"});return a.NextResponse.json({plan:s?.subscription_plan||"free"})}catch(e){return console.error("Error en API user/plan:",e),a.NextResponse.json({error:"Error interno del servidor"},{status:500})}}let c=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/user/plan/route",pathname:"/api/user/plan",filename:"route",bundlePath:"app/api/user/plan/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\user\\plan\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:d,serverHooks:x}=c;function h(){return(0,n.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:d})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580,6345,4386,4999],()=>t(52645));module.exports=s})();