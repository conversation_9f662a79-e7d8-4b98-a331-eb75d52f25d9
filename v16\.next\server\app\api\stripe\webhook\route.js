"use strict";(()=>{var e={};e.id=3287,e.ids=[3287],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{e.exports=require("punycode")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{e.exports=require("tls")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},61730:(e,r,s)=>{s.d(r,{L6:()=>c,Md:()=>i,aN:()=>n,_4:()=>d});var t=s(97877),a=s(8814);let o={free:{id:"free",name:"Plan Gratis",price:0,stripeProductId:null,stripePriceId:null,features:["Incluye:","• Uso de la plataforma solo durante 5 d\xedas","• Subida de documentos: m\xe1ximo 1 documento","• Generador de test: m\xe1ximo 10 preguntas test","• Generador de flashcards: m\xe1ximo 10 tarjetas flashcard","• Generador de mapas mentales: m\xe1ximo 2 mapas mentales","No incluye:","• Planificaci\xf3n de estudios","• Habla con tu preparador IA","• Res\xfamenes A2 y A1"],limits:a.qo.free.limits,planConfig:a.qo.free},usuario:{id:"usuario",name:"Plan Usuario",price:1e3,stripeProductId:"prod_SR65BdKdek1OXd",stripePriceId:"price_1Rae5807kFn3sIXhRf3adX1n",features:["Incluye:","• Uso de la plataforma durante el mes (una vez finalizado no podr\xe1 volver a acceder hasta renovaci\xf3n)","• Subida de documentos","• Habla con tu preparador IA *","• Generador de test *","• Generador de flashcards *","• Generador de mapas mentales *","• * Para las tareas en las que se haga uso de IA, el l\xedmite mensual ser\xe1 de 1.000.000 de tokens, podr\xe1n ampliarse durante el mes mediante pago","No incluye:","• Planificaci\xf3n de estudios","• Res\xfamenes A2 y A1"],limits:a.qo.usuario.limits,planConfig:a.qo.usuario},pro:{id:"pro",name:"Plan Pro",price:1500,stripeProductId:"prod_SR66U2G7bVJqu3",stripePriceId:"price_1Rae3U07kFn3sIXhkvSuJco1",features:["Incluye:","• Uso de la plataforma durante el mes (una vez finalizado no podr\xe1 volver a acceder hasta renovaci\xf3n)","• Subida de documentos","• Planificaci\xf3n de estudios mediante IA*","• Habla con tu preparador IA *","• Generador de test *","• Generador de flashcards *","• Generador de mapas mentales *","• Generaci\xf3n de Res\xfamenes para A2 y A1","• * Para las tareas en las que se haga uso de IA, el l\xedmite mensual ser\xe1 de 1.000.000 de tokens, podr\xe1n ampliarse durante el mes mediante pago"],limits:a.qo.pro.limits,planConfig:a.qo.pro}};function i(e){return o[e]||null}function n(e){return e in o}let c={success:"http://localhost:3000/thank-you",cancel:"http://localhost:3000/upgrade-plan",webhook:"http://localhost:3000/api/stripe/webhook"},d=new t.A(process.env.STRIPE_SECRET_KEY,{apiVersion:"2025-05-28.basil",typescript:!0})},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},81630:e=>{e.exports=require("http")},89408:(e,r,s)=>{s.r(r),s.d(r,{patchFetch:()=>S,routeModule:()=>g,serverHooks:()=>h,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>b});var t={};s.r(t),s.d(t,{POST:()=>_});var a=s(96559),o=s(48088),i=s(37719),n=s(32190),c=s(61730),d=s(44999),u=s(55658),l=s(42049),p=s(31571);class m{static async handleCheckoutSessionCompleted(e){try{let r;if(console.log("\uD83C\uDFAF Procesando checkout.session.completed:",e.id),"paid"!==e.payment_status)return{success:!1,message:"Payment not completed",error:`Payment status: ${e.payment_status}`};if(e.metadata?.type==="token_purchase")return await this.handleTokenPurchase(e);let{planId:s,customerEmail:t,customerName:a}=e.metadata||{};if(!s||!t)return{success:!1,message:"Missing required metadata",error:"planId and customerEmail are required"};let o="subscription"===e.mode?e.subscription:void 0,i=await l.SupabaseAdminService.getTransactionBySessionId(e.id);if(i)return console.log("⚠️ Transacci\xf3n ya procesada:",i.id),{success:!0,message:"Transaction already processed",data:{transactionId:i.id}};let{data:n}=await l.E.from("user_profiles").select("user_id, subscription_plan").eq("stripe_customer_id",e.customer).single();if(n){if(console.log("⚠️ Usuario ya existe para este customer ID:",e.customer),o&&(await u.G.updateUserPlan(n.user_id,s,void 0,"New subscription for existing customer")).success)return{success:!0,message:"Existing user plan updated with new subscription",data:{userId:n.user_id}};return{success:!1,message:"Customer already exists but plan update failed"}}let d=null;if(o&&c._4)try{let e=await c._4.subscriptions.retrieve(o);d=e.current_period_end?new Date(1e3*e.current_period_end).toISOString():null,console.log(`[handleCheckoutSessionCompleted] Plan expires at from subscription: ${d}`)}catch(e){console.error("[handleCheckoutSessionCompleted] Error obteniendo suscripci\xf3n:",e)}let p=e.metadata?.registrationData,m=e.metadata?.userId;if(m){console.log(`🆕 Activando usuario pre-registrado despu\xe9s del pago: ${m}`);try{let i=await l.SupabaseAdminService.createStripeTransaction({stripe_session_id:e.id,stripe_customer_id:e.customer,user_email:t,user_name:a,plan_id:s,amount:e.amount_total||0,currency:e.currency||"eur",payment_status:"paid",subscription_id:o,user_id:m,metadata:{created_by:"webhook",activation_flow:"pre_registered_user"}}),{error:n}=await l.E.from("user_profiles").update({payment_verified:!0,stripe_customer_id:e.customer,stripe_subscription_id:o,last_payment_date:new Date().toISOString(),plan_expires_at:d,auto_renew:!!o,updated_at:new Date().toISOString(),security_flags:{payment_completed:!0,payment_date:new Date().toISOString(),stripe_session_id:e.id,subscription_id:o,pre_registered:!1,activated:!0}}).eq("user_id",m);if(n)console.error("Error actualizando usuario pre-registrado:",n),r={success:!1,error:n.message};else{console.log("\uD83D\uDCE7 Enviando email de confirmaci\xf3n despu\xe9s del pago exitoso...");let e=await l.SupabaseAdminService.sendConfirmationEmailForUser(m);e.success?console.log("✅ Email de confirmaci\xf3n enviado exitosamente despu\xe9s del pago"):console.error("⚠️ Error enviando email de confirmaci\xf3n:",e.error),await l.SupabaseAdminService.activateTransaction(i.id),r={success:!0,userId:m,transactionId:i.id,activated:!0}}}catch(e){console.error("Error activando usuario pre-registrado:",e),r={success:!1,error:e instanceof Error?e.message:"Activation error"}}}else if(p){console.log(`🔄 Flujo legacy: Creando cuenta despu\xe9s del pago exitoso para: ${t}`);try{let s=JSON.parse(p);r=await u.G.createUserWithPlan({email:s.email,password:s.password,name:s.customerName,planId:s.planId,stripeSessionId:e.id,stripeCustomerId:e.customer,amount:e.amount_total||0,currency:e.currency||"eur",subscriptionId:o,planExpiresAt:d,sendConfirmationEmail:!0})}catch(e){console.error("Error parseando datos de registro:",e),r={success:!1,error:"Invalid registration data format"}}}else r=await u.G.createUserWithPlan({email:t,name:a,planId:s,stripeSessionId:e.id,stripeCustomerId:e.customer,amount:e.amount_total||0,currency:e.currency||"eur",subscriptionId:o,planExpiresAt:d});if(!r.success){if(r.error&&(r.error.includes("A user with this email address has already been registered")||r.error.includes("email_exists")||r.error.includes("already been registered")||r.error.includes("User already registered"))){console.log(`🔁 Usuario existente detectado, actualizando plan para: ${t}`);let r=await l.SupabaseAdminService.getUserByEmail(t);if(!r)return{success:!1,message:"Failed to retrieve existing user by email.",error:"User not found despite email_exists error"};let a=r.id,i=await u.G.updateUserPlan(a,s,void 0,"New payment for existing user email"),{error:n}=await l.E.from("user_profiles").update({stripe_customer_id:e.customer,stripe_subscription_id:o,payment_verified:!0}).eq("user_id",a);n&&console.error("Error actualizando perfil con datos de Stripe:",n);let c=await l.SupabaseAdminService.getTransactionBySessionId(e.id);if(c&&(await l.SupabaseAdminService.updateTransactionWithUser(c.id,a),await l.SupabaseAdminService.activateTransaction(c.id)),i.success)return{success:!0,message:"Existing user plan updated successfully",data:{userId:a}};return{success:!1,message:"Failed to update plan for existing user",error:i.error}}return{success:!1,message:"Failed to create user",error:r.error}}return console.log("✅ Usuario creado exitosamente desde webhook"),{success:!0,message:"User created successfully",data:{userId:r.userId,profileId:r.profileId,transactionId:r.transactionId}}}catch(e){return console.error("❌ Error en handleCheckoutSessionCompleted:",e),{success:!1,message:"Internal error processing checkout session",error:e instanceof Error?e.message:"Unknown error"}}}static async handleTokenPurchase(e){try{console.log("\uD83E\uDE99 Procesando compra de tokens:",e.id);let{user_id:r,token_amount:s,price:t}=e.metadata||{};if(!r||!s||!t)return{success:!1,message:"Missing required metadata for token purchase",error:"user_id, token_amount, and price are required"};let{data:a}=await l.E.from("token_purchases").select("id").eq("transaction_id",e.id).single();if(a)return console.log("⚠️ Compra de tokens ya procesada:",a.id),{success:!0,message:"Token purchase already processed",data:{purchaseId:a.id}};let o=parseInt(s),i=parseFloat(t),{data:n,error:c}=await l.E.from("token_purchases").insert([{user_id:r,amount:o,price:i,transaction_id:e.id,status:"completed"}]).select().single();if(c)return console.error("Error registrando compra de tokens:",c),{success:!1,message:"Error registering token purchase",error:c.message};let{data:d,error:u}=await l.E.from("user_profiles").select("monthly_token_limit").eq("user_id",r).single();if(u||!d)return console.error("Error obteniendo perfil de usuario:",u),{success:!1,message:"User profile not found",error:u?.message||"Profile not found"};let p=d.monthly_token_limit+o,{error:m}=await l.E.from("user_profiles").update({monthly_token_limit:p,updated_at:new Date().toISOString()}).eq("user_id",r);if(m)return console.error("Error actualizando l\xedmite de tokens:",m),{success:!1,message:"Error updating token limit",error:m.message};return console.log("✅ Compra de tokens procesada exitosamente:",{userId:r,tokensAdded:o,newLimit:p,purchaseId:n.id}),{success:!0,message:"Token purchase processed successfully",data:{purchaseId:n.id,tokensAdded:o,newTokenLimit:p}}}catch(e){return console.error("❌ Error en handleTokenPurchase:",e),{success:!1,message:"Internal error processing token purchase",error:e instanceof Error?e.message:"Unknown error"}}}static async handlePaymentIntentSucceeded(e){try{return console.log("\uD83D\uDCB3 Procesando payment_intent.succeeded:",e.id),{success:!0,message:"Payment intent logged successfully",data:{paymentIntentId:e.id}}}catch(e){return console.error("❌ Error en handlePaymentIntentSucceeded:",e),{success:!1,message:"Error processing payment intent",error:e instanceof Error?e.message:"Unknown error"}}}static async handleSubscriptionCreated(e){try{console.log("\uD83D\uDD04 Procesando customer.subscription.created:",e.id);let r=e.customer;if(!e.metadata?.planId)return{success:!1,message:"Missing plan ID in subscription metadata"};let{error:s}=await l.E.from("stripe_transactions").update({subscription_id:e.id,metadata:{subscription_status:e.status,subscription_id:e.id,updated_at:new Date().toISOString()}}).eq("stripe_customer_id",r);s&&console.error("Error actualizando transacci\xf3n con suscripci\xf3n:",s);let t=e.current_period_end?new Date(1e3*e.current_period_end).toISOString():new Date(Date.now()+2592e6).toISOString();console.log(`[handleSubscriptionCreated] Actualizando perfil para customerId: ${r}`),console.log(`[handleSubscriptionCreated] stripe_subscription_id: ${e.id}, plan_expires_at: ${t}`);let{error:a,data:o}=await l.E.from("user_profiles").update({stripe_subscription_id:e.id,plan_expires_at:t,auto_renew:!0,last_payment_date:new Date().toISOString(),updated_at:new Date().toISOString(),security_flags:{subscription_status:e.status,subscription_id:e.id,subscription_created_at:new Date().toISOString()}}).eq("stripe_customer_id",r).select();if(!o||0===o.length){console.log(`[handleSubscriptionCreated] No se encontr\xf3 perfil por customerId, buscando por email...`);let s=e.metadata?.customerEmail;if(s){let i=await l.SupabaseAdminService.getUserByEmail(s);if(i){console.log(`[handleSubscriptionCreated] Actualizando perfil por user_id: ${i.id}`);let s=await l.E.from("user_profiles").update({stripe_customer_id:r,stripe_subscription_id:e.id,plan_expires_at:t,auto_renew:!0,last_payment_date:new Date().toISOString(),updated_at:new Date().toISOString(),security_flags:{subscription_status:e.status,subscription_id:e.id,subscription_created_at:new Date().toISOString()}}).eq("user_id",i.id).select();a=s.error,o=s.data}}}return a?console.error("[handleSubscriptionCreated] Error actualizando perfil con suscripci\xf3n:",a):console.log("[handleSubscriptionCreated] Perfil actualizado con datos de suscripci\xf3n:",o),{success:!0,message:"Subscription created successfully",data:{subscriptionId:e.id,planExpiresAt:t}}}catch(e){return console.error("❌ Error en handleSubscriptionCreated:",e),{success:!1,message:"Error processing subscription creation",error:e instanceof Error?e.message:"Unknown error"}}}static async handleSubscriptionUpdated(e){try{console.log("\uD83D\uDD04 Procesando customer.subscription.updated:",e.id);let{data:r}=await l.E.from("user_profiles").select("user_id, subscription_plan").eq("stripe_customer_id",e.customer).single();if(!r)return{success:!1,message:"User profile not found for customer"};let s=e.current_period_end?new Date(1e3*e.current_period_end).toISOString():new Date(Date.now()+2592e6).toISOString(),{error:t}=await l.E.from("user_profiles").update({plan_expires_at:s,updated_at:new Date().toISOString(),security_flags:{subscription_status:e.status,subscription_id:e.id,last_updated:new Date().toISOString()}}).eq("user_id",r.user_id);if(t)return console.error("Error actualizando perfil de usuario:",t),{success:!1,message:"Error updating user profile"};return{success:!0,message:"Subscription updated successfully",data:{subscriptionId:e.id}}}catch(e){return console.error("❌ Error en handleSubscriptionUpdated:",e),{success:!1,message:"Error processing subscription update",error:e instanceof Error?e.message:"Unknown error"}}}static async handleSubscriptionDeleted(e){try{console.log("❌ Procesando customer.subscription.deleted:",e.id);let{data:r}=await l.E.from("user_profiles").select("user_id, subscription_plan, plan_expires_at").eq("stripe_customer_id",e.customer).single();if(!r)return{success:!1,message:"User profile not found for customer"};let s=e.current_period_end?new Date(1e3*e.current_period_end).toISOString():new Date().toISOString();console.log(`🕐 Per\xedodo de gracia hasta: ${s} para usuario: ${r.user_id}`);let{error:t}=await l.E.from("user_profiles").update({plan_expires_at:s,auto_renew:!1,stripe_subscription_id:null,updated_at:new Date().toISOString(),security_flags:{subscription_cancelled:!0,cancellation_date:new Date().toISOString(),grace_period_until:s,cancelled_subscription_id:e.id,last_updated:new Date().toISOString()}}).eq("user_id",r.user_id);if(t)return console.error("Error actualizando perfil para per\xedodo de gracia:",t),{success:!1,message:"Error setting up grace period",error:t.message};await l.SupabaseAdminService.logPlanChange({user_id:r.user_id,old_plan:r.subscription_plan,new_plan:r.subscription_plan,changed_by:"system",reason:`Subscription cancelled - Grace period until ${s}`});try{let{data:e}=await l.E.auth.admin.getUserById(r.user_id);if(e.user?.email){let t=e.user.user_metadata?.name||e.user.email.split("@")[0],a="usuario"===r.subscription_plan?"Usuario":"Pro";await p.X.sendSubscriptionCancelledNotification(e.user.email,t,a,s,r.user_id),console.log(`📧 Notificaci\xf3n de cancelaci\xf3n enviada a: ${e.user.email}`)}}catch(e){console.error("Error enviando notificaci\xf3n de cancelaci\xf3n:",e)}return{success:!0,message:`Subscription cancelled with grace period until ${s}`,data:{userId:r.user_id,gracePeriodEnd:s,currentPlan:r.subscription_plan}}}catch(e){return console.error("❌ Error en handleSubscriptionDeleted:",e),{success:!1,message:"Error processing subscription deletion",error:e instanceof Error?e.message:"Unknown error"}}}static async handleInvoicePaymentSucceeded(e){try{console.log("\uD83D\uDCB0 Procesando invoice.payment_succeeded:",e.id);let r=e.subscription;if(!r)return{success:!0,message:"Invoice not related to subscription, skipping"};let s=null;try{if(r&&c._4){let e=await c._4.subscriptions.retrieve(r);s=e.current_period_end?new Date(1e3*e.current_period_end).toISOString():null}}catch(e){console.error("Error obteniendo informaci\xf3n de suscripci\xf3n:",e)}let{data:t}=await l.E.from("user_profiles").select("user_id, subscription_plan, current_month_tokens, monthly_token_limit").eq("stripe_customer_id",e.customer).single(),a={last_payment_date:new Date().toISOString(),updated_at:new Date().toISOString()};s&&(a.plan_expires_at=s,t&&("usuario"===t.subscription_plan||"pro"===t.subscription_plan)&&(a.current_month_tokens=0,a.current_month=new Date().toISOString().slice(0,7)+"-01",console.log("\uD83D\uDD04 Reseteando tokens para renovaci\xf3n de suscripci\xf3n:",t.user_id)));let{error:o}=await l.E.from("user_profiles").update(a).eq("stripe_customer_id",e.customer);return o&&console.error("Error actualizando fecha de pago:",o),{success:!0,message:"Invoice payment processed successfully",data:{invoiceId:e.id}}}catch(e){return console.error("❌ Error en handleInvoicePaymentSucceeded:",e),{success:!1,message:"Error processing invoice payment",error:e instanceof Error?e.message:"Unknown error"}}}}async function _(e){let r=Date.now();try{let s,t,a=await e.text(),o=(await (0,d.b3)()).get("stripe-signature");if(!o)return console.error("❌ No Stripe signature found"),n.NextResponse.json({error:"No signature found"},{status:400});let i=process.env.STRIPE_WEBHOOK_SECRET;if(!i)return console.error("❌ No webhook secret configured"),n.NextResponse.json({error:"Webhook secret not configured"},{status:500});if(!c._4)return console.error("❌ Stripe not initialized"),n.NextResponse.json({error:"Stripe not configured"},{status:500});try{s=c._4.webhooks.constructEvent(a,o,i)}catch(e){return console.error("❌ Webhook signature verification failed:",e),n.NextResponse.json({error:"Invalid signature"},{status:400})}switch(console.log("\uD83C\uDFAF Webhook event received:",s.type,"ID:",s.id),s.type){case"checkout.session.completed":t=await m.handleCheckoutSessionCompleted(s.data.object);break;case"payment_intent.succeeded":t=await m.handlePaymentIntentSucceeded(s.data.object);break;case"invoice.payment_succeeded":t=await m.handleInvoicePaymentSucceeded(s.data.object);break;case"customer.subscription.created":t=await m.handleSubscriptionCreated(s.data.object);break;case"customer.subscription.updated":t=await m.handleSubscriptionUpdated(s.data.object);break;case"customer.subscription.deleted":t=await m.handleSubscriptionDeleted(s.data.object);break;default:console.log("⚠️ Unhandled event type:",s.type),t={success:!0,message:`Event type ${s.type} not handled`}}let u=Date.now()-r;if(console.log(`✅ Webhook processed in ${u}ms:`,{eventType:s.type,eventId:s.id,success:t.success,message:t.message}),t.success)return n.NextResponse.json({received:!0,processed:!0,eventType:s.type,message:t.message,data:t.data,processingTime:u});return console.error("❌ Webhook processing failed:",t.error),n.NextResponse.json({received:!0,processed:!1,eventType:s.type,error:t.message,details:t.error},{status:422})}catch(s){let e=Date.now()-r;return console.error("❌ Critical webhook error:",{error:s instanceof Error?s.message:"Unknown error",stack:s instanceof Error?s.stack:void 0,processingTime:e}),n.NextResponse.json({error:"Webhook handler failed",details:s instanceof Error?s.message:"Unknown error",processingTime:e},{status:500})}}let g=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/stripe/webhook/route",pathname:"/api/stripe/webhook",filename:"route",bundlePath:"app/api/stripe/webhook/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\stripe\\webhook\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:f,workUnitAsyncStorage:b,serverHooks:h}=g;function S(){return(0,i.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:b})}},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")}};var r=require("../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[4447,580,6345,7877,4999,8295,5658],()=>s(89408));module.exports=t})();