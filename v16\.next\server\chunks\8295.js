exports.id=8295,exports.ids=[8295],exports.modules={31571:(e,t,a)=>{"use strict";a.d(t,{X:()=>c});class r{static generateSubscriptionCancelledEmail(e,t,a){let r=new Date(a),i=r.toLocaleDateString("es-ES",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}),o=Math.ceil((r.getTime()-new Date().getTime())/864e5),n=`
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Suscripci\xf3n Cancelada - OposI</title>
      </head>
      <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
        <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
          <h1 style="color: #2563eb;">Sus<PERSON>ripci\xf3n Cancelada</h1>
          
          <p>Hola ${e},</p>
          
          <p>Hemos recibido tu solicitud de cancelaci\xf3n de la suscripci\xf3n al <strong>Plan ${t}</strong>.</p>
          
          <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="margin-top: 0; color: #059669;">📅 Per\xedodo de Gracia Activo</h3>
            <p><strong>Mantienes acceso completo hasta:</strong> ${i}</p>
            <p><strong>D\xedas restantes:</strong> ${o} d\xedas</p>
            <p>Durante este per\xedodo, puedes seguir usando todas las funciones de tu plan actual.</p>
          </div>
          
          <h3>\xbfQu\xe9 sucede despu\xe9s?</h3>
          <ul>
            <li>Tu acceso a las funciones premium finalizar\xe1 el ${i}</li>
            <li>Tu cuenta se convertir\xe1 autom\xe1ticamente al Plan Gratuito</li>
            <li>Conservar\xe1s acceso a las funciones b\xe1sicas de OposI</li>
            <li>Tus documentos y progreso se mantendr\xe1n guardados</li>
          </ul>
          
          <h3>\xbfCambiaste de opini\xf3n?</h3>
          <p>Si deseas reactivar tu suscripci\xf3n, puedes hacerlo en cualquier momento desde tu panel de control:</p>
          <p style="text-align: center; margin: 20px 0;">
            <a href="http://localhost:3000/upgrade-plan" 
               style="background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
              Reactivar Suscripci\xf3n
            </a>
          </p>
          
          <hr style="margin: 30px 0; border: none; border-top: 1px solid #e5e7eb;">
          
          <p style="font-size: 14px; color: #6b7280;">
            Si tienes alguna pregunta o necesitas ayuda, no dudes en contactarnos.<br>
            Equipo de OposI
          </p>
        </div>
      </body>
      </html>
    `;return{htmlContent:n,textContent:`
Suscripci\xf3n Cancelada - OposI

Hola ${e},

Hemos recibido tu solicitud de cancelaci\xf3n de la suscripci\xf3n al Plan ${t}.

PER\xcdODO DE GRACIA ACTIVO:
- Mantienes acceso completo hasta: ${i}
- D\xedas restantes: ${o} d\xedas

\xbfQu\xe9 sucede despu\xe9s?
- Tu acceso a las funciones premium finalizar\xe1 el ${i}
- Tu cuenta se convertir\xe1 autom\xe1ticamente al Plan Gratuito
- Conservar\xe1s acceso a las funciones b\xe1sicas de OposI
- Tus documentos y progreso se mantendr\xe1n guardados

\xbfCambiaste de opini\xf3n?
Puedes reactivar tu suscripci\xf3n en cualquier momento desde: http://localhost:3000/upgrade-plan

Si tienes alguna pregunta, no dudes en contactarnos.
Equipo de OposI
    `,subject:`Suscripci\xf3n cancelada - Acceso hasta el ${r.toLocaleDateString("es-ES")}`}}static generateGracePeriodEndingEmail(e,t,a){let r=new Date(a),i=r.toLocaleDateString("es-ES",{year:"numeric",month:"long",day:"numeric"}),o=Math.ceil((r.getTime()-new Date().getTime())/36e5),n=`
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Tu acceso premium termina pronto - OposI</title>
      </head>
      <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
        <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
          <h1 style="color: #dc2626;">⏰ Tu acceso premium termina pronto</h1>
          
          <p>Hola ${e},</p>
          
          <p>Te recordamos que tu acceso al <strong>Plan ${t}</strong> terminar\xe1 el <strong>${i}</strong> (en aproximadamente ${o} horas).</p>
          
          <div style="background-color: #fef3c7; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #f59e0b;">
            <h3 style="margin-top: 0; color: #92400e;">\xbfQuieres continuar con tu plan premium?</h3>
            <p>Reactivar tu suscripci\xf3n es f\xe1cil y r\xe1pido. Mant\xe9n acceso a todas las funciones avanzadas de OposI.</p>
          </div>
          
          <p style="text-align: center; margin: 30px 0;">
            <a href="http://localhost:3000/upgrade-plan" 
               style="background-color: #dc2626; color: white; padding: 15px 30px; text-decoration: none; border-radius: 6px; display: inline-block; font-weight: bold;">
              Reactivar Mi Suscripci\xf3n
            </a>
          </p>
          
          <p style="font-size: 14px; color: #6b7280;">
            Si no reactivas tu suscripci\xf3n, tu cuenta se convertir\xe1 autom\xe1ticamente al Plan Gratuito el ${i}.
          </p>
        </div>
      </body>
      </html>
    `;return{htmlContent:n,textContent:`
Tu acceso premium termina pronto - OposI

Hola ${e},

Te recordamos que tu acceso al Plan ${t} terminar\xe1 el ${i} (en aproximadamente ${o} horas).

\xbfQuieres continuar con tu plan premium?
Reactivar tu suscripci\xf3n: http://localhost:3000/upgrade-plan

Si no reactivas tu suscripci\xf3n, tu cuenta se convertir\xe1 autom\xe1ticamente al Plan Gratuito el ${i}.

Equipo de OposI
    `,subject:`⏰ Tu Plan ${t} termina en ${o} horas`}}static generateGenericEmail(e,t,a,r,i){return{htmlContent:`
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>${t} - OposI</title>
      </head>
      <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
        <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
          <h1 style="color: #2563eb;">${t}</h1>
          
          <p>Hola ${e},</p>
          
          <p>${a}</p>
          
          ${r&&i?`
          <p style="text-align: center; margin: 30px 0;">
            <a href="${i}" 
               style="background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
              ${r}
            </a>
          </p>
          `:""}
          
          <hr style="margin: 30px 0; border: none; border-top: 1px solid #e5e7eb;">
          
          <p style="font-size: 14px; color: #6b7280;">
            Si tienes alguna pregunta, no dudes en contactarnos.<br>
            Equipo de OposI
          </p>
        </div>
      </body>
      </html>
    `,textContent:`
${t} - OposI

Hola ${e},

${a}

${r&&i?`${r}: ${i}`:""}

Si tienes alguna pregunta, no dudes en contactarnos.
Equipo de OposI
    `,subject:t}}}var i=a(42049);class o{static async logEmailNotification(e,t="sent"){try{let a={recipient_email:e.to,subject:e.subject,type:e.type,sent_at:new Date().toISOString(),status:t};e.userId&&(a.user_id=e.userId),e.metadata&&(a.metadata=e.metadata);let{data:r,error:o}=await i.E.from("email_notifications").insert(a).select("id").single();if(o)throw o;return console.log("\uD83D\uDCDD Email notification logged:",{id:r.id,type:e.type,recipient:e.to,status:t,userId:e.userId||"N/A"}),r.id}catch(e){return console.error("Error logging email notification:",e),null}}static async updateEmailNotificationStatus(e,t,a){try{let r={status:t,updated_at:new Date().toISOString()};"failed"===t&&a&&(r.metadata={error_message:a,failed_at:new Date().toISOString()}),"sent"===t&&(r.delivered_at=new Date().toISOString());let{error:o}=await i.E.from("email_notifications").update(r).eq("id",e);if(o)throw o;console.log("\uD83D\uDCDD Email notification status updated:",{id:e,status:t,error:a||"N/A"})}catch(e){console.error("Error updating email notification status:",e)}}static async getUserNotifications(e,t=50,a){try{let r=i.E.from("email_notifications").select("*").eq("user_id",e).order("sent_at",{ascending:!1});a&&(r=r.eq("type",a)),t&&(r=r.limit(t));let{data:o,error:n}=await r;if(n)throw n;let s=i.E.from("email_notifications").select("*",{count:"exact",head:!0}).eq("user_id",e);a&&(s=s.eq("type",a));let{count:c}=await s;return{notifications:o||[],total:c||0}}catch(e){return console.error("Error obteniendo notificaciones del usuario:",e),{notifications:[],total:0}}}static async getFailedNotifications(e=24,t=10){try{let a=new Date(Date.now()-60*e*6e4).toISOString(),{data:r,error:o}=await i.E.from("email_notifications").select("*").eq("status","failed").gte("sent_at",a).limit(t);if(o)throw o;return r||[]}catch(e){return console.error("Error obteniendo notificaciones fallidas:",e),[]}}static async markAsRetried(e,t,a){try{await this.updateEmailNotificationStatus(e,t?"retried_successfully":"failed",t?"Successfully retried":a||"Retry failed")}catch(e){console.error("Error marcando notificaci\xf3n como reintentada:",e)}}static async cleanupOldNotifications(e=90){try{let t=new Date(Date.now()-24*e*36e5).toISOString();console.log(`🧹 Limpiando notificaciones anteriores a: ${t}`);let{data:a,error:r}=await i.E.from("email_notifications").delete().lt("sent_at",t).select("id");if(r)throw r;let o=a?.length||0;return console.log(`✅ Limpieza completada: ${o} notificaciones eliminadas`),{deleted:o}}catch(e){return console.error("Error en limpieza de notificaciones:",e),{deleted:0,error:e instanceof Error?e.message:"Unknown error"}}}}class n{static async sendEmail(e,t=0){let a=1e3*Math.pow(2,t),r=null;try{if(console.log(`📧 Enviando email (intento ${t+1}/4):`,{to:e.to,subject:e.subject,type:e.type}),r=await o.logEmailNotification(e,"pending"),await new Promise(e=>setTimeout(e,100)),.1>Math.random()&&0===t)throw Error("Simulated email provider error");return r&&await o.updateEmailNotificationStatus(r,"sent"),console.log("✅ Email enviado exitosamente"),!0}catch(n){let i=n instanceof Error?n.message:"Unknown error";if(console.error(`❌ Error enviando email (intento ${t+1}):`,i),r&&await o.updateEmailNotificationStatus(r,"failed",i),t<3)return console.log(`🔄 Reintentando env\xedo en ${a}ms...`),await new Promise(e=>setTimeout(e,a)),this.sendEmail(e,t+1);return console.error(`💥 Fallo definitivo despu\xe9s de 4 intentos`),!1}}static async retryFailedNotifications(e=24,t=10){try{console.log(`🔄 Buscando notificaciones fallidas para reintentar (m\xe1ximo ${e} horas)...`);let a=await o.getFailedNotifications(e,t);if(0===a.length)return console.log("✅ No se encontraron notificaciones fallidas para reintentar"),{attempted:0,successful:0,failed:0,errors:[]};console.log(`📋 Encontradas ${a.length} notificaciones para reintentar`);let r=0,i=0,n=[];for(let e of a)try{let t={to:e.recipient_email,subject:e.subject,htmlContent:"",textContent:"",type:e.type,userId:e.user_id,metadata:e.metadata};t.metadata={...t.metadata,retry_attempt:!0,original_notification_id:e.id,retry_at:new Date().toISOString()},await this.sendEmail(t)?(r++,await o.markAsRetried(e.id,!0)):(i++,await o.markAsRetried(e.id,!1,"Retry failed"),n.push(`Failed to retry notification ${e.id}`))}catch(a){i++;let t=`Error retrying notification ${e.id}: ${a instanceof Error?a.message:"Unknown error"}`;console.error(t),n.push(t),await o.markAsRetried(e.id,!1,t)}return console.log(`🎯 Reintentos completados: ${r} exitosos, ${i} fallidos`),{attempted:a.length,successful:r,failed:i,errors:n}}catch(e){throw console.error("❌ Error en retryFailedNotifications:",e),e}}static async sendTestEmail(e,t){try{let t={to:e,subject:"Test Email - OposI",htmlContent:`
          <h1>Email de Prueba</h1>
          <p>Este es un email de prueba para verificar la configuraci\xf3n del sistema de notificaciones.</p>
          <p>Enviado el: ${new Date().toLocaleString("es-ES")}</p>
        `,textContent:`
          Email de Prueba
          
          Este es un email de prueba para verificar la configuraci\xf3n del sistema de notificaciones.
          Enviado el: ${new Date().toLocaleString("es-ES")}
        `,type:"other",metadata:{test_email:!0,sent_at:new Date().toISOString()}},a=await this.sendEmail(t);return{success:a,message:a?"Email de prueba enviado exitosamente":"Fallo al enviar email de prueba",details:{to:e,timestamp:new Date().toISOString()}}}catch(e){return console.error("Error enviando email de prueba:",e),{success:!1,message:"Error enviando email de prueba",details:{error:e instanceof Error?e.message:"Unknown error"}}}}static async validateEmailProvider(){try{return{isValid:!0,provider:"Simulado",message:"Proveedor de email configurado correctamente"}}catch(e){return{isValid:!1,provider:"Unknown",message:e instanceof Error?e.message:"Error validando proveedor"}}}}class s{static async getNotificationStats(e,t){try{let a=i.E.from("email_notifications").select("*");e&&(a=a.gte("sent_at",e)),t&&(a=a.lte("sent_at",t));let{data:r,error:o}=await a;if(o)throw o;let n=(r||[]).reduce((e,t)=>(e[t.type]=(e[t.type]||0)+1,e),{}),s=(r||[]).reduce((e,t)=>(e[t.status]=(e[t.status]||0)+1,e),{}),c=(r||[]).sort((e,t)=>new Date(t.sent_at).getTime()-new Date(e.sent_at).getTime()).slice(0,10);return{byType:n,byStatus:s,total:r?.length||0,recentNotifications:c}}catch(e){return console.error("Error obteniendo estad\xedsticas de notificaciones:",e),{byType:{},byStatus:{},total:0,recentNotifications:[]}}}static async getFailureStats(e,t){try{let a=i.E.from("email_notifications").select("*").eq("status","failed");e&&(a=a.gte("sent_at",e)),t&&(a=a.lte("sent_at",t));let{data:r,error:o}=await a;if(o)throw o;let n=i.E.from("email_notifications").select("*",{count:"exact",head:!0});e&&(n=n.gte("sent_at",e)),t&&(n=n.lte("sent_at",t));let{count:s}=await n,c=(r||[]).reduce((e,t)=>{let a=t.metadata?.error_message||"Unknown error",r=this.categorizeError(a);return e[r]=(e[r]||0)+1,e},{}),l=r?.length||0;return{totalFailures:l,failureRate:Math.round(100*(s&&s>0?l/s*100:0))/100,errorsByType:c,recentFailures:(r||[]).sort((e,t)=>new Date(t.sent_at).getTime()-new Date(e.sent_at).getTime()).slice(0,10).map(e=>({id:e.id,type:e.type,recipient:e.recipient_email,error:e.metadata?.error_message||"Unknown error",failedAt:e.metadata?.failed_at||e.sent_at}))}}catch(e){return console.error("Error obteniendo estad\xedsticas de fallos:",e),{totalFailures:0,failureRate:0,errorsByType:{},recentFailures:[]}}}static categorizeError(e){let t=e.toLowerCase();return t.includes("network")||t.includes("timeout")||t.includes("connection")?"Network Error":t.includes("invalid")||t.includes("malformed")||t.includes("email")?"Invalid Email":t.includes("rate limit")||t.includes("quota")||t.includes("limit")?"Rate Limit":t.includes("auth")||t.includes("key")||t.includes("permission")?"Authentication Error":t.includes("bounce")||t.includes("reject")?"Email Bounced":"Other Error"}static async getPerformanceMetrics(e,t){try{let a=i.E.from("email_notifications").select("*");e&&(a=a.gte("sent_at",e)),t&&(a=a.lte("sent_at",t));let{data:r,error:o}=await a;if(o)throw o;let n=r?.length||0,s=r?.filter(e=>"sent"===e.status).length||0,c=(r||[]).reduce((e,t)=>{let a=new Date(t.sent_at).getHours();return e[a]=(e[a]||0)+1,e},{}),l=(r||[]).reduce((e,t)=>{let a=t.sent_at.split("T")[0];return e[a]=(e[a]||0)+1,e},{});return{totalSent:n,successRate:Math.round(100*(n>0?s/n*100:0))/100,avgResponseTime:0,peakHours:c,dailyVolume:l}}catch(e){return console.error("Error obteniendo m\xe9tricas de rendimiento:",e),{totalSent:0,successRate:0,avgResponseTime:0,peakHours:{},dailyVolume:{}}}}static async getTopUsersByVolume(e=10,t,a){try{let r=i.E.from("email_notifications").select("user_id, recipient_email, sent_at").not("user_id","is",null);t&&(r=r.gte("sent_at",t)),a&&(r=r.lte("sent_at",a));let{data:o,error:n}=await r;if(n)throw n;let s=(o||[]).reduce((e,t)=>{let a=t.user_id;return e[a]||(e[a]={userId:a,email:t.recipient_email,count:0,lastNotification:t.sent_at}),e[a].count++,new Date(t.sent_at)>new Date(e[a].lastNotification)&&(e[a].lastNotification=t.sent_at),e},{});return Object.values(s).sort((e,t)=>t.count-e.count).slice(0,e)}catch(e){return console.error("Error obteniendo top usuarios:",e),[]}}}class c{static async sendSubscriptionCancelledNotification(e,t,a,i,o){try{let s=r.generateSubscriptionCancelledEmail(t,a,i),c={to:e,subject:s.subject,htmlContent:s.htmlContent,textContent:s.textContent,type:"subscription_cancelled",userId:o,metadata:{planName:a,gracePeriodEnd:i,userName:t,daysRemaining:Math.ceil((new Date(i).getTime()-new Date().getTime())/864e5)}};return await n.sendEmail(c)}catch(e){return console.error("Error enviando notificaci\xf3n de cancelaci\xf3n:",e),!1}}static async sendGracePeriodEndingNotification(e,t,a,i,o){try{let s=r.generateGracePeriodEndingEmail(t,a,i),c={to:e,subject:s.subject,htmlContent:s.htmlContent,textContent:s.textContent,type:"grace_period_ending",userId:o,metadata:{planName:a,gracePeriodEnd:i,userName:t,hoursRemaining:Math.ceil((new Date(i).getTime()-new Date().getTime())/36e5)}};return await n.sendEmail(c)}catch(e){return console.error("Error enviando recordatorio de per\xedodo de gracia:",e),!1}}static async sendGenericNotification(e,t,a,i,o="other",s,c,l){try{let d=r.generateGenericEmail(t,a,i,c,l),u={to:e,subject:d.subject,htmlContent:d.htmlContent,textContent:d.textContent,type:o,userId:s,metadata:{userName:t,title:a,message:i,ctaText:c,ctaUrl:l}};return await n.sendEmail(u)}catch(e){return console.error("Error enviando notificaci\xf3n gen\xe9rica:",e),!1}}static async getUserNotifications(e,t=50,a){return await o.getUserNotifications(e,t,a)}static async getNotificationStats(e,t){return await s.getNotificationStats(e,t)}static async getFailureStats(e,t){return await s.getFailureStats(e,t)}static async retryFailedNotifications(e=24,t=10){return await n.retryFailedNotifications(e,t)}static async getPerformanceMetrics(e,t){return await s.getPerformanceMetrics(e,t)}static async getTopUsersByVolume(e=10,t,a){return await s.getTopUsersByVolume(e,t,a)}static async sendTestEmail(e,t){return await n.sendTestEmail(e,t)}static async validateEmailProvider(){return await n.validateEmailProvider()}static async cleanupOldNotifications(e=90){return await o.cleanupOldNotifications(e)}static async getSystemSummary(){try{let[e,t,a,r]=await Promise.all([this.validateEmailProvider(),this.getNotificationStats(new Date(Date.now()-6048e5).toISOString(),new Date().toISOString()),this.getFailureStats(new Date(Date.now()-6048e5).toISOString(),new Date().toISOString()),this.getPerformanceMetrics(new Date(Date.now()-6048e5).toISOString(),new Date().toISOString())]);return{providerStatus:e,recentStats:t,failureStats:a,performanceMetrics:r}}catch(e){throw console.error("Error obteniendo resumen del sistema:",e),e}}}},42049:(e,t,a)=>{"use strict";a.d(t,{E:()=>r,SupabaseAdminService:()=>i});let r=(0,a(86345).UU)("https://fxnhpxjijinfuxxxplzj.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY,{auth:{autoRefreshToken:!1,persistSession:!1}});class i{static async createStripeTransaction(e){let{data:t,error:a}=await r.from("stripe_transactions").insert([e]).select().single();if(a)throw console.error("Error creating stripe transaction:",a),Error(`Failed to create transaction: ${a.message}`);return t}static async getTransactionBySessionId(e){let{data:t,error:a}=await r.from("stripe_transactions").select("*").eq("stripe_session_id",e).single();if(a&&"PGRST116"!==a.code)throw console.error("Error fetching transaction:",a),Error(`Failed to fetch transaction: ${a.message}`);return t}static async createUserWithInvitation(e,t){console.log("\uD83D\uDD04 [SUPABASE_ADMIN] Creating user invitation:",{email:e,userData:t,redirectTo:"http://localhost:3000/auth/callback",timestamp:new Date().toISOString()});let{data:a,error:i}=await r.auth.admin.inviteUserByEmail(e,{data:t,redirectTo:"http://localhost:3000/auth/callback"});if(console.log("\uD83D\uDCCA [SUPABASE_ADMIN] Invitation result:",{hasData:!!a,hasUser:!!a?.user,userId:a?.user?.id,userEmail:a?.user?.email,userAud:a?.user?.aud,userRole:a?.user?.role,emailConfirmed:a?.user?.email_confirmed_at,userMetadata:a?.user?.user_metadata,appMetadata:a?.user?.app_metadata,error:i?.message,errorCode:i?.status,fullError:i}),i)throw console.error("❌ [SUPABASE_ADMIN] Error creating user invitation:",{message:i.message,status:i.status,details:i}),Error(`Failed to create user invitation: ${i.message}`);return a}static async createUserWithPassword(e,t,a,i=!0){console.log("\uD83D\uDD04 [SUPABASE_ADMIN] Creating user with password:",{email:e,userData:a,sendConfirmationEmail:i,timestamp:new Date().toISOString()});let{data:o,error:n}=await r.auth.admin.createUser({email:e,password:t,user_metadata:a,email_confirm:!1});if(console.log("\uD83D\uDCCA [SUPABASE_ADMIN] User creation result:",{hasData:!!o,hasUser:!!o?.user,userId:o?.user?.id,userEmail:o?.user?.email,emailConfirmed:o?.user?.email_confirmed_at,userMetadata:o?.user?.user_metadata,error:n?.message,errorCode:n?.status}),n)return console.error("❌ [SUPABASE_ADMIN] Error creating user with password:",{message:n.message,status:n.status,details:n}),{data:null,error:n};if(o?.user&&i){console.log("\uD83D\uDCE7 Enviando email de confirmaci\xf3n...");let{error:a}=await r.auth.admin.generateLink({type:"signup",email:e,password:t,options:{redirectTo:"http://localhost:3000/auth/confirmed"}});a?console.error("⚠️ Error enviando email de confirmaci\xf3n:",a):console.log("✅ Email de confirmaci\xf3n enviado exitosamente")}else o?.user&&!i&&console.log("\uD83D\uDCE7 Email de confirmaci\xf3n omitido (se enviar\xe1 despu\xe9s del pago)");return{data:o,error:null}}static async sendConfirmationEmailForUser(e){console.log("\uD83D\uDCE7 [SUPABASE_ADMIN] Enviando email de confirmaci\xf3n para usuario:",e);try{let{data:t,error:a}=await r.auth.admin.getUserById(e);if(a||!t?.user)return console.error("Error obteniendo datos del usuario:",a),{success:!1,error:"Usuario no encontrado"};let i=t.user,{error:o}=await r.auth.admin.updateUserById(i.id,{email_confirm:!0,user_metadata:{...i.user_metadata,payment_verified:!0,email_confirmed_via_payment:!0,confirmed_at:new Date().toISOString()}});if(o)return console.error("⚠️ Error confirmando email del usuario:",o),{success:!1,error:o.message};return console.log("✅ Usuario confirmado autom\xe1ticamente despu\xe9s del pago exitoso"),{success:!0}}catch(e){return console.error("Error en sendConfirmationEmailForUser:",e),{success:!1,error:e instanceof Error?e.message:"Error desconocido"}}}static async sendConfirmationEmail(e,t){console.log("\uD83D\uDCE7 [SUPABASE_ADMIN] Enviando email de confirmaci\xf3n para:",e);let{error:a}=await r.auth.admin.generateLink({type:"signup",email:e,password:t,options:{redirectTo:"http://localhost:3000/auth/confirmed"}});return a?(console.error("⚠️ Error enviando email de confirmaci\xf3n:",a),{success:!1,error:a.message}):(console.log("✅ Email de confirmaci\xf3n enviado exitosamente"),{success:!0})}static async createUserProfile(e){let{data:t,error:a}=await r.from("user_profiles").insert([e]).select().single();if(a)throw console.error("Error creating user profile:",a),Error(`Failed to create user profile: ${a.message}`);return t}static async upsertUserProfile(e){let{data:t,error:a}=await r.from("user_profiles").upsert([e],{onConflict:"user_id"}).select().single();if(a)throw console.error("Error upserting user profile:",a),Error(`Failed to upsert user profile: ${a.message}`);return t}static async logPlanChange(e){let{data:t,error:a}=await r.from("user_plan_history").insert([e]).select().single();if(a)throw console.error("Error logging plan change:",a),Error(`Failed to log plan change: ${a.message}`);return t}static async logFeatureAccess(e){let{data:t,error:a}=await r.from("feature_access_log").insert([e]).select().single();if(a)throw console.error("Error logging feature access:",a),Error(`Failed to log feature access: ${a.message}`);return t}static async getUserProfile(e){let{data:t,error:a}=await r.from("user_profiles").select("*").eq("user_id",e).single();if(a&&"PGRST116"!==a.code)throw console.error("Error fetching user profile:",a),Error(`Failed to fetch user profile: ${a.message}`);return t}static async updateTransactionWithUser(e,t){let{error:a}=await r.from("stripe_transactions").update({user_id:t,updated_at:new Date().toISOString()}).eq("id",e);if(a)throw console.error("Error updating transaction with user_id:",a),Error(`Failed to update transaction: ${a.message}`)}static async activateTransaction(e){let{error:t}=await r.from("stripe_transactions").update({activated_at:new Date().toISOString()}).eq("id",e);if(t)throw console.error("Error activating transaction:",t),Error(`Failed to activate transaction: ${t.message}`)}static async getDocumentsCount(e){let{count:t,error:a}=await r.from("documentos").select("*",{count:"exact",head:!0}).eq("user_id",e);return a?(console.error("Error getting documents count:",a),0):t||0}static async getUserByEmail(e){try{let{data:{users:t},error:a}=await r.auth.admin.listUsers();if(a)throw console.error("Error getting user by email:",a),Error(`Failed to get user by email: ${a.message}`);if(!t||0===t.length)return null;let i=t.find(t=>t.email===e);if(!i)return null;return{id:i.id,email:i.email,email_confirmed_at:i.email_confirmed_at}}catch(e){throw console.error("Error in getUserByEmail:",e),e}}}},78335:()=>{},96487:()=>{}};