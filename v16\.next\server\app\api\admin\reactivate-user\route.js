(()=>{var e={};e.id=2718,e.ids=[2718],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8814:(e,r,t)=>{"use strict";t.d(r,{IE:()=>s,Nu:()=>i,qo:()=>a,t4:()=>o});let a={free:{id:"free",name:"Plan Gratis",price:0,limits:{documents:1,mindMapsForTrial:2,testsForTrial:10,flashcardsForTrial:10,tokensForTrial:5e4,features:["document_upload","test_generation","flashcard_generation","mind_map_generation"]},features:["document_upload","test_generation","flashcard_generation","mind_map_generation"],restrictedFeatures:["study_planning","ai_tutor_chat","summary_a1_a2"]},usuario:{id:"usuario",name:"Plan Usuario",price:1e3,limits:{documents:-1,mindMapsPerWeek:-1,testsPerWeek:-1,flashcardsPerWeek:-1,monthlyTokens:1e6,features:["document_upload","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation"]},features:["document_upload","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation"],restrictedFeatures:["study_planning","summary_a1_a2"]},pro:{id:"pro",name:"Plan Pro",price:1500,limits:{documents:-1,mindMapsPerWeek:-1,testsPerWeek:-1,flashcardsPerWeek:-1,monthlyTokens:1e6,features:["document_upload","study_planning","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation","summary_a1_a2"]},features:["document_upload","study_planning","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation","summary_a1_a2"],restrictedFeatures:[]}};function s(e){return a[e]||null}function o(e){let r=s(e);return r?"free"===e?r.limits.tokensForTrial||5e4:r.limits.monthlyTokens||1e6:5e4}function i(e,r){let t=s(e);return!(!t||t.restrictedFeatures.includes(r))&&t.features.includes(r)}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},42049:(e,r,t)=>{"use strict";t.d(r,{E:()=>a,SupabaseAdminService:()=>s});let a=(0,t(86345).UU)("https://fxnhpxjijinfuxxxplzj.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY,{auth:{autoRefreshToken:!1,persistSession:!1}});class s{static async createStripeTransaction(e){let{data:r,error:t}=await a.from("stripe_transactions").insert([e]).select().single();if(t)throw console.error("Error creating stripe transaction:",t),Error(`Failed to create transaction: ${t.message}`);return r}static async getTransactionBySessionId(e){let{data:r,error:t}=await a.from("stripe_transactions").select("*").eq("stripe_session_id",e).single();if(t&&"PGRST116"!==t.code)throw console.error("Error fetching transaction:",t),Error(`Failed to fetch transaction: ${t.message}`);return r}static async createUserWithInvitation(e,r){console.log("\uD83D\uDD04 [SUPABASE_ADMIN] Creating user invitation:",{email:e,userData:r,redirectTo:"http://localhost:3000/auth/callback",timestamp:new Date().toISOString()});let{data:t,error:s}=await a.auth.admin.inviteUserByEmail(e,{data:r,redirectTo:"http://localhost:3000/auth/callback"});if(console.log("\uD83D\uDCCA [SUPABASE_ADMIN] Invitation result:",{hasData:!!t,hasUser:!!t?.user,userId:t?.user?.id,userEmail:t?.user?.email,userAud:t?.user?.aud,userRole:t?.user?.role,emailConfirmed:t?.user?.email_confirmed_at,userMetadata:t?.user?.user_metadata,appMetadata:t?.user?.app_metadata,error:s?.message,errorCode:s?.status,fullError:s}),s)throw console.error("❌ [SUPABASE_ADMIN] Error creating user invitation:",{message:s.message,status:s.status,details:s}),Error(`Failed to create user invitation: ${s.message}`);return t}static async createUserWithPassword(e,r,t,s=!0){console.log("\uD83D\uDD04 [SUPABASE_ADMIN] Creating user with password:",{email:e,userData:t,sendConfirmationEmail:s,timestamp:new Date().toISOString()});let{data:o,error:i}=await a.auth.admin.createUser({email:e,password:r,user_metadata:t,email_confirm:!1});if(console.log("\uD83D\uDCCA [SUPABASE_ADMIN] User creation result:",{hasData:!!o,hasUser:!!o?.user,userId:o?.user?.id,userEmail:o?.user?.email,emailConfirmed:o?.user?.email_confirmed_at,userMetadata:o?.user?.user_metadata,error:i?.message,errorCode:i?.status}),i)return console.error("❌ [SUPABASE_ADMIN] Error creating user with password:",{message:i.message,status:i.status,details:i}),{data:null,error:i};if(o?.user&&s){console.log("\uD83D\uDCE7 Enviando email de confirmaci\xf3n...");let{error:t}=await a.auth.admin.generateLink({type:"signup",email:e,password:r,options:{redirectTo:"http://localhost:3000/auth/confirmed"}});t?console.error("⚠️ Error enviando email de confirmaci\xf3n:",t):console.log("✅ Email de confirmaci\xf3n enviado exitosamente")}else o?.user&&!s&&console.log("\uD83D\uDCE7 Email de confirmaci\xf3n omitido (se enviar\xe1 despu\xe9s del pago)");return{data:o,error:null}}static async sendConfirmationEmailForUser(e){console.log("\uD83D\uDCE7 [SUPABASE_ADMIN] Enviando email de confirmaci\xf3n para usuario:",e);try{let{data:r,error:t}=await a.auth.admin.getUserById(e);if(t||!r?.user)return console.error("Error obteniendo datos del usuario:",t),{success:!1,error:"Usuario no encontrado"};let s=r.user,{error:o}=await a.auth.admin.updateUserById(s.id,{email_confirm:!0,user_metadata:{...s.user_metadata,payment_verified:!0,email_confirmed_via_payment:!0,confirmed_at:new Date().toISOString()}});if(o)return console.error("⚠️ Error confirmando email del usuario:",o),{success:!1,error:o.message};return console.log("✅ Usuario confirmado autom\xe1ticamente despu\xe9s del pago exitoso"),{success:!0}}catch(e){return console.error("Error en sendConfirmationEmailForUser:",e),{success:!1,error:e instanceof Error?e.message:"Error desconocido"}}}static async sendConfirmationEmail(e,r){console.log("\uD83D\uDCE7 [SUPABASE_ADMIN] Enviando email de confirmaci\xf3n para:",e);let{error:t}=await a.auth.admin.generateLink({type:"signup",email:e,password:r,options:{redirectTo:"http://localhost:3000/auth/confirmed"}});return t?(console.error("⚠️ Error enviando email de confirmaci\xf3n:",t),{success:!1,error:t.message}):(console.log("✅ Email de confirmaci\xf3n enviado exitosamente"),{success:!0})}static async createUserProfile(e){let{data:r,error:t}=await a.from("user_profiles").insert([e]).select().single();if(t)throw console.error("Error creating user profile:",t),Error(`Failed to create user profile: ${t.message}`);return r}static async upsertUserProfile(e){let{data:r,error:t}=await a.from("user_profiles").upsert([e],{onConflict:"user_id"}).select().single();if(t)throw console.error("Error upserting user profile:",t),Error(`Failed to upsert user profile: ${t.message}`);return r}static async logPlanChange(e){let{data:r,error:t}=await a.from("user_plan_history").insert([e]).select().single();if(t)throw console.error("Error logging plan change:",t),Error(`Failed to log plan change: ${t.message}`);return r}static async logFeatureAccess(e){let{data:r,error:t}=await a.from("feature_access_log").insert([e]).select().single();if(t)throw console.error("Error logging feature access:",t),Error(`Failed to log feature access: ${t.message}`);return r}static async getUserProfile(e){let{data:r,error:t}=await a.from("user_profiles").select("*").eq("user_id",e).single();if(t&&"PGRST116"!==t.code)throw console.error("Error fetching user profile:",t),Error(`Failed to fetch user profile: ${t.message}`);return r}static async updateTransactionWithUser(e,r){let{error:t}=await a.from("stripe_transactions").update({user_id:r,updated_at:new Date().toISOString()}).eq("id",e);if(t)throw console.error("Error updating transaction with user_id:",t),Error(`Failed to update transaction: ${t.message}`)}static async activateTransaction(e){let{error:r}=await a.from("stripe_transactions").update({activated_at:new Date().toISOString()}).eq("id",e);if(r)throw console.error("Error activating transaction:",r),Error(`Failed to activate transaction: ${r.message}`)}static async getDocumentsCount(e){let{count:r,error:t}=await a.from("documentos").select("*",{count:"exact",head:!0}).eq("user_id",e);return t?(console.error("Error getting documents count:",t),0):r||0}static async getUserByEmail(e){try{let{data:{users:r},error:t}=await a.auth.admin.listUsers();if(t)throw console.error("Error getting user by email:",t),Error(`Failed to get user by email: ${t.message}`);if(!r||0===r.length)return null;let s=r.find(r=>r.email===e);if(!s)return null;return{id:s.id,email:s.email,email_confirmed_at:s.email_confirmed_at}}catch(e){throw console.error("Error in getUserByEmail:",e),e}}}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},61414:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>_,routeModule:()=>d,serverHooks:()=>f,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>p});var a={};t.r(a),t.d(a,{POST:()=>l});var s=t(96559),o=t(48088),i=t(37719),n=t(32190),u=t(42049),c=t(61730);async function l(e){try{let{sessionId:r,email:t,userId:a}=await e.json();if(!r&&!t&&!a)return n.NextResponse.json({error:"Se requiere sessionId, email o userId"},{status:400});console.log("\uD83D\uDD04 Iniciando reactivaci\xf3n de usuario:",{sessionId:r,email:t,userId:a});let s=a,o=null;if(r){try{c._4&&(o=await c._4.checkout.sessions.retrieve(r),console.log("\uD83D\uDCCA Sesi\xf3n de Stripe obtenida:",{id:o.id,paymentStatus:o.payment_status,customerEmail:o.customer_details?.email}))}catch(e){return console.error("Error obteniendo sesi\xf3n de Stripe:",e),n.NextResponse.json({error:"Error verificando sesi\xf3n en Stripe"},{status:500})}if(o?.payment_status!=="paid")return n.NextResponse.json({error:"El pago no est\xe1 completado en Stripe"},{status:400})}if(!s){let e=t||o?.customer_details?.email||o?.metadata?.customerEmail;if(!e)return n.NextResponse.json({error:"No se pudo determinar el email del usuario"},{status:400});let r=await u.SupabaseAdminService.getUserByEmail(e);if(!r)return n.NextResponse.json({error:"Usuario no encontrado"},{status:404});s=r.id}console.log("\uD83D\uDC64 Usuario objetivo identificado:",s);let i=null;if(r&&(i=await u.SupabaseAdminService.getTransactionBySessionId(r)),r&&!i&&o){let e=o.metadata?.planId||o.client_reference_id;if(!e)return n.NextResponse.json({error:"No se pudo determinar el plan del usuario"},{status:400});console.log("\uD83D\uDCB3 Creando transacci\xf3n faltante..."),i=await u.SupabaseAdminService.createStripeTransaction({stripe_session_id:r,stripe_customer_id:o.customer,user_email:o.customer_details?.email||o.metadata?.customerEmail||t,user_name:o.customer_details?.name||o.metadata?.customerName,plan_id:e,amount:o.amount_total||0,currency:o.currency||"eur",payment_status:"paid",subscription_id:o.subscription||void 0,user_id:s,metadata:{created_by:"reactivation_api",reactivated_at:new Date().toISOString()}}),console.log("✅ Transacci\xf3n creada:",i.id)}let l=await u.SupabaseAdminService.getUserProfile(s);if(l){console.log("\uD83D\uDD04 Actualizando perfil de usuario...");let e={payment_verified:!0,updated_at:new Date().toISOString(),security_flags:{...l.security_flags,reactivated:!0,reactivated_at:new Date().toISOString(),payment_completed:!0}};o&&(e.stripe_customer_id=o.customer,e.last_payment_date=new Date().toISOString(),o.subscription&&(e.stripe_subscription_id=o.subscription,e.auto_renew=!0)),await u.SupabaseAdminService.upsertUserProfile({...l,...e}),console.log("✅ Perfil de usuario actualizado")}i&&(await u.SupabaseAdminService.activateTransaction(i.id),console.log("✅ Transacci\xf3n activada")),console.log("\uD83D\uDCE7 Enviando email de confirmaci\xf3n...");let d=await u.SupabaseAdminService.sendConfirmationEmailForUser(s);return d.success||console.error("⚠️ Error enviando email:",d.error),n.NextResponse.json({success:!0,message:"Usuario reactivado exitosamente",data:{userId:s,transactionId:i?.id,emailSent:d.success,profileUpdated:!!l}})}catch(e){return console.error("Error reactivando usuario:",e),n.NextResponse.json({error:"Error interno del servidor"},{status:500})}}let d=new s.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/admin/reactivate-user/route",pathname:"/api/admin/reactivate-user",filename:"route",bundlePath:"app/api/admin/reactivate-user/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\admin\\reactivate-user\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:m,workUnitAsyncStorage:p,serverHooks:f}=d;function _(){return(0,i.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:p})}},61730:(e,r,t)=>{"use strict";t.d(r,{L6:()=>u,Md:()=>i,aN:()=>n,_4:()=>c});var a=t(97877),s=t(8814);let o={free:{id:"free",name:"Plan Gratis",price:0,stripeProductId:null,stripePriceId:null,features:["Incluye:","• Uso de la plataforma solo durante 5 d\xedas","• Subida de documentos: m\xe1ximo 1 documento","• Generador de test: m\xe1ximo 10 preguntas test","• Generador de flashcards: m\xe1ximo 10 tarjetas flashcard","• Generador de mapas mentales: m\xe1ximo 2 mapas mentales","No incluye:","• Planificaci\xf3n de estudios","• Habla con tu preparador IA","• Res\xfamenes A2 y A1"],limits:s.qo.free.limits,planConfig:s.qo.free},usuario:{id:"usuario",name:"Plan Usuario",price:1e3,stripeProductId:"prod_SR65BdKdek1OXd",stripePriceId:"price_1Rae5807kFn3sIXhRf3adX1n",features:["Incluye:","• Uso de la plataforma durante el mes (una vez finalizado no podr\xe1 volver a acceder hasta renovaci\xf3n)","• Subida de documentos","• Habla con tu preparador IA *","• Generador de test *","• Generador de flashcards *","• Generador de mapas mentales *","• * Para las tareas en las que se haga uso de IA, el l\xedmite mensual ser\xe1 de 1.000.000 de tokens, podr\xe1n ampliarse durante el mes mediante pago","No incluye:","• Planificaci\xf3n de estudios","• Res\xfamenes A2 y A1"],limits:s.qo.usuario.limits,planConfig:s.qo.usuario},pro:{id:"pro",name:"Plan Pro",price:1500,stripeProductId:"prod_SR66U2G7bVJqu3",stripePriceId:"price_1Rae3U07kFn3sIXhkvSuJco1",features:["Incluye:","• Uso de la plataforma durante el mes (una vez finalizado no podr\xe1 volver a acceder hasta renovaci\xf3n)","• Subida de documentos","• Planificaci\xf3n de estudios mediante IA*","• Habla con tu preparador IA *","• Generador de test *","• Generador de flashcards *","• Generador de mapas mentales *","• Generaci\xf3n de Res\xfamenes para A2 y A1","• * Para las tareas en las que se haga uso de IA, el l\xedmite mensual ser\xe1 de 1.000.000 de tokens, podr\xe1n ampliarse durante el mes mediante pago"],limits:s.qo.pro.limits,planConfig:s.qo.pro}};function i(e){return o[e]||null}function n(e){return e in o}let u={success:"http://localhost:3000/thank-you",cancel:"http://localhost:3000/upgrade-plan",webhook:"http://localhost:3000/api/stripe/webhook"},c=new a.A(process.env.STRIPE_SECRET_KEY,{apiVersion:"2025-05-28.basil",typescript:!0})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4447,580,6345,7877],()=>t(61414));module.exports=a})();