(()=>{var e={};e.id=4520,e.ids=[4520],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4141:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>x});var s=t(60687),a=t(43210),o=t(16189),i=t(65660),n=t(27605),l=t(63442),c=t(37590),d=t(79481),u=t(41835),m=t(17019);function p({isOpen:e,onClose:r}){let[t,o]=(0,a.useState)(!1),[i,p]=(0,a.useState)(!1),x=(0,d.U)(),{register:b,handleSubmit:g,formState:{errors:f},reset:h,setFocus:v}=(0,n.mN)({resolver:(0,l.u)(u.E9),defaultValues:{email:""}}),j=async e=>{o(!0);try{let{error:r}=await x.auth.resetPasswordForEmail(e.email,{redirectTo:"http://localhost:3000/auth/reset-password"});r?(console.error("Error al solicitar reseteo de contrase\xf1a:",r.message),r.message.includes("rate limit")||r.message.includes("too many")?c.oR.error("Has solicitado demasiados enlaces de recuperaci\xf3n. Por favor, espera unos minutos antes de intentar nuevamente."):c.oR.error("Ocurri\xf3 un error al procesar tu solicitud. Por favor, int\xe9ntalo nuevamente.")):(p(!0),c.oR.success("Si tu email est\xe1 registrado, recibir\xe1s un enlace para restablecer tu contrase\xf1a."))}catch(e){console.error("Error inesperado:",e),c.oR.error("Ocurri\xf3 un error inesperado. Por favor, int\xe9ntalo nuevamente.")}finally{o(!1)}},y=()=>{t||r()};return e?(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",onClick:e=>{e.target===e.currentTarget&&y()},role:"dialog","aria-modal":"true","aria-labelledby":"forgot-password-title","aria-describedby":"forgot-password-description",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-md w-full p-6 relative",children:[(0,s.jsx)("button",{onClick:y,disabled:t,className:"absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors disabled:opacity-50","aria-label":"Cerrar modal",children:(0,s.jsx)(m.yGN,{className:"w-6 h-6"})}),(0,s.jsxs)("div",{className:"pr-8",children:[(0,s.jsxs)("div",{className:"flex items-center mb-4",children:[(0,s.jsx)(m.pHD,{className:"w-8 h-8 text-blue-600 mr-3"}),(0,s.jsx)("h2",{id:"forgot-password-title",className:"text-xl font-semibold text-gray-900",children:"Restablecer Contrase\xf1a"})]}),i?(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)(m.kGk,{className:"w-8 h-8 text-green-600"})}),(0,s.jsx)("p",{className:"text-gray-600 mb-6",children:"Si tu email est\xe1 registrado en nuestro sistema, recibir\xe1s un enlace para restablecer tu contrase\xf1a en los pr\xf3ximos minutos."}),(0,s.jsx)("p",{className:"text-sm text-gray-500 mb-6",children:"Revisa tu bandeja de entrada y la carpeta de spam."}),(0,s.jsx)("button",{onClick:y,className:"w-full px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-blue-700 border border-transparent rounded-md hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:"Entendido"})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("p",{id:"forgot-password-description",className:"text-gray-600 mb-6",children:"Ingresa tu direcci\xf3n de email y te enviaremos un enlace para restablecer tu contrase\xf1a."}),(0,s.jsxs)("form",{onSubmit:g(j),className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"reset-email",className:"block text-sm font-medium text-gray-700 mb-1",children:"Email"}),(0,s.jsx)("input",{id:"reset-email",type:"email",autoComplete:"email",disabled:t,...b("email"),className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",placeholder:"<EMAIL>"}),f.email&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",role:"alert",children:f.email.message})]}),(0,s.jsxs)("div",{className:"flex gap-3 pt-2",children:[(0,s.jsx)("button",{type:"button",onClick:y,disabled:t,className:"flex-1 px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed",children:"Cancelar"}),(0,s.jsx)("button",{type:"submit",disabled:t,className:"flex-1 flex items-center justify-center px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-blue-700 border border-transparent rounded-md hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",children:t?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(m.TwU,{className:"w-4 h-4 mr-2 animate-spin"}),"Enviando..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(m.kGk,{className:"w-4 h-4 mr-2"}),"Enviar Enlace"]})})]})]})]})]})]})}):null}function x(){let[e,r]=(0,a.useState)(""),[t,n]=(0,a.useState)(""),[l,c]=(0,a.useState)(""),[d,u]=(0,a.useState)(!1),{iniciarSesion:m,error:x,isLoading:b,user:g}=(0,i.A)();(0,o.useRouter)();let f=async r=>(r.preventDefault(),c(""),e.trim())?t.trim()?void await m(e,t):void c("Por favor, ingresa tu contrase\xf1a"):void c("Por favor, ingresa tu email");return(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:[(0,s.jsxs)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-center bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"OposiAI"}),(0,s.jsx)("h2",{className:"mt-2 text-center text-xl font-semibold text-gray-900",children:"Inicia sesi\xf3n en tu cuenta"}),(0,s.jsx)("p",{className:"mt-2 text-center text-sm text-gray-600",children:"Accede a tu asistente inteligente para oposiciones"})]}),(0,s.jsx)("div",{className:"mt-8 sm:mx-auto sm:w-full sm:max-w-md",children:(0,s.jsxs)("div",{className:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10",children:[(0,s.jsxs)("form",{className:"space-y-6",onSubmit:f,children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email"}),(0,s.jsx)("div",{className:"mt-1",children:(0,s.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:e,onChange:e=>r(e.target.value),className:"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",disabled:b})})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Contrase\xf1a"}),(0,s.jsx)("div",{className:"mt-1",children:(0,s.jsx)("input",{id:"password",name:"password",type:"password",autoComplete:"current-password",required:!0,value:t,onChange:e=>n(e.target.value),className:"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",disabled:b})})]}),(0,s.jsx)("div",{className:"flex items-center justify-end",children:(0,s.jsx)("button",{type:"button",onClick:()=>u(!0),disabled:b,className:"text-sm text-blue-600 hover:text-blue-500 focus:outline-none focus:underline disabled:opacity-50 disabled:cursor-not-allowed",children:"\xbfOlvidaste tu contrase\xf1a?"})}),l&&(0,s.jsxs)("div",{className:"text-red-500 text-sm bg-red-50 p-3 rounded-md border border-red-200",children:[l,l.includes("sincronizaci\xf3n de tiempo")&&(0,s.jsxs)("div",{className:"mt-2 text-gray-600 text-xs",children:[(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Sugerencia:"})," Este error puede ocurrir cuando la hora de tu dispositivo no est\xe1 sincronizada correctamente."]}),(0,s.jsxs)("ol",{className:"list-decimal pl-5 mt-1",children:[(0,s.jsx)("li",{children:"Verifica que la fecha y hora de tu dispositivo est\xe9n configuradas correctamente"}),(0,s.jsx)("li",{children:"Activa la sincronizaci\xf3n autom\xe1tica de hora en tu sistema"}),(0,s.jsx)("li",{children:"Reinicia el navegador e intenta nuevamente"})]})]})]}),(0,s.jsx)("div",{children:(0,s.jsx)("button",{type:"submit",disabled:b,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",children:b?(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Iniciando sesi\xf3n..."]}):"Iniciar sesi\xf3n"})})]}),(0,s.jsx)("div",{className:"mt-6",children:(0,s.jsx)("p",{className:"text-center text-sm text-gray-600",children:"\xbfNo tienes una cuenta? Contacta con el administrador para solicitar acceso."})})]})}),(0,s.jsx)(p,{isOpen:d,onClose:()=>u(!1)})]})}},9871:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var s=t(65239),a=t(48088),o=t(88170),i=t.n(o),n=t(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);t.d(r,l);let c={children:["",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,94934)),"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\login\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\login\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/login/page",pathname:"/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},41835:(e,r,t)=>{"use strict";t.d(r,{E9:()=>c,GS:()=>i,MZ:()=>l,oO:()=>n,oS:()=>a});var s=t(9275);let a=s.z.object({pregunta:s.z.string().min(1,"La pregunta es obligatoria").max(500,"M\xe1ximo 500 caracteres"),documentos:s.z.array(s.z.object({id:s.z.string().optional(),titulo:s.z.string().min(1),contenido:s.z.string().min(1),categoria:s.z.string().optional().nullable(),numero_tema:s.z.union([s.z.number().int().positive(),s.z.string(),s.z.null(),s.z.undefined()]).optional(),creado_en:s.z.string().optional(),actualizado_en:s.z.string().optional(),user_id:s.z.string().optional(),tipo_original:s.z.string().optional()})).min(1,"Debes seleccionar al menos un documento")}),o=s.z.object({peticion:s.z.string().min(1,"La petici\xf3n es obligatoria").max(500,"M\xe1ximo 500 caracteres")}),i=s.z.object({peticion:s.z.string().min(1,"La petici\xf3n es obligatoria").max(500,"M\xe1ximo 500 caracteres"),cantidad:s.z.number().min(1,"M\xednimo 1 pregunta").max(50,"M\xe1ximo 50 preguntas").default(10)}),n=s.z.object({peticion:s.z.string().min(1,"La petici\xf3n es obligatoria").max(500,"M\xe1ximo 500 caracteres"),cantidad:s.z.number().min(1,"M\xednimo 1 flashcard").max(30,"M\xe1ximo 30 flashcards").default(10)}),l=o,c=s.z.object({email:s.z.string().min(1,"El email es obligatorio").email("Por favor, ingresa un email v\xe1lido").max(255,"El email es demasiado largo")});s.z.object({password:s.z.string().min(8,"La contrase\xf1a debe tener al menos 8 caracteres").max(128,"La contrase\xf1a es demasiado larga"),confirmPassword:s.z.string()}).refine(e=>e.password===e.confirmPassword,{message:"Las contrase\xf1as no coinciden",path:["confirmPassword"]})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68652:(e,r,t)=>{Promise.resolve().then(t.bind(t,94934))},74075:e=>{"use strict";e.exports=require("zlib")},74732:(e,r,t)=>{Promise.resolve().then(t.bind(t,4141))},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},94934:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\login\\page.tsx","default")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,6126,7019,2212,1191],()=>t(9871));module.exports=s})();