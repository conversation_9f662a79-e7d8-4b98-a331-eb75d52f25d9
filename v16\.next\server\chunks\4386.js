"use strict";exports.id=4386,exports.ids=[4386],exports.modules={34386:(e,t,r)=>{let o;r.d(t,{createBrowserClient:()=>v,createServerClient:()=>A});var i=r(86345);let n="0.6.1";var a=r(49343);function s(){return"undefined"!=typeof window&&void 0!==window.document}let l={path:"/",sameSite:"lax",httpOnly:!1,maxAge:3456e4},c=/^(.*)[.](0|[1-9][0-9]*)$/;function u(e,t){if(e===t)return!0;let r=e.match(c);return!!r&&r[1]===t}function d(e,t,r){let o=r??3180,i=encodeURIComponent(t);if(i.length<=o)return[{name:e,value:t}];let n=[];for(;i.length>0;){let e=i.slice(0,o),t=e.lastIndexOf("%");t>o-3&&(e=e.slice(0,t));let r="";for(;e.length>0;)try{r=decodeURIComponent(e);break}catch(t){if(t instanceof URIError&&"%"===e.at(-3)&&e.length>3)e=e.slice(0,e.length-3);else throw t}n.push(r),i=i.slice(e.length)}return n.map((t,r)=>({name:`${e}.${r}`,value:t}))}async function f(e,t){let r=await t(e);if(r)return r;let o=[];for(let r=0;;r++){let i=`${e}.${r}`,n=await t(i);if(!n)break;o.push(n)}return o.length>0?o.join(""):null}let p="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),h=" 	\n\r=".split(""),m=(()=>{let e=Array(128);for(let t=0;t<e.length;t+=1)e[t]=-1;for(let t=0;t<h.length;t+=1)e[h[t].charCodeAt(0)]=-2;for(let t=0;t<p.length;t+=1)e[p[t].charCodeAt(0)]=t;return e})();function g(e){let t=[],r=0,o=0;if(function(e,t){for(let r=0;r<e.length;r+=1){let o=e.charCodeAt(r);if(o>55295&&o<=56319){let t=(o-55296)*1024&65535;o=(e.charCodeAt(r+1)-56320&65535|t)+65536,r+=1}!function(e,t){if(e<=127)return t(e);if(e<=2047){t(192|e>>6),t(128|63&e);return}if(e<=65535){t(224|e>>12),t(128|e>>6&63),t(128|63&e);return}if(e<=1114111){t(240|e>>18),t(128|e>>12&63),t(128|e>>6&63),t(128|63&e);return}throw Error(`Unrecognized Unicode codepoint: ${e.toString(16)}`)}(o,t)}}(e,e=>{for(r=r<<8|e,o+=8;o>=6;){let e=r>>o-6&63;t.push(p[e]),o-=6}}),o>0)for(r<<=6-o,o=6;o>=6;){let e=r>>o-6&63;t.push(p[e]),o-=6}return t.join("")}function w(e){let t=[],r=e=>{t.push(String.fromCodePoint(e))},o={utf8seq:0,codepoint:0},i=0,n=0;for(let t=0;t<e.length;t+=1){let a=m[e.charCodeAt(t)];if(a>-1)for(i=i<<6|a,n+=6;n>=8;)(function(e,t,r){if(0===t.utf8seq){if(e<=127)return r(e);for(let r=1;r<6;r+=1)if((e>>7-r&1)==0){t.utf8seq=r;break}if(2===t.utf8seq)t.codepoint=31&e;else if(3===t.utf8seq)t.codepoint=15&e;else if(4===t.utf8seq)t.codepoint=7&e;else throw Error("Invalid UTF-8 sequence");t.utf8seq-=1}else if(t.utf8seq>0){if(e<=127)throw Error("Invalid UTF-8 sequence");t.codepoint=t.codepoint<<6|63&e,t.utf8seq-=1,0===t.utf8seq&&r(t.codepoint)}})(i>>n-8&255,o,r),n-=8;else if(-2===a)continue;else throw Error(`Invalid Base64-URL character "${e.at(t)}" at position ${t}`)}return t.join("")}let y="base64-";function b(e,t){let r,o,i=e.cookies??null,n=e.cookieEncoding,c={},p={};if(i)if("get"in i){let e=async e=>{let t=e.flatMap(e=>[e,...Array.from({length:5}).map((t,r)=>`${e}.${r}`)]),r=[];for(let e=0;e<t.length;e+=1){let o=await i.get(t[e]);(o||"string"==typeof o)&&r.push({name:t[e],value:o})}return r};if(r=async t=>await e(t),"set"in i&&"remove"in i)o=async e=>{for(let t=0;t<e.length;t+=1){let{name:r,value:o,options:n}=e[t];o?await i.set(r,o,n):await i.remove(r,n)}};else if(t)o=async()=>{console.warn("@supabase/ssr: createServerClient was configured without set and remove cookie methods, but the client needs to set cookies. This can lead to issues such as random logouts, early session termination or increased token refresh requests. If in NextJS, check your middleware.ts file, route handlers and server actions for correctness. Consider switching to the getAll and setAll cookie methods instead of get, set and remove which are deprecated and can be difficult to use correctly.")};else throw Error("@supabase/ssr: createBrowserClient requires configuring a getAll and setAll cookie method (deprecated: alternatively both get, set and remove can be used)")}else if("getAll"in i)if(r=async()=>await i.getAll(),"setAll"in i)o=i.setAll;else if(t)o=async()=>{console.warn("@supabase/ssr: createServerClient was configured without the setAll cookie method, but the client needs to set cookies. This can lead to issues such as random logouts, early session termination or increased token refresh requests. If in NextJS, check your middleware.ts file, route handlers and server actions for correctness.")};else throw Error("@supabase/ssr: createBrowserClient requires configuring both getAll and setAll cookie methods (deprecated: alternatively both get, set and remove can be used)");else throw Error(`@supabase/ssr: ${t?"createServerClient":"createBrowserClient"} requires configuring getAll and setAll cookie methods (deprecated: alternatively use get, set and remove).${s()?" As this is called in a browser runtime, consider removing the cookies option object to use the document.cookie API automatically.":""}`);else if(!t&&s()){let e=()=>{let e=(0,a.qg)(document.cookie);return Object.keys(e).map(t=>({name:t,value:e[t]??""}))};r=()=>e(),o=e=>{e.forEach(({name:e,value:t,options:r})=>{document.cookie=(0,a.lK)(e,t,r)})}}else if(t)throw Error("@supabase/ssr: createServerClient must be initialized with cookie options that specify getAll and setAll functions (deprecated, not recommended: alternatively use get, set and remove)");else r=()=>[],o=()=>{throw Error("@supabase/ssr: createBrowserClient in non-browser runtimes (including Next.js pre-rendering mode) was not initialized cookie options that specify getAll and setAll functions (deprecated: alternatively use get, set and remove), but they were needed")};return t?{getAll:r,setAll:o,setItems:c,removedItems:p,storage:{isServer:!0,getItem:async e=>{if("string"==typeof c[e])return c[e];if(p[e])return null;let t=await r([e]),o=await f(e,async e=>{let r=t?.find(({name:t})=>t===e)||null;return r?r.value:null});if(!o)return null;let i=o;return"string"==typeof o&&o.startsWith(y)&&(i=w(o.substring(y.length))),i},setItem:async(t,i)=>{t.endsWith("-code-verifier")&&await k({getAll:r,setAll:o,setItems:{[t]:i},removedItems:{}},{cookieOptions:e?.cookieOptions??null,cookieEncoding:n}),c[t]=i,delete p[t]},removeItem:async e=>{delete c[e],p[e]=!0}}}:{getAll:r,setAll:o,setItems:c,removedItems:p,storage:{isServer:!1,getItem:async e=>{let t=await r([e]),o=await f(e,async e=>{let r=t?.find(({name:t})=>t===e)||null;return r?r.value:null});if(!o)return null;let i=o;return o.startsWith(y)&&(i=w(o.substring(y.length))),i},setItem:async(t,i)=>{let a=await r([t]),s=new Set((a?.map(({name:e})=>e)||[]).filter(e=>u(e,t))),c=i;"base64url"===n&&(c=y+g(i));let f=d(t,c);f.forEach(({name:e})=>{s.delete(e)});let p={...l,...e?.cookieOptions,maxAge:0},h={...l,...e?.cookieOptions,maxAge:l.maxAge};delete p.name,delete h.name;let m=[...[...s].map(e=>({name:e,value:"",options:p})),...f.map(({name:e,value:t})=>({name:e,value:t,options:h}))];m.length>0&&await o(m)},removeItem:async t=>{let i=await r([t]),n=(i?.map(({name:e})=>e)||[]).filter(e=>u(e,t)),a={...l,...e?.cookieOptions,maxAge:0};delete a.name,n.length>0&&await o(n.map(e=>({name:e,value:"",options:a})))}}}}async function k({getAll:e,setAll:t,setItems:r,removedItems:o},i){let n=i.cookieEncoding,a=i.cookieOptions??null,s=await e([...r?Object.keys(r):[],...o?Object.keys(o):[]]),c=s?.map(({name:e})=>e)||[],f=Object.keys(o).flatMap(e=>c.filter(t=>u(t,e))),p=Object.keys(r).flatMap(e=>{let t=new Set(c.filter(t=>u(t,e))),o=r[e];"base64url"===n&&(o=y+g(o));let i=d(e,o);return i.forEach(e=>{t.delete(e.name)}),f.push(...t),i}),h={...l,...a,maxAge:0},m={...l,...a,maxAge:l.maxAge};delete h.name,delete m.name,await t([...f.map(e=>({name:e,value:"",options:h})),...p.map(({name:e,value:t})=>({name:e,value:t,options:m}))])}function v(e,t,r){let a=r?.isSingleton===!0||(!r||!("isSingleton"in r))&&s();if(a&&o)return o;if(!e||!t)throw Error(`@supabase/ssr: Your project's URL and API key are required to create a Supabase client!

Check your Supabase project's API settings to find these values

https://supabase.com/dashboard/project/_/settings/api`);let{storage:l}=b({...r,cookieEncoding:r?.cookieEncoding??"base64url"},!1),c=(0,i.UU)(e,t,{...r,global:{...r?.global,headers:{...r?.global?.headers,"X-Client-Info":`supabase-ssr/${n} createBrowserClient`}},auth:{...r?.auth,...r?.cookieOptions?.name?{storageKey:r.cookieOptions.name}:null,flowType:"pkce",autoRefreshToken:s(),detectSessionInUrl:s(),persistSession:!0,storage:l}});return a&&(o=c),c}function A(e,t,r){if(!e||!t)throw Error(`Your project's URL and Key are required to create a Supabase client!

Check your Supabase project's API settings to find these values

https://supabase.com/dashboard/project/_/settings/api`);let{storage:o,getAll:a,setAll:s,setItems:l,removedItems:c}=b({...r,cookieEncoding:r?.cookieEncoding??"base64url"},!0),u=(0,i.UU)(e,t,{...r,global:{...r?.global,headers:{...r?.global?.headers,"X-Client-Info":`supabase-ssr/${n} createServerClient`}},auth:{...r?.cookieOptions?.name?{storageKey:r.cookieOptions.name}:null,...r?.auth,flowType:"pkce",autoRefreshToken:!1,detectSessionInUrl:!1,persistSession:!0,storage:o}});return u.auth.onAuthStateChange(async e=>{(Object.keys(l).length>0||Object.keys(c).length>0)&&("SIGNED_IN"===e||"TOKEN_REFRESHED"===e||"USER_UPDATED"===e||"PASSWORD_RECOVERY"===e||"SIGNED_OUT"===e||"MFA_CHALLENGE_VERIFIED"===e)&&await k({getAll:a,setAll:s,setItems:l,removedItems:c},{cookieOptions:r?.cookieOptions??null,cookieEncoding:r?.cookieEncoding??"base64url"})}),u}},49343:(e,t)=>{t.qg=function(e,t){let r=new s,o=e.length;if(o<2)return r;let i=t?.decode||u,n=0;do{let t=e.indexOf("=",n);if(-1===t)break;let a=e.indexOf(";",n),s=-1===a?o:a;if(t>s){n=e.lastIndexOf(";",t-1)+1;continue}let u=l(e,n,t),d=c(e,t,u),f=e.slice(u,d);if(void 0===r[f]){let o=l(e,t+1,s),n=c(e,s,o),a=i(e.slice(o,n));r[f]=a}n=s+1}while(n<o);return r},t.lK=function(e,t,s){let l=s?.encode||encodeURIComponent;if(!r.test(e))throw TypeError(`argument name is invalid: ${e}`);let c=l(t);if(!o.test(c))throw TypeError(`argument val is invalid: ${t}`);let u=e+"="+c;if(!s)return u;if(void 0!==s.maxAge){if(!Number.isInteger(s.maxAge))throw TypeError(`option maxAge is invalid: ${s.maxAge}`);u+="; Max-Age="+s.maxAge}if(s.domain){if(!i.test(s.domain))throw TypeError(`option domain is invalid: ${s.domain}`);u+="; Domain="+s.domain}if(s.path){if(!n.test(s.path))throw TypeError(`option path is invalid: ${s.path}`);u+="; Path="+s.path}if(s.expires){var d;if(d=s.expires,"[object Date]"!==a.call(d)||!Number.isFinite(s.expires.valueOf()))throw TypeError(`option expires is invalid: ${s.expires}`);u+="; Expires="+s.expires.toUTCString()}if(s.httpOnly&&(u+="; HttpOnly"),s.secure&&(u+="; Secure"),s.partitioned&&(u+="; Partitioned"),s.priority)switch("string"==typeof s.priority?s.priority.toLowerCase():void 0){case"low":u+="; Priority=Low";break;case"medium":u+="; Priority=Medium";break;case"high":u+="; Priority=High";break;default:throw TypeError(`option priority is invalid: ${s.priority}`)}if(s.sameSite)switch("string"==typeof s.sameSite?s.sameSite.toLowerCase():s.sameSite){case!0:case"strict":u+="; SameSite=Strict";break;case"lax":u+="; SameSite=Lax";break;case"none":u+="; SameSite=None";break;default:throw TypeError(`option sameSite is invalid: ${s.sameSite}`)}return u};let r=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,o=/^[\u0021-\u003A\u003C-\u007E]*$/,i=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,n=/^[\u0020-\u003A\u003D-\u007E]*$/,a=Object.prototype.toString,s=(()=>{let e=function(){};return e.prototype=Object.create(null),e})();function l(e,t,r){do{let r=e.charCodeAt(t);if(32!==r&&9!==r)return t}while(++t<r);return r}function c(e,t,r){for(;t>r;){let r=e.charCodeAt(--t);if(32!==r&&9!==r)return t+1}return r}function u(e){if(-1===e.indexOf("%"))return e;try{return decodeURIComponent(e)}catch(t){return e}}}};