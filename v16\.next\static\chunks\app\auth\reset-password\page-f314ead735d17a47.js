(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4089],{2643:(e,r,t)=>{"use strict";t.d(r,{N:()=>n,U:()=>s});var a=t(9535);function s(){return(0,a.createBrowserClient)("https://fxnhpxjijinfuxxxplzj.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4",{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0}})}let n=s()},4436:(e,r,t)=>{"use strict";t.d(r,{k5:()=>d});var a=t(2115),s={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},n=a.createContext&&a.createContext(s),o=["attr","size","title"];function i(){return(i=Object.assign?Object.assign.bind():function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a])}return e}).apply(this,arguments)}function c(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);r&&(a=a.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,a)}return t}function l(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?c(Object(t),!0).forEach(function(r){var a,s,n;a=e,s=r,n=t[r],(s=function(e){var r=function(e,r){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var a=t.call(e,r||"default");if("object"!=typeof a)return a;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"==typeof r?r:r+""}(s))in a?Object.defineProperty(a,s,{value:n,enumerable:!0,configurable:!0,writable:!0}):a[s]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):c(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function d(e){return r=>a.createElement(u,i({attr:l({},e.attr)},r),function e(r){return r&&r.map((r,t)=>a.createElement(r.tag,l({key:t},r.attr),e(r.child)))}(e.child))}function u(e){var r=r=>{var t,{attr:s,size:n,title:c}=e,d=function(e,r){if(null==e)return{};var t,a,s=function(e,r){if(null==e)return{};var t={};for(var a in e)if(Object.prototype.hasOwnProperty.call(e,a)){if(r.indexOf(a)>=0)continue;t[a]=e[a]}return t}(e,r);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);for(a=0;a<n.length;a++)t=n[a],!(r.indexOf(t)>=0)&&Object.prototype.propertyIsEnumerable.call(e,t)&&(s[t]=e[t])}return s}(e,o),u=n||r.size||"1em";return r.className&&(t=r.className),e.className&&(t=(t?t+" ":"")+e.className),a.createElement("svg",i({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},r.attr,s,d,{className:t,style:l(l({color:e.color||r.color},r.style),e.style),height:u,width:u,xmlns:"http://www.w3.org/2000/svg"}),c&&a.createElement("title",null,c),e.children)};return void 0!==n?a.createElement(n.Consumer,null,e=>r(e)):r(s)}},5311:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>d});var a=t(5155),s=t(2115),n=t(5695),o=t(2643),i=t(8260),c=t(351);function l(){let e=(0,n.useRouter)(),r=(0,n.useSearchParams)(),[t,l]=(0,s.useState)(""),[d,u]=(0,s.useState)(""),[m,p]=(0,s.useState)(!1),[f,h]=(0,s.useState)(""),[x,b]=(0,s.useState)(!0),[v,g]=(0,s.useState)(!1),[y,w]=(0,s.useState)(null),[N,j]=(0,s.useState)([]),S=e=>{let r=new Date().toISOString(),t="[".concat(r,"] ").concat(e);console.log(t),j(e=>[...e,t])},E=()=>{let e=window.location.hash,r=window.location.search;window.location.href;let t=e.includes("type=recovery")||r.includes("type=recovery"),a=e.includes("access_token=")||r.includes("access_token="),s=e.includes("refresh_token=")||r.includes("refresh_token="),n=r.includes("code="),o=""===document.referrer||document.referrer.includes("mail")||document.referrer.includes("gmail");return S("URL Analysis: hash=".concat(e.substring(0,100),", search=").concat(r.substring(0,100))),S("Recovery indicators: type=".concat(t,", access_token=").concat(a,", refresh_token=").concat(s,", code=").concat(n,", fromEmail=").concat(o)),t||a&&s||n},_=async e=>{try{if(!(null==e?void 0:e.user))return!1;let r=e.user.user_metadata||{},t=e.user.app_metadata||{};return S("User metadata: ".concat(JSON.stringify(r))),S("App metadata: ".concat(JSON.stringify(t))),!0===r.recovery_flow||!0===t.recovery_flow}catch(e){return S("Error checking metadata: ".concat(e.message)),!1}};(0,s.useEffect)(()=>{S("ResetPasswordForm: useEffect iniciado. URL: "+window.location.href),b(!0),g(!1),w(null);let e=r.get("error"),t=r.get("error_code"),a=r.get("error_description");if(e||t||a){let r=decodeURIComponent(a||e||"Error desconocido en el enlace.");(r.toLowerCase().includes("link is invalid or has expired")||r.toLowerCase().includes("token has expired")||"token_expired_or_invalid"===t)&&(r="El enlace de recuperaci\xf3n ha expirado o ya fue utilizado. Por favor, solicita un nuevo enlace."),S("Error expl\xedcito en URL: "+JSON.stringify({urlErrorParam:e,errorCodeParam:t,errorDescriptionParam:a})),w(r),g(!1),b(!1);return}let s=(0,o.U)(),n=async()=>{try{S("Verificando sesi\xf3n inicial...");let r=E();S("URL indica recovery: ".concat(r));let{data:{session:t},error:a}=await s.auth.getSession();if(a)return S("Error obteniendo sesi\xf3n inicial: "+a.message),r&&S("URL indica recovery, esperando eventos de auth..."),!1;if(t){var e;S("Sesi\xf3n inicial encontrada. User ID: "+(null==(e=t.user)?void 0:e.id));let a=await _(t);if(r||a)return S("Recovery detectado - URL: ".concat(r,", Metadata: ").concat(a)),g(!0),b(!1),!0;S("Sesi\xf3n encontrada pero no es de recovery")}else r&&S("No hay sesi\xf3n pero URL indica recovery - esperando procesamiento...");return!1}catch(e){return S("Error en verificaci\xf3n inicial: "+e.message),!1}},{data:i}=s.auth.onAuthStateChange(async(e,r)=>{var t;if(S("Evento onAuthStateChange: ".concat(e,", Session: ").concat(!!r,", User: ").concat(null==r||null==(t=r.user)?void 0:t.id)),"PASSWORD_RECOVERY"===e)r?(S("Sesi\xf3n establecida por evento PASSWORD_RECOVERY"),g(!0),w(null)):(S("Evento PASSWORD_RECOVERY sin sesi\xf3n - token inv\xe1lido o expirado"),w("El enlace de recuperaci\xf3n parece ser inv\xe1lido o ha expirado."),g(!1)),b(!1);else if("INITIAL_SESSION"===e)if(r){S("Evento INITIAL_SESSION con sesi\xf3n - verificando si es recovery");let e=E(),t=await _(r);e||t?(S("Recovery confirmado en INITIAL_SESSION - URL: ".concat(e,", Metadata: ").concat(t)),g(!0)):S("Sesi\xf3n normal en INITIAL_SESSION - no es recovery"),b(!1)}else S("Evento INITIAL_SESSION sin sesi\xf3n"),E()?S("Sin sesi\xf3n pero URL indica recovery - continuando verificaci\xf3n..."):b(!1);else if("SIGNED_IN"===e){if(S("Evento SIGNED_IN"),r){let e=E(),t=await _(r);(e||t)&&(S("Recovery detectado en SIGNED_IN - URL: ".concat(e,", Metadata: ").concat(t)),g(!0))}b(!1)}else"SIGNED_OUT"===e&&(S("Evento SIGNED_OUT"),g(!1),b(!1))});n();let c=setTimeout(()=>{x&&(S("Timeout (5 minutos) esperando evento de sesi\xf3n"),b(!1),y||v||w("No se pudo establecer una sesi\xf3n para cambiar la contrase\xf1a. El enlace podr\xeda ser inv\xe1lido o haber expirado."))},3e5);return()=>{null==i||i.subscription.unsubscribe(),clearTimeout(c),S("Listener de autenticaci\xf3n y timeout limpiados")}},[r]);let I=async r=>{var a,s;if(r.preventDefault(),h(""),!v){h("No se ha establecido una sesi\xf3n v\xe1lida para cambiar la contrase\xf1a. Por favor, aseg\xfarate de usar el enlace de tu email."),i.oR.error("Error de sesi\xf3n. Intenta usar el enlace de tu email de nuevo.");return}if(t.length<6)return void h("La nueva contrase\xf1a debe tener al menos 6 caracteres.");if(t!==d)return void h("Las contrase\xf1as no coinciden.");p(!0);let n=(0,o.U)(),{data:c,error:l}=await n.auth.getUser();if(l||!c.user){h("No se pudo verificar la sesi\xf3n actual antes de actualizar. Intenta de nuevo."),p(!1);return}let u=(null==(a=c.user.user_metadata)?void 0:a.requires_terms_acceptance_and_final_password_setup)===!0||(null==(s=c.user.user_metadata)?void 0:s.requires_initial_password_change)===!0,m={};u&&(m.requires_terms_acceptance_and_final_password_setup=!1,m.temporary_password_set=!1,m.requires_initial_password_change=!1);let{error:f}=await n.auth.updateUser({password:t,data:m});p(!1),f?(console.error("ResetPasswordForm: Error al actualizar contrase\xf1a:",f),h("Auth session missing!"===f.message?"Error de sesi\xf3n: Tu sesi\xf3n ha expirado o es inv\xe1lida. Por favor, usa el enlace de tu email de nuevo.":f.message),i.oR.error("Auth session missing!"===f.message?"Error de sesi\xf3n. Usa el enlace de tu email.":"Error al actualizar la contrase\xf1a.")):(i.oR.success("\xa1Contrase\xf1a actualizada exitosamente!"),S("Contrase\xf1a actualizada exitosamente. Redirigiendo a login..."),setTimeout(()=>{e.push("/login")},1500))};return x?(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center items-center p-4",children:[(0,a.jsx)(c.TwU,{className:"w-12 h-12 text-blue-600 mx-auto mb-4 animate-spin"}),(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-800",children:"Verificando enlace..."}),(0,a.jsx)("p",{className:"text-gray-600 mt-2",children:"Esto puede tardar unos segundos."}),!1]}):y?(0,a.jsx)("div",{className:"min-h-screen bg-red-50 flex flex-col justify-center items-center p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-xl p-8 max-w-md w-full text-center",children:[(0,a.jsx)(c.Ohp,{className:"w-12 h-12 text-red-500 mx-auto mb-4"}),(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-800 mb-2",children:"Enlace Inv\xe1lido o Expirado"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:y}),(0,a.jsx)("button",{onClick:()=>e.push("/login"),className:"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors",children:"Volver a Inicio de Sesi\xf3n"})]})}):v||x?(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:[(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsx)(c.F5$,{className:"w-12 h-12 text-blue-600"})}),(0,a.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Crea tu Nueva Contrase\xf1a"})]}),(0,a.jsx)("div",{className:"mt-8 sm:mx-auto sm:w-full sm:max-w-md",children:(0,a.jsx)("div",{className:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10",children:(0,a.jsxs)("form",{className:"space-y-6",onSubmit:I,children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Nueva Contrase\xf1a"}),(0,a.jsx)("div",{className:"mt-1",children:(0,a.jsx)("input",{id:"password",name:"password",type:"password",required:!0,value:t,onChange:e=>l(e.target.value),className:"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"M\xednimo 6 caracteres"})})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700",children:"Confirmar Nueva Contrase\xf1a"}),(0,a.jsx)("div",{className:"mt-1",children:(0,a.jsx)("input",{id:"confirmPassword",name:"confirmPassword",type:"password",required:!0,value:d,onChange:e=>u(e.target.value),className:"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"Repite la contrase\xf1a"})})]}),f&&(0,a.jsx)("div",{className:"text-red-500 text-sm bg-red-50 p-3 rounded-md border border-red-200",children:f}),(0,a.jsx)("div",{children:(0,a.jsx)("button",{type:"submit",disabled:m||!v,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",children:m?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(c.TwU,{className:"animate-spin h-4 w-4 mr-2"})," Actualizando..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(c.YrT,{className:"h-4 w-4 mr-2"})," Establecer Contrase\xf1a"]})})})]})})})]}):(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center items-center p-4",children:[(0,a.jsx)(c.F5$,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-700",children:"Acceso no Autorizado"}),(0,a.jsx)("p",{className:"text-gray-500 mt-2 mb-6 max-w-md text-center",children:"Esta p\xe1gina es para establecer o restablecer tu contrase\xf1a usando un enlace seguro enviado a tu email. Si necesitas restablecer tu contrase\xf1a, solic\xedtalo desde la p\xe1gina de inicio de sesi\xf3n."}),(0,a.jsx)("button",{onClick:()=>e.push("/login"),className:"bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors",children:"Ir a Inicio de Sesi\xf3n"}),!1]})}function d(){return(0,a.jsx)(s.Suspense,{fallback:(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center items-center",children:[(0,a.jsx)(c.TwU,{className:"w-12 h-12 text-blue-600 mx-auto mb-4 animate-spin"}),(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-800",children:"Cargando..."})]}),children:(0,a.jsx)(l,{})})}},8870:(e,r,t)=>{Promise.resolve().then(t.bind(t,5311))}},e=>{var r=r=>e(e.s=r);e.O(0,[844,7361,8260,8441,1684,7358],()=>r(8870)),_N_E=e.O()}]);