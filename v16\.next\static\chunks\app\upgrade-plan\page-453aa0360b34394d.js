(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2723],{2643:(e,r,a)=>{"use strict";a.d(r,{N:()=>n,U:()=>s});var t=a(9535);function s(){return(0,t.createBrowserClient)("https://fxnhpxjijinfuxxxplzj.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4",{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0}})}let n=s()},3945:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>m});var t=a(5155),s=a(2115),n=a(5695),i=a(6874),l=a.n(i),c=a(351),o=a(2643),d=a(9509);function m(){let e=(0,n.useRouter)(),[r,a]=(0,s.useState)(null),[i,m]=(0,s.useState)(!0),[u,p]=(0,s.useState)(null),x=[{id:"usuario",name:"Plan Usuario",price:"€10.00",period:"/mes",description:"Uso de la plataforma durante el mes (una vez finalizado no podr\xe1 volver a acceder hasta renovaci\xf3n)",icon:(0,t.jsx)(c.cfS,{className:"w-8 h-8"}),recommended:!0,features:[{name:"Subida de documentos",included:!0},{name:"Habla con tu preparador IA *",included:!0},{name:"Generador de test *",included:!0},{name:"Generador de flashcards *",included:!0},{name:"Generador de mapas mentales *",included:!0},{name:"* Para las tareas en las que se haga uso de IA, el l\xedmite mensual ser\xe1 de 1,000,000 de tokens, podr\xe1n ampliarse durante el mes mediante pago",included:!0},{name:"Planificaci\xf3n de estudios",included:!1},{name:"Res\xfamenes A2 y A1",included:!1}],stripePriceId:d.env.NEXT_PUBLIC_STRIPE_PRICE_ID_USUARIO},{id:"pro",name:"Plan Pro",price:"€15.00",period:"/mes",description:"Uso de la plataforma durante el mes (una vez finalizado no podr\xe1 volver a acceder hasta renovaci\xf3n)",icon:(0,t.jsx)(c.usP,{className:"w-8 h-8"}),features:[{name:"Subida de documentos",included:!0},{name:"Planificaci\xf3n de estudios mediante IA*",included:!0},{name:"Habla con tu preparador IA *",included:!0},{name:"Generador de test *",included:!0},{name:"Generador de flashcards *",included:!0},{name:"Generador de mapas mentales *",included:!0},{name:"Generaci\xf3n de Res\xfamenes para A2 y A1",included:!0},{name:"* Para las tareas en las que se haga uso de IA, el l\xedmite mensual ser\xe1 de 1,000,000 de tokens, podr\xe1n ampliarse durante el mes mediante pago",included:!0}],stripePriceId:d.env.NEXT_PUBLIC_STRIPE_PRICE_ID_PRO}],h=(0,s.useCallback)(async()=>{try{let r=(0,o.U)(),{data:{user:t},error:s}=await r.auth.getUser();if(s||!t)return void e.push("/login");let{data:n,error:i}=await r.from("user_profiles").select("subscription_plan, payment_verified").eq("user_id",t.id).single();i?console.error("Error loading user profile:",i):a(n)}catch(e){console.error("Error loading user data:",e)}finally{m(!1)}},[e]);(0,s.useEffect)(()=>{h()},[h]);let f=async e=>{if(!e.stripePriceId)return void console.error("No Stripe price ID configured for plan:",e.id);p(e.id);try{let r=await fetch("/api/stripe/create-checkout-session",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({priceId:e.stripePriceId,successUrl:"".concat(window.location.origin,"/app?upgrade=success"),cancelUrl:"".concat(window.location.origin,"/upgrade-plan?upgrade=cancelled")})});if(!r.ok)throw Error("Error creating checkout session");let{url:a}=await r.json();window.location.href=a}catch(e){console.error("Error creating checkout session:",e),p(null)}};return i?(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,t.jsx)("div",{className:"bg-white shadow-sm",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,t.jsxs)(l(),{href:"/app",className:"flex items-center text-gray-600 hover:text-gray-900 transition-colors",children:[(0,t.jsx)(c.kRp,{className:"w-5 h-5 mr-2"}),"Volver al Dashboard"]}),r&&(0,t.jsxs)("div",{className:"text-sm text-gray-600",children:["Plan actual: ",(0,t.jsx)("span",{className:"font-medium capitalize",children:r.subscription_plan})]})]})})}),(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,t.jsxs)("div",{className:"text-center mb-16",children:[(0,t.jsx)("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:"Actualiza tu Plan"}),(0,t.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"Desbloquea todo el potencial de OposiAI con nuestros planes premium. M\xe1s recursos, funciones avanzadas y soporte prioritario."})]}),(0,t.jsx)("div",{className:"grid md:grid-cols-2 gap-8 max-w-5xl mx-auto",children:x.map(e=>(0,t.jsxs)("div",{className:"relative bg-white rounded-2xl shadow-lg overflow-hidden ".concat(e.recommended?"ring-2 ring-blue-500 transform scale-105":""),children:[e.recommended&&(0,t.jsx)("div",{className:"absolute top-0 left-0 right-0 bg-gradient-to-r from-blue-500 to-purple-600 text-white text-center py-2 text-sm font-medium",children:"⭐ M\xe1s Popular"}),(0,t.jsxs)("div",{className:"p-8",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsx)("div",{className:"flex justify-center mb-4 text-blue-600",children:e.icon}),(0,t.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-2",children:e.name}),(0,t.jsx)("p",{className:"text-gray-600 mb-4",children:e.description}),(0,t.jsxs)("div",{className:"flex items-baseline justify-center",children:[(0,t.jsx)("span",{className:"text-4xl font-bold text-gray-900",children:e.price}),(0,t.jsx)("span",{className:"text-gray-600 ml-1",children:e.period})]})]}),(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsxs)("div",{className:"space-y-3 mb-6",children:[(0,t.jsx)("h4",{className:"text-sm font-semibold text-gray-900",children:"Incluye:"}),(0,t.jsx)("ul",{className:"space-y-3",children:e.features.filter(e=>e.included).map((e,r)=>(0,t.jsxs)("li",{className:"flex items-start",children:[(0,t.jsx)(c.YrT,{className:"w-5 h-5 mr-3 mt-0.5 flex-shrink-0 text-green-500"}),(0,t.jsx)("span",{className:"text-sm text-gray-700",children:e.name})]},r))})]}),e.features.filter(e=>!e.included).length>0&&(0,t.jsxs)("div",{className:"space-y-3 pt-4 border-t border-gray-200",children:[(0,t.jsx)("h4",{className:"text-sm font-semibold text-gray-900",children:"No incluye:"}),(0,t.jsx)("ul",{className:"space-y-3",children:e.features.filter(e=>!e.included).map((e,r)=>(0,t.jsxs)("li",{className:"flex items-start",children:[(0,t.jsx)("div",{className:"w-5 h-5 mr-3 mt-0.5 flex-shrink-0 flex items-center justify-center",children:(0,t.jsx)("div",{className:"w-3 h-0.5 bg-red-400 rounded"})}),(0,t.jsx)("span",{className:"text-sm text-gray-500",children:e.name})]},r))})]})]}),(0,t.jsx)("button",{onClick:()=>f(e),disabled:u===e.id||(null==r?void 0:r.subscription_plan)===e.id,className:"w-full py-4 px-6 rounded-xl font-semibold transition-all duration-200 ".concat((null==r?void 0:r.subscription_plan)===e.id?"bg-gray-100 text-gray-500 cursor-not-allowed":e.recommended?"bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5":"bg-gray-900 text-white hover:bg-gray-800"," ").concat(u===e.id?"opacity-50 cursor-not-allowed":""),children:u===e.id?(0,t.jsxs)("div",{className:"flex items-center justify-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"}),"Procesando..."]}):(null==r?void 0:r.subscription_plan)===e.id?"Plan Actual":"Seleccionar ".concat(e.name)})]})]},e.id))}),(0,t.jsx)("div",{className:"mt-16 text-center",children:(0,t.jsxs)("div",{className:"bg-white rounded-xl p-8 shadow-lg max-w-4xl mx-auto",children:[(0,t.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-4",children:"\xbfPor qu\xe9 actualizar?"}),(0,t.jsxs)("div",{className:"grid md:grid-cols-3 gap-8 mt-8",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)(c.FrA,{className:"w-12 h-12 text-blue-600 mx-auto mb-4"}),(0,t.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:"M\xe1s Potencia"}),(0,t.jsx)("p",{className:"text-gray-600 text-sm",children:"Acceso a m\xe1s tokens y recursos para estudiar sin l\xedmites"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)(c.pcC,{className:"w-12 h-12 text-blue-600 mx-auto mb-4"}),(0,t.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:"Funciones Avanzadas"}),(0,t.jsx)("p",{className:"text-gray-600 text-sm",children:"Planificaci\xf3n inteligente, res\xfamenes A1/A2 y an\xe1lisis detallado"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)(c.cfS,{className:"w-12 h-12 text-blue-600 mx-auto mb-4"}),(0,t.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:"Soporte Prioritario"}),(0,t.jsx)("p",{className:"text-gray-600 text-sm",children:"Atenci\xf3n personalizada y acceso anticipado a nuevas funciones"})]})]})]})})]})]})}},4436:(e,r,a)=>{"use strict";a.d(r,{k5:()=>d});var t=a(2115),s={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},n=t.createContext&&t.createContext(s),i=["attr","size","title"];function l(){return(l=Object.assign?Object.assign.bind():function(e){for(var r=1;r<arguments.length;r++){var a=arguments[r];for(var t in a)Object.prototype.hasOwnProperty.call(a,t)&&(e[t]=a[t])}return e}).apply(this,arguments)}function c(e,r){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);r&&(t=t.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),a.push.apply(a,t)}return a}function o(e){for(var r=1;r<arguments.length;r++){var a=null!=arguments[r]?arguments[r]:{};r%2?c(Object(a),!0).forEach(function(r){var t,s,n;t=e,s=r,n=a[r],(s=function(e){var r=function(e,r){if("object"!=typeof e||!e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var t=a.call(e,r||"default");if("object"!=typeof t)return t;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"==typeof r?r:r+""}(s))in t?Object.defineProperty(t,s,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[s]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):c(Object(a)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(a,r))})}return e}function d(e){return r=>t.createElement(m,l({attr:o({},e.attr)},r),function e(r){return r&&r.map((r,a)=>t.createElement(r.tag,o({key:a},r.attr),e(r.child)))}(e.child))}function m(e){var r=r=>{var a,{attr:s,size:n,title:c}=e,d=function(e,r){if(null==e)return{};var a,t,s=function(e,r){if(null==e)return{};var a={};for(var t in e)if(Object.prototype.hasOwnProperty.call(e,t)){if(r.indexOf(t)>=0)continue;a[t]=e[t]}return a}(e,r);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);for(t=0;t<n.length;t++)a=n[t],!(r.indexOf(a)>=0)&&Object.prototype.propertyIsEnumerable.call(e,a)&&(s[a]=e[a])}return s}(e,i),m=n||r.size||"1em";return r.className&&(a=r.className),e.className&&(a=(a?a+" ":"")+e.className),t.createElement("svg",l({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},r.attr,s,d,{className:a,style:o(o({color:e.color||r.color},r.style),e.style),height:m,width:m,xmlns:"http://www.w3.org/2000/svg"}),c&&t.createElement("title",null,c),e.children)};return void 0!==n?t.createElement(n.Consumer,null,e=>r(e)):r(s)}},8609:(e,r,a)=>{Promise.resolve().then(a.bind(a,3945))}},e=>{var r=r=>e(e.s=r);e.O(0,[844,7361,6874,8441,1684,7358],()=>r(8609)),_N_E=e.O()}]);