(()=>{var e={};e.id=8974,e.ids=[8974],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20609:(e,s,r)=>{Promise.resolve().then(r.bind(r,21204))},21204:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\page.tsx","default")},25983:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>d});var t=r(65239),a=r(48088),l=r(88170),i=r.n(l),n=r(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);r.d(s,o);let d=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,21204)),"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}],c=["C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},27910:e=>{"use strict";e.exports=require("stream")},28611:(e,s,r)=>{"use strict";r.d(s,{Md:()=>l,NB:()=>a});var t=r(78956);let a={free:{id:"free",name:"Plan Gratis",price:0,stripeProductId:null,stripePriceId:null,features:["Incluye:","• Uso de la plataforma solo durante 5 d\xedas","• Subida de documentos: m\xe1ximo 1 documento","• Generador de test: m\xe1ximo 10 preguntas test","• Generador de flashcards: m\xe1ximo 10 tarjetas flashcard","• Generador de mapas mentales: m\xe1ximo 2 mapas mentales","No incluye:","• Planificaci\xf3n de estudios","• Habla con tu preparador IA","• Res\xfamenes A2 y A1"],limits:t.qo.free.limits,planConfig:t.qo.free},usuario:{id:"usuario",name:"Plan Usuario",price:1e3,stripeProductId:"prod_SR65BdKdek1OXd",stripePriceId:"price_1Rae5807kFn3sIXhRf3adX1n",features:["Incluye:","• Uso de la plataforma durante el mes (una vez finalizado no podr\xe1 volver a acceder hasta renovaci\xf3n)","• Subida de documentos","• Habla con tu preparador IA *","• Generador de test *","• Generador de flashcards *","• Generador de mapas mentales *","• * Para las tareas en las que se haga uso de IA, el l\xedmite mensual ser\xe1 de 1.000.000 de tokens, podr\xe1n ampliarse durante el mes mediante pago","No incluye:","• Planificaci\xf3n de estudios","• Res\xfamenes A2 y A1"],limits:t.qo.usuario.limits,planConfig:t.qo.usuario},pro:{id:"pro",name:"Plan Pro",price:1500,stripeProductId:"prod_SR66U2G7bVJqu3",stripePriceId:"price_1Rae3U07kFn3sIXhkvSuJco1",features:["Incluye:","• Uso de la plataforma durante el mes (una vez finalizado no podr\xe1 volver a acceder hasta renovaci\xf3n)","• Subida de documentos","• Planificaci\xf3n de estudios mediante IA*","• Habla con tu preparador IA *","• Generador de test *","• Generador de flashcards *","• Generador de mapas mentales *","• Generaci\xf3n de Res\xfamenes para A2 y A1","• * Para las tareas en las que se haga uso de IA, el l\xedmite mensual ser\xe1 de 1.000.000 de tokens, podr\xe1n ampliarse durante el mes mediante pago"],limits:t.qo.pro.limits,planConfig:t.qo.pro}};function l(e){return a[e]||null}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33761:(e,s,r)=>{Promise.resolve().then(r.bind(r,80478))},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78956:(e,s,r)=>{"use strict";r.d(s,{IE:()=>a,qk:()=>i,qo:()=>t});let t={free:{id:"free",name:"Plan Gratis",price:0,limits:{documents:1,mindMapsForTrial:2,testsForTrial:10,flashcardsForTrial:10,tokensForTrial:5e4,features:["document_upload","test_generation","flashcard_generation","mind_map_generation"]},features:["document_upload","test_generation","flashcard_generation","mind_map_generation"],restrictedFeatures:["study_planning","ai_tutor_chat","summary_a1_a2"]},usuario:{id:"usuario",name:"Plan Usuario",price:1e3,limits:{documents:-1,mindMapsPerWeek:-1,testsPerWeek:-1,flashcardsPerWeek:-1,monthlyTokens:1e6,features:["document_upload","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation"]},features:["document_upload","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation"],restrictedFeatures:["study_planning","summary_a1_a2"]},pro:{id:"pro",name:"Plan Pro",price:1500,limits:{documents:-1,mindMapsPerWeek:-1,testsPerWeek:-1,flashcardsPerWeek:-1,monthlyTokens:1e6,features:["document_upload","study_planning","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation","summary_a1_a2"]},features:["document_upload","study_planning","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation","summary_a1_a2"],restrictedFeatures:[]}};function a(e){return t[e]||null}function l(e,s){let r=a(e);return!(!r||r.restrictedFeatures.includes(s))&&r.features.includes(s)}async function i(e){try{let s=await fetch("/api/user/plan");if(!s.ok)return console.error("Error obteniendo plan del usuario"),l("free",e);let{plan:r}=await s.json();return l(r||"free",e)}catch(s){return console.error("Error verificando acceso a caracter\xedstica:",s),l("free",e)}}},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},80478:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>c});var t=r(60687);r(43210);var a=r(16189),l=r(28611),i=r(85814),n=r.n(i);function o({id:e,name:s,price:r,features:a,isPopular:l=!1}){let i=()=>"free"===e?"text-gray-900":"text-white",o=()=>"free"===e?"text-gray-600":"text-white/90";return(0,t.jsxs)("div",{className:`relative rounded-2xl shadow-xl transform hover:scale-105 transition-all duration-300 ${"free"===e?"bg-white border border-gray-200":"usuario"===e?"bg-gradient-to-br from-blue-500 to-blue-600 text-white":"pro"===e?"bg-gradient-to-br from-purple-500 to-purple-600 text-white":"bg-white"} h-full flex flex-col`,children:[l&&(0,t.jsx)("div",{className:"absolute -top-3 left-1/2 transform -translate-x-1/2",children:(0,t.jsx)("span",{className:"bg-yellow-400 text-gray-900 px-4 py-1 text-sm font-bold rounded-full",children:"Recomendado"})}),(0,t.jsxs)("div",{className:"p-8 flex flex-col h-full",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h3",{className:`text-2xl font-bold mb-2 ${i()}`,children:s}),(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("span",{className:`text-4xl font-bold ${i()}`,children:0===r?"Gratis":`€${(r/100).toFixed(2)}`}),r>0&&(0,t.jsx)("span",{className:`text-lg ${"free"===e?"text-gray-500":"text-white/80"}`,children:"/mes"})]})]}),(0,t.jsx)("ul",{className:"space-y-3 mb-8 flex-grow",children:a.map((s,r)=>{if("free"===e||"usuario"===e||"pro"===e){if("Incluye:"===s||"No incluye:"===s)return(0,t.jsx)("li",{className:"mt-4 first:mt-0",children:(0,t.jsx)("span",{className:`text-sm font-semibold ${i()}`,children:s})},r);let l=s.startsWith("• ")&&"No incluye:"===a[r-1]||r>0&&a.slice(0,r).lastIndexOf("No incluye:")>a.slice(0,r).lastIndexOf("Incluye:");return(0,t.jsxs)("li",{className:"flex items-start ml-2",children:[l?(0,t.jsx)("svg",{className:`h-4 w-4 mt-0.5 mr-3 flex-shrink-0 ${"free"===e?"text-red-500":"text-red-400"}`,fill:"currentColor",viewBox:"0 0 20 20",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})}):(0,t.jsx)("svg",{className:`h-4 w-4 mt-0.5 mr-3 flex-shrink-0 ${"free"===e?"text-green-500":"usuario"===e?"text-green-400":"text-white"}`,fill:"currentColor",viewBox:"0 0 20 20",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})}),(0,t.jsx)("span",{className:`text-sm ${o()}`,children:s.startsWith("• ")?s.substring(2):s})]},r)}return(0,t.jsxs)("li",{className:"flex items-start",children:[(0,t.jsx)("svg",{className:`h-5 w-5 mt-0.5 mr-3 flex-shrink-0 ${"free"===e?"text-green-500":"text-white"}`,fill:"currentColor",viewBox:"0 0 20 20",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})}),(0,t.jsx)("span",{className:`text-sm ${o()}`,children:s})]},r)})}),(0,t.jsx)("div",{className:"mt-auto",children:(0,t.jsx)(n(),{href:`/payment?plan=${e}`,className:`w-full flex justify-center py-3 px-6 rounded-lg font-semibold transition-colors ${"free"===e?"bg-blue-600 text-white hover:bg-blue-700":"bg-white text-gray-900 hover:bg-gray-100"}`,children:"free"===e?"Empezar Gratis":`Seleccionar ${s}`})})]})]})}var d=r(65660);function c(){let{user:e,isLoading:s}=(0,d.A)();return((0,a.useRouter)(),s)?(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"}),(0,t.jsx)("p",{className:"mt-4 text-gray-600",children:"Cargando..."})]})}):(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-600 via-blue-700 to-purple-800",children:[(0,t.jsxs)("header",{className:"relative z-10",children:[(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsx)("div",{className:"flex justify-end items-center py-6",children:(0,t.jsxs)("nav",{className:"hidden md:flex space-x-8",children:[(0,t.jsx)("a",{href:"#pricing",className:"text-white/80 hover:text-white transition-colors",children:"Precios"}),(0,t.jsx)("a",{href:"/login",className:"bg-white text-blue-600 px-4 py-2 rounded-lg hover:bg-gray-100 transition-colors font-medium",children:"Iniciar Sesi\xf3n"})]})})}),(0,t.jsx)("div",{className:"absolute inset-x-0 top-5 flex justify-center items-center h-40 pt-4 pointer-events-none",children:(0,t.jsx)("img",{src:"/logo.png",alt:"OposiAI Logo",className:"h-80 w-80 object-contain"})})]}),(0,t.jsx)("section",{className:"pt-16 pb-12 text-center text-white",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,t.jsx)("h1",{className:"text-4xl md:text-6xl font-bold mb-12",children:"OposiAI Tu Preparador Personal Inteligente"}),(0,t.jsx)("a",{href:"#pricing",className:"inline-flex items-center px-6 py-3 bg-white text-blue-600 font-semibold rounded-lg hover:bg-gray-100 transition-colors",children:"Elige tu Plan Perfecto para Ti"})]})}),(0,t.jsx)("section",{className:"py-16 bg-white",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,t.jsx)("div",{className:"text-center mb-12",children:(0,t.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"\xbfC\xf3mo Funciona OposiAI?"})}),(0,t.jsxs)("div",{className:"grid md:grid-cols-2 lg:grid-cols-5 gap-8",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4",children:(0,t.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCDA"})}),(0,t.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Elige tu Temario"}),(0,t.jsx)("p",{className:"text-gray-600 text-sm mb-4",children:"Selecciona un temario predeterminado o sube tus documentos de estudio."}),(0,t.jsx)("div",{className:"bg-gray-100 rounded-lg p-4 border-2 border-dashed border-gray-300",children:(0,t.jsx)("div",{className:"flex items-center justify-center h-24",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("svg",{className:"w-8 h-8 text-gray-400 mx-auto mb-2",fill:"currentColor",viewBox:"0 0 20 20",children:(0,t.jsx)("path",{d:"M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z"})}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"Video Tutorial"})]})})})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4",children:(0,t.jsx)("span",{className:"text-2xl",children:"\uD83C\uDFAF"})}),(0,t.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Selecciona el Tema"}),(0,t.jsx)("p",{className:"text-gray-600 text-sm mb-4",children:"Escoge el tema espec\xedfico que quieres preparar hoy."}),(0,t.jsx)("div",{className:"bg-gray-100 rounded-lg p-4 border-2 border-dashed border-gray-300",children:(0,t.jsx)("div",{className:"flex items-center justify-center h-24",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("svg",{className:"w-8 h-8 text-gray-400 mx-auto mb-2",fill:"currentColor",viewBox:"0 0 20 20",children:(0,t.jsx)("path",{d:"M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z"})}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"Video Tutorial"})]})})})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4",children:(0,t.jsx)("span",{className:"text-2xl",children:"\uD83E\uDD16"})}),(0,t.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Usa IA para Estudiar"}),(0,t.jsx)("p",{className:"text-gray-600 text-sm mb-4",children:"Genera contenido autom\xe1ticamente: tests, flashcards y mapas mentales."}),(0,t.jsx)("div",{className:"bg-gray-100 rounded-lg p-4 border-2 border-dashed border-gray-300",children:(0,t.jsx)("div",{className:"flex items-center justify-center h-24",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("svg",{className:"w-8 h-8 text-gray-400 mx-auto mb-2",fill:"currentColor",viewBox:"0 0 20 20",children:(0,t.jsx)("path",{d:"M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z"})}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"Video Tutorial"})]})})})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"bg-yellow-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4",children:(0,t.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCC5"})}),(0,t.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Planifica tu Tiempo"}),(0,t.jsx)("p",{className:"text-gray-600 text-sm mb-4",children:"Deja que la IA dise\xf1e un plan de estudios personalizado para ti."}),(0,t.jsx)("div",{className:"bg-gray-100 rounded-lg p-4 border-2 border-dashed border-gray-300",children:(0,t.jsx)("div",{className:"flex items-center justify-center h-24",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("svg",{className:"w-8 h-8 text-gray-400 mx-auto mb-2",fill:"currentColor",viewBox:"0 0 20 20",children:(0,t.jsx)("path",{d:"M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z"})}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"Video Tutorial"})]})})})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"bg-red-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4",children:(0,t.jsx)("span",{className:"text-2xl",children:"\uD83D\uDE80"})}),(0,t.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Alcanza tu Objetivo"}),(0,t.jsx)("p",{className:"text-gray-600 text-sm mb-4",children:"Sigue tu progreso y consigue tu plaza con metodolog\xeda inteligente."}),(0,t.jsx)("div",{className:"bg-gray-100 rounded-lg p-4 border-2 border-dashed border-gray-300",children:(0,t.jsx)("div",{className:"flex items-center justify-center h-24",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("svg",{className:"w-8 h-8 text-gray-400 mx-auto mb-2",fill:"currentColor",viewBox:"0 0 20 20",children:(0,t.jsx)("path",{d:"M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z"})}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"Video Tutorial"})]})})})]})]})]})}),(0,t.jsx)("section",{id:"pricing",className:"py-20 bg-gray-50",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"grid md:grid-cols-3 gap-8 max-w-5xl mx-auto items-stretch",children:[(0,t.jsx)(o,{id:l.NB.free.id,name:l.NB.free.name,price:l.NB.free.price,features:l.NB.free.features}),(0,t.jsx)(o,{id:l.NB.usuario.id,name:l.NB.usuario.name,price:l.NB.usuario.price,features:l.NB.usuario.features,isPopular:!0}),(0,t.jsx)(o,{id:l.NB.pro.id,name:l.NB.pro.name,price:l.NB.pro.price,features:l.NB.pro.features})]})})}),(0,t.jsx)("section",{className:"py-20 bg-gradient-to-r from-blue-600 to-purple-700",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:[(0,t.jsx)("h2",{className:"text-3xl font-bold text-white mb-4",children:"\xbfListo para Empezar?"}),(0,t.jsx)("p",{className:"text-xl text-white/90 mb-8",children:"\xdanete a miles de opositores que ya est\xe1n usando OposiAI para conseguir su plaza."}),(0,t.jsx)("a",{href:"/login",className:"inline-flex items-center px-8 py-4 bg-white text-blue-600 font-semibold rounded-lg hover:bg-gray-100 transition-colors text-lg",children:"Comenzar Ahora"})]})}),(0,t.jsx)("footer",{className:"bg-gray-900 text-white py-12",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h3",{className:"text-2xl font-bold text-white mb-4",children:"OposiAI"}),(0,t.jsx)("p",{className:"text-gray-400 mb-6",children:"Tu preparador personal inteligente"}),(0,t.jsx)("p",{className:"text-gray-500",children:"\xa9 2024 OposiAI. Todos los derechos reservados."})]})})})]})}},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[4447,6126,5814,1191],()=>r(25983));module.exports=t})();