/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/payment/page"],{

/***/ "(app-pages-browser)/./node_modules/goober/dist/goober.modern.js":
/*!***************************************************!*\
  !*** ./node_modules/goober/dist/goober.modern.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   css: () => (/* binding */ u),\n/* harmony export */   extractCss: () => (/* binding */ r),\n/* harmony export */   glob: () => (/* binding */ b),\n/* harmony export */   keyframes: () => (/* binding */ h),\n/* harmony export */   setup: () => (/* binding */ m),\n/* harmony export */   styled: () => (/* binding */ j)\n/* harmony export */ });\nlet e={data:\"\"},t=t=>\"object\"==typeof window?((t?t.querySelector(\"#_goober\"):window._goober)||Object.assign((t||document.head).appendChild(document.createElement(\"style\")),{innerHTML:\" \",id:\"_goober\"})).firstChild:t||e,r=e=>{let r=t(e),l=r.data;return r.data=\"\",l},l=/(?:([\\u0080-\\uFFFF\\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\\s*)/g,a=/\\/\\*[^]*?\\*\\/|  +/g,n=/\\n+/g,o=(e,t)=>{let r=\"\",l=\"\",a=\"\";for(let n in e){let c=e[n];\"@\"==n[0]?\"i\"==n[1]?r=n+\" \"+c+\";\":l+=\"f\"==n[1]?o(c,n):n+\"{\"+o(c,\"k\"==n[1]?\"\":t)+\"}\":\"object\"==typeof c?l+=o(c,t?t.replace(/([^,])+/g,e=>n.replace(/([^,]*:\\S+\\([^)]*\\))|([^,])+/g,t=>/&/.test(t)?t.replace(/&/g,e):e?e+\" \"+t:t)):n):null!=c&&(n=/^--/.test(n)?n:n.replace(/[A-Z]/g,\"-$&\").toLowerCase(),a+=o.p?o.p(n,c):n+\":\"+c+\";\")}return r+(t&&a?t+\"{\"+a+\"}\":a)+l},c={},s=e=>{if(\"object\"==typeof e){let t=\"\";for(let r in e)t+=r+s(e[r]);return t}return e},i=(e,t,r,i,p)=>{let u=s(e),d=c[u]||(c[u]=(e=>{let t=0,r=11;for(;t<e.length;)r=101*r+e.charCodeAt(t++)>>>0;return\"go\"+r})(u));if(!c[d]){let t=u!==e?e:(e=>{let t,r,o=[{}];for(;t=l.exec(e.replace(a,\"\"));)t[4]?o.shift():t[3]?(r=t[3].replace(n,\" \").trim(),o.unshift(o[0][r]=o[0][r]||{})):o[0][t[1]]=t[2].replace(n,\" \").trim();return o[0]})(e);c[d]=o(p?{[\"@keyframes \"+d]:t}:t,r?\"\":\".\"+d)}let f=r&&c.g?c.g:null;return r&&(c.g=c[d]),((e,t,r,l)=>{l?t.data=t.data.replace(l,e):-1===t.data.indexOf(e)&&(t.data=r?e+t.data:t.data+e)})(c[d],t,i,f),d},p=(e,t,r)=>e.reduce((e,l,a)=>{let n=t[a];if(n&&n.call){let e=n(r),t=e&&e.props&&e.props.className||/^go/.test(e)&&e;n=t?\".\"+t:e&&\"object\"==typeof e?e.props?\"\":o(e,\"\"):!1===e?\"\":e}return e+l+(null==n?\"\":n)},\"\");function u(e){let r=this||{},l=e.call?e(r.p):e;return i(l.unshift?l.raw?p(l,[].slice.call(arguments,1),r.p):l.reduce((e,t)=>Object.assign(e,t&&t.call?t(r.p):t),{}):l,t(r.target),r.g,r.o,r.k)}let d,f,g,b=u.bind({g:1}),h=u.bind({k:1});function m(e,t,r,l){o.p=t,d=e,f=r,g=l}function j(e,t){let r=this||{};return function(){let l=arguments;function a(n,o){let c=Object.assign({},n),s=c.className||a.className;r.p=Object.assign({theme:f&&f()},c),r.o=/ *go\\d+/.test(s),c.className=u.apply(r,l)+(s?\" \"+s:\"\"),t&&(c.ref=o);let i=e;return e[0]&&(i=c.as||e,delete c.as),g&&i[0]&&g(c),d(i,c)}return t?t(a):a}}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9nb29iZXIvZGlzdC9nb29iZXIubW9kZXJuLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUFBLE9BQU8sUUFBUSw4SkFBOEosMkJBQTJCLHlCQUF5QixvQkFBb0IsbUJBQW1CLHlDQUF5QyxLQUFLLE9BQU8sT0FBTyxJQUFJLGlEQUFpRCxtQkFBbUIsZ0JBQWdCLFdBQVcsZ0NBQWdDLDBCQUEwQix3QkFBd0IsZ1BBQWdQLEdBQUcsbUJBQW1CLE1BQU0sT0FBTyxLQUFLLE9BQU8sdUJBQXVCLFNBQVMsNEJBQTRCLFNBQVMsU0FBUyxpQkFBaUIsOEJBQThCLGFBQWEsS0FBSyxXQUFXLCtCQUErQixhQUFhLE1BQU0sVUFBVSxtQkFBbUIsYUFBYSxFQUFFLEtBQUssMEJBQTBCLGdGQUFnRix5Q0FBeUMsWUFBWSxLQUFLLFVBQVUsb0JBQW9CLGVBQWUsc0JBQXNCLGtDQUFrQyxrRkFBa0YsZ0JBQWdCLCtCQUErQixXQUFXLGNBQWMsNkRBQTZELCtEQUErRCwwQkFBMEIsS0FBSyxjQUFjLGNBQWMsbUJBQW1CLG1IQUFtSCw2QkFBNkIsb0JBQW9CLElBQUksWUFBWSxJQUFJLEVBQUUsb0JBQW9CLGtCQUFrQixnQkFBZ0IsZUFBZSxrQkFBa0IsZ0JBQWdCLGdCQUFnQixzQkFBc0IsK0JBQStCLG1CQUFtQixhQUFhLDZFQUE2RSxRQUFRLDBEQUEwRCxpQkFBa0ciLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcbmFhdGFcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcT3Bvc0lcXHYxNlxcbm9kZV9tb2R1bGVzXFxnb29iZXJcXGRpc3RcXGdvb2Jlci5tb2Rlcm4uanMiXSwic291cmNlc0NvbnRlbnQiOlsibGV0IGU9e2RhdGE6XCJcIn0sdD10PT5cIm9iamVjdFwiPT10eXBlb2Ygd2luZG93PygodD90LnF1ZXJ5U2VsZWN0b3IoXCIjX2dvb2JlclwiKTp3aW5kb3cuX2dvb2Jlcil8fE9iamVjdC5hc3NpZ24oKHR8fGRvY3VtZW50LmhlYWQpLmFwcGVuZENoaWxkKGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoXCJzdHlsZVwiKSkse2lubmVySFRNTDpcIiBcIixpZDpcIl9nb29iZXJcIn0pKS5maXJzdENoaWxkOnR8fGUscj1lPT57bGV0IHI9dChlKSxsPXIuZGF0YTtyZXR1cm4gci5kYXRhPVwiXCIsbH0sbD0vKD86KFtcXHUwMDgwLVxcdUZGRkZcXHctJUBdKykgKjo/ICooW157O10rPyk7fChbXjt9e10qPykgKnspfCh9XFxzKikvZyxhPS9cXC9cXCpbXl0qP1xcKlxcL3wgICsvZyxuPS9cXG4rL2csbz0oZSx0KT0+e2xldCByPVwiXCIsbD1cIlwiLGE9XCJcIjtmb3IobGV0IG4gaW4gZSl7bGV0IGM9ZVtuXTtcIkBcIj09blswXT9cImlcIj09blsxXT9yPW4rXCIgXCIrYytcIjtcIjpsKz1cImZcIj09blsxXT9vKGMsbik6bitcIntcIitvKGMsXCJrXCI9PW5bMV0/XCJcIjp0KStcIn1cIjpcIm9iamVjdFwiPT10eXBlb2YgYz9sKz1vKGMsdD90LnJlcGxhY2UoLyhbXixdKSsvZyxlPT5uLnJlcGxhY2UoLyhbXixdKjpcXFMrXFwoW14pXSpcXCkpfChbXixdKSsvZyx0PT4vJi8udGVzdCh0KT90LnJlcGxhY2UoLyYvZyxlKTplP2UrXCIgXCIrdDp0KSk6bik6bnVsbCE9YyYmKG49L14tLS8udGVzdChuKT9uOm4ucmVwbGFjZSgvW0EtWl0vZyxcIi0kJlwiKS50b0xvd2VyQ2FzZSgpLGErPW8ucD9vLnAobixjKTpuK1wiOlwiK2MrXCI7XCIpfXJldHVybiByKyh0JiZhP3QrXCJ7XCIrYStcIn1cIjphKStsfSxjPXt9LHM9ZT0+e2lmKFwib2JqZWN0XCI9PXR5cGVvZiBlKXtsZXQgdD1cIlwiO2ZvcihsZXQgciBpbiBlKXQrPXIrcyhlW3JdKTtyZXR1cm4gdH1yZXR1cm4gZX0saT0oZSx0LHIsaSxwKT0+e2xldCB1PXMoZSksZD1jW3VdfHwoY1t1XT0oZT0+e2xldCB0PTAscj0xMTtmb3IoO3Q8ZS5sZW5ndGg7KXI9MTAxKnIrZS5jaGFyQ29kZUF0KHQrKyk+Pj4wO3JldHVyblwiZ29cIityfSkodSkpO2lmKCFjW2RdKXtsZXQgdD11IT09ZT9lOihlPT57bGV0IHQscixvPVt7fV07Zm9yKDt0PWwuZXhlYyhlLnJlcGxhY2UoYSxcIlwiKSk7KXRbNF0/by5zaGlmdCgpOnRbM10/KHI9dFszXS5yZXBsYWNlKG4sXCIgXCIpLnRyaW0oKSxvLnVuc2hpZnQob1swXVtyXT1vWzBdW3JdfHx7fSkpOm9bMF1bdFsxXV09dFsyXS5yZXBsYWNlKG4sXCIgXCIpLnRyaW0oKTtyZXR1cm4gb1swXX0pKGUpO2NbZF09byhwP3tbXCJAa2V5ZnJhbWVzIFwiK2RdOnR9OnQscj9cIlwiOlwiLlwiK2QpfWxldCBmPXImJmMuZz9jLmc6bnVsbDtyZXR1cm4gciYmKGMuZz1jW2RdKSwoKGUsdCxyLGwpPT57bD90LmRhdGE9dC5kYXRhLnJlcGxhY2UobCxlKTotMT09PXQuZGF0YS5pbmRleE9mKGUpJiYodC5kYXRhPXI/ZSt0LmRhdGE6dC5kYXRhK2UpfSkoY1tkXSx0LGksZiksZH0scD0oZSx0LHIpPT5lLnJlZHVjZSgoZSxsLGEpPT57bGV0IG49dFthXTtpZihuJiZuLmNhbGwpe2xldCBlPW4ociksdD1lJiZlLnByb3BzJiZlLnByb3BzLmNsYXNzTmFtZXx8L15nby8udGVzdChlKSYmZTtuPXQ/XCIuXCIrdDplJiZcIm9iamVjdFwiPT10eXBlb2YgZT9lLnByb3BzP1wiXCI6byhlLFwiXCIpOiExPT09ZT9cIlwiOmV9cmV0dXJuIGUrbCsobnVsbD09bj9cIlwiOm4pfSxcIlwiKTtmdW5jdGlvbiB1KGUpe2xldCByPXRoaXN8fHt9LGw9ZS5jYWxsP2Uoci5wKTplO3JldHVybiBpKGwudW5zaGlmdD9sLnJhdz9wKGwsW10uc2xpY2UuY2FsbChhcmd1bWVudHMsMSksci5wKTpsLnJlZHVjZSgoZSx0KT0+T2JqZWN0LmFzc2lnbihlLHQmJnQuY2FsbD90KHIucCk6dCkse30pOmwsdChyLnRhcmdldCksci5nLHIubyxyLmspfWxldCBkLGYsZyxiPXUuYmluZCh7ZzoxfSksaD11LmJpbmQoe2s6MX0pO2Z1bmN0aW9uIG0oZSx0LHIsbCl7by5wPXQsZD1lLGY9cixnPWx9ZnVuY3Rpb24gaihlLHQpe2xldCByPXRoaXN8fHt9O3JldHVybiBmdW5jdGlvbigpe2xldCBsPWFyZ3VtZW50cztmdW5jdGlvbiBhKG4sbyl7bGV0IGM9T2JqZWN0LmFzc2lnbih7fSxuKSxzPWMuY2xhc3NOYW1lfHxhLmNsYXNzTmFtZTtyLnA9T2JqZWN0LmFzc2lnbih7dGhlbWU6ZiYmZigpfSxjKSxyLm89LyAqZ29cXGQrLy50ZXN0KHMpLGMuY2xhc3NOYW1lPXUuYXBwbHkocixsKSsocz9cIiBcIitzOlwiXCIpLHQmJihjLnJlZj1vKTtsZXQgaT1lO3JldHVybiBlWzBdJiYoaT1jLmFzfHxlLGRlbGV0ZSBjLmFzKSxnJiZpWzBdJiZnKGMpLGQoaSxjKX1yZXR1cm4gdD90KGEpOmF9fWV4cG9ydHt1IGFzIGNzcyxyIGFzIGV4dHJhY3RDc3MsYiBhcyBnbG9iLGggYXMga2V5ZnJhbWVzLG0gYXMgc2V0dXAsaiBhcyBzdHlsZWR9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/goober/dist/goober.modern.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/api/navigation.js":
/*!**************************************************!*\
  !*** ./node_modules/next/dist/api/navigation.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../client/components/navigation */ \"(app-pages-browser)/./node_modules/next/dist/client/components/navigation.js\");\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_client_components_navigation__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n//# sourceMappingURL=navigation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYXBpL25hdmlnYXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWdEOztBQUVoRCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxuYWF0YVxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxPcG9zSVxcdjE2XFxub2RlX21vZHVsZXNcXG5leHRcXGRpc3RcXGFwaVxcbmF2aWdhdGlvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tICcuLi9jbGllbnQvY29tcG9uZW50cy9uYXZpZ2F0aW9uJztcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bmF2aWdhdGlvbi5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/api/navigation.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Csrc%5C%5Capp%5C%5Cpayment%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Csrc%5C%5Capp%5C%5Cpayment%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/payment/page.tsx */ \"(app-pages-browser)/./src/app/payment/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDbmFhdGElNUMlNUNEb2N1bWVudHMlNUMlNUNhdWdtZW50LXByb2plY3RzJTVDJTVDT3Bvc0klNUMlNUN2MTYlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNwYXltZW50JTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPWZhbHNlISIsIm1hcHBpbmdzIjoiQUFBQSw4S0FBMkgiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXG5hYXRhXFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXE9wb3NJXFxcXHYxNlxcXFxzcmNcXFxcYXBwXFxcXHBheW1lbnRcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Csrc%5C%5Capp%5C%5Cpayment%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcbmFhdGFcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcT3Bvc0lcXHYxNlxcbm9kZV9tb2R1bGVzXFxuZXh0XFxkaXN0XFxjb21waWxlZFxccmVhY3RcXGpzeC1kZXYtcnVudGltZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLnByb2R1Y3Rpb24uanMnKTtcbn0gZWxzZSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLmRldmVsb3BtZW50LmpzJyk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/node_modules/@swc/helpers/esm/_tagged_template_literal.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/next/node_modules/@swc/helpers/esm/_tagged_template_literal.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   _: () => (/* binding */ _tagged_template_literal)\n/* harmony export */ });\nfunction _tagged_template_literal(strings, raw) {\n    if (!raw) raw = strings.slice(0);\n\n    return Object.freeze(Object.defineProperties(strings, { raw: { value: Object.freeze(raw) } }));\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L25vZGVfbW9kdWxlcy9Ac3djL2hlbHBlcnMvZXNtL190YWdnZWRfdGVtcGxhdGVfbGl0ZXJhbC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTs7QUFFQSw0REFBNEQsT0FBTyw2QkFBNkI7QUFDaEc7QUFDeUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcbmFhdGFcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcT3Bvc0lcXHYxNlxcbm9kZV9tb2R1bGVzXFxuZXh0XFxub2RlX21vZHVsZXNcXEBzd2NcXGhlbHBlcnNcXGVzbVxcX3RhZ2dlZF90ZW1wbGF0ZV9saXRlcmFsLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIF90YWdnZWRfdGVtcGxhdGVfbGl0ZXJhbChzdHJpbmdzLCByYXcpIHtcbiAgICBpZiAoIXJhdykgcmF3ID0gc3RyaW5ncy5zbGljZSgwKTtcblxuICAgIHJldHVybiBPYmplY3QuZnJlZXplKE9iamVjdC5kZWZpbmVQcm9wZXJ0aWVzKHN0cmluZ3MsIHsgcmF3OiB7IHZhbHVlOiBPYmplY3QuZnJlZXplKHJhdykgfSB9KSk7XG59XG5leHBvcnQgeyBfdGFnZ2VkX3RlbXBsYXRlX2xpdGVyYWwgYXMgXyB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/node_modules/@swc/helpers/esm/_tagged_template_literal.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs":
/*!*****************************************************!*\
  !*** ./node_modules/react-hot-toast/dist/index.mjs ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CheckmarkIcon: () => (/* binding */ _),\n/* harmony export */   ErrorIcon: () => (/* binding */ k),\n/* harmony export */   LoaderIcon: () => (/* binding */ V),\n/* harmony export */   ToastBar: () => (/* binding */ C),\n/* harmony export */   ToastIcon: () => (/* binding */ M),\n/* harmony export */   Toaster: () => (/* binding */ Oe),\n/* harmony export */   \"default\": () => (/* binding */ Vt),\n/* harmony export */   resolveValue: () => (/* binding */ f),\n/* harmony export */   toast: () => (/* binding */ c),\n/* harmony export */   useToaster: () => (/* binding */ O),\n/* harmony export */   useToasterStore: () => (/* binding */ D)\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal */ \"(app-pages-browser)/./node_modules/next/node_modules/@swc/helpers/esm/_tagged_template_literal.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var goober__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! goober */ \"(app-pages-browser)/./node_modules/goober/dist/goober.modern.js\");\n/* __next_internal_client_entry_do_not_use__ CheckmarkIcon,ErrorIcon,LoaderIcon,ToastBar,ToastIcon,Toaster,default,resolveValue,toast,useToaster,useToasterStore auto */ \nfunction _templateObject() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\nfrom {\\n  transform: scale(0) rotate(45deg);\\n\topacity: 0;\\n}\\nto {\\n transform: scale(1) rotate(45deg);\\n  opacity: 1;\\n}\"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject1() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\nfrom {\\n  transform: scale(0);\\n  opacity: 0;\\n}\\nto {\\n  transform: scale(1);\\n  opacity: 1;\\n}\"\n    ]);\n    _templateObject1 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject2() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\nfrom {\\n  transform: scale(0) rotate(90deg);\\n\topacity: 0;\\n}\\nto {\\n  transform: scale(1) rotate(90deg);\\n\topacity: 1;\\n}\"\n    ]);\n    _templateObject2 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject3() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  width: 20px;\\n  opacity: 0;\\n  height: 20px;\\n  border-radius: 10px;\\n  background: \",\n        \";\\n  position: relative;\\n  transform: rotate(45deg);\\n\\n  animation: \",\n        \" 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)\\n    forwards;\\n  animation-delay: 100ms;\\n\\n  &:after,\\n  &:before {\\n    content: '';\\n    animation: \",\n        \" 0.15s ease-out forwards;\\n    animation-delay: 150ms;\\n    position: absolute;\\n    border-radius: 3px;\\n    opacity: 0;\\n    background: \",\n        \";\\n    bottom: 9px;\\n    left: 4px;\\n    height: 2px;\\n    width: 12px;\\n  }\\n\\n  &:before {\\n    animation: \",\n        \" 0.15s ease-out forwards;\\n    animation-delay: 180ms;\\n    transform: rotate(90deg);\\n  }\\n\"\n    ]);\n    _templateObject3 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject4() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  from {\\n    transform: rotate(0deg);\\n  }\\n  to {\\n    transform: rotate(360deg);\\n  }\\n\"\n    ]);\n    _templateObject4 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject5() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  width: 12px;\\n  height: 12px;\\n  box-sizing: border-box;\\n  border: 2px solid;\\n  border-radius: 100%;\\n  border-color: \",\n        \";\\n  border-right-color: \",\n        \";\\n  animation: \",\n        \" 1s linear infinite;\\n\"\n    ]);\n    _templateObject5 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject6() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\nfrom {\\n  transform: scale(0) rotate(45deg);\\n\topacity: 0;\\n}\\nto {\\n  transform: scale(1) rotate(45deg);\\n\topacity: 1;\\n}\"\n    ]);\n    _templateObject6 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject7() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n0% {\\n\theight: 0;\\n\twidth: 0;\\n\topacity: 0;\\n}\\n40% {\\n  height: 0;\\n\twidth: 6px;\\n\topacity: 1;\\n}\\n100% {\\n  opacity: 1;\\n  height: 10px;\\n}\"\n    ]);\n    _templateObject7 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject8() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  width: 20px;\\n  opacity: 0;\\n  height: 20px;\\n  border-radius: 10px;\\n  background: \",\n        \";\\n  position: relative;\\n  transform: rotate(45deg);\\n\\n  animation: \",\n        \" 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)\\n    forwards;\\n  animation-delay: 100ms;\\n  &:after {\\n    content: '';\\n    box-sizing: border-box;\\n    animation: \",\n        \" 0.2s ease-out forwards;\\n    opacity: 0;\\n    animation-delay: 200ms;\\n    position: absolute;\\n    border-right: 2px solid;\\n    border-bottom: 2px solid;\\n    border-color: \",\n        \";\\n    bottom: 6px;\\n    left: 6px;\\n    height: 10px;\\n    width: 6px;\\n  }\\n\"\n    ]);\n    _templateObject8 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject9() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  position: absolute;\\n\"\n    ]);\n    _templateObject9 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject10() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  position: relative;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  min-width: 20px;\\n  min-height: 20px;\\n\"\n    ]);\n    _templateObject10 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject11() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\nfrom {\\n  transform: scale(0.6);\\n  opacity: 0.4;\\n}\\nto {\\n  transform: scale(1);\\n  opacity: 1;\\n}\"\n    ]);\n    _templateObject11 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject12() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  position: relative;\\n  transform: scale(0.6);\\n  opacity: 0.4;\\n  min-width: 20px;\\n  animation: \",\n        \" 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)\\n    forwards;\\n\"\n    ]);\n    _templateObject12 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject13() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  display: flex;\\n  align-items: center;\\n  background: #fff;\\n  color: #363636;\\n  line-height: 1.3;\\n  will-change: transform;\\n  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);\\n  max-width: 350px;\\n  pointer-events: auto;\\n  padding: 8px 10px;\\n  border-radius: 8px;\\n\"\n    ]);\n    _templateObject13 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject14() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  display: flex;\\n  justify-content: center;\\n  margin: 4px 10px;\\n  color: inherit;\\n  flex: 1 1 auto;\\n  white-space: pre-line;\\n\"\n    ]);\n    _templateObject14 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject15() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  z-index: 9999;\\n  > * {\\n    pointer-events: auto;\\n  }\\n\"\n    ]);\n    _templateObject15 = function() {\n        return data;\n    };\n    return data;\n}\nvar _s = $RefreshSig$();\nvar W = (e)=>typeof e == \"function\", f = (e, t)=>W(e) ? e(t) : e;\nvar F = (()=>{\n    let e = 0;\n    return ()=>(++e).toString();\n})(), A = (()=>{\n    let e;\n    return ()=>{\n        if (e === void 0 && typeof window < \"u\") {\n            let t = matchMedia(\"(prefers-reduced-motion: reduce)\");\n            e = !t || t.matches;\n        }\n        return e;\n    };\n})();\n\nvar Y = 20;\nvar U = (e, t)=>{\n    switch(t.type){\n        case 0:\n            return {\n                ...e,\n                toasts: [\n                    t.toast,\n                    ...e.toasts\n                ].slice(0, Y)\n            };\n        case 1:\n            return {\n                ...e,\n                toasts: e.toasts.map((o)=>o.id === t.toast.id ? {\n                        ...o,\n                        ...t.toast\n                    } : o)\n            };\n        case 2:\n            let { toast: r } = t;\n            return U(e, {\n                type: e.toasts.find((o)=>o.id === r.id) ? 1 : 0,\n                toast: r\n            });\n        case 3:\n            let { toastId: s } = t;\n            return {\n                ...e,\n                toasts: e.toasts.map((o)=>o.id === s || s === void 0 ? {\n                        ...o,\n                        dismissed: !0,\n                        visible: !1\n                    } : o)\n            };\n        case 4:\n            return t.toastId === void 0 ? {\n                ...e,\n                toasts: []\n            } : {\n                ...e,\n                toasts: e.toasts.filter((o)=>o.id !== t.toastId)\n            };\n        case 5:\n            return {\n                ...e,\n                pausedAt: t.time\n            };\n        case 6:\n            let a = t.time - (e.pausedAt || 0);\n            return {\n                ...e,\n                pausedAt: void 0,\n                toasts: e.toasts.map((o)=>({\n                        ...o,\n                        pauseDuration: o.pauseDuration + a\n                    }))\n            };\n    }\n}, P = [], y = {\n    toasts: [],\n    pausedAt: void 0\n}, u = (e)=>{\n    y = U(y, e), P.forEach((t)=>{\n        t(y);\n    });\n}, q = {\n    blank: 4e3,\n    error: 4e3,\n    success: 2e3,\n    loading: 1 / 0,\n    custom: 4e3\n}, D = function() {\n    let e = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    let [t, r] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(y), s = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(y);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>(s.current !== y && r(y), P.push(r), ()=>{\n            let o = P.indexOf(r);\n            o > -1 && P.splice(o, 1);\n        }), []);\n    let a = t.toasts.map((o)=>{\n        var n, i, p;\n        return {\n            ...e,\n            ...e[o.type],\n            ...o,\n            removeDelay: o.removeDelay || ((n = e[o.type]) == null ? void 0 : n.removeDelay) || (e == null ? void 0 : e.removeDelay),\n            duration: o.duration || ((i = e[o.type]) == null ? void 0 : i.duration) || (e == null ? void 0 : e.duration) || q[o.type],\n            style: {\n                ...e.style,\n                ...(p = e[o.type]) == null ? void 0 : p.style,\n                ...o.style\n            }\n        };\n    });\n    return {\n        ...t,\n        toasts: a\n    };\n};\nvar J = function(e) {\n    let t = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"blank\", r = arguments.length > 2 ? arguments[2] : void 0;\n    return {\n        createdAt: Date.now(),\n        visible: !0,\n        dismissed: !1,\n        type: t,\n        ariaProps: {\n            role: \"status\",\n            \"aria-live\": \"polite\"\n        },\n        message: e,\n        pauseDuration: 0,\n        ...r,\n        id: (r == null ? void 0 : r.id) || F()\n    };\n}, x = (e)=>(t, r)=>{\n        let s = J(t, e, r);\n        return u({\n            type: 2,\n            toast: s\n        }), s.id;\n    }, c = (e, t)=>x(\"blank\")(e, t);\nc.error = x(\"error\");\nc.success = x(\"success\");\nc.loading = x(\"loading\");\nc.custom = x(\"custom\");\nc.dismiss = (e)=>{\n    u({\n        type: 3,\n        toastId: e\n    });\n};\nc.remove = (e)=>u({\n        type: 4,\n        toastId: e\n    });\nc.promise = (e, t, r)=>{\n    let s = c.loading(t.loading, {\n        ...r,\n        ...r == null ? void 0 : r.loading\n    });\n    return typeof e == \"function\" && (e = e()), e.then((a)=>{\n        let o = t.success ? f(t.success, a) : void 0;\n        return o ? c.success(o, {\n            id: s,\n            ...r,\n            ...r == null ? void 0 : r.success\n        }) : c.dismiss(s), a;\n    }).catch((a)=>{\n        let o = t.error ? f(t.error, a) : void 0;\n        o ? c.error(o, {\n            id: s,\n            ...r,\n            ...r == null ? void 0 : r.error\n        }) : c.dismiss(s);\n    }), e;\n};\n\nvar K = (e, t)=>{\n    u({\n        type: 1,\n        toast: {\n            id: e,\n            height: t\n        }\n    });\n}, X = ()=>{\n    u({\n        type: 5,\n        time: Date.now()\n    });\n}, b = new Map, Z = 1e3, ee = function(e) {\n    let t = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : Z;\n    if (b.has(e)) return;\n    let r = setTimeout(()=>{\n        b.delete(e), u({\n            type: 4,\n            toastId: e\n        });\n    }, t);\n    b.set(e, r);\n}, O = (e)=>{\n    let { toasts: t, pausedAt: r } = D(e);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (r) return;\n        let o = Date.now(), n = t.map((i)=>{\n            if (i.duration === 1 / 0) return;\n            let p = (i.duration || 0) + i.pauseDuration - (o - i.createdAt);\n            if (p < 0) {\n                i.visible && c.dismiss(i.id);\n                return;\n            }\n            return setTimeout(()=>c.dismiss(i.id), p);\n        });\n        return ()=>{\n            n.forEach((i)=>i && clearTimeout(i));\n        };\n    }, [\n        t,\n        r\n    ]);\n    let s = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        r && u({\n            type: 6,\n            time: Date.now()\n        });\n    }, [\n        r\n    ]), a = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((o, n)=>{\n        let { reverseOrder: i = !1, gutter: p = 8, defaultPosition: d } = n || {}, h = t.filter((m)=>(m.position || d) === (o.position || d) && m.height), v = h.findIndex((m)=>m.id === o.id), S = h.filter((m, E)=>E < v && m.visible).length;\n        return h.filter((m)=>m.visible).slice(...i ? [\n            S + 1\n        ] : [\n            0,\n            S\n        ]).reduce((m, E)=>m + (E.height || 0) + p, 0);\n    }, [\n        t\n    ]);\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        t.forEach((o)=>{\n            if (o.dismissed) ee(o.id, o.removeDelay);\n            else {\n                let n = b.get(o.id);\n                n && (clearTimeout(n), b.delete(o.id));\n            }\n        });\n    }, [\n        t\n    ]), {\n        toasts: t,\n        handlers: {\n            updateHeight: K,\n            startPause: X,\n            endPause: s,\n            calculateOffset: a\n        }\n    };\n};\n\n\n\n\n\nvar oe = (0,goober__WEBPACK_IMPORTED_MODULE_2__.keyframes)(_templateObject()), re = (0,goober__WEBPACK_IMPORTED_MODULE_2__.keyframes)(_templateObject1()), se = (0,goober__WEBPACK_IMPORTED_MODULE_2__.keyframes)(_templateObject2()), k = (0,goober__WEBPACK_IMPORTED_MODULE_2__.styled)(\"div\")(_templateObject3(), (e)=>e.primary || \"#ff4b4b\", oe, re, (e)=>e.secondary || \"#fff\", se);\n\nvar ne = (0,goober__WEBPACK_IMPORTED_MODULE_2__.keyframes)(_templateObject4()), V = (0,goober__WEBPACK_IMPORTED_MODULE_2__.styled)(\"div\")(_templateObject5(), (e)=>e.secondary || \"#e0e0e0\", (e)=>e.primary || \"#616161\", ne);\n\nvar pe = (0,goober__WEBPACK_IMPORTED_MODULE_2__.keyframes)(_templateObject6()), de = (0,goober__WEBPACK_IMPORTED_MODULE_2__.keyframes)(_templateObject7()), _ = (0,goober__WEBPACK_IMPORTED_MODULE_2__.styled)(\"div\")(_templateObject8(), (e)=>e.primary || \"#61d345\", pe, de, (e)=>e.secondary || \"#fff\");\nvar ue = (0,goober__WEBPACK_IMPORTED_MODULE_2__.styled)(\"div\")(_templateObject9()), le = (0,goober__WEBPACK_IMPORTED_MODULE_2__.styled)(\"div\")(_templateObject10()), fe = (0,goober__WEBPACK_IMPORTED_MODULE_2__.keyframes)(_templateObject11()), Te = (0,goober__WEBPACK_IMPORTED_MODULE_2__.styled)(\"div\")(_templateObject12(), fe), M = (param)=>{\n    let { toast: e } = param;\n    let { icon: t, type: r, iconTheme: s } = e;\n    return t !== void 0 ? typeof t == \"string\" ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(Te, null, t) : t : r === \"blank\" ? null : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(le, null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(V, {\n        ...s\n    }), r !== \"loading\" && /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(ue, null, r === \"error\" ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(k, {\n        ...s\n    }) : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(_, {\n        ...s\n    })));\n};\nvar ye = (e)=>\"\\n0% {transform: translate3d(0,\".concat(e * -200, \"%,0) scale(.6); opacity:.5;}\\n100% {transform: translate3d(0,0,0) scale(1); opacity:1;}\\n\"), ge = (e)=>\"\\n0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}\\n100% {transform: translate3d(0,\".concat(e * -150, \"%,-1px) scale(.6); opacity:0;}\\n\"), he = \"0%{opacity:0;} 100%{opacity:1;}\", xe = \"0%{opacity:1;} 100%{opacity:0;}\", be = (0,goober__WEBPACK_IMPORTED_MODULE_2__.styled)(\"div\")(_templateObject13()), Se = (0,goober__WEBPACK_IMPORTED_MODULE_2__.styled)(\"div\")(_templateObject14()), Ae = (e, t)=>{\n    let s = e.includes(\"top\") ? 1 : -1, [a, o] = A() ? [\n        he,\n        xe\n    ] : [\n        ye(s),\n        ge(s)\n    ];\n    return {\n        animation: t ? \"\".concat((0,goober__WEBPACK_IMPORTED_MODULE_2__.keyframes)(a), \" 0.35s cubic-bezier(.21,1.02,.73,1) forwards\") : \"\".concat((0,goober__WEBPACK_IMPORTED_MODULE_2__.keyframes)(o), \" 0.4s forwards cubic-bezier(.06,.71,.55,1)\")\n    };\n}, C = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.memo((param)=>{\n    let { toast: e, position: t, style: r, children: s } = param;\n    let a = e.height ? Ae(e.position || t || \"top-center\", e.visible) : {\n        opacity: 0\n    }, o = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(M, {\n        toast: e\n    }), n = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(Se, {\n        ...e.ariaProps\n    }, f(e.message, e));\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(be, {\n        className: e.className,\n        style: {\n            ...a,\n            ...r,\n            ...e.style\n        }\n    }, typeof s == \"function\" ? s({\n        icon: o,\n        message: n\n    }) : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(react__WEBPACK_IMPORTED_MODULE_1__.Fragment, null, o, n));\n});\n\n\n(0,goober__WEBPACK_IMPORTED_MODULE_2__.setup)(react__WEBPACK_IMPORTED_MODULE_1__.createElement);\nvar ve = (param)=>{\n    let { id: e, className: t, style: r, onHeightUpdate: s, children: a } = param;\n    _s();\n    let o = react__WEBPACK_IMPORTED_MODULE_1__.useCallback({\n        \"ve.useCallback[o]\": (n)=>{\n            if (n) {\n                let i = {\n                    \"ve.useCallback[o].i\": ()=>{\n                        let p = n.getBoundingClientRect().height;\n                        s(e, p);\n                    }\n                }[\"ve.useCallback[o].i\"];\n                i(), new MutationObserver(i).observe(n, {\n                    subtree: !0,\n                    childList: !0,\n                    characterData: !0\n                });\n            }\n        }\n    }[\"ve.useCallback[o]\"], [\n        e,\n        s\n    ]);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"div\", {\n        ref: o,\n        className: t,\n        style: r\n    }, a);\n}, Ee = (e, t)=>{\n    let r = e.includes(\"top\"), s = r ? {\n        top: 0\n    } : {\n        bottom: 0\n    }, a = e.includes(\"center\") ? {\n        justifyContent: \"center\"\n    } : e.includes(\"right\") ? {\n        justifyContent: \"flex-end\"\n    } : {};\n    return {\n        left: 0,\n        right: 0,\n        display: \"flex\",\n        position: \"absolute\",\n        transition: A() ? void 0 : \"all 230ms cubic-bezier(.21,1.02,.73,1)\",\n        transform: \"translateY(\".concat(t * (r ? 1 : -1), \"px)\"),\n        ...s,\n        ...a\n    };\n}, De = (0,goober__WEBPACK_IMPORTED_MODULE_2__.css)(_templateObject15()), R = 16, Oe = (param)=>{\n    let { reverseOrder: e, position: t = \"top-center\", toastOptions: r, gutter: s, children: a, containerStyle: o, containerClassName: n } = param;\n    let { toasts: i, handlers: p } = O(r);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"div\", {\n        id: \"_rht_toaster\",\n        style: {\n            position: \"fixed\",\n            zIndex: 9999,\n            top: R,\n            left: R,\n            right: R,\n            bottom: R,\n            pointerEvents: \"none\",\n            ...o\n        },\n        className: n,\n        onMouseEnter: p.startPause,\n        onMouseLeave: p.endPause\n    }, i.map((d)=>{\n        let h = d.position || t, v = p.calculateOffset(d, {\n            reverseOrder: e,\n            gutter: s,\n            defaultPosition: t\n        }), S = Ee(h, v);\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(ve, {\n            id: d.id,\n            key: d.id,\n            onHeightUpdate: p.updateHeight,\n            className: d.visible ? De : \"\",\n            style: S\n        }, d.type === \"custom\" ? f(d.message, d) : a ? a(d) : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(C, {\n            toast: d,\n            position: h\n        }));\n    }));\n};\n_s(ve, \"LQ34HCRCKbaP7NB9wB8OQNidTak=\");\nvar Vt = c;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/payment/page.tsx":
/*!**********************************!*\
  !*** ./src/app/payment/page.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PaymentPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_stripe_plans__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/stripe/plans */ \"(app-pages-browser)/./src/lib/stripe/plans.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n// ===== Archivo: src\\app\\payment\\page.tsx =====\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction PaymentContent() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const planId = searchParams.get('plan') || 'free';\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [confirmPassword, setConfirmPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [customerName, setCustomerName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const plan = (0,_lib_stripe_plans__WEBPACK_IMPORTED_MODULE_3__.getPlanById)(planId);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PaymentContent.useEffect\": ()=>{\n            if (!plan) {\n                router.push('/'); // Redirigir si el plan no es válido\n            }\n        }\n    }[\"PaymentContent.useEffect\"], [\n        plan,\n        router\n    ]);\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        // Validaciones básicas\n        if (!email.trim()) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error('Por favor, ingresa tu email');\n            return;\n        }\n        if (!password.trim()) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error('Por favor, ingresa una contraseña');\n            return;\n        }\n        if (password.length < 6) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error('La contraseña debe tener al menos 6 caracteres');\n            return;\n        }\n        if (password !== confirmPassword) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error('Las contraseñas no coinciden');\n            return;\n        }\n        setIsLoading(true);\n        try {\n            // Para el plan gratuito\n            if (planId === 'free') {\n                // Llamar al endpoint de registro gratuito\n                const registerResponse = await fetch('/api/auth/register-free', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        email,\n                        password,\n                        customerName\n                    })\n                });\n                const registerData = await registerResponse.json();\n                if (registerResponse.ok && registerData.success) {\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].success('Registro exitoso. Revisa tu email para confirmar tu cuenta.');\n                    router.push(\"/thank-you?plan=\".concat(planId, \"&email_sent=true\"));\n                } else {\n                    // Manejo de errores del endpoint de registro\n                    if (registerResponse.status === 429) {\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error('Demasiados intentos. Inténtalo en 15 minutos.');\n                    } else {\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error(registerData.error || 'Error al crear la cuenta gratuita');\n                    }\n                }\n            } else {\n                // NUEVO FLUJO: Para planes de pago, crear usuario primero y luego ir a Stripe\n                console.log('🔄 Iniciando nuevo flujo de pre-registro para plan de pago');\n                // Paso 1: Pre-registrar usuario\n                const preRegisterResponse = await fetch('/api/auth/pre-register-paid', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        email,\n                        password,\n                        customerName: customerName || email.split('@')[0],\n                        planId\n                    })\n                });\n                const preRegisterData = await preRegisterResponse.json();\n                if (!preRegisterResponse.ok) {\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error(preRegisterData.error || 'Error al crear la cuenta');\n                    return;\n                }\n                console.log('✅ Usuario pre-registrado exitosamente:', preRegisterData.userId);\n                // Paso 2: Crear sesión de Stripe con el userId\n                const stripeResponse = await fetch('/api/stripe/create-checkout-session', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        planId,\n                        email,\n                        customerName,\n                        userId: preRegisterData.userId\n                    })\n                });\n                const stripeData = await stripeResponse.json();\n                if (stripeResponse.ok && stripeData.url) {\n                    console.log('🔄 Redirigiendo a Stripe Checkout...');\n                    window.location.href = stripeData.url; // Redirigir a Stripe Checkout\n                } else {\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error(stripeData.error || 'Error al crear la sesión de pago');\n                }\n            }\n        } catch (error) {\n            console.error('Error en handleSubmit:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error('Error al procesar la solicitud. Por favor, intenta de nuevo.');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    if (!plan) {\n        // Este return se activará si el useEffect redirige, o si el plan es inválido inicialmente\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: \"Cargando detalles del plan o redirigiendo...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n            lineNumber: 144,\n            columnNumber: 9\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900\",\n                            children: plan.name\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-2xl font-semibold text-blue-600 mt-2\",\n                            children: [\n                                plan.price === 0 ? 'Gratis' : \"€\".concat((plan.price / 100).toFixed(2)),\n                                (planId === 'pro' || planId === 'usuario') && plan.price > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-500\",\n                                    children: \"/mes\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 78\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                    lineNumber: 153,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white shadow rounded-lg p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-gray-900 mb-3\",\n                                    children: \"Caracter\\xedsticas del plan:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: plan.features.map((feature, index)=>{\n                                        // Si es un encabezado (Incluye: o No incluye:) - SIN ICONO\n                                        if (feature === 'Incluye:' || feature === 'No incluye:') {\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"mt-4 first:mt-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-semibold text-gray-900\",\n                                                    children: feature\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                                    lineNumber: 174,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 21\n                                            }, this);\n                                        }\n                                        // Lógica mejorada para determinar si un ítem está bajo \"No incluye:\"\n                                        const isNotIncludedItem = (()=>{\n                                            if (!feature.startsWith('• ')) return false;\n                                            // Buscar hacia atrás el encabezado más cercano\n                                            for(let i = index - 1; i >= 0; i--){\n                                                if (plan.features[i] === 'Incluye:') return false;\n                                                if (plan.features[i] === 'No incluye:') return true;\n                                            }\n                                            return false;\n                                        })();\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-start ml-2\",\n                                            children: [\n                                                feature.startsWith('• ') ? isNotIncludedItem ? // Icono de Cruz Roja para características no incluidas\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-4 w-4 mt-0.5 mr-3 flex-shrink-0 text-red-500\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 20 20\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fillRule: \"evenodd\",\n                                                        d: \"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\",\n                                                        clipRule: \"evenodd\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                                        lineNumber: 201,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                                    lineNumber: 196,\n                                                    columnNumber: 25\n                                                }, this) : // Icono de Check Verde para características incluidas\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-4 w-4 mt-0.5 mr-3 flex-shrink-0 text-green-500\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 20 20\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fillRule: \"evenodd\",\n                                                        d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                        clipRule: \"evenodd\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                                        lineNumber: 214,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                                    lineNumber: 209,\n                                                    columnNumber: 25\n                                                }, this) : null,\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: feature.startsWith('• ') ? feature.substring(2) : feature\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                                    lineNumber: 222,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"email\",\n                                            className: \"block text-sm font-medium text-gray-700\",\n                                            children: \"Email *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            id: \"email\",\n                                            required: true,\n                                            value: email,\n                                            onChange: (e)=>setEmail(e.target.value),\n                                            className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                            placeholder: \"<EMAIL>\",\n                                            disabled: isLoading\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"password\",\n                                            className: \"block text-sm font-medium text-gray-700\",\n                                            children: \"Contrase\\xf1a *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"password\",\n                                            id: \"password\",\n                                            required: true,\n                                            value: password,\n                                            onChange: (e)=>setPassword(e.target.value),\n                                            className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                            placeholder: \"M\\xednimo 6 caracteres\",\n                                            disabled: isLoading,\n                                            minLength: 6\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                    lineNumber: 248,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"confirmPassword\",\n                                            className: \"block text-sm font-medium text-gray-700\",\n                                            children: \"Confirmar Contrase\\xf1a *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                            lineNumber: 266,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"password\",\n                                            id: \"confirmPassword\",\n                                            required: true,\n                                            value: confirmPassword,\n                                            onChange: (e)=>setConfirmPassword(e.target.value),\n                                            className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                            placeholder: \"Repite tu contrase\\xf1a\",\n                                            disabled: isLoading,\n                                            minLength: 6\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                            lineNumber: 269,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                    lineNumber: 265,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"customerName\",\n                                            className: \"block text-sm font-medium text-gray-700\",\n                                            children: \"Nombre (opcional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            id: \"customerName\",\n                                            value: customerName,\n                                            onChange: (e)=>setCustomerName(e.target.value),\n                                            className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                            placeholder: \"Tu nombre\",\n                                            disabled: isLoading\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                            lineNumber: 286,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: isLoading,\n                                    className: \"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: isLoading ? 'Procesando...' : planId === 'free' ? 'Solicitar Acceso Gratuito' : 'Proceder al Pago'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                    lineNumber: 163,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n            lineNumber: 152,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n        lineNumber: 151,\n        columnNumber: 5\n    }, this);\n}\n_s(PaymentContent, \"XkvHlsYN6OWBkX73yA7n4AKHYs8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = PaymentContent;\nfunction PaymentPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-md mx-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white shadow rounded-lg p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                lineNumber: 319,\n                                columnNumber: 15\n                            }, void 0),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-4 text-gray-600\",\n                                children: \"Cargando...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                lineNumber: 320,\n                                columnNumber: 15\n                            }, void 0)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                        lineNumber: 318,\n                        columnNumber: 13\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                    lineNumber: 317,\n                    columnNumber: 11\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                lineNumber: 316,\n                columnNumber: 9\n            }, void 0)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n            lineNumber: 315,\n            columnNumber: 7\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PaymentContent, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n            lineNumber: 326,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n        lineNumber: 314,\n        columnNumber: 5\n    }, this);\n}\n_c1 = PaymentPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"PaymentContent\");\n$RefreshReg$(_c1, \"PaymentPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/payment/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/stripe/plans.ts":
/*!*********************************!*\
  !*** ./src/lib/stripe/plans.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ADDITIONAL_PRODUCTS: () => (/* binding */ ADDITIONAL_PRODUCTS),\n/* harmony export */   APP_URLS: () => (/* binding */ APP_URLS),\n/* harmony export */   PLANS: () => (/* binding */ PLANS),\n/* harmony export */   getFullPlanConfig: () => (/* binding */ getFullPlanConfig),\n/* harmony export */   getPlanById: () => (/* binding */ getPlanById),\n/* harmony export */   isValidPlan: () => (/* binding */ isValidPlan)\n/* harmony export */ });\n/* harmony import */ var _lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/utils/planLimits */ \"(app-pages-browser)/./src/lib/utils/planLimits.ts\");\n// src/lib/stripe/plans.ts\n// Configuración de planes integrada con sistema de límites\n\nconst PLANS = {\n    free: {\n        id: 'free',\n        name: 'Plan Gratis',\n        price: 0,\n        stripeProductId: null,\n        stripePriceId: null,\n        features: [\n            'Incluye:',\n            '• Uso de la plataforma solo durante 5 días',\n            '• Subida de documentos: máximo 1 documento',\n            '• Generador de test: máximo 10 preguntas test',\n            '• Generador de flashcards: máximo 10 tarjetas flashcard',\n            '• Generador de mapas mentales: máximo 2 mapas mentales',\n            'No incluye:',\n            '• Planificación de estudios',\n            '• Habla con tu preparador IA',\n            '• Resúmenes para el ejercicio de desarrollo para cuerpos superiores A2 y A1'\n        ],\n        limits: _lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_0__.PLAN_CONFIGURATIONS.free.limits,\n        planConfig: _lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_0__.PLAN_CONFIGURATIONS.free\n    },\n    usuario: {\n        id: 'usuario',\n        name: 'Plan Usuario',\n        price: 1000,\n        stripeProductId: 'prod_SR65BdKdek1OXd',\n        // IMPORTANTE: Este precio debe ser recurrente (suscripción mensual) en Stripe\n        // Si actualmente es un pago único, crear un nuevo precio recurrente en Stripe Dashboard\n        stripePriceId: 'price_1Rae5807kFn3sIXhRf3adX1n',\n        features: [\n            'Incluye:',\n            '• Uso de la plataforma durante el mes (una vez finalizado no podrá volver a acceder hasta renovación)',\n            '• Subida de documentos',\n            '• Habla con tu preparador IA *',\n            '• Generador de test *',\n            '• Generador de flashcards *',\n            '• Generador de mapas mentales *',\n            '• * Para las tareas en las que se haga uso de IA, el límite mensual será de 1.000.000 de tokens, podrán ampliarse durante el mes mediante pago',\n            'No incluye:',\n            '• Planificación de estudios',\n            '• Resúmenes para el ejercicio de desarrollo para cuerpos superiores A2 y A1'\n        ],\n        limits: _lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_0__.PLAN_CONFIGURATIONS.usuario.limits,\n        planConfig: _lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_0__.PLAN_CONFIGURATIONS.usuario\n    },\n    pro: {\n        id: 'pro',\n        name: 'Plan Pro',\n        price: 1500,\n        stripeProductId: 'prod_SR66U2G7bVJqu3',\n        stripePriceId: 'price_1Rae3U07kFn3sIXhkvSuJco1',\n        features: [\n            'Incluye:',\n            '• Uso de la plataforma durante el mes (una vez finalizado no podrá volver a acceder hasta renovación)',\n            '• Subida de documentos',\n            '• Planificación de estudios mediante IA*',\n            '• Habla con tu preparador IA *',\n            '• Generador de test *',\n            '• Generador de flashcards *',\n            '• Generador de mapas mentales *',\n            '• Generación de resúmenes para el ejercicio de desarrollo para cuerpos superiores A2 y A1',\n            '• * Para las tareas en las que se haga uso de IA, el límite mensual será de 1.000.000 de tokens, podrán ampliarse durante el mes mediante pago'\n        ],\n        limits: _lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_0__.PLAN_CONFIGURATIONS.pro.limits,\n        planConfig: _lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_0__.PLAN_CONFIGURATIONS.pro\n    }\n};\n// Función para obtener plan por ID\nfunction getPlanById(planId) {\n    return PLANS[planId] || null;\n}\n// Función para validar si un plan es válido\nfunction isValidPlan(planId) {\n    return planId in PLANS;\n}\n// Función para obtener configuración completa del plan\nfunction getFullPlanConfig(planId) {\n    const plan = getPlanById(planId);\n    return (plan === null || plan === void 0 ? void 0 : plan.planConfig) || null;\n}\n// Configuración de productos adicionales\nconst ADDITIONAL_PRODUCTS = {\n    tokens: {\n        id: 'tokens',\n        name: 'Tokens Adicionales',\n        description: '1,000,000 tokens adicionales para tu cuenta',\n        price: 1000,\n        tokenAmount: 1000000,\n        // Estos IDs se deben crear en Stripe Dashboard\n        stripeProductId: 'prod_tokens_additional',\n        stripePriceId: 'price_tokens_additional'\n    }\n};\n// URLs de la aplicación\nconst APP_URLS = {\n    success: \"\".concat(\"http://localhost:3000\", \"/thank-you\"),\n    cancel: \"\".concat(\"http://localhost:3000\", \"/upgrade-plan\"),\n    webhook: \"\".concat(\"http://localhost:3000\", \"/api/stripe/webhook\")\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/stripe/plans.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/utils/planLimits.ts":
/*!*************************************!*\
  !*** ./src/lib/utils/planLimits.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PLAN_CONFIGURATIONS: () => (/* binding */ PLAN_CONFIGURATIONS),\n/* harmony export */   canPerformAction: () => (/* binding */ canPerformAction),\n/* harmony export */   checkUserFeatureAccess: () => (/* binding */ checkUserFeatureAccess),\n/* harmony export */   getPlanConfiguration: () => (/* binding */ getPlanConfiguration),\n/* harmony export */   getTokenLimitForPlan: () => (/* binding */ getTokenLimitForPlan),\n/* harmony export */   getTrialLimit: () => (/* binding */ getTrialLimit),\n/* harmony export */   getWeeklyLimit: () => (/* binding */ getWeeklyLimit),\n/* harmony export */   hasFeatureAccess: () => (/* binding */ hasFeatureAccess),\n/* harmony export */   isUnlimited: () => (/* binding */ isUnlimited)\n/* harmony export */ });\n// src/lib/utils/planLimits.ts\n// Configuración centralizada de límites y características por plan\n// Configuración completa de planes con límites y características\nconst PLAN_CONFIGURATIONS = {\n    free: {\n        id: 'free',\n        name: 'Plan Gratis',\n        price: 0,\n        limits: {\n            documents: 1,\n            mindMapsForTrial: 2,\n            testsForTrial: 10,\n            flashcardsForTrial: 10,\n            tokensForTrial: 50000,\n            features: [\n                'document_upload',\n                'test_generation',\n                'flashcard_generation',\n                'mind_map_generation'\n            ]\n        },\n        features: [\n            'document_upload',\n            'test_generation',\n            'flashcard_generation',\n            'mind_map_generation'\n        ],\n        restrictedFeatures: [\n            'study_planning',\n            'ai_tutor_chat',\n            'summary_a1_a2'\n        ]\n    },\n    usuario: {\n        id: 'usuario',\n        name: 'Plan Usuario',\n        price: 1000,\n        limits: {\n            documents: -1,\n            mindMapsPerWeek: -1,\n            testsPerWeek: -1,\n            flashcardsPerWeek: -1,\n            monthlyTokens: 1000000,\n            features: [\n                'document_upload',\n                'ai_tutor_chat',\n                'test_generation',\n                'flashcard_generation',\n                'mind_map_generation'\n            ]\n        },\n        features: [\n            'document_upload',\n            'ai_tutor_chat',\n            'test_generation',\n            'flashcard_generation',\n            'mind_map_generation'\n        ],\n        restrictedFeatures: [\n            'study_planning',\n            'summary_a1_a2'\n        ]\n    },\n    pro: {\n        id: 'pro',\n        name: 'Plan Pro',\n        price: 1500,\n        limits: {\n            documents: -1,\n            mindMapsPerWeek: -1,\n            testsPerWeek: -1,\n            flashcardsPerWeek: -1,\n            monthlyTokens: 1000000,\n            features: [\n                'document_upload',\n                'study_planning',\n                'ai_tutor_chat',\n                'test_generation',\n                'flashcard_generation',\n                'mind_map_generation',\n                'summary_a1_a2'\n            ]\n        },\n        features: [\n            'document_upload',\n            'study_planning',\n            'ai_tutor_chat',\n            'test_generation',\n            'flashcard_generation',\n            'mind_map_generation',\n            'summary_a1_a2'\n        ],\n        restrictedFeatures: []\n    }\n};\n// Funciones de utilidad\nfunction getPlanConfiguration(planId) {\n    return PLAN_CONFIGURATIONS[planId] || null;\n}\nfunction getTokenLimitForPlan(planId) {\n    const config = getPlanConfiguration(planId);\n    if (!config) return 50000;\n    // Para plan gratuito, usar tokensForTrial\n    if (planId === 'free') {\n        return config.limits.tokensForTrial || 50000;\n    }\n    // Para planes de pago, usar monthlyTokens\n    return config.limits.monthlyTokens || 1000000;\n}\nfunction hasFeatureAccess(planId, feature) {\n    const config = getPlanConfiguration(planId);\n    if (!config) return false;\n    // Si la característica está en la lista de restringidas, no tiene acceso\n    if (config.restrictedFeatures.includes(feature)) {\n        return false;\n    }\n    // Si no está restringida, verificar si está en las características permitidas\n    return config.features.includes(feature);\n}\nfunction getWeeklyLimit(planId, limitType) {\n    const config = getPlanConfiguration(planId);\n    if (!config) return 0;\n    switch(limitType){\n        case 'mindMaps':\n            return config.limits.mindMapsPerWeek || 0;\n        case 'tests':\n            return config.limits.testsPerWeek || 0;\n        case 'flashcards':\n            return config.limits.flashcardsPerWeek || 0;\n        default:\n            return 0;\n    }\n}\n// Nueva función para obtener límites de trial (para plan gratuito)\nfunction getTrialLimit(planId, limitType) {\n    const config = getPlanConfiguration(planId);\n    if (!config || planId !== 'free') return -1; // -1 para ilimitado o no aplica\n    switch(limitType){\n        case 'mindMaps':\n            return config.limits.mindMapsForTrial || 0;\n        case 'tests':\n            return config.limits.testsForTrial || 0;\n        case 'flashcards':\n            return config.limits.flashcardsForTrial || 0;\n        case 'tokens':\n            return config.limits.tokensForTrial || 0;\n        default:\n            return 0;\n    }\n}\nfunction isUnlimited(limit) {\n    return limit === -1;\n}\n// Validar si un usuario puede realizar una acción específica\nfunction canPerformAction(planId, feature, currentUsage, limitType) {\n    const config = getPlanConfiguration(planId);\n    if (!config) {\n        return {\n            allowed: false,\n            reason: 'Plan no válido'\n        };\n    }\n    // Verificar si tiene acceso a la característica\n    if (!hasFeatureAccess(planId, feature)) {\n        return {\n            allowed: false,\n            reason: \"Caracter\\xedstica \".concat(feature, \" no disponible en \").concat(config.name)\n        };\n    }\n    // Verificar límites semanales si aplica\n    if (limitType) {\n        const weeklyLimit = getWeeklyLimit(planId, limitType);\n        if (!isUnlimited(weeklyLimit) && currentUsage >= weeklyLimit) {\n            return {\n                allowed: false,\n                reason: \"L\\xedmite semanal de \".concat(limitType, \" alcanzado (\").concat(weeklyLimit, \")\")\n            };\n        }\n    }\n    return {\n        allowed: true\n    };\n}\n// Función para verificar acceso de usuario (para uso en frontend)\nasync function checkUserFeatureAccess(feature) {\n    try {\n        // Obtener el plan del usuario desde la API\n        const response = await fetch('/api/user/plan');\n        if (!response.ok) {\n            console.error('Error obteniendo plan del usuario');\n            // Si hay error, asumir plan gratuito\n            return hasFeatureAccess('free', feature);\n        }\n        const { plan } = await response.json();\n        const userPlan = plan || 'free';\n        // Usar la misma lógica que la función hasFeatureAccess\n        return hasFeatureAccess(userPlan, feature);\n    } catch (error) {\n        console.error('Error verificando acceso a característica:', error);\n        // En caso de error, asumir plan gratuito\n        return hasFeatureAccess('free', feature);\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/utils/planLimits.ts\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Csrc%5C%5Capp%5C%5Cpayment%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);