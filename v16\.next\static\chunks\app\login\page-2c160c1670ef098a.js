(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4520],{4436:(e,t,r)=>{"use strict";r.d(t,{k5:()=>d});var a=r(2115),s={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},o=a.createContext&&a.createContext(s),i=["attr","size","title"];function n(){return(n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&(e[a]=r[a])}return e}).apply(this,arguments)}function l(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,a)}return r}function c(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?l(Object(r),!0).forEach(function(t){var a,s,o;a=e,s=t,o=r[t],(s=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var a=r.call(e,t||"default");if("object"!=typeof a)return a;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(s))in a?Object.defineProperty(a,s,{value:o,enumerable:!0,configurable:!0,writable:!0}):a[s]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function d(e){return t=>a.createElement(m,n({attr:c({},e.attr)},t),function e(t){return t&&t.map((t,r)=>a.createElement(t.tag,c({key:r},t.attr),e(t.child)))}(e.child))}function m(e){var t=t=>{var r,{attr:s,size:o,title:l}=e,d=function(e,t){if(null==e)return{};var r,a,s=function(e,t){if(null==e)return{};var r={};for(var a in e)if(Object.prototype.hasOwnProperty.call(e,a)){if(t.indexOf(a)>=0)continue;r[a]=e[a]}return r}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(a=0;a<o.length;a++)r=o[a],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(s[r]=e[r])}return s}(e,i),m=o||t.size||"1em";return t.className&&(r=t.className),e.className&&(r=(r?r+" ":"")+e.className),a.createElement("svg",n({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,s,d,{className:r,style:c(c({color:e.color||t.color},t.style),e.style),height:m,width:m,xmlns:"http://www.w3.org/2000/svg"}),l&&a.createElement("title",null,l),e.children)};return void 0!==o?a.createElement(o.Consumer,null,e=>t(e)):t(s)}},5556:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>b});var a=r(5155),s=r(2115),o=r(5695),i=r(4092),n=r(2177),l=r(221),c=r(8260),d=r(2643),m=r(8781),u=r(351);function p(e){let{isOpen:t,onClose:r}=e,[o,i]=(0,s.useState)(!1),[p,b]=(0,s.useState)(!1),x=(0,d.U)(),{register:f,handleSubmit:g,formState:{errors:h},reset:y,setFocus:v}=(0,n.mN)({resolver:(0,l.u)(m.E9),defaultValues:{email:""}});(0,s.useEffect)(()=>{t&&(b(!1),y(),setTimeout(()=>{v("email")},100))},[t,y,v]),(0,s.useEffect)(()=>{let e=e=>{"Escape"===e.key&&t&&r()};return t&&(document.addEventListener("keydown",e),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",e),document.body.style.overflow="unset"}},[t,r]);let j=async e=>{i(!0);try{let{error:t}=await x.auth.resetPasswordForEmail(e.email,{redirectTo:"".concat("http://localhost:3000","/auth/reset-password")});t?(console.error("Error al solicitar reseteo de contrase\xf1a:",t.message),t.message.includes("rate limit")||t.message.includes("too many")?c.oR.error("Has solicitado demasiados enlaces de recuperaci\xf3n. Por favor, espera unos minutos antes de intentar nuevamente."):c.oR.error("Ocurri\xf3 un error al procesar tu solicitud. Por favor, int\xe9ntalo nuevamente.")):(b(!0),c.oR.success("Si tu email est\xe1 registrado, recibir\xe1s un enlace para restablecer tu contrase\xf1a."))}catch(e){console.error("Error inesperado:",e),c.oR.error("Ocurri\xf3 un error inesperado. Por favor, int\xe9ntalo nuevamente.")}finally{i(!1)}},w=()=>{o||r()};return t?(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",onClick:e=>{e.target===e.currentTarget&&w()},role:"dialog","aria-modal":"true","aria-labelledby":"forgot-password-title","aria-describedby":"forgot-password-description",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-md w-full p-6 relative",children:[(0,a.jsx)("button",{onClick:w,disabled:o,className:"absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors disabled:opacity-50","aria-label":"Cerrar modal",children:(0,a.jsx)(u.yGN,{className:"w-6 h-6"})}),(0,a.jsxs)("div",{className:"pr-8",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[(0,a.jsx)(u.pHD,{className:"w-8 h-8 text-blue-600 mr-3"}),(0,a.jsx)("h2",{id:"forgot-password-title",className:"text-xl font-semibold text-gray-900",children:"Restablecer Contrase\xf1a"})]}),p?(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)(u.kGk,{className:"w-8 h-8 text-green-600"})}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"Si tu email est\xe1 registrado en nuestro sistema, recibir\xe1s un enlace para restablecer tu contrase\xf1a en los pr\xf3ximos minutos."}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mb-6",children:"Revisa tu bandeja de entrada y la carpeta de spam."}),(0,a.jsx)("button",{onClick:w,className:"w-full px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-blue-700 border border-transparent rounded-md hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:"Entendido"})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("p",{id:"forgot-password-description",className:"text-gray-600 mb-6",children:"Ingresa tu direcci\xf3n de email y te enviaremos un enlace para restablecer tu contrase\xf1a."}),(0,a.jsxs)("form",{onSubmit:g(j),className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"reset-email",className:"block text-sm font-medium text-gray-700 mb-1",children:"Email"}),(0,a.jsx)("input",{id:"reset-email",type:"email",autoComplete:"email",disabled:o,...f("email"),className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",placeholder:"<EMAIL>"}),h.email&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",role:"alert",children:h.email.message})]}),(0,a.jsxs)("div",{className:"flex gap-3 pt-2",children:[(0,a.jsx)("button",{type:"button",onClick:w,disabled:o,className:"flex-1 px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed",children:"Cancelar"}),(0,a.jsx)("button",{type:"submit",disabled:o,className:"flex-1 flex items-center justify-center px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-blue-700 border border-transparent rounded-md hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",children:o?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(u.TwU,{className:"w-4 h-4 mr-2 animate-spin"}),"Enviando..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(u.kGk,{className:"w-4 h-4 mr-2"}),"Enviar Enlace"]})})]})]})]})]})]})}):null}function b(){let[e,t]=(0,s.useState)(""),[r,n]=(0,s.useState)(""),[l,c]=(0,s.useState)(""),[d,m]=(0,s.useState)(!1),{iniciarSesion:u,error:b,isLoading:x,user:f}=(0,i.A)();(0,o.useRouter)(),(0,s.useEffect)(()=>{b&&(c(b),b.includes("sincronizaci\xf3n de tiempo")&&console.info("Sugerencia: Verifica que la hora de tu dispositivo est\xe9 correctamente configurada y sincronizada con un servidor de tiempo."))},[b]);let g=async t=>(t.preventDefault(),c(""),e.trim())?r.trim()?void await u(e,r):void c("Por favor, ingresa tu contrase\xf1a"):void c("Por favor, ingresa tu email");return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-center bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"OposiAI"}),(0,a.jsx)("h2",{className:"mt-2 text-center text-xl font-semibold text-gray-900",children:"Inicia sesi\xf3n en tu cuenta"}),(0,a.jsx)("p",{className:"mt-2 text-center text-sm text-gray-600",children:"Accede a tu asistente inteligente para oposiciones"})]}),(0,a.jsx)("div",{className:"mt-8 sm:mx-auto sm:w-full sm:max-w-md",children:(0,a.jsxs)("div",{className:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10",children:[(0,a.jsxs)("form",{className:"space-y-6",onSubmit:g,children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email"}),(0,a.jsx)("div",{className:"mt-1",children:(0,a.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:e,onChange:e=>t(e.target.value),className:"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",disabled:x})})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Contrase\xf1a"}),(0,a.jsx)("div",{className:"mt-1",children:(0,a.jsx)("input",{id:"password",name:"password",type:"password",autoComplete:"current-password",required:!0,value:r,onChange:e=>n(e.target.value),className:"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",disabled:x})})]}),(0,a.jsx)("div",{className:"flex items-center justify-end",children:(0,a.jsx)("button",{type:"button",onClick:()=>m(!0),disabled:x,className:"text-sm text-blue-600 hover:text-blue-500 focus:outline-none focus:underline disabled:opacity-50 disabled:cursor-not-allowed",children:"\xbfOlvidaste tu contrase\xf1a?"})}),l&&(0,a.jsxs)("div",{className:"text-red-500 text-sm bg-red-50 p-3 rounded-md border border-red-200",children:[l,l.includes("sincronizaci\xf3n de tiempo")&&(0,a.jsxs)("div",{className:"mt-2 text-gray-600 text-xs",children:[(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Sugerencia:"})," Este error puede ocurrir cuando la hora de tu dispositivo no est\xe1 sincronizada correctamente."]}),(0,a.jsxs)("ol",{className:"list-decimal pl-5 mt-1",children:[(0,a.jsx)("li",{children:"Verifica que la fecha y hora de tu dispositivo est\xe9n configuradas correctamente"}),(0,a.jsx)("li",{children:"Activa la sincronizaci\xf3n autom\xe1tica de hora en tu sistema"}),(0,a.jsx)("li",{children:"Reinicia el navegador e intenta nuevamente"})]})]})]}),(0,a.jsx)("div",{children:(0,a.jsx)("button",{type:"submit",disabled:x,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",children:x?(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Iniciando sesi\xf3n..."]}):"Iniciar sesi\xf3n"})})]}),(0,a.jsx)("div",{className:"mt-6",children:(0,a.jsx)("p",{className:"text-center text-sm text-gray-600",children:"\xbfNo tienes una cuenta? Contacta con el administrador para solicitar acceso."})})]})}),(0,a.jsx)(p,{isOpen:d,onClose:()=>m(!1)})]})}},8404:(e,t,r)=>{Promise.resolve().then(r.bind(r,5556))},8781:(e,t,r)=>{"use strict";r.d(t,{E9:()=>c,GS:()=>i,MZ:()=>l,oO:()=>n,oS:()=>s});var a=r(1153);let s=a.z.object({pregunta:a.z.string().min(1,"La pregunta es obligatoria").max(500,"M\xe1ximo 500 caracteres"),documentos:a.z.array(a.z.object({id:a.z.string().optional(),titulo:a.z.string().min(1),contenido:a.z.string().min(1),categoria:a.z.string().optional().nullable(),numero_tema:a.z.union([a.z.number().int().positive(),a.z.string(),a.z.null(),a.z.undefined()]).optional(),creado_en:a.z.string().optional(),actualizado_en:a.z.string().optional(),user_id:a.z.string().optional(),tipo_original:a.z.string().optional()})).min(1,"Debes seleccionar al menos un documento")}),o=a.z.object({peticion:a.z.string().min(1,"La petici\xf3n es obligatoria").max(500,"M\xe1ximo 500 caracteres")}),i=a.z.object({peticion:a.z.string().min(1,"La petici\xf3n es obligatoria").max(500,"M\xe1ximo 500 caracteres"),cantidad:a.z.number().min(1,"M\xednimo 1 pregunta").max(50,"M\xe1ximo 50 preguntas").default(10)}),n=a.z.object({peticion:a.z.string().min(1,"La petici\xf3n es obligatoria").max(500,"M\xe1ximo 500 caracteres"),cantidad:a.z.number().min(1,"M\xednimo 1 flashcard").max(30,"M\xe1ximo 30 flashcards").default(10)}),l=o,c=a.z.object({email:a.z.string().min(1,"El email es obligatorio").email("Por favor, ingresa un email v\xe1lido").max(255,"El email es demasiado largo")});a.z.object({password:a.z.string().min(8,"La contrase\xf1a debe tener al menos 8 caracteres").max(128,"La contrase\xf1a es demasiado larga"),confirmPassword:a.z.string()}).refine(e=>e.password===e.confirmPassword,{message:"Las contrase\xf1as no coinciden",path:["confirmPassword"]})}},e=>{var t=t=>e(e.s=t);e.O(0,[844,7361,8260,861,4092,8441,1684,7358],()=>t(8404)),_N_E=e.O()}]);