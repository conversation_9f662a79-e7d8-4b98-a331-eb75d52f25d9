(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6769],{447:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>d});var t=r(5155),s=r(2115),i=r(5695),n=r(6317);function o(){let e=(0,i.useRouter)(),a=(0,i.useSearchParams)(),[r,o]=(0,s.useState)("loading"),[d,l]=(0,s.useState)("Procesando autenticaci\xf3n, por favor espera..."),[c,u]=(0,s.useState)([]),p=(0,s.useRef)(null),m=(e,a)=>{let r=new Date().toISOString(),t="[".concat(r,"] ").concat(e);console.log(t,a||""),u(e=>[...e,t])};return(0,s.useEffect)(()=>{m("--- AuthCallbackContent useEffect INICIADO ---"),m("URL Completa en el cliente:",window.location.href),m("Query Params (searchParams):",Object.fromEntries(a.entries()));let t=a.get("error_description"),s=a.get("error_code"),i=a.get("error");if(t||s||i){let e=decodeURIComponent(t||i||"Error desconocido");"user_already_invited"===s||t&&t.includes("User already invited")?e="Ya se ha enviado una invitaci\xf3n a este correo. Por favor, revisa tu bandeja de entrada (y spam). Si no la encuentras, puedes intentar recuperar tu contrase\xf1a desde la p\xe1gina de login.":("token_expired_or_invalid"===s||t&&(t.includes("invalid token")||t.includes("expired")))&&(e="El enlace de autenticaci\xf3n es inv\xe1lido, ha expirado o ya fue utilizado. Por favor, intenta registrarte o iniciar sesi\xf3n de nuevo."),m("❌ Error expl\xedcito detectado en la URL.",{error:e,code:s,error_param:i}),o("error"),l(e),p.current&&clearTimeout(p.current);return}let d=(0,n.U)();m("Cliente Supabase del navegador inicializado.");let{data:c}=d.auth.onAuthStateChange(async(a,r)=>{var t,s,i,n,d;m("EVENTO onAuthStateChange RECIBIDO: ".concat(a),{hasSession:!!r,userId:null==r||null==(t=r.user)?void 0:t.id,userMetadata:null==r||null==(s=r.user)?void 0:s.user_metadata}),p.current&&(clearTimeout(p.current),p.current=null),"PASSWORD_RECOVERY"===a?(m("✅ Evento PASSWORD_RECOVERY detectado. Redirigiendo a /auth/reset-password..."),o("success"),l("Sesi\xf3n de recuperaci\xf3n lista. Redirigiendo para establecer contrase\xf1a..."),e.push("/auth/reset-password")):"SIGNED_IN"===a?(m("✅ Evento SIGNED_IN detectado."),(null==r?void 0:r.user)?(null==(i=r.user.user_metadata)?void 0:i.requires_password_setup)===!0?(m("\uD83D\uDC64 Usuario necesita establecer contrase\xf1a inicial. Redirigiendo a /auth/reset-password..."),o("success"),l("\xa1Cuenta activada! Ahora, crea tu contrase\xf1a..."),e.push("/auth/reset-password")):(m("\uD83D\uDE80 Usuario ya tiene contrase\xf1a o no requiere setup. Redirigiendo a /app..."),o("success"),l("\xa1Autenticaci\xf3n exitosa! Redirigiendo..."),e.push("/app")):(m("⚠️ Evento SIGNED_IN sin sesi\xf3n completa. Comportamiento inesperado."),o("error"),l("Error inesperado durante el inicio de sesi\xf3n. Por favor, int\xe9ntalo de nuevo."))):"USER_UPDATED"===a&&(null==r?void 0:r.user)?(m("✅ Evento USER_UPDATED con sesi\xf3n."),(null==(n=r.user.user_metadata)?void 0:n.requires_password_setup)===!0?(m("\uD83D\uDC64 Usuario actualizado necesita establecer contrase\xf1a. Redirigiendo a /auth/reset-password..."),o("success"),l("Actualizaci\xf3n de cuenta completada. Crea tu contrase\xf1a..."),e.push("/auth/reset-password")):(m("\uD83D\uDE80 Cuenta actualizada. Redirigiendo a /app..."),o("success"),l("Cuenta actualizada. Redirigiendo..."),e.push("/app"))):"INITIAL_SESSION"!==a||r?"INITIAL_SESSION"===a&&(null==r?void 0:r.user)&&(m("✅ Evento INITIAL_SESSION con sesi\xf3n. Procediendo como SIGNED_IN."),(null==(d=r.user.user_metadata)?void 0:d.requires_password_setup)===!0?e.push("/auth/reset-password"):e.push("/app")):m("\uD83D\uDD39 Evento INITIAL_SESSION sin sesi\xf3n. Esperando m\xe1s eventos o timeout.")});return m("Listener onAuthStateChange configurado."),p.current=setTimeout(async()=>{if("loading"===r){let{data:{session:r},error:t}=await d.auth.getSession();if(null==r?void 0:r.user){var a;m("✅ Sesi\xf3n encontrada en la verificaci\xf3n final del timeout."),(null==(a=r.user.user_metadata)?void 0:a.requires_password_setup)===!0?(m("\uD83D\uDC64 Redirigiendo a /auth/reset-password desde timeout."),e.push("/auth/reset-password")):(m("\uD83D\uDE80 Redirigiendo a /app desde timeout."),e.push("/app")),o("success"),l("\xa1Autenticaci\xf3n completada! Redirigiendo...")}else m("❌ TIMEOUT (10s): No se recibi\xf3 un evento de autenticaci\xf3n v\xe1lido y no hay sesi\xf3n activa."),m("Detalles del error en la verificaci\xf3n final del timeout:",null==t?void 0:t.message),o("error"),l("El enlace de autenticaci\xf3n es inv\xe1lido, ha expirado o ya fue utilizado. Si el problema persiste, intenta registrarte de nuevo o contacta a soporte.")}},1e4),()=>{m("--- AuthCallbackContent useEffect LIMPIEZA ---"),null==c||c.subscription.unsubscribe(),m("Suscripci\xf3n a onAuthStateChange eliminada."),p.current&&(clearTimeout(p.current),m("Temporizador de timeout limpiado."))}},[e,a]),(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-xl p-8 max-w-lg w-full text-center",children:["loading"===r&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,t.jsx)("h2",{className:"text-xl font-semibold text-gray-800 mb-2",children:"Procesando..."}),(0,t.jsx)("p",{className:"text-gray-600",children:d})]}),"success"===r&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,t.jsx)("svg",{className:"w-6 h-6 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),(0,t.jsx)("h2",{className:"text-xl font-semibold text-gray-800 mb-2",children:"\xa1\xc9xito!"}),(0,t.jsx)("p",{className:"text-gray-600",children:d})]}),"error"===r&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,t.jsx)("svg",{className:"w-6 h-6 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})}),(0,t.jsx)("h2",{className:"text-xl font-semibold text-gray-800 mb-2",children:"Error de Autenticaci\xf3n"}),(0,t.jsx)("p",{className:"text-gray-600 mb-4",children:d}),(0,t.jsx)("div",{className:"space-y-2",children:(0,t.jsx)("button",{onClick:()=>e.push("/login"),className:"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors",children:"Ir a Login"})})]}),(0,t.jsxs)("div",{className:"mt-6 pt-4 border-t border-gray-200 text-left bg-gray-50 p-3 rounded max-h-48 overflow-y-auto",children:[(0,t.jsx)("h4",{className:"text-sm font-semibold text-gray-700 mb-2",children:"Registro de Eventos:"}),(0,t.jsx)("div",{className:"text-xs text-gray-500 space-y-1",children:c.map((e,a)=>(0,t.jsx)("div",{children:e},a))})]})]})})}function d(){return(0,t.jsx)(s.Suspense,{fallback:(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-xl p-8 max-w-md w-full text-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,t.jsx)("h2",{className:"text-xl font-semibold text-gray-800 mb-2",children:"Cargando..."}),(0,t.jsx)("p",{className:"text-gray-600",children:"Procesando autenticaci\xf3n..."})]})}),children:(0,t.jsx)(o,{})})}},2643:(e,a,r)=>{"use strict";r.d(a,{N:()=>i,U:()=>s});var t=r(9535);function s(){return(0,t.createBrowserClient)("https://fxnhpxjijinfuxxxplzj.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4",{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0}})}let i=s()},6317:(e,a,r)=>{"use strict";r.d(a,{N:()=>t.N,U:()=>t.U});var t=r(2643)},8430:(e,a,r)=>{Promise.resolve().then(r.bind(r,447))}},e=>{var a=a=>e(e.s=a);e.O(0,[7361,8441,1684,7358],()=>a(8430)),_N_E=e.O()}]);