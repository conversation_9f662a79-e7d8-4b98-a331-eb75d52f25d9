(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[979],{7257:(e,s,l)=>{"use strict";l.r(s),l.d(s,{default:()=>d});var a=l(5155),r=l(2115),i=l(8260);function d(){var e,s,l,d;let[t,o]=(0,r.useState)(""),[n,c]=(0,r.useState)(""),[m,u]=(0,r.useState)(""),[x,h]=(0,r.useState)(!1),[v,b]=(0,r.useState)(null),j=async e=>{if(e.preventDefault(),!t&&!n&&!m)return void i.oR.error("Proporciona al menos un identificador (Session ID, Email o User ID)");h(!0),b(null);try{let e=await fetch("/api/admin/reactivate-user",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({sessionId:t||void 0,email:n||void 0,userId:m||void 0})}),s=await e.json();e.ok?(i.oR.success("Usuario reactivado exitosamente"),b(s),o(""),c(""),u("")):(i.oR.error(s.error||"Error reactivando usuario"),b({error:s.error}))}catch(e){console.error("Error:",e),i.oR.error("Error de conexi\xf3n"),b({error:"Error de conexi\xf3n"})}finally{h(!1)}};return(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,a.jsx)("div",{className:"max-w-2xl mx-auto",children:(0,a.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Reactivar Usuario"}),(0,a.jsx)("p",{className:"text-gray-600 mt-2",children:"Herramienta administrativa para reactivar usuarios cuando el webhook de Stripe falla."})]}),(0,a.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-6",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("svg",{className:"h-5 w-5 text-yellow-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})})}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-yellow-800",children:"Uso Administrativo"}),(0,a.jsxs)("div",{className:"mt-2 text-sm text-yellow-700",children:[(0,a.jsx)("p",{children:"Esta herramienta debe usarse solo cuando:"}),(0,a.jsxs)("ul",{className:"list-disc list-inside mt-1",children:[(0,a.jsx)("li",{children:"El webhook de Stripe fall\xf3"}),(0,a.jsx)("li",{children:"El pago est\xe1 confirmado en Stripe"}),(0,a.jsx)("li",{children:"El usuario no recibi\xf3 su email de activaci\xf3n"})]})]})]})]})}),(0,a.jsxs)("form",{onSubmit:j,className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"sessionId",className:"block text-sm font-medium text-gray-700",children:"Stripe Session ID"}),(0,a.jsx)("input",{type:"text",id:"sessionId",value:t,onChange:e=>o(e.target.value),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"cs_test_...",disabled:x}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"ID de la sesi\xf3n de checkout de Stripe (recomendado)"})]}),(0,a.jsx)("div",{className:"text-center text-gray-500 text-sm",children:"- O -"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email del Usuario"}),(0,a.jsx)("input",{type:"email",id:"email",value:n,onChange:e=>c(e.target.value),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"<EMAIL>",disabled:x})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"userId",className:"block text-sm font-medium text-gray-700",children:"User ID de Supabase"}),(0,a.jsx)("input",{type:"text",id:"userId",value:m,onChange:e=>u(e.target.value),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"uuid-del-usuario",disabled:x})]}),(0,a.jsx)("button",{type:"submit",disabled:x,className:"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",children:x?"Reactivando...":"Reactivar Usuario"})]}),v&&(0,a.jsxs)("div",{className:"mt-6",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-3",children:"Resultado"}),v.success?(0,a.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-md p-4",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("svg",{className:"h-5 w-5 text-green-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})})}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-green-800",children:"Usuario reactivado exitosamente"}),(0,a.jsx)("div",{className:"mt-2 text-sm text-green-700",children:(0,a.jsxs)("ul",{className:"list-disc list-inside",children:[(0,a.jsxs)("li",{children:["User ID: ",null==(e=v.data)?void 0:e.userId]}),(null==(s=v.data)?void 0:s.transactionId)&&(0,a.jsxs)("li",{children:["Transaction ID: ",v.data.transactionId]}),(0,a.jsxs)("li",{children:["Email enviado: ",(null==(l=v.data)?void 0:l.emailSent)?"S\xed":"No"]}),(0,a.jsxs)("li",{children:["Perfil actualizado: ",(null==(d=v.data)?void 0:d.profileUpdated)?"S\xed":"No"]})]})})]})]})}):(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-md p-4",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-red-800",children:"Error en la reactivaci\xf3n"}),(0,a.jsx)("div",{className:"mt-2 text-sm text-red-700",children:(0,a.jsx)("p",{children:v.error})})]})]})})]})]})})})}},9027:(e,s,l)=>{Promise.resolve().then(l.bind(l,7257))}},e=>{var s=s=>e(e.s=s);e.O(0,[8260,8441,1684,7358],()=>s(9027)),_N_E=e.O()}]);