"use strict";(()=>{var e={};e.id=5577,e.ids=[5577],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{e.exports=require("punycode")},27910:e=>{e.exports=require("stream")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{e.exports=require("tls")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},62230:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>f,routeModule:()=>l,serverHooks:()=>g,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>m});var s={};r.r(s),r.d(s,{GET:()=>d});var a=r(96559),i=r(48088),o=r(37719),n=r(32190),p=r(34386),u=r(31571);let c=["<EMAIL>"];async function d(e){try{let t=(0,p.createServerClient)("https://fxnhpxjijinfuxxxplzj.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4",{cookies:{getAll:()=>e.cookies.getAll(),setAll(){}}}),{data:{user:r},error:s}=await t.auth.getUser();if(s||!r||!r.email||!c.includes(r.email))return n.NextResponse.json({error:"Acceso denegado. Solo administradores pueden ver estas estad\xedsticas."},{status:403});let{searchParams:a}=new URL(e.url),i=a.get("startDate"),o=a.get("endDate"),d=i||new Date(Date.now()-2592e6).toISOString(),l=o||new Date().toISOString();console.log(`📊 Obteniendo estad\xedsticas de notificaciones desde ${d} hasta ${l}`);let x=await u.X.getNotificationStats(d,l),m=x.byStatus.sent||0,g=x.byStatus.failed||0,f=x.total>0?(m/x.total*100).toFixed(2):"0",y=x.recentNotifications.reduce((e,t)=>{let r=t.sent_at.split("T")[0];return e[r]=(e[r]||0)+1,e},{});return n.NextResponse.json({success:!0,data:{period:{startDate:d,endDate:l},summary:{total:x.total,sent:m,failed:g,successRate:`${f}%`},byType:x.byType,byStatus:x.byStatus,byDay:y,recentNotifications:x.recentNotifications.map(e=>({id:e.id,type:e.type,recipient:e.recipient_email,subject:e.subject,status:e.status,sentAt:e.sent_at,userId:e.user_id,metadata:e.metadata}))},timestamp:new Date().toISOString()})}catch(e){return console.error("❌ Error obteniendo estad\xedsticas de notificaciones:",e),n.NextResponse.json({success:!1,error:"Error interno del servidor",details:e instanceof Error?e.message:"Error desconocido"},{status:500})}}let l=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/notification-stats/route",pathname:"/api/admin/notification-stats",filename:"route",bundlePath:"app/api/admin/notification-stats/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\admin\\notification-stats\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:m,serverHooks:g}=l;function f(){return(0,o.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:m})}},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,6345,4386,8295],()=>r(62230));module.exports=s})();