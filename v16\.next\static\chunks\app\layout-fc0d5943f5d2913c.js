(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7177],{347:()=>{},5790:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,347,23)),Promise.resolve().then(r.bind(r,8803))},8803:(e,t,r)=>{"use strict";r.d(t,{default:()=>w});var a=r(5155),s=r(2115),n=r(4092),l=r(9679),i=r(6317);function c(){return(0,s.useEffect)(()=>{(async()=>{try{let e="https://fxnhpxjijinfuxxxplzj.supabase.co",t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4";if(!e||!t)return void console.warn("Variables de entorno de Supabase no configuradas");let r=await fetch("".concat(e,"/rest/v1/"),{method:"GET",headers:{"Content-Type":"application/json",apikey:t}}),a=new Date(r.headers.get("date")||""),s=new Date,n=Math.abs((a.getTime()-s.getTime())/1e3);n>60&&console.warn("Posible problema de sincronizaci\xf3n de tiempo detectado. "+"La diferencia entre tu hora local y el servidor es de ".concat(Math.round(n)," segundos. ")+"Esto puede causar problemas de autenticaci\xf3n.")}catch(e){console.error("Error al verificar sincronizaci\xf3n de tiempo:",e)}})();let{data:e}=i.N.auth.onAuthStateChange((e,t)=>{});return()=>{e.subscription.unsubscribe()}},[]),null}let o=s.forwardRef(function(e,t){let{title:r,titleId:a,...n}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},n),r?s.createElement("title",{id:a},r):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}),d=s.forwardRef(function(e,t){let{title:r,titleId:a,...n}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},n),r?s.createElement("title",{id:a},r):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.*************.083.22.127.325.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 0 1 1.37.49l1.296 2.247a1.125 1.125 0 0 1-.26 1.431l-1.003.827c-.293.241-.438.613-.43.992a7.723 7.723 0 0 1 0 .255c-.008.378.137.75.43.991l1.004.827c.424.35.534.955.26 1.43l-1.298 2.247a1.125 1.125 0 0 1-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.47 6.47 0 0 1-.22.128c-.331.183-.581.495-.644.869l-.213 1.281c-.09.543-.56.94-1.11.94h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 0 1-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 0 1-1.369-.49l-1.297-2.247a1.125 1.125 0 0 1 .26-1.431l1.004-.827c.292-.24.437-.613.43-.991a6.932 6.932 0 0 1 0-.255c.007-.38-.138-.751-.43-.992l-1.004-.827a1.125 1.125 0 0 1-.26-1.43l1.297-2.247a1.125 1.125 0 0 1 1.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.086.22-.128.332-.183.582-.495.644-.869l.214-1.28Z"}),s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))}),m=s.forwardRef(function(e,t){let{title:r,titleId:a,...n}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},n),r?s.createElement("title",{id:a},r):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}),u=s.forwardRef(function(e,t){let{title:r,titleId:a,...n}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},n),r?s.createElement("title",{id:a},r):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z"}))}),h=s.forwardRef(function(e,t){let{title:r,titleId:a,...n}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},n),r?s.createElement("title",{id:a},r):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m19.5 8.25-7.5 7.5-7.5-7.5"}))}),x=s.forwardRef(function(e,t){let{title:r,titleId:a,...n}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},n),r?s.createElement("title",{id:a},r):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m4.5 15.75 7.5-7.5 7.5 7.5"}))}),p=s.forwardRef(function(e,t){let{title:r,titleId:a,...n}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},n),r?s.createElement("title",{id:a},r):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 18 18 6M6 6l12 12"}))}),f=()=>{let{activeTasks:e,completedTasks:t,removeTask:r,clearCompletedTasks:n}=(0,l.M)(),[i,c]=(0,s.useState)(!1),[f,g]=(0,s.useState)(!1);if(0===e.length+t.length)return null;let w=e=>{switch(e.status){case"pending":return(0,a.jsx)(o,{className:"h-4 w-4 text-yellow-500"});case"processing":return(0,a.jsx)(d,{className:"h-4 w-4 text-blue-500 animate-spin"});case"completed":return(0,a.jsx)(m,{className:"h-4 w-4 text-green-500"});case"error":return(0,a.jsx)(u,{className:"h-4 w-4 text-red-500"});default:return(0,a.jsx)(o,{className:"h-4 w-4 text-gray-500"})}},j=e=>{switch(e){case"mapa-mental":return"Mapa Mental";case"test":return"Test";case"flashcards":return"Flashcards";default:return"Tarea"}},b=e=>e.toLocaleTimeString("es-ES",{hour:"2-digit",minute:"2-digit"});return(0,a.jsx)("div",{className:"fixed bottom-4 right-4 z-50 max-w-sm",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-lg border border-gray-200 overflow-hidden",children:[(0,a.jsxs)("div",{className:"bg-gradient-to-r from-blue-500 to-purple-600 text-white p-3 cursor-pointer flex items-center justify-between",onClick:()=>c(!i),children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(d,{className:"h-5 w-5"}),(0,a.jsxs)("span",{className:"font-medium",children:["Tareas (",e.length," activas)"]})]}),i?(0,a.jsx)(h,{className:"h-5 w-5"}):(0,a.jsx)(x,{className:"h-5 w-5"})]}),i&&(0,a.jsxs)("div",{className:"max-h-96 overflow-y-auto",children:[e.length>0&&(0,a.jsxs)("div",{className:"p-3 border-b border-gray-100",children:[(0,a.jsx)("h4",{className:"text-sm font-semibold text-gray-700 mb-2",children:"Tareas Activas"}),(0,a.jsx)("div",{className:"space-y-2",children:e.map(e=>(0,a.jsxs)("div",{className:"flex items-center space-x-3 p-2 bg-gray-50 rounded-md",children:[w(e),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900 truncate",children:j(e.type)}),(0,a.jsx)("p",{className:"text-xs text-gray-500 truncate",children:e.title}),void 0!==e.progress&&(0,a.jsx)("div",{className:"mt-1",children:(0,a.jsx)("div",{className:"bg-gray-200 rounded-full h-1.5",children:(0,a.jsx)("div",{className:"bg-blue-500 h-1.5 rounded-full transition-all duration-300",style:{width:"".concat(e.progress,"%")}})})})]}),(0,a.jsx)("span",{className:"text-xs text-gray-400",children:b(e.createdAt)})]},e.id))})]}),t.length>0&&(0,a.jsxs)("div",{className:"p-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsxs)("button",{onClick:()=>g(!f),className:"text-sm font-semibold text-gray-700 hover:text-gray-900 flex items-center space-x-1",children:[(0,a.jsxs)("span",{children:["Completadas (",t.length,")"]}),f?(0,a.jsx)(x,{className:"h-4 w-4"}):(0,a.jsx)(h,{className:"h-4 w-4"})]}),t.length>0&&(0,a.jsx)("button",{onClick:n,className:"text-xs text-gray-500 hover:text-red-600 transition-colors",children:"Limpiar"})]}),f&&(0,a.jsx)("div",{className:"space-y-2",children:t.map(e=>(0,a.jsxs)("div",{className:"flex items-center space-x-3 p-2 bg-gray-50 rounded-md",children:[w(e),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900 truncate",children:j(e.type)}),(0,a.jsx)("p",{className:"text-xs text-gray-500 truncate",children:e.title}),e.error&&(0,a.jsx)("p",{className:"text-xs text-red-500 truncate",children:e.error})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-xs text-gray-400",children:e.completedAt?b(e.completedAt):b(e.createdAt)}),(0,a.jsx)("button",{onClick:()=>r(e.id),className:"text-gray-400 hover:text-red-500 transition-colors",children:(0,a.jsx)(p,{className:"h-4 w-4"})})]})]},e.id))})]})]})]})})};var g=r(8260);function w(e){let{children:t}=e;return(0,a.jsx)(l.W,{children:(0,a.jsxs)(n.O,{children:[(0,a.jsx)(c,{}),(0,a.jsx)(g.l$,{position:"top-right",toastOptions:{duration:5e3,style:{background:"#363636",color:"#fff"},success:{duration:3e3,style:{background:"#10b981",color:"#fff"}},error:{duration:5e3,style:{background:"#ef4444",color:"#fff"}}}}),(0,a.jsx)(f,{}),t]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[7690,7361,8260,4092,8441,1684,7358],()=>t(5790)),_N_E=e.O()}]);