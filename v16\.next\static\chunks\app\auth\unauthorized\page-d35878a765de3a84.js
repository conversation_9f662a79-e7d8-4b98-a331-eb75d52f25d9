(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2140],{1310:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>o});var a=s(5155),n=s(2115),l=s(5695),t=s(6317);function i(){let e=(0,l.useRouter)(),r=(0,l.useSearchParams)(),[s,i]=(0,n.useState)(null),[o,u]=(0,n.useState)(!0);if((0,n.useEffect)(()=>{(async()=>{try{var e;let s=(0,t.U)(),a=r.get("reason")||"Acceso denegado",n=r.get("feature")||"",l=(null==(e=r.get("required_plan"))?void 0:e.split(","))||[],{data:{user:o}}=await s.auth.getUser(),c="none";if(o){let{data:e}=await s.from("user_profiles").select("subscription_plan").eq("user_id",o.id).single();c=(null==e?void 0:e.subscription_plan)||"none"}i({reason:a,userPlan:c,requiredPlan:l,feature:n,hasUser:!!o}),u(!1)}catch(e){console.error("Error loading unauthorized info:",e),u(!1)}})()},[r]),o)return(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-red-50 to-orange-100 flex items-center justify-center",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-red-600"})});let c=e=>({free:"Gratis",usuario:"Usuario",pro:"Pro"})[e]||e;return(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-red-50 to-orange-100 flex items-center justify-center p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-xl p-8 max-w-md w-full text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,a.jsx)("svg",{className:"w-8 h-8 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 15v2m0 0v2m0-2h2m-2 0H10m2-5V9m0 0V7m0 2h2m-2 0H10"})})}),(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-800 mb-4",children:"Acceso Restringido"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:(()=>{if(!s)return"";if("none"===s.userPlan)return"Necesitas crear una cuenta para acceder a esta funci\xf3n.";if(s.requiredPlan.length>0){let e=s.requiredPlan.map(c).join(" o ");return"Esta funci\xf3n requiere plan ".concat(e,". Tu plan actual es ").concat(c(s.userPlan),".")}return s.reason})()}),(null==s?void 0:s.feature)&&(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4 mb-6",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-800 mb-2",children:"Funci\xf3n solicitada:"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:s.feature})]}),(null==s?void 0:s.requiredPlan)&&s.requiredPlan.length>0&&(0,a.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4 mb-6",children:[(0,a.jsx)("h3",{className:"font-semibold text-blue-800 mb-2",children:"Planes requeridos:"}),(0,a.jsx)("div",{className:"flex justify-center space-x-2",children:s.requiredPlan.map((e,r)=>(0,a.jsx)("span",{className:"bg-blue-200 text-blue-800 px-3 py-1 rounded-full text-sm font-medium",children:c(e)},r))}),s.hasUser&&(0,a.jsxs)("p",{className:"text-blue-600 text-sm mt-2",children:["Tu plan actual: ",(0,a.jsx)("strong",{children:c(s.userPlan)})]})]}),s?s.hasUser?"free"===s.userPlan&&s.requiredPlan.includes("usuario")?(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("button",{onClick:()=>e.push("/payment?upgrade=usuario"),className:"w-full bg-blue-600 text-white py-3 px-6 rounded-md hover:bg-blue-700 transition-colors font-semibold",children:"Actualizar a Plan Usuario"}),(0,a.jsx)("button",{onClick:()=>e.push("/app"),className:"w-full bg-gray-200 text-gray-800 py-3 px-6 rounded-md hover:bg-gray-300 transition-colors",children:"Volver a la App"})]}):("free"===s.userPlan||"usuario"===s.userPlan)&&s.requiredPlan.includes("pro")?(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("button",{onClick:()=>e.push("/payment?upgrade=pro"),className:"w-full bg-purple-600 text-white py-3 px-6 rounded-md hover:bg-purple-700 transition-colors font-semibold",children:"Actualizar a Plan Pro"}),(0,a.jsx)("button",{onClick:()=>e.push("/app"),className:"w-full bg-gray-200 text-gray-800 py-3 px-6 rounded-md hover:bg-gray-300 transition-colors",children:"Volver a la App"})]}):(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("button",{onClick:()=>e.push("/upgrade-plan"),className:"w-full bg-blue-600 text-white py-3 px-6 rounded-md hover:bg-blue-700 transition-colors font-semibold",children:"Ver Planes"}),(0,a.jsx)("button",{onClick:()=>e.push("/app"),className:"w-full bg-gray-200 text-gray-800 py-3 px-6 rounded-md hover:bg-gray-300 transition-colors",children:"Volver a la App"})]}):(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("button",{onClick:()=>e.push("/login"),className:"w-full bg-blue-600 text-white py-3 px-6 rounded-md hover:bg-blue-700 transition-colors font-semibold",children:"Iniciar Sesi\xf3n"}),(0,a.jsx)("button",{onClick:()=>e.push("/upgrade-plan"),className:"w-full bg-green-600 text-white py-3 px-6 rounded-md hover:bg-green-700 transition-colors",children:"Ver Planes"})]}):null,(0,a.jsx)("div",{className:"mt-6 pt-4 border-t border-gray-200",children:(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["\xbfPreguntas sobre los planes? ",(0,a.jsx)("a",{href:"/contact",className:"text-blue-600 hover:underline",children:"Contacta soporte"})]})})]})})}function o(){return(0,a.jsx)(n.Suspense,{fallback:(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-red-50 to-orange-100 flex items-center justify-center",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-red-600"})}),children:(0,a.jsx)(i,{})})}},2643:(e,r,s)=>{"use strict";s.d(r,{N:()=>l,U:()=>n});var a=s(9535);function n(){return(0,a.createBrowserClient)("https://fxnhpxjijinfuxxxplzj.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4",{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0}})}let l=n()},6317:(e,r,s)=>{"use strict";s.d(r,{N:()=>a.N,U:()=>a.U});var a=s(2643)},7039:(e,r,s)=>{Promise.resolve().then(s.bind(s,1310))}},e=>{var r=r=>e(e.s=r);e.O(0,[7361,8441,1684,7358],()=>r(7039)),_N_E=e.O()}]);