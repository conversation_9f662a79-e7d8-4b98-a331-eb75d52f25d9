{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|manifest.json|robots.txt|.*\\..*).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|manifest.json|robots.txt|.*\\..*).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "g-PUag7O1sssXAMesueqc", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "1GAq5X8154gkB+1VHf9VIgdB5+2Q24VjYDHsRlt4wdI=", "__NEXT_PREVIEW_MODE_ID": "257327864b4d77428f30805a719a366d", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "5285e9d489d605486420ed5afc9392b1b70075cbfd8312a6c4a3785037675e2d", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "8d01b2bb28fd4e5f9dd6f5a8de2602adb3e2af439c00ed64a7ed4be7b0ec1da6"}}}, "functions": {}, "sortedMiddleware": ["/"]}