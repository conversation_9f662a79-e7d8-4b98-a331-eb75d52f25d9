{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|manifest.json|robots.txt|.*\\..*).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|manifest.json|robots.txt|.*\\..*).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "8OJqa4Fqs90fjIoGkGPpz", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "XU7vuQXYRlqQ/jCK2YZF9drXy1TEs+bjUjTRxJ3KYD0=", "__NEXT_PREVIEW_MODE_ID": "46a100fd7d53536460287beb5c964bbb", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "63e1ef4d860bd5f44bbde8c300f50d1ef4b50b5ffeb5ee2c013517709ba39a50", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "c4edde04f0bd0e1158ce0d3fbdc6646ec4d0fe8453b1e5e2ce73ddc232928abe"}}}, "functions": {}, "sortedMiddleware": ["/"]}