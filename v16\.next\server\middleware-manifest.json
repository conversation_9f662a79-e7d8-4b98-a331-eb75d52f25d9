{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|manifest.json|robots.txt|.*\\..*).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|manifest.json|robots.txt|.*\\..*).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "77imGhcoH3MMdnaYEcHMv", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "xZvqtrO0jZHOksJgqjMQPo0L0fGsEIv5YlavDyeb5N4=", "__NEXT_PREVIEW_MODE_ID": "aa4382e286e08ca2b16ce0acc8965b9f", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7baf77824e27625a024e96372bba74548e6595ba3714ce1914f238081fd692c1", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "04456fcfec2a2b922af402d0210fa5256afd8d4c637ae575cb0eda7c0b4c4365"}}}, "functions": {}, "sortedMiddleware": ["/"]}