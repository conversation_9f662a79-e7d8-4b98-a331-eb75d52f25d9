{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|manifest.json|robots.txt|.*\\..*).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|manifest.json|robots.txt|.*\\..*).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "gDIrQjeg1HEJfi5nEVApb", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "alm8+ewY0XIIEmX+7h5c1Isg3jXTwYsCwUwGqA/jq6k=", "__NEXT_PREVIEW_MODE_ID": "38545a4fb70a1dacca5afd7adaee6289", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "90ecf8fb6098cb115da45aad057b3d95d74d95e7393251c856308093c44c44a9", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "bc158bfe44124844dcca6b6efc87c109c8f900dd29120c906736043548fbb0f5"}}}, "functions": {}, "sortedMiddleware": ["/"]}