"use strict";exports.id=6033,exports.ids=[6033],exports.modules={36033:(e,t,r)=>{r.r(t),r.d(t,{canPerformActivity:()=>m,checkTokenLimit:()=>u,getTokenPurchaseHistory:()=>p,getTokenUsageProgress:()=>g,getUserPlanInfo:()=>_,getUserProfile:()=>c,getUserTokenStats:()=>s,saveTokenUsage:()=>a});var o=r(63511),n=r(8814);async function a(e){try{console.log("\uD83D\uDD04 saveTokenUsage (cliente) iniciado con data:",e);let t=(0,o.U)();console.log("✅ Cliente Supabase creado");let{data:{user:r},error:n}=await t.auth.getUser();if(console.log("\uD83D\uDC64 Usuario obtenido:",r?`ID: ${r.id}, Email: ${r.email}`:"No autenticado"),n||!r)return void console.warn("❌ No hay usuario autenticado para guardar tokens:",n?.message);let a=await d(r.id,e.activity,e.usage.totalTokens);if(!a.allowed)throw console.warn("❌ Acceso denegado para actividad:",a.reason),Error(a.reason);let s={user_id:r.id,activity_type:e.activity,model_name:e.model,prompt_tokens:e.usage.promptTokens,completion_tokens:e.usage.completionTokens,total_tokens:e.usage.totalTokens,estimated_cost:e.usage.estimatedCost||0,usage_month:new Date().toISOString().slice(0,7)+"-01"};console.log("\uD83D\uDCDD Registro a insertar:",s);let{error:l}=await t.from("user_token_usage").insert([s]);if(l)return void console.error("❌ Error al guardar uso de tokens:",l);console.log("✅ Registro insertado exitosamente en user_token_usage"),await i(r.id,e.usage.totalTokens)}catch(e){console.error("Error en saveTokenUsage:",e)}}async function i(e,t){try{let r=(0,o.U)(),n=new Date().toISOString().slice(0,7)+"-01",{data:a,error:i}=await r.from("user_profiles").select("*").eq("user_id",e).single();if(i&&"PGRST116"!==i.code)return void console.error("Error al obtener perfil:",i);if(a){let o=a.current_month===n?a.current_month_tokens+t:t,{error:i}=await r.from("user_profiles").update({current_month_tokens:o,current_month:n,updated_at:new Date().toISOString()}).eq("user_id",e);i&&console.error("Error al actualizar perfil:",i)}else{let{error:o}=await r.from("user_profiles").insert([{user_id:e,subscription_plan:"free",monthly_token_limit:5e4,current_month_tokens:t,current_month:n}]);o&&console.error("Error al crear perfil:",o)}}catch(e){console.error("Error en updateMonthlyTokenCount:",e)}}async function s(){try{let t=(0,o.U)(),{data:{user:r},error:n}=await t.auth.getUser();if(n||!r)return l();let{data:a,error:i}=await t.from("user_token_usage").select("*").eq("user_id",r.id).order("created_at",{ascending:!1});if(i)return console.error("Error al obtener estad\xedsticas:",i),l();var e=a||[];let s={totalSessions:e.length,totalTokens:0,totalCost:0,byActivity:{},byModel:{}};return e.forEach(e=>{let t=e.total_tokens,r=e.estimated_cost;s.totalTokens+=t,s.totalCost+=r,s.byActivity[e.activity_type]||(s.byActivity[e.activity_type]={tokens:0,cost:0,count:0}),s.byActivity[e.activity_type].tokens+=t,s.byActivity[e.activity_type].cost+=r,s.byActivity[e.activity_type].count+=1,s.byModel[e.model_name]||(s.byModel[e.model_name]={tokens:0,cost:0,count:0}),s.byModel[e.model_name].tokens+=t,s.byModel[e.model_name].cost+=r,s.byModel[e.model_name].count+=1}),s}catch(e){return console.error("Error en getUserTokenStats:",e),l()}}function l(){return{totalSessions:0,totalTokens:0,totalCost:0,byActivity:{},byModel:{}}}async function c(){try{let e=(0,o.U)(),{data:{user:t},error:r}=await e.auth.getUser();if(r||!t)return null;let{data:n,error:a}=await e.from("user_profiles").select("*").eq("user_id",t.id).single();if(a&&"PGRST116"!==a.code)return console.error("Error al obtener perfil:",a),null;return n}catch(e){return console.error("Error en getUserProfile:",e),null}}async function u(){try{let e=await c();if(!e)return{hasReachedLimit:!1,currentTokens:0,limit:5e4,percentage:0};let t=e.current_month_tokens||0,r=e.monthly_token_limit||0;return{hasReachedLimit:t>=r,currentTokens:t,limit:r,percentage:r>0?t/r*100:0}}catch(e){return console.error("Error en checkTokenLimit:",e),{hasReachedLimit:!1,currentTokens:0,limit:5e4,percentage:0}}}async function d(e,t,r){try{let a=(0,o.U)(),{data:i,error:s}=await a.from("user_profiles").select("subscription_plan, payment_verified, current_month_tokens, monthly_token_limit, current_month").eq("user_id",e).single();if(s||!i)return{allowed:!1,reason:"Perfil de usuario no encontrado"};if(!(0,n.Nu)(i.subscription_plan,{test_generation:"test_generation",flashcard_generation:"flashcard_generation",mind_map_generation:"mind_map_generation",ai_chat:"ai_tutor_chat",study_planning:"study_planning",summary_generation:"summary_a1_a2",document_analysis:"document_upload"}[t]||t))return{allowed:!1,reason:`La actividad ${t} no est\xe1 disponible en el plan ${i.subscription_plan}`};if("free"!==i.subscription_plan&&!i.payment_verified)return{allowed:!1,reason:"Pago no verificado. Complete el proceso de pago para usar esta funci\xf3n."};let l=new Date().toISOString().slice(0,7)+"-01",c=i.current_month_tokens;if(i.current_month!==l&&(c=0),c+r>i.monthly_token_limit)return{allowed:!1,reason:`L\xedmite mensual de tokens alcanzado. Usado: ${c}/${i.monthly_token_limit}`};return{allowed:!0}}catch(e){return console.error("Error validating activity access:",e),{allowed:!1,reason:"Error interno de validaci\xf3n"}}}async function _(){try{let e=await c();if(!e)return null;let t=(0,n.IE)(e.subscription_plan);if(!t)return null;let r=e.current_month_tokens||0,o=e.monthly_token_limit||0;return{plan:e.subscription_plan,planName:t.name,features:t.features,tokenUsage:{current:r,limit:o,percentage:Math.round(o>0?r/o*100:0),remaining:Math.max(0,o-r)},paymentVerified:e.payment_verified||"free"===e.subscription_plan}}catch(e){return console.error("Error getting user plan info:",e),null}}async function m(e,t=0){try{let r=(0,o.U)(),{data:{user:n},error:a}=await r.auth.getUser();if(a||!n)return{allowed:!1,reason:"Usuario no autenticado"};let i=await d(n.id,e,t);if(!i.allowed){let e=await _();return{allowed:!1,reason:i.reason,planInfo:e}}return{allowed:!0}}catch(e){return console.error("Error checking activity permission:",e),{allowed:!1,reason:"Error interno de validaci\xf3n"}}}async function g(){try{let e=(0,o.U)(),{data:{user:t},error:r}=await e.auth.getUser();if(r||!t)return null;let n=await c();if(!n)return null;let a=n.current_month_tokens/n.monthly_token_limit*100,i=new Date;i.setDate(i.getDate()-30);let{data:s,error:l}=await e.from("user_token_usage").select("created_at, total_tokens").eq("user_id",t.id).gte("created_at",i.toISOString()).order("created_at",{ascending:!0});l&&console.error("Error al obtener historial diario:",l);let u=[],d=new Map;if(s){s.forEach(e=>{let t=new Date(e.created_at).toISOString().split("T")[0],r=d.get(t)||0;d.set(t,r+e.total_tokens)});for(let e=29;e>=0;e--){let t=new Date;t.setDate(t.getDate()-e);let r=t.toISOString().split("T")[0];u.push({date:r,tokens:d.get(r)||0})}}return{percentage:Math.round(a),limit:n.monthly_token_limit,used:n.current_month_tokens,remaining:n.monthly_token_limit-n.current_month_tokens,dailyHistory:u}}catch(e){return console.error("Error en getTokenUsageProgress:",e),null}}async function p(){try{let e=(0,o.U)(),{data:{user:t},error:r}=await e.auth.getUser();if(r||!t)return null;let{data:n,error:a}=await e.from("token_purchases").select("id, amount, price, created_at, status").eq("user_id",t.id).order("created_at",{ascending:!1});if(a)return console.error("Error al obtener historial de compras:",a),null;return n||[]}catch(e){return console.error("Error en getTokenPurchaseHistory:",e),null}}},63511:(e,t,r)=>{r.d(t,{U:()=>n});var o=r(34386);function n(){return(0,o.createBrowserClient)("https://fxnhpxjijinfuxxxplzj.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4",{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0}})}n()}};