(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9755],{1544:(e,r,t)=>{"use strict";t.d(r,{IE:()=>n,qk:()=>i,qo:()=>a});let a={free:{id:"free",name:"Plan Gratis",price:0,limits:{documents:1,mindMapsForTrial:2,testsForTrial:10,flashcardsForTrial:10,tokensForTrial:5e4,features:["document_upload","test_generation","flashcard_generation","mind_map_generation"]},features:["document_upload","test_generation","flashcard_generation","mind_map_generation"],restrictedFeatures:["study_planning","ai_tutor_chat","summary_a1_a2"]},usuario:{id:"usuario",name:"Plan Usuario",price:1e3,limits:{documents:-1,mindMapsPerWeek:-1,testsPerWeek:-1,flashcardsPerWeek:-1,monthlyTokens:1e6,features:["document_upload","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation"]},features:["document_upload","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation"],restrictedFeatures:["study_planning","summary_a1_a2"]},pro:{id:"pro",name:"Plan Pro",price:1500,limits:{documents:-1,mindMapsPerWeek:-1,testsPerWeek:-1,flashcardsPerWeek:-1,monthlyTokens:1e6,features:["document_upload","study_planning","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation","summary_a1_a2"]},features:["document_upload","study_planning","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation","summary_a1_a2"],restrictedFeatures:[]}};function n(e){return a[e]||null}function s(e,r){let t=n(e);return!(!t||t.restrictedFeatures.includes(r))&&t.features.includes(r)}async function i(e){try{let r=await fetch("/api/user/plan");if(!r.ok)return console.error("Error obteniendo plan del usuario"),s("free",e);let{plan:t}=await r.json();return s(t||"free",e)}catch(r){return console.error("Error verificando acceso a caracter\xedstica:",r),s("free",e)}}},1547:(e,r,t)=>{"use strict";t.d(r,{Md:()=>s,NB:()=>n});var a=t(1544);let n={free:{id:"free",name:"Plan Gratis",price:0,stripeProductId:null,stripePriceId:null,features:["Incluye:","• Uso de la plataforma solo durante 5 d\xedas","• Subida de documentos: m\xe1ximo 1 documento","• Generador de test: m\xe1ximo 10 preguntas test","• Generador de flashcards: m\xe1ximo 10 tarjetas flashcard","• Generador de mapas mentales: m\xe1ximo 2 mapas mentales","No incluye:","• Planificaci\xf3n de estudios","• Habla con tu preparador IA","• Res\xfamenes A2 y A1"],limits:a.qo.free.limits,planConfig:a.qo.free},usuario:{id:"usuario",name:"Plan Usuario",price:1e3,stripeProductId:"prod_SR65BdKdek1OXd",stripePriceId:"price_1Rae5807kFn3sIXhRf3adX1n",features:["Incluye:","• Uso de la plataforma durante el mes (una vez finalizado no podr\xe1 volver a acceder hasta renovaci\xf3n)","• Subida de documentos","• Habla con tu preparador IA *","• Generador de test *","• Generador de flashcards *","• Generador de mapas mentales *","• * Para las tareas en las que se haga uso de IA, el l\xedmite mensual ser\xe1 de 1.000.000 de tokens, podr\xe1n ampliarse durante el mes mediante pago","No incluye:","• Planificaci\xf3n de estudios","• Res\xfamenes A2 y A1"],limits:a.qo.usuario.limits,planConfig:a.qo.usuario},pro:{id:"pro",name:"Plan Pro",price:1500,stripeProductId:"prod_SR66U2G7bVJqu3",stripePriceId:"price_1Rae3U07kFn3sIXhkvSuJco1",features:["Incluye:","• Uso de la plataforma durante el mes (una vez finalizado no podr\xe1 volver a acceder hasta renovaci\xf3n)","• Subida de documentos","• Planificaci\xf3n de estudios mediante IA*","• Habla con tu preparador IA *","• Generador de test *","• Generador de flashcards *","• Generador de mapas mentales *","• Generaci\xf3n de Res\xfamenes para A2 y A1","• * Para las tareas en las que se haga uso de IA, el l\xedmite mensual ser\xe1 de 1.000.000 de tokens, podr\xe1n ampliarse durante el mes mediante pago"],limits:a.qo.pro.limits,planConfig:a.qo.pro}};function s(e){return n[e]||null}},3337:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>u});var a=t(5155),n=t(2115),s=t(5695),i=t(1547),o=t(6874),l=t.n(o),c=t(351);function d(){let e=(0,s.useSearchParams)();(0,s.useRouter)();let r=e.get("plan")||"free",t=e.get("session_id"),n="true"===e.get("email_sent");e.get("payment");let o=(0,i.Md)(r);return n&&"free"===r?(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:(0,a.jsx)("div",{className:"sm:mx-auto sm:w-full sm:max-w-xl",children:(0,a.jsx)("div",{className:"bg-white py-8 px-6 shadow-xl sm:rounded-lg sm:px-10",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(c.pHD,{className:"mx-auto h-12 w-12 text-blue-600 mb-4"}),(0,a.jsx)("h2",{className:"text-2xl md:text-3xl font-bold text-gray-800 mb-3",children:"\xa1Registro Exitoso!"}),(0,a.jsxs)("p",{className:"text-md text-gray-600 mb-6",children:["Tu cuenta gratuita de ",(0,a.jsx)("strong",{children:(null==o?void 0:o.name)||"OposicionesIA"})," ha sido creada exitosamente."]}),(0,a.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6",children:[(0,a.jsx)("h3",{className:"font-semibold text-blue-800 mb-2",children:"\uD83D\uDCE7 Confirma tu Email"}),(0,a.jsx)("p",{className:"text-blue-700 text-sm mb-3",children:"Hemos enviado un email de confirmaci\xf3n a tu direcci\xf3n de correo."}),(0,a.jsxs)("p",{className:"text-blue-700 text-sm",children:[(0,a.jsx)("strong",{children:"Haz clic en el enlace del email"})," para activar tu cuenta y poder iniciar sesi\xf3n."]})]}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-6",children:"Si no recibes el email en unos minutos, revisa tu carpeta de spam."}),(0,a.jsx)(l(),{href:"/auth/login",className:"inline-flex justify-center py-3 px-6 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-blue-600 hover:bg-blue-700 transition-colors",children:"Ir a Iniciar Sesi\xf3n"})]})})})}):t&&"undefined"!==t?(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:(0,a.jsx)("div",{className:"sm:mx-auto sm:w-full sm:max-w-xl",children:(0,a.jsx)("div",{className:"bg-white py-10 px-6 shadow-xl sm:rounded-lg sm:px-10",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(c.A3x,{className:"mx-auto h-16 w-16 text-green-500 mb-5"}),(0,a.jsx)("h2",{className:"text-2xl md:text-3xl font-bold text-gray-800 mb-4",children:"\xa1Pago Confirmado!"}),(0,a.jsxs)("p",{className:"text-md text-gray-700 mb-6",children:["Tu pago para el plan ",(0,a.jsx)("strong",{children:(null==o?void 0:o.name)||"seleccionado"})," ha sido procesado exitosamente."]}),(0,a.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4 mb-6",children:(0,a.jsxs)("p",{className:"text-green-800 text-sm",children:[(0,a.jsx)("strong",{children:"✅ Tu cuenta se est\xe1 activando"}),(0,a.jsx)("br",{}),"En unos momentos, tu cuenta estar\xe1 completamente activada. Mientras tanto, puedes intentar iniciar sesi\xf3n."]})}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-8",children:"Si tienes alg\xfan problema para iniciar sesi\xf3n, espera unos minutos y vuelve a intentarlo."}),(0,a.jsx)(l(),{href:"/auth/login",className:"inline-flex justify-center py-3 px-8 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-blue-600 hover:bg-blue-700 transition-colors",children:"Ir a Iniciar Sesi\xf3n"})]})})})}):(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center items-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(c.eHT,{className:"mx-auto h-12 w-12 text-orange-500 mb-4"}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-4",children:"P\xe1gina de Agradecimiento"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"No se encontraron par\xe1metros v\xe1lidos para mostrar el contenido apropiado."}),(0,a.jsx)(l(),{href:"/",className:"inline-flex justify-center py-3 px-6 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-blue-600 hover:bg-blue-700 transition-colors",children:"Volver al Inicio"})]})})}function u(){return(0,a.jsx)(n.Suspense,{fallback:(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center items-center",children:[(0,a.jsx)(c.TwU,{className:"animate-spin h-12 w-12 text-blue-600"}),(0,a.jsx)("p",{className:"mt-4 text-gray-600",children:"Cargando..."})]}),children:(0,a.jsx)(d,{})})}},4315:(e,r,t)=>{Promise.resolve().then(t.bind(t,3337))},4436:(e,r,t)=>{"use strict";t.d(r,{k5:()=>d});var a=t(2115),n={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},s=a.createContext&&a.createContext(n),i=["attr","size","title"];function o(){return(o=Object.assign?Object.assign.bind():function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a])}return e}).apply(this,arguments)}function l(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);r&&(a=a.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,a)}return t}function c(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?l(Object(t),!0).forEach(function(r){var a,n,s;a=e,n=r,s=t[r],(n=function(e){var r=function(e,r){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var a=t.call(e,r||"default");if("object"!=typeof a)return a;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"==typeof r?r:r+""}(n))in a?Object.defineProperty(a,n,{value:s,enumerable:!0,configurable:!0,writable:!0}):a[n]=s}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):l(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function d(e){return r=>a.createElement(u,o({attr:c({},e.attr)},r),function e(r){return r&&r.map((r,t)=>a.createElement(r.tag,c({key:t},r.attr),e(r.child)))}(e.child))}function u(e){var r=r=>{var t,{attr:n,size:s,title:l}=e,d=function(e,r){if(null==e)return{};var t,a,n=function(e,r){if(null==e)return{};var t={};for(var a in e)if(Object.prototype.hasOwnProperty.call(e,a)){if(r.indexOf(a)>=0)continue;t[a]=e[a]}return t}(e,r);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(a=0;a<s.length;a++)t=s[a],!(r.indexOf(t)>=0)&&Object.prototype.propertyIsEnumerable.call(e,t)&&(n[t]=e[t])}return n}(e,i),u=s||r.size||"1em";return r.className&&(t=r.className),e.className&&(t=(t?t+" ":"")+e.className),a.createElement("svg",o({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},r.attr,n,d,{className:t,style:c(c({color:e.color||r.color},r.style),e.style),height:u,width:u,xmlns:"http://www.w3.org/2000/svg"}),l&&a.createElement("title",null,l),e.children)};return void 0!==s?a.createElement(s.Consumer,null,e=>r(e)):r(n)}},5695:(e,r,t)=>{"use strict";var a=t(8999);t.o(a,"usePathname")&&t.d(r,{usePathname:function(){return a.usePathname}}),t.o(a,"useRouter")&&t.d(r,{useRouter:function(){return a.useRouter}}),t.o(a,"useSearchParams")&&t.d(r,{useSearchParams:function(){return a.useSearchParams}})}},e=>{var r=r=>e(e.s=r);e.O(0,[844,6874,8441,1684,7358],()=>r(4315)),_N_E=e.O()}]);