(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5223],{380:(e,r,t)=>{Promise.resolve().then(t.bind(t,3197))},2643:(e,r,t)=>{"use strict";t.d(r,{N:()=>a,U:()=>o});var n=t(9535);function o(){return(0,n.createBrowserClient)("https://fxnhpxjijinfuxxxplzj.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4",{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0}})}let a=o()},3197:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>c});var n=t(5155),o=t(2115),a=t(5695),i=t(2643),s=t(351);function c(){let e=(0,a.useRouter)(),r=(0,i.U)(),[t,c]=(0,o.useState)("loading"),[l,u]=(0,o.useState)("Verificando tu confirmaci\xf3n de email...");return(0,o.useEffect)(()=>{(async()=>{try{let{data:{subscription:t}}=r.auth.onAuthStateChange(async(t,n)=>{if(console.log("Auth event:",t,"Session:",n),"SIGNED_IN"===t&&n){u("Email confirmado exitosamente. Verificando tu perfil...");try{let{data:t,error:o}=await r.from("user_profiles").select("*").eq("user_id",n.user.id).single();if(o){console.error("Error obteniendo perfil:",o),c("error"),u("Error al verificar tu perfil. Por favor, contacta con soporte.");return}if(!t){c("error"),u("No se encontr\xf3 tu perfil. Por favor, contacta con soporte.");return}!1===t.payment_verified&&"free"!==t.subscription_plan?(u("Redirigiendo a completar tu pago..."),c("redirecting"),setTimeout(()=>{e.push("/payment?plan="+t.subscription_plan)},2e3)):!0===t.payment_verified||"free"===t.subscription_plan?(u("\xa1Cuenta activada! Redirigiendo a tu dashboard..."),c("success"),setTimeout(()=>{e.push("/app")},2e3)):(c("error"),u("Estado de cuenta inesperado. Por favor, contacta con soporte."))}catch(e){console.error("Error procesando confirmaci\xf3n:",e),c("error"),u("Error al procesar la confirmaci\xf3n. Por favor, intenta de nuevo.")}}else"SIGNED_OUT"===t&&(c("error"),u("Error en la confirmaci\xf3n. Por favor, intenta de nuevo."))});return()=>{t.unsubscribe()}}catch(e){console.error("Error en handleEmailConfirmation:",e),c("error"),u("Error al procesar la confirmaci\xf3n. Por favor, intenta de nuevo.")}})()},[r,e]),(0,n.jsx)("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center items-center p-4",children:(0,n.jsxs)("div",{className:"bg-white shadow-lg rounded-lg p-8 max-w-md w-full text-center",children:[(()=>{switch(t){case"loading":default:return(0,n.jsx)(s.TwU,{className:"w-12 h-12 text-blue-600 mx-auto mb-4 animate-spin"});case"success":case"redirecting":return(0,n.jsx)(s.A3x,{className:"w-12 h-12 text-green-600 mx-auto mb-4"});case"error":return(0,n.jsx)(s.eHT,{className:"w-12 h-12 text-red-600 mx-auto mb-4"})}})(),(0,n.jsx)("h1",{className:"text-2xl font-semibold text-gray-800 mb-4",children:(()=>{switch(t){case"loading":return"Confirmando tu email...";case"success":return"\xa1Email confirmado!";case"redirecting":return"\xa1Confirmaci\xf3n exitosa!";case"error":return"Error en la confirmaci\xf3n";default:return"Procesando..."}})()}),(0,n.jsx)("p",{className:"text-gray-600 mb-6",children:l}),"error"===t&&(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsx)("button",{onClick:()=>e.push("/auth/login"),className:"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors",children:"Ir a Iniciar Sesi\xf3n"}),(0,n.jsx)("button",{onClick:()=>e.push("/"),className:"w-full bg-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-400 transition-colors",children:"Volver al Inicio"})]})]})})}},4436:(e,r,t)=>{"use strict";t.d(r,{k5:()=>u});var n=t(2115),o={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},a=n.createContext&&n.createContext(o),i=["attr","size","title"];function s(){return(s=Object.assign?Object.assign.bind():function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}return e}).apply(this,arguments)}function c(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,n)}return t}function l(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?c(Object(t),!0).forEach(function(r){var n,o,a;n=e,o=r,a=t[r],(o=function(e){var r=function(e,r){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,r||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"==typeof r?r:r+""}(o))in n?Object.defineProperty(n,o,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[o]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):c(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function u(e){return r=>n.createElement(f,s({attr:l({},e.attr)},r),function e(r){return r&&r.map((r,t)=>n.createElement(r.tag,l({key:t},r.attr),e(r.child)))}(e.child))}function f(e){var r=r=>{var t,{attr:o,size:a,title:c}=e,u=function(e,r){if(null==e)return{};var t,n,o=function(e,r){if(null==e)return{};var t={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(r.indexOf(n)>=0)continue;t[n]=e[n]}return t}(e,r);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)t=a[n],!(r.indexOf(t)>=0)&&Object.prototype.propertyIsEnumerable.call(e,t)&&(o[t]=e[t])}return o}(e,i),f=a||r.size||"1em";return r.className&&(t=r.className),e.className&&(t=(t?t+" ":"")+e.className),n.createElement("svg",s({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},r.attr,o,u,{className:t,style:l(l({color:e.color||r.color},r.style),e.style),height:f,width:f,xmlns:"http://www.w3.org/2000/svg"}),c&&n.createElement("title",null,c),e.children)};return void 0!==a?n.createElement(a.Consumer,null,e=>r(e)):r(o)}}},e=>{var r=r=>e(e.s=r);e.O(0,[844,7361,8441,1684,7358],()=>r(380)),_N_E=e.O()}]);