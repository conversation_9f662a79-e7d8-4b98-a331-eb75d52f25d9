"use strict";(()=>{var e={};e.id=397,e.ids=[397],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{e.exports=require("punycode")},27910:e=>{e.exports=require("stream")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32571:(e,r,t)=>{t.d(r,{o:()=>i});var a=t(42049),n=t(8814),s=t(89546);class o{static async logWebhookEvent(e){try{let r=e.success?"✅":"❌",t=new Date().toISOString();console.log(`${r} [WEBHOOK] ${t}`,{eventType:e.eventType,eventId:e.eventId,success:e.success,processingTime:`${e.processingTime}ms`,message:e.message,...e.error&&{error:e.error},...e.data&&{data:e.data}}),await this.logToExternalService(e)}catch(e){console.error("Error logging webhook event:",e)}}static async logFeatureAccess(e,r,a,n,s=0,o){try{if(process.env.SUPABASE_SERVICE_ROLE_KEY){let{SupabaseAdminService:i}=await Promise.resolve().then(t.bind(t,42049));await i.logFeatureAccess({user_id:e,feature_name:r,access_granted:a,plan_at_time:n,tokens_used:s,denial_reason:o})}let i=a?"✅":"❌";console.log(`${i} [FEATURE_ACCESS]`,{userId:e,feature:r,granted:a,plan:n,tokens:s,...o&&{reason:o}})}catch(e){console.error("Error logging feature access:",e)}}static async logPlanChange(e,r,a,n,s,o){try{if(process.env.SUPABASE_SERVICE_ROLE_KEY){let{SupabaseAdminService:i}=await Promise.resolve().then(t.bind(t,42049));await i.logPlanChange({user_id:e,old_plan:r||void 0,new_plan:a,changed_by:n,reason:s,transaction_id:o})}console.log("\uD83D\uDD04 [PLAN_CHANGE]",{userId:e,oldPlan:r,newPlan:a,changedBy:n,reason:s,transactionId:o})}catch(e){console.error("Error logging plan change:",e)}}static async logCriticalError(e,r,t){try{let a={context:e,message:r.message,stack:r.stack,timestamp:new Date().toISOString(),additionalData:t};console.error("\uD83D\uDEA8 [CRITICAL_ERROR]",a),await this.sendCriticalAlert(a)}catch(e){console.error("Error logging critical error:",e)}}static logPerformanceMetrics(e,r,t,a){let n={operation:e,duration:`${r}ms`,success:t,timestamp:new Date().toISOString(),...a};console.log("\uD83D\uDCCA [PERFORMANCE]",n),this.sendMetrics(n)}static async logToExternalService(e){}static async sendCriticalAlert(e){}static sendMetrics(e){"true"===process.env.ENABLE_METRICS_LOGGING&&console.log("\uD83D\uDCC8 [METRICS_EXPORT]",e)}static async getWebhookStats(e="day"){return{totalEvents:0,successRate:0,averageProcessingTime:0,errorsByType:{}}}}class i{static async validateFeatureAccess(e,r,t=0){try{let s=await a.SupabaseAdminService.getUserProfile(e);if(!s)return await o.logFeatureAccess(e,r,!1,"unknown",0,"User profile not found"),{allowed:!1,reason:"Perfil de usuario no encontrado"};if("free"!==s.subscription_plan&&!s.payment_verified)return await o.logFeatureAccess(e,r,!1,s.subscription_plan,0,"Payment not verified"),{allowed:!1,reason:"Pago no verificado. Complete el proceso de pago para acceder a esta caracter\xedstica."};if(!(0,n.Nu)(s.subscription_plan,r))return await o.logFeatureAccess(e,r,!1,s.subscription_plan,0,`Feature not available in ${s.subscription_plan} plan`),{allowed:!1,reason:`La caracter\xedstica ${r} no est\xe1 disponible en su plan ${s.subscription_plan}`};if(t>0){let a=await this.validateTokenUsage(s,t);if(!a.allowed)return await o.logFeatureAccess(e,r,!1,s.subscription_plan,t,a.reason),a}return await o.logFeatureAccess(e,r,!0,s.subscription_plan,t),{allowed:!0,remainingUsage:s.monthly_token_limit-s.current_month_tokens,planLimits:{monthlyTokens:s.monthly_token_limit,currentTokens:s.current_month_tokens}}}catch(a){return console.error("Error validating feature access:",a),await o.logFeatureAccess(e,r,!1,"error",t,"Internal validation error"),{allowed:!1,reason:"Error interno de validaci\xf3n"}}}static async validateTokenUsage(e,r){let t=new Date().toISOString().slice(0,7)+"-01";e.current_month!==t&&(await a.SupabaseAdminService.upsertUserProfile({...e,current_month_tokens:0,current_month:t,updated_at:new Date().toISOString()}),e.current_month_tokens=0,e.current_month=t);let n=e.current_month_tokens+r;return n>e.monthly_token_limit?{allowed:!1,reason:`L\xedmite mensual de tokens alcanzado. Usado: ${e.current_month_tokens}/${e.monthly_token_limit}`,remainingUsage:Math.max(0,e.monthly_token_limit-e.current_month_tokens)}:{allowed:!0,remainingUsage:e.monthly_token_limit-n}}static async getUserAccessInfo(e){try{let r=await a.SupabaseAdminService.getUserProfile(e);if(!r)return null;let s=(0,n.IE)(r.subscription_plan);if(!s)return null;let o={tokens:r.current_month_tokens,tokenLimit:r.monthly_token_limit,month:r.current_month,documents:0,tests:0,flashcards:0,mindMaps:0};try{let r=await a.SupabaseAdminService.getDocumentsCount(e);o.documents=r}catch(e){console.error("Error getting documents count:",e)}if("free"===r.subscription_plan)try{let{FreeAccountService:r}=await Promise.resolve().then(t.bind(t,89546)),a=await r.getFreeAccountStatus(e);a&&(o={...o,tests:a.usageCount.tests||0,flashcards:a.usageCount.flashcards||0,mindMaps:a.usageCount.mindMaps||0})}catch(e){console.error("Error getting free account usage:",e)}let i={...s.limits,tests:s.limits.testsPerWeek??0,flashcards:s.limits.flashcardsPerWeek??0,mindMaps:s.limits.mindMapsPerWeek??0};return{userId:e,plan:r.subscription_plan,paymentVerified:r.payment_verified,features:s.features||[],limits:i,currentUsage:o}}catch(e){return console.error("Error getting user access info:",e),null}}static async canUserPerformAction(e,r,t=1){try{let n=await a.SupabaseAdminService.getUserProfile(e);if(!n)return{allowed:!1,reason:"Usuario no encontrado"};let o=await this.validateFeatureAccess(e,{test_generation:"test_generation",flashcard_generation:"flashcard_generation",mind_map_generation:"mind_map_generation",ai_chat:"ai_tutor_chat",study_planning:"study_planning",summary_generation:"summary_a1_a2"}[r]);if(!o.allowed)return o;if("free"===n.subscription_plan){let a={test_generation:"tests",flashcard_generation:"flashcards",mind_map_generation:"mindMaps"}[r];if(a){let n=await s.FreeAccountService.canPerformAction(e,a,t);if(!n.allowed)return{allowed:!1,reason:n.reason||`L\xedmite alcanzado para ${r}`,remainingUsage:n.remaining}}}return{allowed:!0,remainingUsage:o.remainingUsage}}catch(e){return console.error("Error checking user action:",e),{allowed:!1,reason:"Error interno de validaci\xf3n"}}}static async updateTokenUsage(e,r,t){try{let t=await a.SupabaseAdminService.getUserProfile(e);if(!t)return!1;let n=t.current_month_tokens+r;return await a.SupabaseAdminService.upsertUserProfile({...t,current_month_tokens:n,updated_at:new Date().toISOString()}),console.log(`✅ Tokens actualizados para usuario ${e}: +${r} (Total: ${n}/${t.monthly_token_limit})`),!0}catch(e){return console.error("Error updating token usage:",e),!1}}static async checkUpgradeNeeded(e){try{let r=await a.SupabaseAdminService.getUserProfile(e);if(!r)return{needsUpgrade:!1};let t=r.current_month_tokens/r.monthly_token_limit*100;if(t>=90){let e="free"===r.subscription_plan?"usuario":"pro";return{needsUpgrade:!0,reason:`Has usado el ${t.toFixed(1)}% de tus tokens mensuales`,suggestedPlan:e}}return{needsUpgrade:!1}}catch(e){return console.error("Error checking upgrade need:",e),{needsUpgrade:!1}}}}},34631:e=>{e.exports=require("tls")},40553:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>m,serverHooks:()=>f,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>_});var a={};t.r(a),t.d(a,{GET:()=>p,PUT:()=>d});var n=t(96559),s=t(48088),o=t(37719),i=t(32190),l=t(34386),c=t(32571),u=t(42049);async function p(e){try{let r=(0,l.createServerClient)("https://fxnhpxjijinfuxxxplzj.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4",{cookies:{getAll:()=>e.cookies.getAll(),setAll(){}}}),{data:{user:t},error:a}=await r.auth.getUser();if(a||!t)return i.NextResponse.json({error:"No autorizado"},{status:401});let n=await c.o.getUserAccessInfo(t.id);console.log("[API /user/profile] accessInfo recuperado:",JSON.stringify(n,null,2));let{data:s,error:o}=await u.E.from("user_profiles").select("*").eq("user_id",t.id).single();if(console.log("[API /user/profile] Perfil directo de BD:",JSON.stringify(s,null,2)),o&&console.error("[API /user/profile] Error perfil directo BD:",o),!n)return i.NextResponse.json({error:"Perfil no encontrado"},{status:404});let{data:p,error:d}=await r.from("user_profiles").select("*").eq("user_id",t.id).single();if(d)return i.NextResponse.json({error:"Error obteniendo perfil"},{status:500});let m=await c.o.checkUpgradeNeeded(t.id);return i.NextResponse.json({user:{id:t.id,email:t.email,name:t.user_metadata?.name||t.email?.split("@")[0],created_at:t.created_at},profile:{...p,plan_name:p.subscription_plan},access:{plan:n.plan||"free",features:Array.isArray(n.features)?n.features:[],limits:n.limits||{},currentUsage:n.currentUsage||{},paymentVerified:n.paymentVerified||!1},upgrade:m||{needsUpgrade:!1},tokenUsage:{current:p.current_month_tokens||0,limit:p.monthly_token_limit||0,percentage:p.monthly_token_limit>0?Math.round((p.current_month_tokens||0)/p.monthly_token_limit*100):0,remaining:Math.max(0,(p.monthly_token_limit||0)-(p.current_month_tokens||0))}})}catch(e){return console.error("Error in profile API:",e),i.NextResponse.json({error:"Error interno del servidor"},{status:500})}}async function d(e){try{let r=(0,l.createServerClient)("https://fxnhpxjijinfuxxxplzj.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4",{cookies:{getAll:()=>e.cookies.getAll(),setAll(){}}}),{data:{user:t},error:a}=await r.auth.getUser();if(a||!t)return i.NextResponse.json({error:"No autorizado"},{status:401});let{name:n,preferences:s}=await e.json();if(n){let{error:e}=await u.E.auth.admin.updateUserById(t.id,{user_metadata:{name:n}});e&&console.error("Error updating user metadata:",e)}if(s){let{error:e}=await r.from("user_profiles").update({security_flags:{...s,updated_at:new Date().toISOString()},updated_at:new Date().toISOString()}).eq("user_id",t.id);if(e)return console.error("Error updating profile preferences:",e),i.NextResponse.json({error:"Error actualizando preferencias"},{status:500})}return i.NextResponse.json({success:!0,message:"Perfil actualizado correctamente"})}catch(e){return console.error("Error updating profile:",e),i.NextResponse.json({error:"Error interno del servidor"},{status:500})}}let m=new n.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/user/profile/route",pathname:"/api/user/profile",filename:"route",bundlePath:"app/api/user/profile/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\user\\profile\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:g,workUnitAsyncStorage:_,serverHooks:f}=m;function h(){return(0,o.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:_})}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4447,580,6345,4386,7902],()=>t(40553));module.exports=a})();