(()=>{var e={};e.id=4015,e.ids=[4015],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8814:(e,r,t)=>{"use strict";t.d(r,{IE:()=>s,Nu:()=>i,qo:()=>a,t4:()=>o});let a={free:{id:"free",name:"Plan Gratis",price:0,limits:{documents:1,mindMapsForTrial:2,testsForTrial:10,flashcardsForTrial:10,tokensForTrial:5e4,features:["document_upload","test_generation","flashcard_generation","mind_map_generation"]},features:["document_upload","test_generation","flashcard_generation","mind_map_generation"],restrictedFeatures:["study_planning","ai_tutor_chat","summary_a1_a2"]},usuario:{id:"usuario",name:"Plan Usuario",price:1e3,limits:{documents:-1,mindMapsPerWeek:-1,testsPerWeek:-1,flashcardsPerWeek:-1,monthlyTokens:1e6,features:["document_upload","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation"]},features:["document_upload","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation"],restrictedFeatures:["study_planning","summary_a1_a2"]},pro:{id:"pro",name:"Plan Pro",price:1500,limits:{documents:-1,mindMapsPerWeek:-1,testsPerWeek:-1,flashcardsPerWeek:-1,monthlyTokens:1e6,features:["document_upload","study_planning","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation","summary_a1_a2"]},features:["document_upload","study_planning","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation","summary_a1_a2"],restrictedFeatures:[]}};function s(e){return a[e]||null}function o(e){let r=s(e);return r?"free"===e?r.limits.tokensForTrial||5e4:r.limits.monthlyTokens||1e6:5e4}function i(e,r){let t=s(e);return!(!t||t.restrictedFeatures.includes(r))&&t.features.includes(r)}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39336:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>f,routeModule:()=>d,serverHooks:()=>g,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>p});var a={};t.r(a),t.d(a,{POST:()=>l});var s=t(96559),o=t(48088),i=t(37719),n=t(32190),u=t(42049),c=t(8814);async function l(e){console.log("\uD83D\uDE80 [PRE-REGISTER-PAID] Endpoint llamado");try{console.log("\uD83D\uDCE5 [PRE-REGISTER-PAID] Parseando body...");let r=await e.json();console.log("\uD83D\uDCE5 [PRE-REGISTER-PAID] Body parseado:",{...r,password:"[HIDDEN]"});let{email:t,password:a,customerName:s,planId:o}=r;if(!t||!a||!o)return console.log("❌ [PRE-REGISTER-PAID] Validaci\xf3n b\xe1sica fall\xf3"),n.NextResponse.json({error:"Email, contrase\xf1a y plan son requeridos"},{status:400});if(a.length<6)return console.log("❌ [PRE-REGISTER-PAID] Contrase\xf1a muy corta"),n.NextResponse.json({error:"La contrase\xf1a debe tener al menos 6 caracteres"},{status:400});console.log("\uD83D\uDD0D [PRE-REGISTER-PAID] Validando plan...");let i=(0,c.IE)(o);if(!i)return console.log("❌ [PRE-REGISTER-PAID] Plan no v\xe1lido:",o),n.NextResponse.json({error:"Plan no v\xe1lido"},{status:400});if("free"===o)return console.log("❌ [PRE-REGISTER-PAID] Plan gratuito no permitido"),n.NextResponse.json({error:"Este endpoint es solo para planes de pago"},{status:400});console.log("\uD83D\uDD04 Pre-registrando usuario para plan de pago:",{email:t,planId:o,customerName:s,timestamp:new Date().toISOString()});try{let e={name:s||t.split("@")[0],plan:o,payment_verified:!1,pre_registered:!0,pre_registration_date:new Date().toISOString(),awaiting_payment:!0},{data:r,error:l}=await u.SupabaseAdminService.createUserWithPassword(t,a,e,!1);if(l){if(console.error("Error creando usuario:",l),l.message?.includes("User already registered")||l.message?.includes("email_address_not_authorized"))return n.NextResponse.json({error:"Ya existe una cuenta con este email. Por favor, usa otro email o inicia sesi\xf3n."},{status:409});return n.NextResponse.json({error:"Error creando la cuenta. Por favor, int\xe9ntalo de nuevo."},{status:500})}if(!r?.user)return n.NextResponse.json({error:"Error creando la cuenta"},{status:500});let d=r.user.id;console.log("✅ Usuario pre-registrado exitosamente:",d);try{await u.SupabaseAdminService.createUserProfile({user_id:d,subscription_plan:o,monthly_token_limit:(0,c.t4)(o),current_month_tokens:0,current_month:new Date().toISOString().split("T")[0],payment_verified:!1,plan_features:i.features,security_flags:{pre_registered:!0,awaiting_payment:!0,created_at:new Date().toISOString()}}),console.log("✅ Perfil de usuario creado en estado pendiente")}catch(e){console.error("Error creando perfil:",e)}return n.NextResponse.json({success:!0,userId:d,message:"Usuario pre-registrado exitosamente",data:{email:t,planId:o,awaitingPayment:!0}})}catch(e){return console.error("Error en pre-registro:",e),n.NextResponse.json({error:"Error interno del servidor"},{status:500})}}catch(e){return console.error("Error procesando pre-registro:",e),n.NextResponse.json({error:"Error procesando la solicitud"},{status:500})}}let d=new s.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/auth/pre-register-paid/route",pathname:"/api/auth/pre-register-paid",filename:"route",bundlePath:"app/api/auth/pre-register-paid/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\auth\\pre-register-paid\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:m,workUnitAsyncStorage:p,serverHooks:g}=d;function f(){return(0,i.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:p})}},42049:(e,r,t)=>{"use strict";t.d(r,{E:()=>a,SupabaseAdminService:()=>s});let a=(0,t(86345).UU)("https://fxnhpxjijinfuxxxplzj.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY,{auth:{autoRefreshToken:!1,persistSession:!1}});class s{static async createStripeTransaction(e){let{data:r,error:t}=await a.from("stripe_transactions").insert([e]).select().single();if(t)throw console.error("Error creating stripe transaction:",t),Error(`Failed to create transaction: ${t.message}`);return r}static async getTransactionBySessionId(e){let{data:r,error:t}=await a.from("stripe_transactions").select("*").eq("stripe_session_id",e).single();if(t&&"PGRST116"!==t.code)throw console.error("Error fetching transaction:",t),Error(`Failed to fetch transaction: ${t.message}`);return r}static async createUserWithInvitation(e,r){console.log("\uD83D\uDD04 [SUPABASE_ADMIN] Creating user invitation:",{email:e,userData:r,redirectTo:"http://localhost:3000/auth/callback",timestamp:new Date().toISOString()});let{data:t,error:s}=await a.auth.admin.inviteUserByEmail(e,{data:r,redirectTo:"http://localhost:3000/auth/callback"});if(console.log("\uD83D\uDCCA [SUPABASE_ADMIN] Invitation result:",{hasData:!!t,hasUser:!!t?.user,userId:t?.user?.id,userEmail:t?.user?.email,userAud:t?.user?.aud,userRole:t?.user?.role,emailConfirmed:t?.user?.email_confirmed_at,userMetadata:t?.user?.user_metadata,appMetadata:t?.user?.app_metadata,error:s?.message,errorCode:s?.status,fullError:s}),s)throw console.error("❌ [SUPABASE_ADMIN] Error creating user invitation:",{message:s.message,status:s.status,details:s}),Error(`Failed to create user invitation: ${s.message}`);return t}static async createUserWithPassword(e,r,t,s=!0){console.log("\uD83D\uDD04 [SUPABASE_ADMIN] Creating user with password:",{email:e,userData:t,sendConfirmationEmail:s,timestamp:new Date().toISOString()});let{data:o,error:i}=await a.auth.admin.createUser({email:e,password:r,user_metadata:t,email_confirm:!1});if(console.log("\uD83D\uDCCA [SUPABASE_ADMIN] User creation result:",{hasData:!!o,hasUser:!!o?.user,userId:o?.user?.id,userEmail:o?.user?.email,emailConfirmed:o?.user?.email_confirmed_at,userMetadata:o?.user?.user_metadata,error:i?.message,errorCode:i?.status}),i)return console.error("❌ [SUPABASE_ADMIN] Error creating user with password:",{message:i.message,status:i.status,details:i}),{data:null,error:i};if(o?.user&&s){console.log("\uD83D\uDCE7 Enviando email de confirmaci\xf3n...");let{error:t}=await a.auth.admin.generateLink({type:"signup",email:e,password:r,options:{redirectTo:"http://localhost:3000/auth/confirmed"}});t?console.error("⚠️ Error enviando email de confirmaci\xf3n:",t):console.log("✅ Email de confirmaci\xf3n enviado exitosamente")}else o?.user&&!s&&console.log("\uD83D\uDCE7 Email de confirmaci\xf3n omitido (se enviar\xe1 despu\xe9s del pago)");return{data:o,error:null}}static async sendConfirmationEmailForUser(e){console.log("\uD83D\uDCE7 [SUPABASE_ADMIN] Enviando email de confirmaci\xf3n para usuario:",e);try{let{data:r,error:t}=await a.auth.admin.getUserById(e);if(t||!r?.user)return console.error("Error obteniendo datos del usuario:",t),{success:!1,error:"Usuario no encontrado"};let s=r.user,{error:o}=await a.auth.admin.updateUserById(s.id,{email_confirm:!0,user_metadata:{...s.user_metadata,payment_verified:!0,email_confirmed_via_payment:!0,confirmed_at:new Date().toISOString()}});if(o)return console.error("⚠️ Error confirmando email del usuario:",o),{success:!1,error:o.message};return console.log("✅ Usuario confirmado autom\xe1ticamente despu\xe9s del pago exitoso"),{success:!0}}catch(e){return console.error("Error en sendConfirmationEmailForUser:",e),{success:!1,error:e instanceof Error?e.message:"Error desconocido"}}}static async sendConfirmationEmail(e,r){console.log("\uD83D\uDCE7 [SUPABASE_ADMIN] Enviando email de confirmaci\xf3n para:",e);let{error:t}=await a.auth.admin.generateLink({type:"signup",email:e,password:r,options:{redirectTo:"http://localhost:3000/auth/confirmed"}});return t?(console.error("⚠️ Error enviando email de confirmaci\xf3n:",t),{success:!1,error:t.message}):(console.log("✅ Email de confirmaci\xf3n enviado exitosamente"),{success:!0})}static async createUserProfile(e){let{data:r,error:t}=await a.from("user_profiles").insert([e]).select().single();if(t)throw console.error("Error creating user profile:",t),Error(`Failed to create user profile: ${t.message}`);return r}static async upsertUserProfile(e){let{data:r,error:t}=await a.from("user_profiles").upsert([e],{onConflict:"user_id"}).select().single();if(t)throw console.error("Error upserting user profile:",t),Error(`Failed to upsert user profile: ${t.message}`);return r}static async logPlanChange(e){let{data:r,error:t}=await a.from("user_plan_history").insert([e]).select().single();if(t)throw console.error("Error logging plan change:",t),Error(`Failed to log plan change: ${t.message}`);return r}static async logFeatureAccess(e){let{data:r,error:t}=await a.from("feature_access_log").insert([e]).select().single();if(t)throw console.error("Error logging feature access:",t),Error(`Failed to log feature access: ${t.message}`);return r}static async getUserProfile(e){let{data:r,error:t}=await a.from("user_profiles").select("*").eq("user_id",e).single();if(t&&"PGRST116"!==t.code)throw console.error("Error fetching user profile:",t),Error(`Failed to fetch user profile: ${t.message}`);return r}static async updateTransactionWithUser(e,r){let{error:t}=await a.from("stripe_transactions").update({user_id:r,updated_at:new Date().toISOString()}).eq("id",e);if(t)throw console.error("Error updating transaction with user_id:",t),Error(`Failed to update transaction: ${t.message}`)}static async activateTransaction(e){let{error:r}=await a.from("stripe_transactions").update({activated_at:new Date().toISOString()}).eq("id",e);if(r)throw console.error("Error activating transaction:",r),Error(`Failed to activate transaction: ${r.message}`)}static async getDocumentsCount(e){let{count:r,error:t}=await a.from("documentos").select("*",{count:"exact",head:!0}).eq("user_id",e);return t?(console.error("Error getting documents count:",t),0):r||0}static async getUserByEmail(e){try{let{data:{users:r},error:t}=await a.auth.admin.listUsers();if(t)throw console.error("Error getting user by email:",t),Error(`Failed to get user by email: ${t.message}`);if(!r||0===r.length)return null;let s=r.find(r=>r.email===e);if(!s)return null;return{id:s.id,email:s.email,email_confirmed_at:s.email_confirmed_at}}catch(e){throw console.error("Error in getUserByEmail:",e),e}}}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4447,580,6345],()=>t(39336));module.exports=a})();