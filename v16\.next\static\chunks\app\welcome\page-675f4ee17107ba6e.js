(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6593],{1544:(e,a,s)=>{"use strict";s.d(a,{IE:()=>t,qk:()=>i,qo:()=>r});let r={free:{id:"free",name:"Plan Gratis",price:0,limits:{documents:1,mindMapsForTrial:2,testsForTrial:10,flashcardsForTrial:10,tokensForTrial:5e4,features:["document_upload","test_generation","flashcard_generation","mind_map_generation"]},features:["document_upload","test_generation","flashcard_generation","mind_map_generation"],restrictedFeatures:["study_planning","ai_tutor_chat","summary_a1_a2"]},usuario:{id:"usuario",name:"Plan Usuario",price:1e3,limits:{documents:-1,mindMapsPerWeek:-1,testsPerWeek:-1,flashcardsPerWeek:-1,monthlyTokens:1e6,features:["document_upload","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation"]},features:["document_upload","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation"],restrictedFeatures:["study_planning","summary_a1_a2"]},pro:{id:"pro",name:"Plan Pro",price:1500,limits:{documents:-1,mindMapsPerWeek:-1,testsPerWeek:-1,flashcardsPerWeek:-1,monthlyTokens:1e6,features:["document_upload","study_planning","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation","summary_a1_a2"]},features:["document_upload","study_planning","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation","summary_a1_a2"],restrictedFeatures:[]}};function t(e){return r[e]||null}function n(e,a){let s=t(e);return!(!s||s.restrictedFeatures.includes(a))&&s.features.includes(a)}async function i(e){try{let a=await fetch("/api/user/plan");if(!a.ok)return console.error("Error obteniendo plan del usuario"),n("free",e);let{plan:s}=await a.json();return n(s||"free",e)}catch(a){return console.error("Error verificando acceso a caracter\xedstica:",a),n("free",e)}}},2643:(e,a,s)=>{"use strict";s.d(a,{N:()=>n,U:()=>t});var r=s(9535);function t(){return(0,r.createBrowserClient)("https://fxnhpxjijinfuxxxplzj.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4",{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0}})}let n=t()},3095:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>d});var r=s(5155),t=s(2115),n=s(5695),i=s(6317),l=s(1544);function o(){let e=(0,n.useRouter)();(0,n.useSearchParams)();let[a,s]=(0,t.useState)(null),[o,d]=(0,t.useState)(!0);return((0,t.useEffect)(()=>{(async()=>{try{var a,r;let t=(0,i.U)(),{data:{user:n},error:o}=await t.auth.getUser();if(o||!n)return void e.push("/login");let{data:c,error:u}=await t.from("user_profiles").select("*").eq("user_id",n.id).single();if(u||!c)return void e.push("/payment");let m=(0,l.IE)(c.subscription_plan);if(!m)return void e.push("/payment");s({name:(null==(a=n.user_metadata)?void 0:a.name)||(null==(r=n.email)?void 0:r.split("@")[0])||"Usuario",email:n.email||"",plan:c.subscription_plan,planName:m.name,features:m.features,tokenLimit:c.monthly_token_limit}),d(!1)}catch(a){console.error("Error loading user info:",a),e.push("/login")}})()},[e]),o)?(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):a?(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-xl p-8 max-w-2xl w-full",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)("svg",{className:"w-8 h-8 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),(0,r.jsxs)("h1",{className:"text-3xl font-bold text-gray-800 mb-2",children:["\xa1Bienvenido a OposiAI, ",a.name,"!"]}),(0,r.jsx)("p",{className:"text-gray-600",children:"Tu cuenta ha sido activada exitosamente"})]}),(0,r.jsxs)("div",{className:"bg-blue-50 rounded-lg p-6 mb-6",children:[(0,r.jsxs)("h2",{className:"text-xl font-semibold text-blue-800 mb-3",children:["Tu Plan: ",a.planName]}),"free"!==a.plan&&(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsxs)("p",{className:"text-blue-700 mb-2",children:[(0,r.jsx)("strong",{children:"L\xedmite mensual de tokens:"})," ",a.tokenLimit.toLocaleString()]}),(0,r.jsx)("div",{className:"w-full bg-blue-200 rounded-full h-2",children:(0,r.jsx)("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:"0%"}})}),(0,r.jsx)("p",{className:"text-sm text-blue-600 mt-1",children:"0 tokens utilizados este mes"})]}),(0,r.jsx)("h3",{className:"font-semibold text-blue-800 mb-2",children:"Caracter\xedsticas incluidas:"}),(0,r.jsx)("ul",{className:"space-y-1",children:a.features.map((e,a)=>{var s;return(0,r.jsxs)("li",{className:"flex items-center text-blue-700",children:[(0,r.jsx)("svg",{className:"w-4 h-4 text-green-500 mr-2",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})}),{document_upload:"Subida de documentos",test_generation:"Generaci\xf3n de tests",flashcard_generation:"Generaci\xf3n de flashcards",mind_map_generation:"Generaci\xf3n de mapas mentales",ai_tutor_chat:"Chat con preparador IA",study_planning:"Planificaci\xf3n de estudios",summary_a1_a2:"Res\xfamenes A1 y A2"}[s=e]||s]},a)})})]}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-800 mb-4",children:"Primeros pasos"}),(0,r.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,r.jsx)("h3",{className:"font-semibold text-gray-800 mb-2",children:"1. Sube tus documentos"}),(0,r.jsx)("p",{className:"text-gray-600 text-sm",children:"Comienza subiendo tus materiales de estudio en formato PDF o TXT"})]}),(0,r.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,r.jsx)("h3",{className:"font-semibold text-gray-800 mb-2",children:"2. Genera contenido"}),(0,r.jsx)("p",{className:"text-gray-600 text-sm",children:"Crea tests, flashcards y mapas mentales basados en tus documentos"})]}),"free"!==a.plan&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,r.jsx)("h3",{className:"font-semibold text-gray-800 mb-2",children:"3. Chatea con la IA"}),(0,r.jsx)("p",{className:"text-gray-600 text-sm",children:"Haz preguntas espec\xedficas sobre tu temario a tu preparador IA"})]}),"pro"===a.plan&&(0,r.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,r.jsx)("h3",{className:"font-semibold text-gray-800 mb-2",children:"4. Planifica tu estudio"}),(0,r.jsx)("p",{className:"text-gray-600 text-sm",children:"Crea un plan de estudios personalizado con IA"})]})]})]})]}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,r.jsx)("button",{onClick:()=>{e.push("/app")},className:"flex-1 bg-blue-600 text-white py-3 px-6 rounded-md hover:bg-blue-700 transition-colors font-semibold",children:"Comenzar a usar OposiAI"}),(0,r.jsx)("button",{onClick:()=>e.push("/app/profile"),className:"flex-1 bg-gray-200 text-gray-800 py-3 px-6 rounded-md hover:bg-gray-300 transition-colors",children:"Ver mi perfil"})]}),(0,r.jsx)("div",{className:"mt-6 pt-4 border-t border-gray-200 text-center",children:(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:["\xbfNecesitas ayuda? ",(0,r.jsx)("a",{href:"/contact",className:"text-blue-600 hover:underline",children:"Contacta nuestro soporte"})]})})]})}):null}function d(){return(0,r.jsx)(t.Suspense,{fallback:(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}),children:(0,r.jsx)(o,{})})}},6317:(e,a,s)=>{"use strict";s.d(a,{N:()=>r.N,U:()=>r.U});var r=s(2643)},8345:(e,a,s)=>{Promise.resolve().then(s.bind(s,3095))}},e=>{var a=a=>e(e.s=a);e.O(0,[7361,8441,1684,7358],()=>a(8345)),_N_E=e.O()}]);