(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7954],{604:(s,e,a)=>{"use strict";a.r(e),a.d(e,{default:()=>n});var t=a(5155),l=a(2115),i=a(4092);function n(){let{user:s}=(0,i.A)(),[e,a]=(0,l.useState)(null),[n,r]=(0,l.useState)(null),[c,d]=(0,l.useState)(!0);return((0,l.useEffect)(()=>{let e=async()=>{try{d(!0);let s=await fetch("/api/user/plan");if(s.ok){let e=await s.json();a(e)}let e=await fetch("/api/auth/free-account-status");if(e.ok){let s=await e.json();r(s)}}catch(s){console.error("Error cargando informaci\xf3n del usuario:",s)}finally{d(!1)}};s&&e()},[s]),c)?(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"}),(0,t.jsx)("p",{children:"Cargando informaci\xf3n del usuario..."})]})}):s?(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 p-8",children:(0,t.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-8",children:"Debug: Informaci\xf3n del Usuario"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Usuario Autenticado"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"ID:"})," ",s.id]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Email:"})," ",s.email]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Creado:"})," ",new Date(s.created_at).toLocaleString()]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"\xdaltima conexi\xf3n:"})," ",s.last_sign_in_at?new Date(s.last_sign_in_at).toLocaleString():"N/A"]})]})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Plan Actual"}),e?(0,t.jsx)("div",{className:"space-y-2",children:(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Plan:"})," ",(0,t.jsx)("span",{className:"px-2 py-1 rounded text-sm font-medium ".concat("free"===e.plan?"bg-yellow-100 text-yellow-800":"usuario"===e.plan?"bg-blue-100 text-blue-800":"pro"===e.plan?"bg-purple-100 text-purple-800":"bg-gray-100 text-gray-800"),children:e.plan||"No definido"})]})}):(0,t.jsx)("p",{className:"text-gray-500",children:"No se pudo cargar informaci\xf3n del plan"})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 md:col-span-2",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Estado de Cuenta Gratuita"}),n?(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Es cuenta gratuita:"})," ",n.isFreeAccount?"S\xed":"No"]}),n.isFreeAccount&&n.status&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Estado:"})," ",n.status.isActive?"Activa":"Inactiva"]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Expira:"})," ",new Date(n.status.expiresAt).toLocaleString()]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"D\xedas restantes:"})," ",n.status.daysRemaining]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-800 mb-2",children:"Uso Actual:"}),(0,t.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,t.jsxs)("div",{className:"bg-gray-50 p-3 rounded",children:[(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Documentos"}),(0,t.jsxs)("p",{className:"text-lg font-semibold",children:[n.status.usageCount.documents,"/",n.status.limits.documents]})]}),(0,t.jsxs)("div",{className:"bg-gray-50 p-3 rounded",children:[(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Tests"}),(0,t.jsxs)("p",{className:"text-lg font-semibold",children:[n.status.usageCount.tests,"/",n.status.limits.tests]})]}),(0,t.jsxs)("div",{className:"bg-gray-50 p-3 rounded",children:[(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Flashcards"}),(0,t.jsxs)("p",{className:"text-lg font-semibold",children:[n.status.usageCount.flashcards,"/",n.status.limits.flashcards]})]}),(0,t.jsxs)("div",{className:"bg-gray-50 p-3 rounded",children:[(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Mapas Mentales"}),(0,t.jsxs)("p",{className:"text-lg font-semibold",children:[n.status.usageCount.mindMaps,"/",n.status.limits.mindMaps]})]})]})]})]})]}):(0,t.jsx)("p",{className:"text-gray-500",children:"No se pudo cargar informaci\xf3n de cuenta gratuita"})]})]}),(0,t.jsx)("div",{className:"mt-8 text-center",children:(0,t.jsx)("button",{onClick:()=>window.location.href="/app",className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Volver a la aplicaci\xf3n"})})]})}):(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-red-600 mb-4",children:"No hay usuario autenticado"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Debes iniciar sesi\xf3n para ver esta informaci\xf3n."})]})})}},2840:(s,e,a)=>{Promise.resolve().then(a.bind(a,604))}},s=>{var e=e=>s(s.s=e);s.O(0,[7361,8260,4092,8441,1684,7358],()=>e(2840)),_N_E=s.O()}]);