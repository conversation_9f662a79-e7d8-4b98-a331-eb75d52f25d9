"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4092],{1881:(e,s,t)=>{t.d(s,{F3:()=>o,iF:()=>n,n4:()=>a});var r=t(6317);async function o(e,s){try{if(!e||!s)return{user:null,session:null,error:"Por favor, ingresa tu email y contrase\xf1a"};let{data:t,error:o}=await r.N.auth.signInWithPassword({email:e.trim(),password:s});if(o){if(o.message.includes("issued in the future")||o.message.includes("clock for skew"))return{user:null,session:null,error:"Error de sincronizaci\xf3n de tiempo. Por favor, verifica que la hora de tu dispositivo est\xe9 correctamente configurada."};if(o.message.includes("Invalid login credentials"))return{user:null,session:null,error:"Email o contrase\xf1a incorrectos. Por favor, verifica tus credenciales."};return{user:null,session:null,error:o.message}}if(t&&t.user&&t.session)return await new Promise(e=>setTimeout(e,800)),await r.N.auth.getSession(),{user:t.user,session:t.session,error:null};return{user:null,session:null,error:"Respuesta inesperada del servidor al iniciar sesi\xf3n."}}catch(e){return{user:null,session:null,error:e instanceof Error&&e.message?e.message:"Ha ocurrido un error inesperado al iniciar sesi\xf3n"}}}async function a(){try{console.log("\uD83D\uDD13 Iniciando proceso de logout...");let{error:e}=await r.N.auth.signOut({scope:"global"});if(e)return console.error("❌ Error en signOut:",e.message),{error:e.message};return console.log("✅ SignOut exitoso"),console.log("\uD83E\uDDF9 Limpiando almacenamiento local..."),Object.keys(localStorage).forEach(e=>{(e.startsWith("sb-")||e.includes("supabase"))&&(console.log("\uD83D\uDDD1️ Eliminando localStorage:",e),localStorage.removeItem(e))}),Object.keys(sessionStorage).forEach(e=>{(e.startsWith("sb-")||e.includes("supabase"))&&(console.log("\uD83D\uDDD1️ Eliminando sessionStorage:",e),sessionStorage.removeItem(e))}),document.cookie.split(";").forEach(function(e){let s=e.indexOf("="),t=s>-1?e.substr(0,s).trim():e.trim();(t.startsWith("sb-")||t.includes("supabase"))&&(console.log("\uD83C\uDF6A Eliminando cookie:",t),document.cookie=t+"=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain="+window.location.hostname,document.cookie=t+"=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/")}),console.log("✅ Limpieza de almacenamiento completada"),{error:null}}catch(e){return console.error("❌ Error inesperado en logout:",e),{error:"Ha ocurrido un error inesperado al cerrar sesi\xf3n"}}}async function n(){try{let{data:{user:e},error:s}=await r.N.auth.getUser();if(s){if("Auth session missing!"===s.message)return{user:null,error:null};return{user:null,error:s.message}}return{user:e,error:null}}catch(e){return{user:null,error:"Ha ocurrido un error inesperado al obtener el usuario actual"}}}},2643:(e,s,t)=>{t.d(s,{N:()=>a,U:()=>o});var r=t(9535);function o(){return(0,r.createBrowserClient)("https://fxnhpxjijinfuxxxplzj.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4",{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0}})}let a=o()},4092:(e,s,t)=>{t.d(s,{O:()=>m,A:()=>g});var r=t(5155),o=t(2115),a=t(5695),n=t(6317),i=t(1881),u=t(9679);let l=e=>{let{timeout:s,onTimeout:t,enabled:r=!0}=e,a=(0,o.useRef)(null),n=(0,o.useRef)(Date.now()),{tasks:i}=(0,u.M)();i.some(e=>"pending"===e.status||"processing"===e.status);let l=(0,o.useCallback)(()=>{r&&(a.current&&clearTimeout(a.current),n.current=Date.now(),a.current=setTimeout(()=>{0===i.filter(e=>"pending"===e.status||"processing"===e.status).length?t():(console.log("\uD83D\uDD04 Logout diferido: hay tareas de IA en progreso, reintentando en 1 minuto"),setTimeout(()=>l(),6e4))},s))},[s,t,r,i]),c=(0,o.useCallback)(()=>{a.current&&(clearTimeout(a.current),a.current=null)},[]),d=(0,o.useCallback)(()=>r&&a.current?Math.max(0,s-(Date.now()-n.current)):0,[s,r]),m=["mousedown","mousemove","keypress","scroll","touchstart","click","keydown"];return(0,o.useEffect)(()=>{if(!r)return void c();let e=()=>{l()};return m.forEach(s=>{document.addEventListener(s,e,!0)}),l(),()=>{m.forEach(s=>{document.removeEventListener(s,e,!0)}),c()}},[r,l,c]),{resetTimer:l,clearTimer:c,getTimeRemaining:d}},c=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:5,s=arguments.length>1?arguments[1]:void 0,t=!(arguments.length>2)||void 0===arguments[2]||arguments[2];return l({timeout:60*e*1e3,onTimeout:s,enabled:t})},d=(0,o.createContext)(void 0),m=e=>{let{children:s}=e,[t,u]=(0,o.useState)(null),[l,m]=(0,o.useState)(null),[g,h]=(0,o.useState)(!0),[p,f]=(0,o.useState)(null),[k,b]=(0,o.useState)(!1),[v,w]=(0,o.useState)(60),E=(0,a.useRouter)(),_=(0,a.usePathname)();(0,o.useEffect)(()=>{h(!0);let{data:e}=n.N.auth.onAuthStateChange((e,s)=>{var t;m(s),u(null!=(t=null==s?void 0:s.user)?t:null),f(null),("INITIAL_SESSION"===e||"SIGNED_IN"===e||"SIGNED_OUT"===e||"TOKEN_REFRESHED"===e||"USER_UPDATED"===e||"PASSWORD_RECOVERY"===e)&&h(!1)});return n.N.auth.getSession().then(e=>{let{data:{session:s},error:t}=e;if(t&&(f(t.message),h(!1)),!s)try{localStorage.getItem("supabase.auth.token")&&setTimeout(async()=>{try{let{data:{session:e}}=await n.N.auth.getSession();e&&(m(e),u(e.user),h(!1))}catch(e){}},200)}catch(e){}}).catch(e=>{f(e.message),h(!1)}),()=>{null==e||e.subscription.unsubscribe()}},[]),(0,o.useEffect)(()=>g||_.startsWith("/api")||_.startsWith("/_next")?void 0:l&&"/login"===_?void E.replace("/app"):l||["/","/login","/payment","/thank-you","/auth/callback","/auth/confirmed","/auth/unauthorized","/auth/reset-password","/auth/confirm-reset","/auth/confirm-invitation"].includes(_)||_.startsWith("/api")||_.startsWith("/_next")?void 0:void E.replace("/login"),[l,g,_,E]);let S=(0,o.useCallback)(async(e,s)=>{h(!0),f(null);try{let{user:t,session:r,error:o}=await (0,i.F3)(e,s);if(o)return f(o),h(!1),{user:null,session:null,error:o};return r&&(await new Promise(e=>setTimeout(e,300)),E.replace("/app")),{user:t,session:r,error:null}}catch(s){let e=s instanceof Error&&s.message?s.message:"Error desconocido durante el inicio de sesi\xf3n.";return f(e),h(!1),{user:null,session:null,error:e}}},[E]),I=(0,o.useCallback)(async()=>{h(!0),f(null);let{error:e}=await (0,i.n4)();e&&(f(e),h(!1))},[]),C=(0,o.useCallback)(()=>!!t&&!!l&&!g,[t,l,g]),y=(0,o.useCallback)(async()=>{await I()},[I]);(0,o.useCallback)(()=>{b(!1)},[]),(0,o.useCallback)(async()=>{b(!1),await I()},[I]);let{resetTimer:N}=c(5,y,C());return(0,r.jsxs)(d.Provider,{value:{user:t,session:l,isLoading:g,error:p,iniciarSesion:S,cerrarSesion:I,estaAutenticado:C},children:[s,!1]})},g=()=>{let e=(0,o.useContext)(d);if(void 0===e)throw Error("useAuth debe ser utilizado dentro de un AuthProvider");return e}},6317:(e,s,t)=>{t.d(s,{N:()=>r.N,U:()=>r.U});var r=t(2643)},9679:(e,s,t)=>{t.d(s,{M:()=>u,W:()=>i});var r=t(5155),o=t(2115),a=t(8260);let n=(0,o.createContext)(void 0),i=e=>{let{children:s}=e,[t,i]=(0,o.useState)([]),u=(0,o.useCallback)(e=>{let s="task_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),t={...e,id:s,status:"pending",createdAt:new Date};return i(e=>[...e,t]),a.oR.loading("Iniciando: ".concat(t.title),{id:"task_start_".concat(s),duration:2e3}),s},[]),l=(0,o.useCallback)((e,s)=>{i(t=>t.map(t=>{if(t.id===e){let r={...t,...s};return"processing"===s.status&&"pending"===t.status?(a.oR.dismiss("task_start_".concat(e)),a.oR.loading("Procesando: ".concat(t.title),{id:"task_processing_".concat(e)})):"completed"===s.status&&"completed"!==t.status?(a.oR.dismiss("task_processing_".concat(e)),a.oR.success("Completado: ".concat(t.title),{id:"task_completed_".concat(e),duration:4e3}),r.completedAt=new Date):"error"===s.status&&"error"!==t.status&&(a.oR.dismiss("task_processing_".concat(e)),a.oR.error("Error: ".concat(t.title),{id:"task_error_".concat(e),duration:5e3})),r}return t}))},[]),c=(0,o.useCallback)(e=>{i(s=>s.filter(s=>s.id!==e)),a.oR.dismiss("task_start_".concat(e)),a.oR.dismiss("task_processing_".concat(e)),a.oR.dismiss("task_completed_".concat(e)),a.oR.dismiss("task_error_".concat(e))},[]),d=(0,o.useCallback)(e=>t.find(s=>s.id===e),[t]),m=(0,o.useCallback)(e=>t.filter(s=>s.type===e),[t]),g=(0,o.useCallback)(()=>{i(e=>e.filter(e=>"completed"!==e.status&&"error"!==e.status))},[]),h=(0,o.useMemo)(()=>t.filter(e=>"pending"===e.status||"processing"===e.status),[t]),p=(0,o.useMemo)(()=>t.filter(e=>"completed"===e.status||"error"===e.status),[t]),f=(0,o.useMemo)(()=>({tasks:t,addTask:u,updateTask:l,removeTask:c,getTask:d,getTasksByType:m,clearCompletedTasks:g,activeTasks:h,completedTasks:p}),[t,u,l,c,d,m,g,h,p]);return(0,r.jsx)(n.Provider,{value:f,children:s})},u=()=>{let e=(0,o.useContext)(n);if(void 0===e)throw Error("useBackgroundTasks must be used within a BackgroundTasksProvider");return e}}}]);