{"/_not-found/page": "app/_not-found/page.js", "/api/admin/cleanup-expired-free/route": "app/api/admin/cleanup-expired-free/route.js", "/api/admin/email-failures/route": "app/api/admin/email-failures/route.js", "/api/admin/reactivate-user/route": "app/api/admin/reactivate-user/route.js", "/api/admin/notification-stats/route": "app/api/admin/notification-stats/route.js", "/api/admin/send-grace-period-reminders/route": "app/api/admin/send-grace-period-reminders/route.js", "/api/ai/route": "app/api/ai/route.js", "/api/admin/process-expired-grace-periods/route": "app/api/admin/process-expired-grace-periods/route.js", "/api/auth/generate-password-reset/route": "app/api/auth/generate-password-reset/route.js", "/api/auth/pre-register-paid/route": "app/api/auth/pre-register-paid/route.js", "/api/auth/free-account-status/route": "app/api/auth/free-account-status/route.js", "/api/auth/register-free/route": "app/api/auth/register-free/route.js", "/api/auth/resend-confirmation/route": "app/api/auth/resend-confirmation/route.js", "/api/health/route": "app/api/health/route.js", "/api/payment/status/route": "app/api/payment/status/route.js", "/api/stripe/webhook/route": "app/api/stripe/webhook/route.js", "/api/notify-signup/route": "app/api/notify-signup/route.js", "/api/document/upload/route": "app/api/document/upload/route.js", "/api/stripe/create-checkout-session/route": "app/api/stripe/create-checkout-session/route.js", "/api/user/cancel-subscription/route": "app/api/user/cancel-subscription/route.js", "/api/tokens/purchase/route": "app/api/tokens/purchase/route.js", "/api/user/notifications/route": "app/api/user/notifications/route.js", "/api/user/plan/route": "app/api/user/plan/route.js", "/api/user/profile/route": "app/api/user/profile/route.js", "/api/user/status/route": "app/api/user/status/route.js", "/api/user/reactivate/route": "app/api/user/reactivate/route.js", "/api/user/validate-access/route": "app/api/user/validate-access/route.js", "/admin/reactivate/page": "app/admin/reactivate/page.js", "/app/page": "app/app/page.js", "/auth/confirm-reset/page": "app/auth/confirm-reset/page.js", "/auth/callback/page": "app/auth/callback/page.js", "/auth/confirmed/page": "app/auth/confirmed/page.js", "/debug-user/page": "app/debug-user/page.js", "/auth/reset-password/page": "app/auth/reset-password/page.js", "/auth/unauthorized/page": "app/auth/unauthorized/page.js", "/login/page": "app/login/page.js", "/page": "app/page.js", "/payment/page": "app/payment/page.js", "/payment-pending/page": "app/payment-pending/page.js", "/plan-estudios/page": "app/plan-estudios/page.js", "/profile/page": "app/profile/page.js", "/thank-you/page": "app/thank-you/page.js", "/upgrade-plan/page": "app/upgrade-plan/page.js", "/welcome/page": "app/welcome/page.js"}