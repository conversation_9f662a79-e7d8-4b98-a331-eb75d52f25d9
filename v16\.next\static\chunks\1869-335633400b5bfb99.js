"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1869],{572:(e,a,r)=>{r.d(a,{A:()=>D});var t=r(5155),o=r(2115),n=r(351),s=r(2646),i=r(8260);let l={firstDayOfWeek:1,locale:"es-ES"},c=["Lunes","Martes","Mi\xe9rcoles","Jueves","Viernes","S\xe1bado","Domingo"],d=["LU","MA","MI","JU","VI","SA","DO"],u=["Enero","Febrero","Marzo","Abril","Mayo","Junio","Julio","Agosto","Septiembre","Octubre","Noviembre","Diciembre"];function m(e){if(!e||"string"!=typeof e||!/^\d{4}-\d{2}-\d{2}$/.test(e))return null;let a=new Date(e+"T00:00:00.000Z");if(isNaN(a.getTime()))return null;let r=a.getUTCFullYear(),t=String(a.getUTCMonth()+1).padStart(2,"0"),o=String(a.getUTCDate()).padStart(2,"0");return"".concat(r,"-").concat(t,"-").concat(o)!==e?null:a}function f(e){if(!e||isNaN(e.getTime()))return"";let a=e.getFullYear(),r=String(e.getMonth()+1).padStart(2,"0"),t=String(e.getDate()).padStart(2,"0");return"".concat(a,"-").concat(r,"-").concat(t)}function p(e,a){let r=m(e);if(!r)return null;let t=function(e){let a=c.findIndex(a=>a.toLowerCase()===e.toLowerCase());return a>=0?a:-1}(a);if(-1===t)return null;let o=new Date(r);return o.setDate(r.getDate()+t),o}function h(e,a){return new Date(e,a,1)}function g(e){var a;return a=new Date,!!e&&!!a&&e.getFullYear()===a.getFullYear()&&e.getMonth()===a.getMonth()&&e.getDate()===a.getDate()}function x(e,a,r){return e.getFullYear()===a&&e.getMonth()===r}function y(e,a){let r=f(e),t=a.mapaDias.get(r);return(null==t?void 0:t.tareas)||[]}let b="oposiciones-ia-calendario-prefs";function v(){try{var e;let a=localStorage.getItem(b);if(!a)return w();let r=JSON.parse(a);return{mesActual:"number"==typeof r.mesActual&&r.mesActual>=0&&r.mesActual<=11?r.mesActual:void 0,yearActual:"number"==typeof r.yearActual&&r.yearActual>=2020&&r.yearActual<=2030?r.yearActual:void 0,fechaSeleccionada:"string"==typeof r.fechaSeleccionada&&(e=r.fechaSeleccionada)&&"string"==typeof e&&!isNaN(new Date(e).getTime())&&e.includes("T")?r.fechaSeleccionada:void 0,calendarioExpandido:"boolean"!=typeof r.calendarioExpandido||r.calendarioExpandido,primerDiaSemana:0===r.primerDiaSemana||1===r.primerDiaSemana?r.primerDiaSemana:1,vistaTamaño:["compacto","normal","grande"].includes(r.vistaTamaño)?r.vistaTamaño:"normal"}}catch(e){return console.warn("Error al leer preferencias del calendario:",e),w()}}function N(e){try{let a={...v(),...e};localStorage.setItem(b,JSON.stringify(a))}catch(e){console.warn("Error al guardar preferencias del calendario:",e)}}function w(){let e=new Date;return{mesActual:e.getMonth(),yearActual:e.getFullYear(),fechaSeleccionada:void 0,calendarioExpandido:!0,primerDiaSemana:1,vistaTamaño:"normal"}}function j(e){N({fechaSeleccionada:e?e.toISOString():void 0})}let _=e=>{let{plan:a,progresoPlan:r,fechaSeleccionada:s,onFechaSeleccionada:i,onMesChanged:c,className:_=""}=e,{estadoCalendario:E,isLoading:S,error:D,navegarMes:A,irAHoy:C,tituloMes:T,esFechaSeleccionable:q}=function(e,a,r){let{preferences:t,updatePreference:n}=function(){let[e,a]=(0,o.useState)(w);(0,o.useEffect)(()=>{a(v())},[]);let r=(0,o.useCallback)(e=>{a(a=>{let r={...a,...e};return N(e),r})},[]),t=(0,o.useCallback)((e,a)=>{r({[e]:a})},[r]);return{preferences:e,updatePreferences:r,updatePreference:t,clearPreferences:()=>{try{localStorage.removeItem(b)}catch(e){console.warn("Error al limpiar preferencias del calendario:",e)}a(w())}}}(),[s,i]=(0,o.useState)(()=>{var e,a;try{let e=!1,a={};["plan-calendario-mes","plan-calendario-year","plan-calendario-expandido"].forEach(r=>{let t=localStorage.getItem(r);if(null!==t){switch(e=!0,r){case"plan-calendario-mes":let o=parseInt(t);!isNaN(o)&&o>=0&&o<=11&&(a.mesActual=o);break;case"plan-calendario-year":let n=parseInt(t);!isNaN(n)&&n>=2020&&n<=2030&&(a.yearActual=n);break;case"plan-calendario-expandido":a.calendarioExpandido="true"===t}localStorage.removeItem(r)}}),e&&(N(a),console.log("Preferencias del calendario migradas exitosamente"))}catch(e){console.warn("Error al migrar preferencias antiguas:",e)}void 0!==t.primerDiaSemana&&(l.firstDayOfWeek=t.primerDiaSemana);let o=new Date,n=function(){let e=v();if(!e.fechaSeleccionada)return null;try{let a=new Date(e.fechaSeleccionada);return isNaN(a.getTime())?null:a}catch(e){return null}}();return{yearActual:null!=(e=t.yearActual)?e:o.getFullYear(),mesActual:null!=(a=t.mesActual)?a:o.getMonth(),fechaSeleccionada:r||n||null,fechasCalendario:[],diasCalendario:[]}}),[c,d]=(0,o.useState)(!1),[_,E]=(0,o.useState)(null),S=(0,o.useMemo)(()=>{if(!e)return null;try{d(!0),E(null);let r=function(e,a){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},t=[],o=new Map,n={incluirDiasSinTareas:!0,calcularEstadisticas:!0,validarFechas:!0,ordenarTareasPorTipo:!1,...r};if(!e||!e.semanas||!Array.isArray(e.semanas)){var s;return t.push("Plan de estudios inv\xe1lido o sin semanas"),s=t,{datosPlan:{fechaInicio:new Date,fechaFin:new Date,totalSemanas:0,mapaDias:new Map,rangoFechas:{minYear:new Date().getFullYear(),maxYear:new Date().getFullYear(),minMonth:new Date().getMonth(),maxMonth:new Date().getMonth()}},estadisticas:{totalTareas:0,tareasCompletadas:0,porcentajeGeneral:0,diasConTareas:0,diasCompletados:0},errores:s}}if(n.validarFechas){let a=function(e){let a=[],r=[],t=[];if(!e.semanas||!Array.isArray(e.semanas))return a.push("El plan no contiene semanas v\xe1lidas"),{esValido:!1,errores:a,advertencias:r,fechasProblematicas:t};let o=null;for(let n of e.semanas){if(!n||"number"!=typeof n.numero){a.push("Semana inv\xe1lida encontrada");continue}let e=m(n.fechaInicio),s=m(n.fechaFin);if(!e){a.push("Fecha de inicio inv\xe1lida en semana ".concat(n.numero,": ").concat(n.fechaInicio)),t.push({semana:n.numero,fecha:n.fechaInicio,problema:"Fecha de inicio inv\xe1lida"});continue}if(!s){a.push("Fecha de fin inv\xe1lida en semana ".concat(n.numero,": ").concat(n.fechaFin)),t.push({semana:n.numero,fecha:n.fechaFin,problema:"Fecha de fin inv\xe1lida"});continue}s<=e&&a.push("La fecha de fin debe ser posterior a la de inicio en semana ".concat(n.numero)),o&&e<o&&r.push("La semana ".concat(n.numero," comienza antes de que termine la anterior")),o=s}return{esValido:0===a.length,errores:a,advertencias:r,fechasProblematicas:t}}(e);a.esValido||t.push(...a.errores)}let i=null,l=null;for(let r of e.semanas){if(!r||"number"!=typeof r.numero){t.push("Semana inv\xe1lida encontrada");continue}let e=m(r.fechaInicio),s=m(r.fechaFin);if(!e||!s){t.push("Fechas inv\xe1lidas en semana ".concat(r.numero));continue}if((!i||e<i)&&(i=e),(!l||s>l)&&(l=s),r.dias&&Array.isArray(r.dias))for(let e of r.dias){let s=function(e,a,r,t){var o,n,s;if(!e||!e.dia)return{error:"D\xeda inv\xe1lido en semana ".concat(a.numero)};let i=p(a.fechaInicio,e.dia);if(!i)return{error:"No se pudo calcular la fecha para ".concat(e.dia," en semana ").concat(a.numero)};let l=[];if(e.tareas&&Array.isArray(e.tareas))for(let t of e.tareas){if(!t||!t.titulo)continue;let o=r.find(r=>r.semana_numero===a.numero&&r.dia_nombre===e.dia&&r.tarea_titulo===t.titulo);l.push({tarea:t,semanaNumero:a.numero,diaNombre:e.dia,completada:(null==o?void 0:o.completado)||!1,fechaCompletado:null==o?void 0:o.fecha_completado})}t.ordenarTareasPorTipo&&l.sort((e,a)=>{let r={estudio:0,repaso:1,practica:2,evaluacion:3};return(r[e.tarea.tipo]||99)-(r[a.tarea.tipo]||99)});let c=l.length,d=l.filter(e=>e.completada).length,u=c>0?d/c*100:0,m=(o=i,n=c,s=d,g(o)?"hoy":0===n?"normal":0===s?"con-tareas":s===n?"completado":"parcial");return{diaCalendario:{fecha:i,dia:i.getDate(),estaEnMesActual:!0,esHoy:g(i),estado:m,tareas:l,totalTareas:c,tareasCompletadas:d,porcentajeCompletado:u}}}(e,r,a,n);if(s.error){t.push(s.error);continue}if(s.diaCalendario){let e=f(s.diaCalendario.fecha);o.set(e,s.diaCalendario),s.diaCalendario.totalTareas}}}return{datosPlan:{fechaInicio:i||new Date,fechaFin:l||new Date,totalSemanas:e.semanas.length,mapaDias:o,rangoFechas:function(e,a){if(!e||!a){let e=new Date;return{minYear:e.getFullYear(),maxYear:e.getFullYear(),minMonth:e.getMonth(),maxMonth:e.getMonth()}}return{minYear:e.getFullYear(),maxYear:a.getFullYear(),minMonth:e.getMonth(),maxMonth:a.getMonth()}}(i,l)},estadisticas:n.calcularEstadisticas?function(e,a){let r=0,t=0,o=0,n=0;for(let a of Array.from(e.values()))a.totalTareas>0&&(o++,r+=a.totalTareas,t+=a.tareasCompletadas,a.tareasCompletadas===a.totalTareas&&n++);return{totalTareas:r,tareasCompletadas:t,porcentajeGeneral:r>0?t/r*100:0,diasConTareas:o,diasCompletados:n}}(o,0):{totalTareas:0,tareasCompletadas:0,porcentajeGeneral:0,diasConTareas:0,diasCompletados:0},errores:t}}(e,a,{incluirDiasSinTareas:!0,calcularEstadisticas:!0,validarFechas:!0,ordenarTareasPorTipo:!0});return r.errores.length>0&&(console.warn("Errores al procesar el plan:",r.errores),E(r.errores[0])),r.datosPlan}catch(e){return E(e instanceof Error?e.message:"Error desconocido al procesar el plan"),console.error("Error al procesar plan para calendario:",e),null}finally{d(!1)}},[e,a]),D=(0,o.useMemo)(()=>(function(e,a){let r=[],t=h(e,a),o=new Date(e,a+1,0),n=function(e,a){let r=h(e,a).getDay();return 1===l.firstDayOfWeek?0===r?6:r-1:r}(e,a);for(let e=n-1;e>=0;e--){let a=new Date(t);a.setDate(a.getDate()-(e+1)),r.push(a)}for(let t=1;t<=o.getDate();t++)r.push(new Date(e,a,t));let s=42-r.length;for(let e=1;e<=s;e++){let a=new Date(o);a.setDate(a.getDate()+e),r.push(a)}return r})(s.yearActual,s.mesActual),[s.yearActual,s.mesActual]),A=(0,o.useMemo)(()=>S?D.map(e=>{let a=f(e),r=S.mapaDias.get(a);return r?{...r,estaEnMesActual:x(e,s.yearActual,s.mesActual),esHoy:g(e)}:{fecha:e,dia:e.getDate(),estaEnMesActual:x(e,s.yearActual,s.mesActual),esHoy:g(e),estado:g(e)?"hoy":x(e,s.yearActual,s.mesActual)?"normal":"fuera-mes",tareas:[],totalTareas:0,tareasCompletadas:0,porcentajeCompletado:0}}):[],[D,S,s.yearActual,s.mesActual]);(0,o.useEffect)(()=>{i(e=>({...e,fechasCalendario:D,diasCalendario:A}))},[D,A]);let C=(0,o.useCallback)(e=>{i(a=>{var r,t,o,s;let{year:i,month:l}="anterior"===e?(r=a.yearActual,0===(t=a.mesActual)?{year:r-1,month:11}:{year:r,month:t-1}):(o=a.yearActual,11===(s=a.mesActual)?{year:o+1,month:0}:{year:o,month:s+1});return n("yearActual",i),n("mesActual",l),{...a,yearActual:i,mesActual:l}})},[n]),T=(0,o.useCallback)((e,a)=>{n("yearActual",e),n("mesActual",a),i(r=>({...r,yearActual:e,mesActual:a}))},[n]),q=(0,o.useCallback)(e=>{j(e),i(a=>({...a,fechaSeleccionada:e}))},[]),F=(0,o.useCallback)(()=>{let e=new Date;n("yearActual",e.getFullYear()),n("mesActual",e.getMonth()),j(e),i(a=>({...a,yearActual:e.getFullYear(),mesActual:e.getMonth(),fechaSeleccionada:e}))},[n]),I=(0,o.useCallback)(e=>S?y(e,S):[],[S]),k=(0,o.useCallback)(e=>{if(!S)return"normal";let a=f(e),r=S.mapaDias.get(a);return(null==r?void 0:r.estado)||"normal"},[S]),M=(0,o.useCallback)(e=>{if(!S)return!1;let a=f(e);return void 0!==S.mapaDias.get(a)||e>=S.fechaInicio&&e<=S.fechaFin},[S]),P=(0,o.useMemo)(()=>{let e=new Date(s.yearActual,s.mesActual);return"".concat(u[e.getMonth()]," ").concat(s.yearActual)},[s.yearActual,s.mesActual]),z=(0,o.useMemo)(()=>s.fechaSeleccionada&&S?y(s.fechaSeleccionada,S):[],[s.fechaSeleccionada,S]),R=(0,o.useMemo)(()=>{if(!s.fechaSeleccionada||!S)return null;let e=f(s.fechaSeleccionada),a=S.mapaDias.get(e);return a?{total:a.totalTareas,completadas:a.tareasCompletadas,porcentaje:a.porcentajeCompletado}:null},[s.fechaSeleccionada,S]);return{estadoCalendario:s,datosPlan:S,isLoading:c,error:_,navegarMes:C,irAMes:T,seleccionarFecha:q,irAHoy:F,obtenerTareasDelDia:I,obtenerEstadoDia:k,esFechaSeleccionable:M,tituloMes:P,tareasDelDiaSeleccionado:z,estadisticasDelDia:R}}(a,r,s),F=e=>{q(e.fecha)&&i(e.fecha)},I=e=>{A(e),c&&c(E.yearActual,E.mesActual)},k=(e,a)=>{("Enter"===e.key||" "===e.key)&&(e.preventDefault(),F(a))},M=(e,a)=>{("Enter"===e.key||" "===e.key)&&(e.preventDefault(),a())},P=e=>{let a=["relative","aspect-square","flex","items-center","justify-center","text-xs","sm:text-sm","font-medium","cursor-pointer","calendario-day-hover","calendario-estado-transition","rounded-none","sm:rounded-lg","border","border-transparent","min-h-[2.5rem]","sm:min-h-[3rem]"];switch(e.estaEnMesActual?a.push("text-gray-700","hover:text-gray-900"):a.push("text-gray-300","hover:text-gray-400"),e.estado){case"hoy":a.push("bg-blue-100","text-blue-900","border-blue-300","font-bold","ring-2","ring-blue-400","ring-opacity-50","calendario-pulso");break;case"con-tareas":a.push("bg-orange-50","text-orange-800","border-orange-200","hover:bg-orange-100","hover:border-orange-300");break;case"completado":a.push("bg-green-50","text-green-800","border-green-200","hover:bg-green-100","hover:border-green-300");break;case"parcial":a.push("bg-yellow-50","text-yellow-800","border-yellow-200","hover:bg-yellow-100","hover:border-yellow-300");break;case"normal":e.estaEnMesActual&&a.push("hover:bg-gray-50","hover:border-gray-200")}return s&&e.fecha.getTime()===s.getTime()&&a.push("ring-2","ring-blue-500","ring-opacity-75","bg-blue-50","border-blue-300"),q(e.fecha)||a.push("cursor-not-allowed","opacity-50"),a.join(" ")},z=e=>{if(0===e.totalTareas)return null;let a=e.porcentajeCompletado,r="bg-orange-400";return 100===a?r="bg-green-400":a>0&&(r="bg-yellow-400"),(0,t.jsx)("div",{className:"absolute bottom-1 right-1",children:(0,t.jsx)("div",{className:"w-2 h-2 rounded-full ".concat(r),title:"".concat(e.tareasCompletadas,"/").concat(e.totalTareas," tareas completadas")})})};return D?(0,t.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 ".concat(_),children:[(0,t.jsxs)("div",{className:"flex items-center text-red-800",children:[(0,t.jsx)(n.wIk,{className:"w-5 h-5 mr-2"}),(0,t.jsx)("span",{className:"font-medium",children:"Error en el calendario"})]}),(0,t.jsx)("p",{className:"text-red-600 text-sm mt-1",children:D})]}):(0,t.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm calendario-fade-in ".concat(_),children:[(0,t.jsx)("div",{className:"bg-gray-50 px-3 sm:px-4 py-2 sm:py-3 border-b border-gray-200",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("button",{onClick:()=>I("anterior"),onKeyDown:e=>M(e,()=>I("anterior")),className:"p-1 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors","aria-label":"Mes anterior",tabIndex:0,children:(0,t.jsx)(n.irw,{className:"w-5 h-5 text-gray-600"})}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(n.wIk,{className:"w-4 h-4 text-gray-600 hidden sm:block"}),(0,t.jsx)("h3",{className:"font-semibold text-gray-900 text-sm sm:text-base",children:T})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)("button",{onClick:C,onKeyDown:e=>M(e,C),className:"p-1 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors",title:"Ir a hoy","aria-label":"Ir a hoy",tabIndex:0,children:(0,t.jsx)(n.V5Y,{className:"w-4 h-4 text-gray-600"})}),(0,t.jsx)("button",{onClick:()=>I("siguiente"),onKeyDown:e=>M(e,()=>I("siguiente")),className:"p-1 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors","aria-label":"Mes siguiente",tabIndex:0,children:(0,t.jsx)(n.fOo,{className:"w-5 h-5 text-gray-600"})})]})]})}),(0,t.jsx)("div",{className:"grid grid-cols-7 bg-gray-100 border-b border-gray-200",children:d.map(e=>(0,t.jsx)("div",{className:"py-1 sm:py-2 text-center text-xs font-medium text-gray-600 uppercase tracking-wide",children:e},e))}),(0,t.jsx)("div",{className:"grid grid-cols-7 gap-0",children:S?Array.from({length:42},(e,a)=>(0,t.jsx)("div",{className:"aspect-square flex items-center justify-center border-r border-b border-gray-100 last:border-r-0",children:(0,t.jsx)("div",{className:"w-6 h-6 bg-gray-200 rounded animate-pulse"})},a)):E.diasCalendario.map((e,a)=>(0,t.jsx)("div",{className:"border-r border-b border-gray-100 last:border-r-0 ".concat(5===Math.floor(a/7)?"border-b-0":""),children:(0,t.jsxs)("button",{onClick:()=>F(e),onKeyDown:a=>k(a,e),className:"".concat(P(e)," focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"),disabled:!q(e.fecha),tabIndex:q(e.fecha)?0:-1,"aria-label":"".concat(e.dia," de ").concat(T).concat(e.totalTareas>0?", ".concat(e.totalTareas," tareas"):"").concat(e.esHoy?", hoy":"").concat("completado"===e.estado?", completado":"parcial"===e.estado?", parcialmente completado":"con-tareas"===e.estado?", con tareas pendientes":""),"aria-pressed":s&&e.fecha.getTime()===s.getTime()?"true":"false",children:[e.dia,z(e)]})},a))}),(0,t.jsx)("div",{className:"bg-gray-50 px-3 sm:px-4 py-2 border-t border-gray-200",children:(0,t.jsxs)("div",{className:"flex items-center justify-center space-x-2 sm:space-x-4 text-xs text-gray-600",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-orange-400 rounded-full"}),(0,t.jsx)("span",{className:"hidden sm:inline",children:"Pendientes"}),(0,t.jsx)("span",{className:"sm:hidden",children:"Pend."})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-yellow-400 rounded-full"}),(0,t.jsx)("span",{className:"hidden sm:inline",children:"Parcial"}),(0,t.jsx)("span",{className:"sm:hidden",children:"Parc."})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-green-400 rounded-full"}),(0,t.jsx)("span",{className:"hidden sm:inline",children:"Completado"}),(0,t.jsx)("span",{className:"sm:hidden",children:"Comp."})]})]})})]})},E=e=>{let{fecha:a,tareas:r,isLoading:s=!1,onTareaClick:i,onTareaCompletadaChange:c,className:d=""}=e,[u,m]=(0,o.useState)(new Set),f=e=>{switch(e){case"estudio":return(0,t.jsx)(n.H9b,{className:"w-4 h-4"});case"repaso":return(0,t.jsx)(n.jTZ,{className:"w-4 h-4"});case"practica":return(0,t.jsx)(n.aze,{className:"w-4 h-4"});case"evaluacion":return(0,t.jsx)(n.x_j,{className:"w-4 h-4"});default:return(0,t.jsx)(n.Ohp,{className:"w-4 h-4"})}},p=e=>{switch(e){case"estudio":return"text-blue-600 bg-blue-50 border-blue-200";case"repaso":return"text-green-600 bg-green-50 border-green-200";case"practica":return"text-purple-600 bg-purple-50 border-purple-200";case"evaluacion":return"text-red-600 bg-red-50 border-red-200";default:return"text-gray-600 bg-gray-50 border-gray-200"}},h=e=>{switch(e){case"estudio":return"Estudio";case"repaso":return"Repaso";case"practica":return"Pr\xe1ctica";case"evaluacion":return"Evaluaci\xf3n";default:return"Tarea"}},g=o.useMemo(()=>{if(!r.length)return null;let e=r.filter(e=>e.completada).length,a=r.length,t=Math.round(e/a*100);return{completadas:e,total:a,porcentaje:t}},[r]),x=e=>{i&&i(e)},y=async(e,a)=>{if(!c)return;let r="".concat(e.semanaNumero,"-").concat(e.diaNombre,"-").concat(e.tarea.titulo);try{m(e=>new Set(e).add(r)),await c(e,a)}catch(e){console.error("Error al actualizar tarea:",e)}finally{m(e=>{let a=new Set(e);return a.delete(r),a})}},b=e=>{let a="".concat(e.semanaNumero,"-").concat(e.diaNombre,"-").concat(e.tarea.titulo);return u.has(a)};return a?(0,t.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm calendario-slide-in ".concat(d),children:[(0,t.jsx)("div",{className:"bg-gray-50 px-3 sm:px-4 py-2 sm:py-3 border-b border-gray-200",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold text-gray-900 text-sm sm:text-base",children:function(e){return!e||isNaN(e.getTime())?"":e.toLocaleDateString(l.locale,{day:"2-digit",month:"2-digit",year:"numeric"})}(a)}),g&&(0,t.jsxs)("p",{className:"text-xs sm:text-sm text-gray-600",children:[g.completadas," de ",g.total," tareas",(0,t.jsx)("span",{className:"hidden sm:inline",children:" completadas"})]})]}),g&&(0,t.jsx)("div",{className:"text-right",children:(0,t.jsxs)("div",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ".concat(100===g.porcentaje?"bg-green-100 text-green-800":g.porcentaje>0?"bg-yellow-100 text-yellow-800":"bg-gray-100 text-gray-800"),children:[g.porcentaje,"%"]})})]})}),(0,t.jsx)("div",{className:"p-3 sm:p-4",children:s?(0,t.jsx)("div",{className:"space-y-3",children:Array.from({length:3},(e,a)=>(0,t.jsx)("div",{className:"animate-pulse",children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-4 h-4 bg-gray-200 rounded"}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4 mb-2"}),(0,t.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/2"})]})]})},a))}):0===r.length?(0,t.jsxs)("div",{className:"text-center py-6",children:[(0,t.jsx)(n.y3G,{className:"w-8 h-8 mx-auto mb-2 text-gray-400"}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"No hay tareas programadas"}),(0,t.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"para este d\xeda"})]}):(0,t.jsx)("div",{className:"space-y-3",children:r.map((e,a)=>{let r=b(e);return(0,t.jsx)("div",{className:"border rounded-lg p-3 calendario-estado-transition ".concat(e.completada?"bg-green-50 border-green-200":"bg-white border-gray-200"," ").concat(r?"opacity-75":""),children:(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[c?(0,t.jsx)("button",{onClick:a=>{a.stopPropagation(),y(e,!e.completada)},disabled:r,className:"flex-shrink-0 w-5 h-5 rounded border-2 mt-0.5 flex items-center justify-center transition-all hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 ".concat(e.completada?"bg-green-500 border-green-500 hover:bg-green-600":"border-gray-300 hover:border-gray-400"," ").concat(r?"opacity-50 cursor-not-allowed":"cursor-pointer"),"aria-label":"".concat(e.completada?"Desmarcar":"Marcar"," como completada: ").concat(e.tarea.titulo),title:"".concat(e.completada?"Desmarcar":"Marcar"," como completada"),children:r?(0,t.jsx)(n.TwU,{className:"w-3 h-3 text-gray-400 animate-spin"}):e.completada?(0,t.jsx)(n.YrT,{className:"w-3 h-3 text-white"}):null}):(0,t.jsx)("div",{className:"flex-shrink-0 w-5 h-5 rounded border-2 mt-0.5 flex items-center justify-center ".concat(e.completada?"bg-green-500 border-green-500":"border-gray-300"),children:e.completada&&(0,t.jsx)(n.YrT,{className:"w-3 h-3 text-white"})}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,t.jsxs)("div",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ".concat(p(e.tarea.tipo)),children:[f(e.tarea.tipo),(0,t.jsx)("span",{className:"ml-1",children:h(e.tarea.tipo)})]}),e.tarea.duracionEstimada&&(0,t.jsxs)("div",{className:"flex items-center text-xs text-gray-500",children:[(0,t.jsx)(n.Ohp,{className:"w-3 h-3 mr-1"}),e.tarea.duracionEstimada]})]}),(0,t.jsx)("h5",{className:"font-medium text-sm ".concat(e.completada?"text-green-800 line-through":"text-gray-900"),children:e.tarea.titulo}),e.tarea.descripcion&&(0,t.jsx)("p",{className:"text-xs mt-1 ".concat(e.completada?"text-green-600":"text-gray-600"),children:e.tarea.descripcion}),(0,t.jsxs)("div",{className:"flex items-center justify-between mt-2",children:[(0,t.jsxs)("div",{className:"text-xs text-gray-500",children:["Semana ",e.semanaNumero," • ",e.diaNombre]}),e.completada&&e.fechaCompletado&&(0,t.jsx)("div",{className:"text-xs text-green-600",children:"✓ Completada"})]})]}),i&&(0,t.jsx)("button",{onClick:a=>{a.stopPropagation(),x(e)},className:"flex-shrink-0 p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50","aria-label":"Ir a esta tarea en el plan: ".concat(e.tarea.titulo),title:"Ir al plan de estudios",children:(0,t.jsx)(n.HaR,{className:"w-4 h-4"})})]})},a)})})}),r.length>0&&!s&&(0,t.jsx)("div",{className:"bg-gray-50 px-4 py-2 border-t border-gray-200",children:(0,t.jsxs)("div",{className:"flex items-center justify-between text-xs text-gray-600",children:[(0,t.jsxs)("span",{children:[r.filter(e=>e.completada).length," completadas"]}),(0,t.jsxs)("span",{children:[r.filter(e=>!e.completada).length," pendientes"]})]})})]}):(0,t.jsx)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-3 sm:p-4 ".concat(d),children:(0,t.jsxs)("div",{className:"text-center text-gray-500",children:[(0,t.jsx)(n.wIk,{className:"w-6 h-6 sm:w-8 sm:h-8 mx-auto mb-2 text-gray-400"}),(0,t.jsx)("p",{className:"text-xs sm:text-sm",children:"Selecciona un d\xeda en el calendario"}),(0,t.jsx)("p",{className:"text-xs text-gray-400 mt-1 hidden sm:block",children:"para ver las tareas programadas"})]})})},S=e=>{let{isOpen:a,onClose:r,plan:s,progresoPlan:i,fechaSeleccionada:l,onFechaSeleccionada:c,tareasDelDia:d,onTareaClick:u,onTareaCompletadaChange:m}=e;return((0,o.useEffect)(()=>{let e=e=>{"Escape"===e.key&&r()};return a&&(document.addEventListener("keydown",e),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",e),document.body.style.overflow="unset"}},[a,r]),a)?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden calendario-modal-overlay",onClick:r,"aria-hidden":"true"}),(0,t.jsx)("div",{className:"fixed inset-0 z-50 lg:hidden",children:(0,t.jsx)("div",{className:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:(0,t.jsxs)("div",{className:"inline-block align-bottom bg-white rounded-t-lg sm:rounded-lg text-left overflow-hidden shadow-xl calendario-modal-content sm:my-8 sm:align-middle sm:max-w-lg sm:w-full",children:[(0,t.jsxs)("div",{className:"bg-gray-50 px-4 py-3 border-b border-gray-200 flex items-center justify-between",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Calendario del Plan"}),(0,t.jsx)("button",{onClick:r,className:"p-2 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors","aria-label":"Cerrar calendario",children:(0,t.jsx)(n.yGN,{className:"w-5 h-5 text-gray-600"})})]}),(0,t.jsxs)("div",{className:"bg-white px-4 py-4 space-y-4 max-h-[70vh] overflow-y-auto",children:[(0,t.jsx)(_,{plan:s,progresoPlan:i,fechaSeleccionada:l,onFechaSeleccionada:c}),l&&(0,t.jsx)(E,{fecha:l,tareas:d,onTareaClick:e=>{u&&u(e),r()},onTareaCompletadaChange:m})]}),(0,t.jsx)("div",{className:"bg-gray-50 px-4 py-3 border-t border-gray-200",children:(0,t.jsx)("button",{onClick:r,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors",children:"Cerrar"})})]})})})]}):null},D=e=>{let{plan:a,temarioId:r}=e,[l,c]=(0,o.useState)([]),[d,u]=(0,o.useState)(null),[m,h]=(0,o.useState)(!0),[g,x]=(0,o.useState)(null),[y,b]=(0,o.useState)(!1),v=(0,o.useRef)({});(0,o.useEffect)(()=>{N()},[r]);let N=async()=>{try{let e=await (0,s.fF)(r);if(!e){console.warn("No se encontr\xf3 plan activo para el temario:",r),u(null),c([]),h(!1);return}u(e.id);let a=await (0,s.$S)(e.id);c(a)}catch(e){console.error("Error al cargar progreso:",e),u(null),c([])}finally{h(!1)}},w=async(e,a,r)=>{if(!d)return void i.oR.error("No se pudo identificar el plan de estudios");try{let t=l.find(t=>t.semana_numero===a&&t.dia_nombre===r&&t.tarea_titulo===e.titulo),o=!(null==t?void 0:t.completado);await (0,s.d7)(d,a,r,e.titulo,e.tipo,o)?(c(t=>{let n=t.findIndex(t=>t.semana_numero===a&&t.dia_nombre===r&&t.tarea_titulo===e.titulo);if(!(n>=0))return[...t,{id:"temp-".concat(Date.now()),plan_id:d,user_id:"",semana_numero:a,dia_nombre:r,tarea_titulo:e.titulo,tarea_tipo:e.tipo,completado:o,fecha_completado:o?new Date().toISOString():void 0,creado_en:new Date().toISOString(),actualizado_en:new Date().toISOString()}];{let e=[...t];return e[n]={...e[n],completado:o,fecha_completado:o?new Date().toISOString():void 0},e}}),i.oR.success(o?"Tarea completada":"Tarea marcada como pendiente")):i.oR.error("Error al actualizar el progreso")}catch(e){console.error("Error al actualizar tarea:",e),i.oR.error("Error al actualizar el progreso")}},j=(e,a,r)=>l.some(t=>t.semana_numero===a&&t.dia_nombre===r&&t.tarea_titulo===e.titulo&&t.completado),D=e=>{if(x(e),a&&a.semanas)for(let r of a.semanas)for(let a of r.dias||[]){let t=p(r.fechaInicio,a.dia);if(t&&f(t)===f(e)){let e=v.current[r.numero];e&&e.scrollIntoView({behavior:"smooth",block:"start",inline:"nearest"});return}}},A=()=>{if(!g||!a||!a.semanas)return[];let e=[];for(let t of a.semanas)for(let a of t.dias||[]){let o=p(t.fechaInicio,a.dia);if(o&&f(o)===f(g))for(let o of a.tareas||[]){var r;let n=j(o,t.numero,a.dia);e.push({tarea:o,semanaNumero:t.numero,diaNombre:a.dia,completada:n,fechaCompletado:null==(r=l.find(e=>e.semana_numero===t.numero&&e.dia_nombre===a.dia&&e.tarea_titulo===o.titulo))?void 0:r.fecha_completado})}}return e},C=e=>{let a=v.current[e.semanaNumero];a&&a.scrollIntoView({behavior:"smooth",block:"center",inline:"nearest"})},T=async(e,a)=>{try{await w(e.tarea,e.semanaNumero,e.diaNombre),i.oR.success(a?"✅ Tarea marcada como completada":"↩️ Tarea marcada como pendiente")}catch(e){throw console.error("Error al actualizar tarea desde calendario:",e),i.oR.error("Error al actualizar la tarea"),e}},q=(()=>{if(!a||!a.semanas||!Array.isArray(a.semanas))return{completadas:0,total:0,porcentaje:0};let e=a.semanas.reduce((e,a)=>a&&a.dias&&Array.isArray(a.dias)?e+a.dias.reduce((e,a)=>a&&a.tareas&&Array.isArray(a.tareas)?e+a.tareas.length:e,0):e,0),r=l.filter(e=>e.completado).length;return{completadas:r,total:e,porcentaje:e>0?Math.round(r/e*100):0}})();return m?(0,t.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),(0,t.jsx)("span",{className:"ml-3 text-gray-600",children:"Cargando progreso..."})]}):a?r&&""!==r.trim()?(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,t.jsx)("h3",{className:"font-semibold text-blue-900 mb-2",children:"Introducci\xf3n"}),(0,t.jsx)("p",{className:"text-blue-800",children:a.introduccion||"Introducci\xf3n no disponible"})]}),(0,t.jsxs)("div",{className:"bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-lg p-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-900",children:"Progreso General"}),(0,t.jsxs)("span",{className:"text-2xl font-bold text-green-600",children:[q.porcentaje,"%"]})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-3 mb-2",children:(0,t.jsx)("div",{className:"bg-gradient-to-r from-green-500 to-blue-500 h-3 rounded-full transition-all duration-300",style:{width:"".concat(q.porcentaje,"%")}})}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:[q.completadas," de ",q.total," tareas completadas"]})]}),a.resumen&&(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,t.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(n.Ohp,{className:"w-5 h-5 text-blue-600 mr-2"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Tiempo Total"}),(0,t.jsx)("p",{className:"font-semibold",children:a.resumen.tiempoTotalEstudio||"No disponible"})]})]})}),(0,t.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(n.H9b,{className:"w-5 h-5 text-green-600 mr-2"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Temas"}),(0,t.jsx)("p",{className:"font-semibold",children:a.resumen.numeroTemas||"No disponible"})]})]})}),(0,t.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(n.x_j,{className:"w-5 h-5 text-purple-600 mr-2"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Estudio Nuevo"}),(0,t.jsx)("p",{className:"font-semibold",children:a.resumen.duracionEstudioNuevo||"No disponible"})]})]})}),(0,t.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(n.jTZ,{className:"w-5 h-5 text-orange-600 mr-2"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Repaso Final"}),(0,t.jsx)("p",{className:"font-semibold",children:a.resumen.duracionRepasoFinal||"No disponible"})]})]})})]}),a.semanas&&a.semanas.length>0&&(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("h3",{className:"text-xl font-semibold text-gray-900 flex items-center",children:[(0,t.jsx)(n.wIk,{className:"w-5 h-5 mr-2"}),"Cronograma Semanal"]}),(0,t.jsxs)("button",{onClick:()=>b(!0),className:"lg:hidden flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors",children:[(0,t.jsx)(n.wIk,{className:"w-4 h-4 mr-2"}),"Ver Calendario"]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-12 gap-6",children:[(0,t.jsx)("div",{className:"lg:col-span-8 space-y-6",children:a.semanas.map((e,a)=>(0,t.jsxs)("div",{ref:a=>{v.current[e.numero]=a},className:"border border-gray-200 rounded-lg overflow-hidden",children:[(0,t.jsxs)("div",{className:"bg-gray-50 px-6 py-4 border-b border-gray-200",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("h4",{className:"text-lg font-semibold text-gray-900",children:["Semana ",(null==e?void 0:e.numero)||"N/A"]}),(0,t.jsxs)("span",{className:"text-sm text-gray-600",children:[(null==e?void 0:e.fechaInicio)||"N/A"," - ",(null==e?void 0:e.fechaFin)||"N/A"]})]}),(0,t.jsx)("p",{className:"text-gray-700 mt-2",children:(null==e?void 0:e.objetivoPrincipal)||"Objetivo no especificado"})]}),(0,t.jsx)("div",{className:"p-6 space-y-4",children:e.dias&&Array.isArray(e.dias)?e.dias.map((a,r)=>(0,t.jsxs)("div",{className:"border border-gray-100 rounded-lg p-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,t.jsx)("h5",{className:"font-semibold text-gray-900",children:(null==a?void 0:a.dia)||"D\xeda no especificado"}),(0,t.jsxs)("span",{className:"text-sm text-gray-600 bg-gray-100 px-2 py-1 rounded",children:[(null==a?void 0:a.horas)||0,"h"]})]}),(0,t.jsx)("div",{className:"space-y-2",children:a.tareas&&Array.isArray(a.tareas)?a.tareas.map((r,o)=>{let s=j(r,e.numero,a.dia);return(0,t.jsxs)("div",{className:"flex items-start p-3 rounded-lg border transition-all cursor-pointer ".concat(s?"bg-green-50 border-green-200":"bg-white border-gray-200 hover:border-blue-300"),onClick:()=>w(r,e.numero,a.dia),children:[(0,t.jsx)("div",{className:"flex-shrink-0 w-5 h-5 rounded border-2 mr-3 mt-0.5 flex items-center justify-center ".concat(s?"bg-green-500 border-green-500":"border-gray-300 hover:border-blue-400"),children:s&&(0,t.jsx)(n.YrT,{className:"w-3 h-3 text-white"})}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("h6",{className:"font-medium ".concat(s?"text-green-800 line-through":"text-gray-900"),children:(null==r?void 0:r.titulo)||"Tarea sin t\xedtulo"}),(null==r?void 0:r.descripcion)&&(0,t.jsx)("p",{className:"text-sm mt-1 ".concat(s?"text-green-700":"text-gray-600"),children:r.descripcion}),(0,t.jsxs)("div",{className:"flex items-center mt-2 space-x-3",children:[(0,t.jsx)("span",{className:"text-xs px-2 py-1 rounded ".concat((null==r?void 0:r.tipo)==="estudio"?"bg-blue-100 text-blue-800":(null==r?void 0:r.tipo)==="repaso"?"bg-yellow-100 text-yellow-800":(null==r?void 0:r.tipo)==="practica"?"bg-purple-100 text-purple-800":"bg-gray-100 text-gray-800"),children:(null==r?void 0:r.tipo)||"general"}),(0,t.jsx)("span",{className:"text-xs text-gray-500",children:(null==r?void 0:r.duracionEstimada)||"No especificado"})]})]})]},o)}):(0,t.jsx)("p",{className:"text-gray-500 text-sm",children:"No hay tareas disponibles"})})]},r)):(0,t.jsx)("p",{className:"text-gray-500 text-sm",children:"No hay d\xedas disponibles"})})]},a))}),(0,t.jsxs)("div",{className:"hidden lg:block lg:col-span-4 space-y-4",children:[(0,t.jsx)(_,{plan:a,progresoPlan:l,fechaSeleccionada:g,onFechaSeleccionada:D,className:"sticky top-4"}),(0,t.jsx)(E,{fecha:g,tareas:A(),onTareaClick:C,onTareaCompletadaChange:T,className:"sticky top-4"})]})]})]}),(0,t.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:[(0,t.jsx)("h3",{className:"font-semibold text-yellow-900 mb-2",children:"Estrategia de Repasos"}),(0,t.jsx)("p",{className:"text-yellow-800",children:"string"==typeof a.estrategiaRepasos?a.estrategiaRepasos:a.estrategiaRepasos&&"object"==typeof a.estrategiaRepasos&&a.estrategiaRepasos.descripcion||"Estrategia de repasos no disponible"})]}),(0,t.jsxs)("div",{className:"bg-purple-50 border border-purple-200 rounded-lg p-4",children:[(0,t.jsx)("h3",{className:"font-semibold text-purple-900 mb-2",children:"Pr\xf3ximos Pasos y Consejos"}),(0,t.jsx)("p",{className:"text-purple-800",children:"string"==typeof a.proximosPasos?a.proximosPasos:a.proximosPasos&&"object"==typeof a.proximosPasos&&a.proximosPasos.descripcion||"Pr\xf3ximos pasos no disponibles"})]}),(0,t.jsx)(S,{isOpen:y,onClose:()=>b(!1),plan:a,progresoPlan:l,fechaSeleccionada:g,onFechaSeleccionada:D,tareasDelDia:A(),onTareaClick:C,onTareaCompletadaChange:T})]}):(0,t.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,t.jsx)("div",{className:"text-center",children:(0,t.jsx)("p",{className:"text-gray-600",children:"ID de temario no v\xe1lido"})})}):(0,t.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,t.jsx)("div",{className:"text-center",children:(0,t.jsx)("p",{className:"text-gray-600",children:"No se pudo cargar el plan de estudios"})})})}},1544:(e,a,r)=>{r.d(a,{IE:()=>o,qk:()=>s,qo:()=>t});let t={free:{id:"free",name:"Plan Gratis",price:0,limits:{documents:1,mindMapsForTrial:2,testsForTrial:10,flashcardsForTrial:10,tokensForTrial:5e4,features:["document_upload","test_generation","flashcard_generation","mind_map_generation"]},features:["document_upload","test_generation","flashcard_generation","mind_map_generation"],restrictedFeatures:["study_planning","ai_tutor_chat","summary_a1_a2"]},usuario:{id:"usuario",name:"Plan Usuario",price:1e3,limits:{documents:-1,mindMapsPerWeek:-1,testsPerWeek:-1,flashcardsPerWeek:-1,monthlyTokens:1e6,features:["document_upload","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation"]},features:["document_upload","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation"],restrictedFeatures:["study_planning","summary_a1_a2"]},pro:{id:"pro",name:"Plan Pro",price:1500,limits:{documents:-1,mindMapsPerWeek:-1,testsPerWeek:-1,flashcardsPerWeek:-1,monthlyTokens:1e6,features:["document_upload","study_planning","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation","summary_a1_a2"]},features:["document_upload","study_planning","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation","summary_a1_a2"],restrictedFeatures:[]}};function o(e){return t[e]||null}function n(e,a){let r=o(e);return!(!r||r.restrictedFeatures.includes(a))&&r.features.includes(a)}async function s(e){try{let a=await fetch("/api/user/plan");if(!a.ok)return console.error("Error obteniendo plan del usuario"),n("free",e);let{plan:r}=await a.json();return n(r||"free",e)}catch(a){return console.error("Error verificando acceso a caracter\xedstica:",a),n("free",e)}}},2646:(e,a,r)=>{r.d(a,{$S:()=>s,d7:()=>i,fF:()=>n});var t=r(2643),o=r(1881);async function n(e){try{let{user:a,error:r}=await (0,o.iF)();if(!a||r)return null;let{data:n,error:s}=await t.N.from("temarios").select("id").eq("id",e).eq("user_id",a.id).single();if(s){if("PGRST116"===s.code)return console.log("Temario no encontrado:",e),null;return console.error("Error al verificar temario:",s),null}if(!n)return console.log("Temario no encontrado:",e),null;let{data:i,error:l}=await t.N.from("planes_estudios").select("*").eq("user_id",a.id).eq("temario_id",e).eq("activo",!0).single();if(l){if("PGRST116"===l.code)return null;return console.error("Error al obtener plan activo:",l),null}return i}catch(e){return console.error("Error al obtener plan activo:",e),null}}async function s(e){try{let{user:a,error:r}=await (0,o.iF)();if(!a||r)return[];let{data:n,error:s}=await t.N.from("progreso_plan_estudios").select("*").eq("plan_id",e).eq("user_id",a.id).order("semana_numero",{ascending:!0}).order("creado_en",{ascending:!0});if(s)return console.error("Error al obtener progreso del plan:",s),[];return n||[]}catch(e){return console.error("Error al obtener progreso del plan:",e),[]}}async function i(e,a,r,n,s,i,l,c,d){try{let{user:u,error:m}=await (0,o.iF)();if(!u||m)return!1;let{data:f}=await t.N.from("progreso_plan_estudios").select("id").eq("plan_id",e).eq("user_id",u.id).eq("semana_numero",a).eq("dia_nombre",r).eq("tarea_titulo",n).single();if(f){let{error:e}=await t.N.from("progreso_plan_estudios").update({completado:i,fecha_completado:i?new Date().toISOString():null,tiempo_real_minutos:l,notas_progreso:c,calificacion:d,actualizado_en:new Date().toISOString()}).eq("id",f.id);if(e)return console.error("Error al actualizar progreso:",e),!1}else{let{error:o}=await t.N.from("progreso_plan_estudios").insert([{plan_id:e,user_id:u.id,semana_numero:a,dia_nombre:r,tarea_titulo:n,tarea_tipo:s,completado:i,fecha_completado:i?new Date().toISOString():null,tiempo_real_minutos:l,notas_progreso:c,calificacion:d}]);if(o)return console.error("Error al crear progreso:",o),!1}return!0}catch(e){return console.error("Error al guardar progreso de tarea:",e),!1}}},2973:(e,a,r)=>{r.d(a,{vW:()=>l.vW,fW:()=>l.fW,xq:()=>c.xq,qJ:()=>c.qJ,Yp:()=>l.Yp,_4:()=>u._4,CM:()=>l.CM,sq:()=>l.sq,Q3:()=>i,hE:()=>s,yK:()=>c.yK,QE:()=>l.QE,OA:()=>u.OA,oE:()=>c.oE,Sl:()=>l.Sl,sj:()=>l.sj,R1:()=>n,yV:()=>d.yV,wU:()=>d.wU,oC:()=>u.oC,dd:()=>u.dd,C9:()=>l.C9,hg:()=>u.hg,Kj:()=>u.Kj,Lx:()=>u.Lx,Gl:()=>u.Gl});var t=r(6317),o=r(1881);async function n(){try{let{user:e}=await (0,o.iF)();if(!e)return console.error("No hay usuario autenticado"),[];let{data:a,error:r}=await t.N.from("documentos").select("*").eq("user_id",e.id).order("numero_tema",{ascending:!0});if(r)return console.error("Error al obtener documentos:",r),[];return a||[]}catch(e){return console.error("Error al obtener documentos:",e),[]}}async function s(e){try{var a;let{user:r}=await (0,o.iF)();if(!r)return console.error("No hay usuario autenticado"),null;let n={...e,user_id:r.id,tipo_original:e.tipo_original},{data:s,error:i}=await t.N.from("documentos").insert([n]).select();if(i)return console.error("Error al guardar documento:",i),null;return(null==s||null==(a=s[0])?void 0:a.id)||null}catch(e){return console.error("Error al guardar documento:",e),null}}async function i(e){try{console.log("\uD83D\uDDD1️ Iniciando eliminaci\xf3n de documento:",e);let{user:a}=await (0,o.iF)();if(!a)return console.error("❌ No hay usuario autenticado para eliminar documento"),!1;console.log("\uD83D\uDC64 Usuario autenticado:",a.id),console.log("\uD83D\uDCC4 Eliminando documento ID:",e);let{error:r,count:n}=await t.N.from("documentos").delete({count:"exact"}).eq("id",e).eq("user_id",a.id);if(r)return console.error("❌ Error al eliminar documento de Supabase:",r),!1;if(console.log("✅ Documento eliminado exitosamente. Filas afectadas:",n),0===n)return console.warn("⚠️ No se elimin\xf3 ning\xfan documento. Posibles causas: documento no existe o no pertenece al usuario"),!1;return!0}catch(e){return console.error("\uD83D\uDCA5 Error inesperado al eliminar documento:",e),!1}}var l=r(5759),c=r(3796),d=r(4794),u=r(7616);r(5307)},3737:(e,a,r)=>{r.d(a,{A:()=>l});var t=r(5155),o=r(2115),n=r(9183),s=r(2973);let i=(0,o.forwardRef)((e,a)=>{let{onSelectionChange:r}=e,[i,l]=(0,o.useState)([]),[c,d]=(0,o.useState)([]),[u,m]=(0,o.useState)(!0),f=async()=>{m(!0);try{let e=await (0,s.R1)();l(e)}catch(e){console.error("Error al cargar documentos:",e)}finally{m(!1)}};(0,o.useEffect)(()=>{f()},[]),(0,o.useImperativeHandle)(a,()=>({recargarDocumentos:f}));let p=i.map(e=>({value:e.id,label:"".concat(e.numero_tema?"Tema ".concat(e.numero_tema,": "):"").concat(e.titulo," ").concat(e.categoria?"(".concat(e.categoria,")"):"")}));return(0,t.jsxs)("div",{className:"mb-3",children:[(0,t.jsx)("label",{className:"block text-gray-700 text-sm font-medium mb-1",children:"Selecciona los documentos para consultar:"}),(0,t.jsx)(n.Ay,{instanceId:"document-selector",isMulti:!0,isLoading:u,options:p,className:"basic-multi-select",classNamePrefix:"select",placeholder:"Selecciona uno o m\xe1s documentos...",noOptionsMessage:()=>"No hay documentos disponibles",onChange:e=>{d(e||[]),r(e.map(e=>i.find(a=>a.id===e.value)).filter(Boolean))},value:c,styles:{control:e=>({...e,minHeight:"36px",fontSize:"14px"}),multiValue:e=>({...e,fontSize:"12px"}),placeholder:e=>({...e,fontSize:"14px"})}}),0===c.length&&(0,t.jsx)("p",{className:"text-red-500 text-xs italic mt-0.5",children:"Debes seleccionar al menos un documento"})]})});i.displayName="DocumentSelector";let l=i},3792:(e,a,r)=>{r.d(a,{Pk:()=>i,u9:()=>s,vD:()=>n});var t=r(2643),o=r(1881);async function n(e){try{let{user:a,error:r}=await (0,o.iF)();if(!a||r)return console.log("No hay usuario autenticado o error:",r),!1;let{data:n,error:s}=await t.N.from("temarios").select("id").eq("id",e).eq("user_id",a.id).single();if(s){if("PGRST116"===s.code)return!1;return console.error("Error al verificar temario:",s),!1}if(!n)return!1;let{data:i,error:l}=await t.N.from("planificacion_usuario").select("id").eq("user_id",a.id).eq("temario_id",e).eq("completado",!0).limit(1);if(l)return console.error("Error al verificar planificaci\xf3n:",l),!1;return i&&i.length>0}catch(e){return console.error("Error al verificar planificaci\xf3n:",e),!1}}async function s(e){try{let{user:a,error:r}=await (0,o.iF)();if(!a||r)return null;let{data:n,error:s}=await t.N.from("planificacion_usuario").select("*").eq("user_id",a.id).eq("temario_id",e).single();if(s){if("PGRST116"===s.code)return null;return console.error("Error al obtener planificaci\xf3n:",s),null}return n}catch(e){return console.error("Error al obtener planificaci\xf3n:",e),null}}async function i(e,a){try{let{user:r,error:n}=await (0,o.iF)();if(!r||n)return console.error("No hay usuario autenticado"),null;let i=await s(e);if(i){let{data:e,error:r}=await t.N.from("planificacion_usuario").update({...a,completado:!0,actualizado_en:new Date().toISOString()}).eq("id",i.id).select().single();if(r)return console.error("Error al actualizar planificaci\xf3n:",r),null;return e.id}{let{data:o,error:n}=await t.N.from("planificacion_usuario").insert([{user_id:r.id,temario_id:e,...a,completado:!0}]).select().single();if(n)return console.error("Error al crear planificaci\xf3n:",n),null;return o.id}}catch(e){return console.error("Error al guardar planificaci\xf3n:",e),null}}},3796:(e,a,r)=>{r.d(a,{Iv:()=>f,Og:()=>c,Q1:()=>i,QU:()=>g,_W:()=>m,_p:()=>u,as:()=>x,kO:()=>d,oE:()=>s,qJ:()=>n,xq:()=>h,yK:()=>l,yf:()=>p});var t=r(6317),o=r(1881);async function n(e,a){try{var r;let{user:n}=await (0,o.iF)();if(!n)return console.error("No hay usuario autenticado"),null;let{data:s,error:i}=await t.N.from("colecciones_flashcards").insert([{titulo:e,descripcion:a,user_id:n.id}]).select();if(i)return console.error("Error al crear colecci\xf3n de flashcards:",i),null;return(null==s||null==(r=s[0])?void 0:r.id)||null}catch(e){return console.error("Error al crear colecci\xf3n de flashcards:",e),null}}async function s(){try{let{user:e,error:a}=await (0,o.iF)();if(a)return console.error("Error al obtener usuario:",a),[];if(!e)return console.error("No hay usuario autenticado"),[];let{data:r,error:n}=await t.N.from("colecciones_flashcards").select("*").eq("user_id",e.id).order("creado_en",{ascending:!1});if(n)return console.error("Error al obtener colecciones de flashcards:",n),[];if(!r||0===r.length)return[];return await Promise.all(r.map(async e=>{try{let{data:a,error:r}=await t.N.from("flashcards").select("id").eq("coleccion_id",e.id);if(r)return console.error("Error al contar flashcards para colecci\xf3n",e.id,":",r),{...e,numero_flashcards:0,pendientes_hoy:0};let{data:o,error:n}=await t.N.from("flashcards").select("\n              id,\n              progreso_flashcards!inner(\n                proxima_revision,\n                estado\n              )\n            ").eq("coleccion_id",e.id).lte("progreso_flashcards.proxima_revision",new Date().toISOString()),s=n?0:(null==o?void 0:o.length)||0;return{...e,numero_flashcards:(null==a?void 0:a.length)||0,pendientes_hoy:s}}catch(a){return console.error("Error al procesar colecci\xf3n",e.id,":",a),{...e,numero_flashcards:0,pendientes_hoy:0}}}))}catch(e){return console.error("Error general al obtener colecciones de flashcards:",e),[]}}async function i(e){let{data:a,error:r}=await t.N.from("flashcards").select("*").eq("coleccion_id",e).order("creado_en",{ascending:!0});return r?(console.error("Error al obtener flashcards:",r),[]):a||[]}async function l(e){let{data:a,error:r}=await t.N.from("flashcards").insert(e).select();return r?(console.error("Error al guardar flashcards:",r),null):(null==a?void 0:a.map(e=>e.id))||null}async function c(e){let a=await i(e),{data:r,error:o}=await t.N.from("progreso_flashcards").select("*").in("flashcard_id",a.map(e=>e.id));if(o)return console.error("Error al obtener progreso de flashcards:",o),[];let n=new Date,s=new Date(n.getFullYear(),n.getMonth(),n.getDate());return a.map(e=>{let a=null==r?void 0:r.find(a=>a.flashcard_id===e.id);if(!a)return{...e,debeEstudiar:!0};let t=new Date(a.proxima_revision),o=new Date(t.getFullYear(),t.getMonth(),t.getDate());return{...e,debeEstudiar:o<=s,progreso:{factor_facilidad:a.factor_facilidad,intervalo:a.intervalo,repeticiones:a.repeticiones,estado:a.estado,proxima_revision:a.proxima_revision}}})}async function d(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10;try{let r=await c(e),o=r.map(e=>e.id),{data:n,error:s}=await t.N.from("historial_revisiones").select("flashcard_id, dificultad").in("flashcard_id",o);if(s)return console.error("Error al obtener historial de revisiones:",s),r.slice(0,a);let i=new Map;return null==n||n.forEach(e=>{let a=i.get(e.flashcard_id)||{dificil:0,total:0};a.total++,"dificil"===e.dificultad&&a.dificil++,i.set(e.flashcard_id,a)}),r.map(e=>{let a=i.get(e.id),r=a?a.dificil/a.total:0;return{...e,ratioDificultad:r}}).sort((e,a)=>a.ratioDificultad-e.ratioDificultad).slice(0,a)}catch(e){return console.error("Error al obtener flashcards m\xe1s dif\xedciles:",e),[]}}async function u(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10;try{return[...await c(e)].sort(()=>Math.random()-.5).slice(0,a)}catch(e){return console.error("Error al obtener flashcards aleatorias:",e),[]}}async function m(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10;try{let r=await c(e),o=r.map(e=>e.id),{data:n,error:s}=await t.N.from("historial_revisiones").select("flashcard_id, fecha").in("flashcard_id",o).order("fecha",{ascending:!1});if(s)return console.error("Error al obtener \xfaltimas revisiones:",s),r.slice(0,a);let i=new Map;return null==n||n.forEach(e=>{i.has(e.flashcard_id)||i.set(e.flashcard_id,e.fecha)}),r.map(e=>{let a=i.get(e.id);return{...e,ultimaRevision:new Date(a||0)}}).sort((e,a)=>e.ultimaRevision.getTime()-a.ultimaRevision.getTime()).slice(0,a)}catch(e){return console.error("Error al obtener flashcards no recientes:",e),[]}}async function f(e,a){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:10;try{return(await c(e)).filter(e=>e.progreso?e.progreso.estado===a:"nuevo"===a).slice(0,r)}catch(e){return console.error("Error al obtener flashcards por estado:",e),[]}}async function p(e,a){try{let r=2.5,o=1,n=0,s="nuevo",i=!1,{data:l,error:c}=await t.N.from("progreso_flashcards").select("factor_facilidad, intervalo, repeticiones, estado").eq("flashcard_id",e).single();!c&&l&&(r=l.factor_facilidad||2.5,o=l.intervalo||1,n=l.repeticiones||0,s=l.estado||"nuevo",i=!0);let d=r,u=o,m=n,f=s;"dificil"===a?(d=Math.max(1.3,r-.3),m=0,u=1,f="aprendiendo"):(m++,"normal"===a?d=r-.15:"facil"===a&&(d=r+.1),d=Math.max(1.3,Math.min(2.5,d)),1===m?(u=1,f="aprendiendo"):2===m?(u=6,f="repasando"):f=(u=Math.round(o*d))>30?"aprendido":"repasando");let p=new Date,h=new Date(p);h.setDate(h.getDate()+u);let g=null;if(i){let{error:a}=await t.N.from("progreso_flashcards").update({factor_facilidad:d,intervalo:u,repeticiones:m,estado:f,ultima_revision:p.toISOString(),proxima_revision:h.toISOString()}).eq("flashcard_id",e);g=a}else{let{error:a}=await t.N.from("progreso_flashcards").insert({flashcard_id:e,factor_facilidad:d,intervalo:u,repeticiones:m,estado:f,ultima_revision:p.toISOString(),proxima_revision:h.toISOString()});g=a}if(g)return console.error("Error al guardar progreso:",g),!1;let{error:x}=await t.N.from("historial_revisiones").insert({flashcard_id:e,dificultad:a,factor_facilidad:d,intervalo:u,repeticiones:m,fecha:p.toISOString()});return!0}catch(e){return!1}}async function h(e,a,r){try{let{error:o}=await t.N.from("flashcards").update({pregunta:a,respuesta:r,actualizado_en:new Date().toISOString()}).eq("id",e);if(o)return!1;return!0}catch(e){return!1}}async function g(e){try{let{error:a}=await t.N.from("progreso_flashcards").delete().eq("flashcard_id",e);if(a)return!1;let{error:r}=await t.N.from("historial_revisiones").delete().eq("flashcard_id",e);if(r)return!1;let{error:o,count:n}=await t.N.from("flashcards").delete({count:"exact"}).eq("id",e);if(o||0===n)return!1;return!0}catch(e){return!1}}async function x(e){try{let{user:a}=await (0,o.iF)();if(!a)return!1;let{data:r,error:n}=await t.N.from("flashcards").select("id").eq("coleccion_id",e);if(n)return!1;let s=(null==r?void 0:r.map(e=>e.id))||[];if(s.length>0){let{error:a}=await t.N.from("progreso_flashcards").delete().in("flashcard_id",s);if(a)return!1;let{error:r}=await t.N.from("historial_revisiones").delete().in("flashcard_id",s);if(r)return!1;let{error:o}=await t.N.from("flashcards").delete().eq("coleccion_id",e);if(o)return!1}let{error:i,count:l}=await t.N.from("colecciones_flashcards").delete({count:"exact"}).eq("id",e).eq("user_id",a.id);if(i||0===l)return!1;return!0}catch(e){return!1}}},4794:(e,a,r)=>{r.d(a,{wU:()=>s,yV:()=>n});var t=r(6317),o=r(3796);async function n(e){let a=await (0,o.Q1)(e);if(0===a.length)return{total:0,nuevas:0,aprendiendo:0,repasando:0,aprendidas:0,paraHoy:0};let{data:r,error:n}=await t.N.from("progreso_flashcards").select("*").in("flashcard_id",a.map(e=>e.id));if(n)return console.error("Error al obtener progreso de flashcards:",n),{total:a.length,nuevas:a.length,aprendiendo:0,repasando:0,aprendidas:0,paraHoy:a.length};let s={total:a.length,nuevas:0,aprendiendo:0,repasando:0,aprendidas:0,paraHoy:0},i=new Date;return a.forEach(e=>{let a=null==r?void 0:r.find(a=>a.flashcard_id===e.id);if(a){switch(a.estado){case"nuevo":s.nuevas++;break;case"aprendiendo":s.aprendiendo++;break;case"repasando":s.repasando++;break;case"aprendido":s.aprendidas++}let e=new Date(a.proxima_revision);new Date(e.getFullYear(),e.getMonth(),e.getDate())<=new Date(i.getFullYear(),i.getMonth(),i.getDate())&&s.paraHoy++}else s.nuevas++,s.paraHoy++}),s}async function s(e){try{let a=await (0,o.Q1)(e);if(0===a.length)return{totalSesiones:0,totalRevisiones:0,distribucionDificultad:{dificil:0,normal:0,facil:0},progresoTiempo:[],tarjetasMasDificiles:[]};let r=a.map(e=>e.id),{data:n,error:s}=await t.N.from("historial_revisiones").select("*").in("flashcard_id",r).order("fecha",{ascending:!0});if(s)return console.error("Error al obtener revisiones:",s),null;let{data:i,error:l}=await t.N.from("progreso_flashcards").select("*").in("flashcard_id",r);l&&console.error("Error al obtener progreso:",l);let c={totalSesiones:0,totalRevisiones:n?n.length:0,distribucionDificultad:{dificil:0,normal:0,facil:0},progresoTiempo:[],tarjetasMasDificiles:[]};if(n&&n.length>0){n.forEach(e=>{"dificil"===e.dificultad?c.distribucionDificultad.dificil++:"normal"===e.dificultad?c.distribucionDificultad.normal++:"facil"===e.dificultad&&c.distribucionDificultad.facil++});let e=new Set;n.forEach(a=>{let r=new Date(a.fecha).toISOString().split("T")[0];e.add(r)}),c.totalSesiones=e.size;let r=new Map;a.forEach(e=>{r.set(e.id,{dificil:0,normal:0,facil:0,total:0})}),n.forEach(e=>{let a=r.get(e.flashcard_id);a&&("dificil"===e.dificultad?a.dificil++:"normal"===e.dificultad?a.normal++:"facil"===e.dificultad&&a.facil++,a.total++)}),c.tarjetasMasDificiles=a.map(e=>{let a=r.get(e.id)||{dificil:0,normal:0,facil:0,total:0};return{id:e.id,pregunta:e.pregunta,dificil:a.dificil,normal:a.normal,facil:a.facil,totalRevisiones:a.total}}).filter(e=>e.totalRevisiones>0).sort((e,a)=>{let r=e.totalRevisiones>0?e.dificil/e.totalRevisiones:0;return(a.totalRevisiones>0?a.dificil/a.totalRevisiones:0)-r}).slice(0,10)}return c}catch(e){return console.error("Error al calcular estad\xedsticas detalladas:",e),null}}},5307:(e,a,r)=>{r.d(a,{w:()=>c,x:()=>l});var t=r(6317),o=r(1881),n=r(3796),s=r(7616),i=r(4794);async function l(){try{let{user:e}=await (0,o.iF)();if(!e)throw Error("Usuario no autenticado");let{data:a}=await t.N.from("documentos").select("id").eq("user_id",e.id),r=await (0,n.oE)(),l=await (0,s.Lx)(),c=await (0,s.oC)(),d=0,u=0,m=0,f=0,p=0,h=(await Promise.all(r.map(async e=>{let a=await (0,i.yV)(e.id);return d+=a.total,u+=a.paraHoy,m+=a.nuevas,f+=a.aprendiendo,p+=a.repasando,{id:e.id,titulo:e.titulo,fechaCreacion:e.creado_en,paraHoy:a.paraHoy}}))).sort((e,a)=>new Date(a.fechaCreacion).getTime()-new Date(e.fechaCreacion).getTime()).slice(0,5),g=l.map(e=>({id:e.id,titulo:e.titulo,fechaCreacion:e.creado_en,numeroPreguntas:e.numero_preguntas||0})).slice(0,5);return{totalDocumentos:(null==a?void 0:a.length)||0,totalColeccionesFlashcards:r.length,totalTests:l.length,totalFlashcards:d,flashcardsParaHoy:u,flashcardsNuevas:m,flashcardsAprendiendo:f,flashcardsRepasando:p,testsRealizados:c.totalTests,porcentajeAcierto:c.porcentajeAcierto,coleccionesRecientes:h,testsRecientes:g}}catch(e){return console.error("Error al obtener estad\xedsticas del dashboard:",e),{totalDocumentos:0,totalColeccionesFlashcards:0,totalTests:0,totalFlashcards:0,flashcardsParaHoy:0,flashcardsNuevas:0,flashcardsAprendiendo:0,flashcardsRepasando:0,testsRealizados:0,porcentajeAcierto:0,coleccionesRecientes:[],testsRecientes:[]}}}async function c(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10;try{let{user:a}=await (0,o.iF)();if(!a)return[];let r=await (0,n.oE)();if(0===r.length)return[];let s=new Date;s.setHours(23,59,59,999);let{data:i,error:l}=await t.N.from("progreso_flashcards").select("flashcard_id, proxima_revision, estado").lte("proxima_revision",s.toISOString()).order("proxima_revision",{ascending:!0}).limit(e);if(l)return console.error("Error al obtener progreso de flashcards:",l),[];if(!i||0===i.length)return[];let c=i.map(e=>e.flashcard_id),{data:d,error:u}=await t.N.from("flashcards").select("id, pregunta, coleccion_id").in("id",c);if(u)return console.error("Error al obtener flashcards:",u),[];let m=[];for(let e of i){let a=null==d?void 0:d.find(a=>a.id===e.flashcard_id);if(a){let t=r.find(e=>e.id===a.coleccion_id);t&&m.push({id:a.id,pregunta:a.pregunta,coleccionTitulo:t.titulo,coleccionId:t.id,proximaRevision:e.proxima_revision,estado:e.estado||"nuevo"})}}return m}catch(e){return console.error("Error al obtener pr\xf3ximas flashcards:",e),[]}}},5759:(e,a,r)=>{r.d(a,{C9:()=>u,CM:()=>l,QE:()=>d,Sl:()=>c,Yp:()=>o,fW:()=>s,sj:()=>n,sq:()=>m,vW:()=>i});var t=r(6317);async function o(e){let a=arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{var r;let{data:{user:o}}=await t.N.auth.getUser();if(!o)return console.error("No hay usuario autenticado para crear conversaci\xf3n"),null;a&&await l();let{data:n,error:s}=await t.N.from("conversaciones").insert([{titulo:e,activa:a,user_id:o.id}]).select();if(s)return console.error("Error al crear conversaci\xf3n:",s),null;return(null==n||null==(r=n[0])?void 0:r.id)||null}catch(e){return console.error("Error inesperado al crear conversaci\xf3n:",e),null}}async function n(){try{let{data:{user:e}}=await t.N.auth.getUser();if(!e)return console.error("No hay usuario autenticado para obtener conversaciones"),[];let{data:a,error:r}=await t.N.from("conversaciones").select("*").eq("user_id",e.id).order("actualizado_en",{ascending:!1});if(r)return console.error("Error al obtener conversaciones:",r),[];return a||[]}catch(e){return console.error("Error inesperado al obtener conversaciones:",e),[]}}async function s(e,a){let{error:r}=await t.N.from("conversaciones").update({titulo:a,actualizado_en:new Date().toISOString()}).eq("id",e);return!r||(console.error("Error al actualizar conversaci\xf3n:",r),!1)}async function i(e){try{await l();let{error:a}=await t.N.from("conversaciones").update({activa:!0,actualizado_en:new Date().toISOString()}).eq("id",e);if(a)return console.error("Error al activar conversaci\xf3n:",a),!1;return!0}catch(e){return console.error("Error inesperado al activar conversaci\xf3n:",e),!1}}async function l(){try{let{data:{user:e}}=await t.N.auth.getUser();if(!e)return console.error("No hay usuario autenticado para desactivar conversaciones"),!1;let{error:a}=await t.N.from("conversaciones").update({activa:!1}).eq("user_id",e.id).eq("activa",!0);if(a)return console.error("Error al desactivar todas las conversaciones:",a),!1;return!0}catch(e){return console.error("Error inesperado al desactivar conversaciones:",e),!1}}async function c(){try{let{data:{user:e}}=await t.N.auth.getUser();if(!e)return console.warn("No hay usuario autenticado para obtener conversaci\xf3n activa"),null;let{data:a,error:r}=await t.N.from("conversaciones").select("*").eq("user_id",e.id);r&&console.error("Error al obtener todas las conversaciones:",r);let{data:o,error:n}=await t.N.from("conversaciones").select("*").eq("user_id",e.id).eq("activa",!0).limit(1);if(n){if("406"===n.code||n.message.includes("406"))return null;return console.error("Error al obtener conversaci\xf3n activa:",n),null}return o&&o.length>0?o[0]:null}catch(e){return console.error("Error inesperado al obtener conversaci\xf3n activa:",e),null}}async function d(e){try{var a;let{data:r,error:o}=await t.N.from("conversaciones").select("id").eq("id",e.conversacion_id).single();if(o)return console.error("Error al verificar la conversaci\xf3n:",o),null;let{data:n,error:s}=await t.N.from("mensajes").insert([e]).select();if(s)return console.error("Error al guardar mensaje:",s),null;let{error:i}=await t.N.from("conversaciones").update({actualizado_en:new Date().toISOString()}).eq("id",e.conversacion_id);return i&&console.error("Error al actualizar la fecha de la conversaci\xf3n:",i),(null==n||null==(a=n[0])?void 0:a.id)||null}catch(e){return console.error("Error inesperado al guardar mensaje:",e),null}}async function u(e){let{data:a,error:r}=await t.N.from("mensajes").select("*").eq("conversacion_id",e).order("timestamp",{ascending:!0});return r?(console.error("Error al obtener mensajes:",r),[]):a||[]}async function m(e){try{let{data:{user:a}}=await t.N.auth.getUser();if(!a)return console.error("No hay usuario autenticado para eliminar conversaci\xf3n"),!1;let{error:r}=await t.N.from("mensajes").delete().eq("conversacion_id",e);if(r)return console.error("Error al eliminar mensajes de la conversaci\xf3n:",r),!1;let{error:o,count:n}=await t.N.from("conversaciones").delete({count:"exact"}).eq("id",e).eq("user_id",a.id);if(o)return console.error("Error al eliminar conversaci\xf3n:",o),!1;if(0===n)return!1;return!0}catch(e){return console.error("Error inesperado al eliminar conversaci\xf3n:",e),!1}}},5967:(e,a,r)=>{r.d(a,{A:()=>i});var t=r(5155);r(2115);var o=r(6874),n=r.n(o),s=r(351);function i(e){let{feature:a,featureDescription:r,benefits:o=[],className:i=""}=e,l=o.length>0?o:["Acceso ilimitado a todas las funcionalidades","Generaci\xf3n de contenido sin l\xedmites","Soporte prioritario","Nuevas funcionalidades en primicia"],c={ai_tutor_chat:{name:"Chat con IA",description:"Conversa con tu preparador personal de IA para resolver dudas y obtener explicaciones detalladas."},study_planning:{name:"Planificaci\xf3n de Estudios",description:"Crea planes de estudio personalizados con IA que se adaptan a tu ritmo y objetivos."},summary_a1_a2:{name:"Res\xfamenes Avanzados",description:"Genera res\xfamenes inteligentes y estructurados de tus documentos de estudio."}}[a]||{name:a,description:r||"Esta funcionalidad avanzada te ayudar\xe1 a mejorar tu preparaci\xf3n."};return(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center p-4 ".concat(i),children:(0,t.jsx)("div",{className:"max-w-2xl w-full",children:(0,t.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl overflow-hidden",children:[(0,t.jsxs)("div",{className:"bg-gradient-to-r from-blue-600 to-purple-600 px-8 py-12 text-center text-white",children:[(0,t.jsx)("div",{className:"w-20 h-20 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,t.jsx)(s.F5$,{className:"w-10 h-10"})}),(0,t.jsx)("h1",{className:"text-3xl font-bold mb-4",children:c.name}),(0,t.jsx)("p",{className:"text-blue-100 text-lg leading-relaxed",children:c.description})]}),(0,t.jsxs)("div",{className:"px-8 py-8",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsxs)("div",{className:"inline-flex items-center px-4 py-2 bg-yellow-100 text-yellow-800 rounded-full text-sm font-medium mb-6",children:[(0,t.jsx)(s.usP,{className:"w-4 h-4 mr-2"}),"Funcionalidad Premium"]}),(0,t.jsx)("h2",{className:"text-2xl font-semibold text-gray-900 mb-4",children:"Actualiza tu plan para acceder"}),(0,t.jsx)("p",{className:"text-gray-600 text-lg",children:"Esta funcionalidad est\xe1 disponible para usuarios con planes de pago. Actualiza ahora y desbloquea todo el potencial de OposiAI."})]}),(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4 text-center",children:"\xbfQu\xe9 obtienes al actualizar?"}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:l.map((e,a)=>(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)("div",{className:"w-6 h-6 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5",children:(0,t.jsx)(s.YrT,{className:"w-4 h-4 text-green-600"})}),(0,t.jsx)("span",{className:"text-gray-700",children:e})]},a))})]}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,t.jsxs)(n(),{href:"/upgrade-plan",className:"inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5",children:[(0,t.jsx)(s.ei4,{className:"w-5 h-5 mr-2"}),"Actualizar Plan"]}),(0,t.jsx)(n(),{href:"/app",className:"inline-flex items-center justify-center px-8 py-4 border-2 border-gray-300 text-gray-700 font-semibold rounded-xl hover:border-gray-400 hover:bg-gray-50 transition-colors",children:"Volver al Dashboard"})]}),(0,t.jsx)("div",{className:"mt-8 text-center",children:(0,t.jsxs)("p",{className:"text-sm text-gray-500",children:["\xbfTienes preguntas? ",(0,t.jsx)("a",{href:"mailto:<EMAIL>",className:"text-blue-600 hover:underline",children:"Cont\xe1ctanos"})]})})]})]})})})}},7616:(e,a,r)=>{r.d(a,{Gl:()=>d,HE:()=>h,Kj:()=>l,Lx:()=>s,OA:()=>c,_4:()=>n,aN:()=>p,dd:()=>m,hg:()=>i,oC:()=>u,xD:()=>f});var t=r(6317),o=r(1881);async function n(e,a,r){try{var n,s;console.log("\uD83D\uDCDD Creando nuevo test:",e);let{user:i}=await (0,o.iF)();if(!i)return console.error("❌ No hay usuario autenticado para crear test"),null;console.log("\uD83D\uDC64 Usuario autenticado:",i.id);let{data:l,error:c}=await t.N.from("tests").insert([{titulo:e,descripcion:a,documentos_ids:r,user_id:i.id}]).select();if(c)return console.error("❌ Error al crear test:",c),null;return console.log("✅ Test creado exitosamente:",null==l||null==(n=l[0])?void 0:n.id),(null==l||null==(s=l[0])?void 0:s.id)||null}catch(e){return console.error("\uD83D\uDCA5 Error inesperado al crear test:",e),null}}async function s(){try{let{user:e}=await (0,o.iF)();if(!e)return console.error("No hay usuario autenticado"),[];let{data:a,error:r}=await t.N.from("tests").select("*").eq("user_id",e.id).order("creado_en",{ascending:!1});if(r)return console.error("Error al obtener tests:",r),[];return a||[]}catch(e){return console.error("Error al obtener tests:",e),[]}}async function i(e){let{data:a,error:r}=await t.N.from("preguntas_test").select("*").eq("test_id",e);return r?(console.error("Error al obtener preguntas de test:",r),[]):a||[]}async function l(e){let{count:a,error:r}=await t.N.from("preguntas_test").select("*",{count:"exact",head:!0}).eq("test_id",e);return r?(console.error("Error al obtener conteo de preguntas:",r),0):a||0}async function c(e){let{error:a}=await t.N.from("preguntas_test").insert(e);return!a||(console.error("Error al guardar preguntas de test:",a),!1)}async function d(e,a,r,o){let{error:n}=await t.N.from("estadisticas_test").insert([{test_id:e,pregunta_id:a,respuesta_seleccionada:r,es_correcta:o,fecha_respuesta:new Date().toISOString()}]);return!n||(console.error("Error al registrar respuesta de test:",n),!1)}async function u(){let{data:e,error:a}=await t.N.from("estadisticas_test").select("*");if(a)return console.error("Error al obtener estad\xedsticas de tests:",a),{totalTests:0,totalPreguntas:0,totalRespuestasCorrectas:0,totalRespuestasIncorrectas:0,porcentajeAcierto:0};let r=new Set((null==e?void 0:e.map(e=>e.test_id))||[]),o=new Set((null==e?void 0:e.map(e=>e.pregunta_id))||[]),n=(null==e?void 0:e.filter(e=>e.es_correcta).length)||0,s=((null==e?void 0:e.length)||0)-n,i=e&&e.length>0?Math.round(n/e.length*100):0;return{totalTests:r.size,totalPreguntas:o.size,totalRespuestasCorrectas:n,totalRespuestasIncorrectas:s,porcentajeAcierto:i}}async function m(e){let{data:a,error:r}=await t.N.from("estadisticas_test").select("*").eq("test_id",e);if(r)return console.error("Error al obtener estad\xedsticas del test:",r),{testId:e,totalPreguntas:0,totalCorrectas:0,totalIncorrectas:0,porcentajeAcierto:0,fechasRealizacion:[],preguntasMasFalladas:[]};let{data:o}=await t.N.from("preguntas_test").select("*").eq("test_id",e),n=(null==a?void 0:a.filter(e=>e.es_correcta).length)||0,s=((null==a?void 0:a.length)||0)-n,i=a&&a.length>0?Math.round(n/a.length*100):0,l=new Set;null==a||a.forEach(e=>{let a=new Date(e.fecha_respuesta);l.add("".concat(a.getDate(),"/").concat(a.getMonth()+1,"/").concat(a.getFullYear()))});let c=Array.from(l),d=new Map;null==a||a.forEach(e=>{let a=d.get(e.pregunta_id)||{fallos:0,aciertos:0};e.es_correcta?a.aciertos++:a.fallos++,d.set(e.pregunta_id,a)});let u=Array.from(d.entries()).map(e=>{var a;let[r,t]=e;return{preguntaId:r,totalFallos:t.fallos,totalAciertos:t.aciertos,pregunta:(null==o||null==(a=o.find(e=>e.id===r))?void 0:a.pregunta)||"Desconocida"}}).sort((e,a)=>a.totalFallos-e.totalFallos).slice(0,5);return{testId:e,totalPreguntas:(null==o?void 0:o.length)||0,totalCorrectas:n,totalIncorrectas:s,porcentajeAcierto:i,fechasRealizacion:c,preguntasMasFalladas:u}}async function f(e,a,r){try{let{user:n}=await (0,o.iF)();if(!n)return console.error("No hay usuario autenticado"),null;let{data:s,error:i}=await t.N.from("tests").update({titulo:a,descripcion:r,actualizado_en:new Date().toISOString()}).eq("id",e).eq("user_id",n.id).select().single();if(i)return console.error("Error al actualizar test:",i),null;return s}catch(e){return console.error("Error al actualizar test:",e),null}}async function p(e){try{let{user:a}=await (0,o.iF)();if(!a)return console.error("No hay usuario autenticado"),!1;await t.N.from("estadisticas_test").delete().eq("test_id",e),await t.N.from("preguntas_test").delete().eq("test_id",e);let{error:r}=await t.N.from("tests").delete().eq("id",e).eq("user_id",a.id);if(r)return console.error("Error al eliminar test:",r),!1;return!0}catch(e){return console.error("Error al eliminar test:",e),!1}}async function h(e){try{let a=[];for(let r of e)if(r.cantidad>0){let e=(await i(r.testId)).sort(()=>Math.random()-.5).slice(0,r.cantidad);a.push(...e)}return a.sort(()=>Math.random()-.5)}catch(e){return console.error("Error al obtener preguntas de repaso:",e),[]}}},7634:(e,a,r)=>{r.d(a,{B$:()=>m,Il:()=>p,Se:()=>d,cN:()=>f,cm:()=>l,jg:()=>s,oS:()=>u,r5:()=>i,sW:()=>c,xv:()=>h,yr:()=>n});var t=r(2643),o=r(1881);async function n(){try{let{user:r,error:n}=await (0,o.iF)();if(!r||n)return!1;let{data:s,error:i}=await t.N.from("temarios").select("id").eq("user_id",r.id).limit(1);if(i){var e,a;if("PGRST116"===i.code||(null==(e=i.message)?void 0:e.includes("relation"))||(null==(a=i.message)?void 0:a.includes("does not exist")))return!1;return console.error("Error al verificar temario en Supabase:",i),!1}return s&&s.length>0}catch(e){return console.error("Error general al verificar temario:",e),!1}}async function s(){try{let{user:e,error:a}=await (0,o.iF)();if(!e||a)return null;let{data:r,error:n}=await t.N.from("temarios").select("*").eq("user_id",e.id).single();if(n){if("PGRST116"===n.code)return null;return console.error("Error al obtener temario en Supabase:",n),null}return r}catch(e){return console.error("Error general al obtener temario:",e),null}}async function i(e,a,r){try{let{user:n,error:s}=await (0,o.iF)();if(!n||s)return console.error("No hay usuario autenticado o error:",s),null;let{data:i,error:l}=await t.N.from("temarios").insert([{titulo:e,descripcion:a,tipo:r,user_id:n.id}]).select().single();if(l)return console.error("Error al crear temario:",l),null;return i.id}catch(e){return console.error("Error al crear temario:",e),null}}async function l(e){try{let{data:a,error:r}=await t.N.from("temas").select("*").eq("temario_id",e).order("orden",{ascending:!0});if(r)return console.error("Error al obtener temas:",r),[];return a||[]}catch(e){return console.error("Error al obtener temas:",e),[]}}async function c(e,a){try{let r=a.map(a=>({...a,temario_id:e})),{error:o}=await t.N.from("temas").insert(r);if(o)return console.error("Error al crear temas:",o),!1;return!0}catch(e){return console.error("Error al crear temas:",e),!1}}async function d(e,a,r){try{let{error:o}=await t.N.from("temarios").update({titulo:a,descripcion:r,actualizado_en:new Date().toISOString()}).eq("id",e);if(o)return console.error("Error al actualizar temario:",o),!1;return!0}catch(e){return console.error("Error al actualizar temario:",e),!1}}async function u(e,a,r){try{let{error:o}=await t.N.from("temas").update({titulo:a,descripcion:r,actualizado_en:new Date().toISOString()}).eq("id",e);if(o)return console.error("Error al actualizar tema:",o),!1;return!0}catch(e){return console.error("Error al actualizar tema:",e),!1}}async function m(e){try{let{error:a}=await t.N.from("temas").delete().eq("id",e);if(a)return console.error("Error al eliminar tema:",a),!1;return!0}catch(e){return console.error("Error al eliminar tema:",e),!1}}async function f(e,a){try{let r={completado:a,actualizado_en:new Date().toISOString()};a?r.fecha_completado=new Date().toISOString():r.fecha_completado=null;let{error:o}=await t.N.from("temas").update(r).eq("id",e);if(o)return console.error("Error al actualizar estado del tema:",o),!1;return!0}catch(e){return console.error("Error al actualizar estado del tema:",e),!1}}async function p(e){try{let{data:a,error:r}=await t.N.from("temas").select("completado").eq("temario_id",e);if(r)return console.error("Error al obtener estad\xedsticas del temario:",r),null;let o=a.length,n=a.filter(e=>e.completado).length;return{totalTemas:o,temasCompletados:n,porcentajeCompletado:o>0?n/o*100:0}}catch(e){return console.error("Error al obtener estad\xedsticas del temario:",e),null}}async function h(e){try{let{error:a}=await t.N.from("temarios").delete().eq("id",e);if(a)return console.error("Error al eliminar temario:",a),!1;return!0}catch(e){return console.error("Error al eliminar temario:",e),!1}}}}]);