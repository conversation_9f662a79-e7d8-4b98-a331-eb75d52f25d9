"use strict";exports.id=5658,exports.ids=[5658],exports.modules={8814:(e,r,a)=>{a.d(r,{IE:()=>i,Nu:()=>n,qo:()=>t,t4:()=>s});let t={free:{id:"free",name:"Plan Gratis",price:0,limits:{documents:1,mindMapsForTrial:2,testsForTrial:10,flashcardsForTrial:10,tokensForTrial:5e4,features:["document_upload","test_generation","flashcard_generation","mind_map_generation"]},features:["document_upload","test_generation","flashcard_generation","mind_map_generation"],restrictedFeatures:["study_planning","ai_tutor_chat","summary_a1_a2"]},usuario:{id:"usuario",name:"Plan Usuario",price:1e3,limits:{documents:-1,mindMapsPerWeek:-1,testsPerWeek:-1,flashcardsPerWeek:-1,monthlyTokens:1e6,features:["document_upload","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation"]},features:["document_upload","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation"],restrictedFeatures:["study_planning","summary_a1_a2"]},pro:{id:"pro",name:"Plan Pro",price:1500,limits:{documents:-1,mindMapsPerWeek:-1,testsPerWeek:-1,flashcardsPerWeek:-1,monthlyTokens:1e6,features:["document_upload","study_planning","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation","summary_a1_a2"]},features:["document_upload","study_planning","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation","summary_a1_a2"],restrictedFeatures:[]}};function i(e){return t[e]||null}function s(e){let r=i(e);return r?"free"===e?r.limits.tokensForTrial||5e4:r.limits.monthlyTokens||1e6:5e4}function n(e,r){let a=i(e);return!(!a||a.restrictedFeatures.includes(r))&&a.features.includes(r)}},55658:(e,r,a)=>{a.d(r,{G:()=>s});var t=a(42049),i=a(8814);class s{static async createUserWithPlan(e){try{let r;console.log("\uD83D\uDE80 Iniciando creaci\xf3n de usuario:",e.email);let a=(0,i.IE)(e.planId);if(!a)throw Error(`Plan inv\xe1lido: ${e.planId}`);let s=await t.SupabaseAdminService.getTransactionBySessionId(e.stripeSessionId);if(s)return console.log("⚠️ Transacci\xf3n ya existe:",s.id),{success:!1,error:"Transacci\xf3n ya procesada"};let n=await t.SupabaseAdminService.createStripeTransaction({stripe_session_id:e.stripeSessionId,stripe_customer_id:e.stripeCustomerId,user_email:e.email,user_name:e.name,plan_id:e.planId,amount:e.amount,currency:e.currency,payment_status:"paid",subscription_id:e.subscriptionId,metadata:{created_by:"webhook",plan_name:a.name}});console.log("✅ Transacci\xf3n creada:",n.id);let o=null;if(e.password&&e.sendConfirmationEmail){console.log("\uD83C\uDD95 Creando cuenta con contrase\xf1a espec\xedfica y email de confirmaci\xf3n");try{r=(await t.SupabaseAdminService.createUserWithPassword(e.email,e.password,{name:e.name,plan:e.planId,stripe_session_id:e.stripeSessionId,stripe_customer_id:e.stripeCustomerId,transaction_id:n.id,payment_verified:!0})).user.id,console.log("✅ Usuario creado con contrase\xf1a y email de confirmaci\xf3n:",r)}catch(e){throw console.error("Error creando usuario con contrase\xf1a:",e),e}}else{let a={name:e.name,plan:e.planId,stripe_session_id:e.stripeSessionId,stripe_customer_id:e.stripeCustomerId,transaction_id:n.id,payment_verified:!0};try{r=(await t.SupabaseAdminService.createUserWithInvitation(e.email,a)).user.id,console.log("✅ Invitaci\xf3n de usuario creada:",r)}catch(a){if(a.message&&(a.message.includes("A user with this email address has already been registered")||a.message.includes("email_exists")||a.message.includes("already been registered")||a.message.includes("User already registered"))){console.log("⚠️ Usuario ya existe, actualizando plan existente");let a=await t.SupabaseAdminService.getUserByEmail(e.email);if(!a)throw Error("Error obteniendo usuario existente.");r=a.id}else throw a}}let c=new Date().toISOString().slice(0,7)+"-01",d=!!e.subscriptionId,l=await t.SupabaseAdminService.getUserProfile(r),u={subscription_plan:e.planId,monthly_token_limit:(0,i.t4)(e.planId),current_month_tokens:0,current_month:c,payment_verified:!0,stripe_customer_id:e.stripeCustomerId,stripe_subscription_id:e.subscriptionId,last_payment_date:new Date().toISOString(),auto_renew:d,plan_expires_at:e.planExpiresAt||(d?null:this.calculatePlanExpiration(e.planId)),plan_features:a.features,security_flags:{created_via_webhook:!0,payment_method:"stripe",activation_date:new Date().toISOString(),subscription_type:d?"recurring":"one_time",...l?.security_flags||{}}},{data:p,error:_}=await t.E.rpc("create_user_profile_and_history",{p_user_id:r,p_transaction_id:n.id,p_profile_data:u}).single();if(_)throw console.error("Error al ejecutar la funci\xf3n create_user_profile_and_history:",_),Error(`Error en la creaci\xf3n at\xf3mica del perfil: ${_.message}`);let m=p.created_profile_id;return console.log("✅ Perfil y historial creados at\xf3micamente. Profile ID:",m),await t.SupabaseAdminService.updateTransactionWithUser(n.id,r),await t.SupabaseAdminService.activateTransaction(n.id),console.log("✅ Usuario procesado exitosamente:",r),{success:!0,userId:r,profileId:m,transactionId:n.id}}catch(e){return console.error("❌ Error creando usuario:",e),{success:!1,error:e instanceof Error?e.message:"Error desconocido"}}}static async updateUserPlan(e,r,a,s="Plan upgrade/downgrade"){try{console.log("\uD83D\uDD04 Actualizando plan de usuario:",e,"a",r);let n=await t.SupabaseAdminService.getUserProfile(e);if(!n)throw Error("Usuario no encontrado");let o=(0,i.IE)(r);if(!o)throw Error(`Plan inv\xe1lido: ${r}`);let c={...n,subscription_plan:r,monthly_token_limit:(0,i.t4)(r),last_payment_date:new Date().toISOString(),payment_verified:!0,plan_features:o.features,updated_at:new Date().toISOString()},d=await t.SupabaseAdminService.upsertUserProfile(c);return await t.SupabaseAdminService.logPlanChange({user_id:e,old_plan:n.subscription_plan,new_plan:r,changed_by:"system",reason:s,transaction_id:a}),console.log("✅ Plan actualizado exitosamente"),{success:!0,userId:e,profileId:d.id}}catch(e){return console.error("❌ Error actualizando plan:",e),{success:!1,error:e instanceof Error?e.message:"Error desconocido"}}}static async verifyUserPaymentStatus(e){try{let r=await t.SupabaseAdminService.getUserProfile(e);if(!r)return{verified:!1,plan:"none"};return{verified:r.payment_verified,plan:r.subscription_plan,expiresAt:r.plan_expires_at||void 0,lastPayment:r.last_payment_date||void 0}}catch(e){return console.error("Error verificando estado de pago:",e),{verified:!1,plan:"error"}}}static async getUserStats(){try{return{total:0,byPlan:{free:0,usuario:0,pro:0},verified:0,unverified:0}}catch(e){throw console.error("Error obteniendo estad\xedsticas:",e),e}}static calculatePlanExpiration(e,r=!1){let a=new Date;switch(e){case"free":a.setDate(a.getDate()+5);break;case"usuario":r?a.setDate(a.getDate()+7):a.setDate(a.getDate()+30);break;case"pro":r?a.setDate(a.getDate()+14):a.setDate(a.getDate()+30);break;default:a.setDate(a.getDate()+30)}return a.toISOString()}static async handleSubscriptionCancellation(e,r,a,i="Subscription cancelled"){try{let s;console.log("\uD83D\uDD04 Manejando cancelaci\xf3n de suscripci\xf3n:",e,"plan:",r);let n=await t.SupabaseAdminService.getUserProfile(e);if(!n)throw Error("Usuario no encontrado");let o="free";a?(s=a,o=r):"usuario"===r||"pro"===r?(s=this.calculatePlanExpiration(r,!0),o=r):(s=new Date().toISOString(),o="free");let c={...n,subscription_plan:o,plan_expires_at:s,auto_renew:!1,stripe_subscription_id:void 0,updated_at:new Date().toISOString(),security_flags:{...n.security_flags,subscription_cancelled:!0,cancellation_date:new Date().toISOString(),grace_period_until:s}},d=await t.SupabaseAdminService.upsertUserProfile(c);return await t.SupabaseAdminService.logPlanChange({user_id:e,old_plan:n.subscription_plan,new_plan:o,changed_by:"system",reason:`${i} - Grace period until ${s}`}),console.log("✅ Cancelaci\xf3n de suscripci\xf3n procesada con per\xedodo de gracia"),{success:!0,userId:e,profileId:d.id}}catch(e){return console.error("❌ Error manejando cancelaci\xf3n de suscripci\xf3n:",e),{success:!1,error:e instanceof Error?e.message:"Error desconocido"}}}static async processExpiredGracePeriods(){try{console.log("\uD83D\uDD0D Buscando usuarios con per\xedodo de gracia expirado...");let e=new Date().toISOString(),{data:r,error:a}=await t.E.from("user_profiles").select("user_id, subscription_plan, plan_expires_at, security_flags").lt("plan_expires_at",e).eq("auto_renew",!1).neq("subscription_plan","free").limit(100);if(a)throw Error(`Error buscando usuarios expirados: ${a.message}`);if(!r||0===r.length)return console.log("✅ No se encontraron usuarios con per\xedodo de gracia expirado"),{processed:0,errors:[]};console.log(`📋 Encontrados ${r.length} usuarios con per\xedodo de gracia expirado`);let i=[],s=0;for(let e of r)try{if(e.security_flags?.subscription_cancelled!==!0){console.log(`⚠️ Usuario ${e.user_id} expirado pero no en per\xedodo de gracia, omitiendo`);continue}console.log(`⬇️ Degradando usuario ${e.user_id} de ${e.subscription_plan} a free`);let r=await this.updateUserPlan(e.user_id,"free",void 0,`Grace period expired - was ${e.subscription_plan}`);if(r.success)s++,console.log(`✅ Usuario ${e.user_id} degradado exitosamente`);else{let a=`Error degradando usuario ${e.user_id}: ${r.error}`;console.error(a),i.push(a)}}catch(a){let r=`Error procesando usuario ${e.user_id}: ${a instanceof Error?a.message:"Unknown error"}`;console.error(r),i.push(r)}return console.log(`🎯 Procesamiento completado: ${s} usuarios degradados, ${i.length} errores`),{processed:s,errors:i}}catch(e){throw console.error("❌ Error en processExpiredGracePeriods:",e),e}}}}};