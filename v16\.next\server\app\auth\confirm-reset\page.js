(()=>{var e={};e.id=308,e.ids=[308],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29817:(e,t,r)=>{Promise.resolve().then(r.bind(r,30764))},30764:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\auth\\\\confirm-reset\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\auth\\confirm-reset\\page.tsx","default")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},35534:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var s=r(60687),a=r(43210),n=r(16189),o=r(17019);function i(){let e=(0,n.useRouter)();(0,n.useSearchParams)();let[t,r]=(0,a.useState)(null),[i,l]=(0,a.useState)(null),[c,u]=(0,a.useState)(!1);return i?(0,s.jsx)("div",{className:"min-h-screen bg-red-50 flex flex-col justify-center items-center p-4",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-xl p-8 max-w-md w-full text-center",children:[(0,s.jsx)(o.eHT,{className:"w-12 h-12 text-red-500 mx-auto mb-4"}),(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-800 mb-2",children:"Enlace Inv\xe1lido"}),(0,s.jsx)("p",{className:"text-gray-600 mb-6",children:i}),(0,s.jsx)("button",{onClick:()=>e.push("/login"),className:"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors",children:"Volver a Inicio de Sesi\xf3n"})]})}):t||i?(0,s.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex flex-col justify-center items-center p-4",children:[(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-xl p-8 max-w-md w-full text-center",children:[(0,s.jsx)(o.pcC,{className:"w-16 h-16 text-blue-600 mx-auto mb-6"}),(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-800 mb-4",children:"Confirmar Restablecimiento de Contrase\xf1a"}),(0,s.jsx)("p",{className:"text-gray-600 mb-8",children:"Est\xe1s a un paso de restablecer tu contrase\xf1a. Haz clic en el bot\xf3n de abajo para continuar."}),(0,s.jsxs)("button",{onClick:()=>{if(t&&!c){u(!0);let e=`http://localhost:3000/auth/reset-password#access_token=${t}&type=recovery`;window.location.href=e}else t||l("No se puede proceder sin un token de recuperaci\xf3n v\xe1lido.")},disabled:c||!t,className:"w-full bg-blue-600 text-white py-3 px-6 rounded-md hover:bg-blue-700 transition-colors font-semibold text-lg flex items-center justify-center disabled:opacity-50",children:[c?(0,s.jsx)(o.TwU,{className:"animate-spin mr-2"}):(0,s.jsx)(o.dyV,{className:"mr-2"}),c?"Redirigiendo...":"Continuar al Restablecimiento"]}),c&&(0,s.jsx)("p",{className:"text-sm text-gray-500 mt-4",children:"Si no eres redirigido autom\xe1ticamente, por favor verifica que las ventanas emergentes no est\xe9n bloqueadas."})]}),(0,s.jsx)("p",{className:"text-sm text-gray-500 mt-8",children:"Este paso adicional ayuda a proteger tu cuenta."})]}):(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center items-center p-4",children:[(0,s.jsx)(o.TwU,{className:"w-12 h-12 text-blue-600 mx-auto mb-4 animate-spin"}),(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-800",children:"Verificando enlace..."})]})}function l(){return(0,s.jsx)(a.Suspense,{fallback:(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center items-center p-4",children:[(0,s.jsx)(o.TwU,{className:"w-12 h-12 text-blue-600 mx-auto mb-4 animate-spin"}),(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-800",children:"Cargando..."})]}),children:(0,s.jsx)(i,{})})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66265:(e,t,r)=>{Promise.resolve().then(r.bind(r,35534))},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},93451:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>d,pages:()=>u,routeModule:()=>p,tree:()=>c});var s=r(65239),a=r(48088),n=r(88170),o=r.n(n),i=r(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let c={children:["",{children:["auth",{children:["confirm-reset",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,30764)),"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\auth\\confirm-reset\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,u=["C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\auth\\confirm-reset\\page.tsx"],d={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/auth/confirm-reset/page",pathname:"/auth/confirm-reset",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,6126,7019,1191],()=>r(93451));module.exports=s})();