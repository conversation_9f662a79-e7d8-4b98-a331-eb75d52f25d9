exports.id=7902,exports.ids=[7902],exports.modules={8814:(e,r,t)=>{"use strict";t.d(r,{IE:()=>s,Nu:()=>o,qo:()=>a,t4:()=>i});let a={free:{id:"free",name:"Plan Gratis",price:0,limits:{documents:1,mindMapsForTrial:2,testsForTrial:10,flashcardsForTrial:10,tokensForTrial:5e4,features:["document_upload","test_generation","flashcard_generation","mind_map_generation"]},features:["document_upload","test_generation","flashcard_generation","mind_map_generation"],restrictedFeatures:["study_planning","ai_tutor_chat","summary_a1_a2"]},usuario:{id:"usuario",name:"Plan Usuario",price:1e3,limits:{documents:-1,mindMapsPerWeek:-1,testsPerWeek:-1,flashcardsPerWeek:-1,monthlyTokens:1e6,features:["document_upload","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation"]},features:["document_upload","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation"],restrictedFeatures:["study_planning","summary_a1_a2"]},pro:{id:"pro",name:"Plan Pro",price:1500,limits:{documents:-1,mindMapsPerWeek:-1,testsPerWeek:-1,flashcardsPerWeek:-1,monthlyTokens:1e6,features:["document_upload","study_planning","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation","summary_a1_a2"]},features:["document_upload","study_planning","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation","summary_a1_a2"],restrictedFeatures:[]}};function s(e){return a[e]||null}function i(e){let r=s(e);return r?"free"===e?r.limits.tokensForTrial||5e4:r.limits.monthlyTokens||1e6:5e4}function o(e,r){let t=s(e);return!(!t||t.restrictedFeatures.includes(r))&&t.features.includes(r)}},42049:(e,r,t)=>{"use strict";t.d(r,{E:()=>a,SupabaseAdminService:()=>s});let a=(0,t(86345).UU)("https://fxnhpxjijinfuxxxplzj.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY,{auth:{autoRefreshToken:!1,persistSession:!1}});class s{static async createStripeTransaction(e){let{data:r,error:t}=await a.from("stripe_transactions").insert([e]).select().single();if(t)throw console.error("Error creating stripe transaction:",t),Error(`Failed to create transaction: ${t.message}`);return r}static async getTransactionBySessionId(e){let{data:r,error:t}=await a.from("stripe_transactions").select("*").eq("stripe_session_id",e).single();if(t&&"PGRST116"!==t.code)throw console.error("Error fetching transaction:",t),Error(`Failed to fetch transaction: ${t.message}`);return r}static async createUserWithInvitation(e,r){console.log("\uD83D\uDD04 [SUPABASE_ADMIN] Creating user invitation:",{email:e,userData:r,redirectTo:"http://localhost:3000/auth/callback",timestamp:new Date().toISOString()});let{data:t,error:s}=await a.auth.admin.inviteUserByEmail(e,{data:r,redirectTo:"http://localhost:3000/auth/callback"});if(console.log("\uD83D\uDCCA [SUPABASE_ADMIN] Invitation result:",{hasData:!!t,hasUser:!!t?.user,userId:t?.user?.id,userEmail:t?.user?.email,userAud:t?.user?.aud,userRole:t?.user?.role,emailConfirmed:t?.user?.email_confirmed_at,userMetadata:t?.user?.user_metadata,appMetadata:t?.user?.app_metadata,error:s?.message,errorCode:s?.status,fullError:s}),s)throw console.error("❌ [SUPABASE_ADMIN] Error creating user invitation:",{message:s.message,status:s.status,details:s}),Error(`Failed to create user invitation: ${s.message}`);return t}static async createUserWithPassword(e,r,t,s=!0){console.log("\uD83D\uDD04 [SUPABASE_ADMIN] Creating user with password:",{email:e,userData:t,sendConfirmationEmail:s,timestamp:new Date().toISOString()});let{data:i,error:o}=await a.auth.admin.createUser({email:e,password:r,user_metadata:t,email_confirm:!1});if(console.log("\uD83D\uDCCA [SUPABASE_ADMIN] User creation result:",{hasData:!!i,hasUser:!!i?.user,userId:i?.user?.id,userEmail:i?.user?.email,emailConfirmed:i?.user?.email_confirmed_at,userMetadata:i?.user?.user_metadata,error:o?.message,errorCode:o?.status}),o)return console.error("❌ [SUPABASE_ADMIN] Error creating user with password:",{message:o.message,status:o.status,details:o}),{data:null,error:o};if(i?.user&&s){console.log("\uD83D\uDCE7 Enviando email de confirmaci\xf3n...");let{error:t}=await a.auth.admin.generateLink({type:"signup",email:e,password:r,options:{redirectTo:"http://localhost:3000/auth/confirmed"}});t?console.error("⚠️ Error enviando email de confirmaci\xf3n:",t):console.log("✅ Email de confirmaci\xf3n enviado exitosamente")}else i?.user&&!s&&console.log("\uD83D\uDCE7 Email de confirmaci\xf3n omitido (se enviar\xe1 despu\xe9s del pago)");return{data:i,error:null}}static async sendConfirmationEmailForUser(e){console.log("\uD83D\uDCE7 [SUPABASE_ADMIN] Enviando email de confirmaci\xf3n para usuario:",e);try{let{data:r,error:t}=await a.auth.admin.getUserById(e);if(t||!r?.user)return console.error("Error obteniendo datos del usuario:",t),{success:!1,error:"Usuario no encontrado"};let s=r.user,{error:i}=await a.auth.admin.updateUserById(s.id,{email_confirm:!0,user_metadata:{...s.user_metadata,payment_verified:!0,email_confirmed_via_payment:!0,confirmed_at:new Date().toISOString()}});if(i)return console.error("⚠️ Error confirmando email del usuario:",i),{success:!1,error:i.message};return console.log("✅ Usuario confirmado autom\xe1ticamente despu\xe9s del pago exitoso"),{success:!0}}catch(e){return console.error("Error en sendConfirmationEmailForUser:",e),{success:!1,error:e instanceof Error?e.message:"Error desconocido"}}}static async sendConfirmationEmail(e,r){console.log("\uD83D\uDCE7 [SUPABASE_ADMIN] Enviando email de confirmaci\xf3n para:",e);let{error:t}=await a.auth.admin.generateLink({type:"signup",email:e,password:r,options:{redirectTo:"http://localhost:3000/auth/confirmed"}});return t?(console.error("⚠️ Error enviando email de confirmaci\xf3n:",t),{success:!1,error:t.message}):(console.log("✅ Email de confirmaci\xf3n enviado exitosamente"),{success:!0})}static async createUserProfile(e){let{data:r,error:t}=await a.from("user_profiles").insert([e]).select().single();if(t)throw console.error("Error creating user profile:",t),Error(`Failed to create user profile: ${t.message}`);return r}static async upsertUserProfile(e){let{data:r,error:t}=await a.from("user_profiles").upsert([e],{onConflict:"user_id"}).select().single();if(t)throw console.error("Error upserting user profile:",t),Error(`Failed to upsert user profile: ${t.message}`);return r}static async logPlanChange(e){let{data:r,error:t}=await a.from("user_plan_history").insert([e]).select().single();if(t)throw console.error("Error logging plan change:",t),Error(`Failed to log plan change: ${t.message}`);return r}static async logFeatureAccess(e){let{data:r,error:t}=await a.from("feature_access_log").insert([e]).select().single();if(t)throw console.error("Error logging feature access:",t),Error(`Failed to log feature access: ${t.message}`);return r}static async getUserProfile(e){let{data:r,error:t}=await a.from("user_profiles").select("*").eq("user_id",e).single();if(t&&"PGRST116"!==t.code)throw console.error("Error fetching user profile:",t),Error(`Failed to fetch user profile: ${t.message}`);return r}static async updateTransactionWithUser(e,r){let{error:t}=await a.from("stripe_transactions").update({user_id:r,updated_at:new Date().toISOString()}).eq("id",e);if(t)throw console.error("Error updating transaction with user_id:",t),Error(`Failed to update transaction: ${t.message}`)}static async activateTransaction(e){let{error:r}=await a.from("stripe_transactions").update({activated_at:new Date().toISOString()}).eq("id",e);if(r)throw console.error("Error activating transaction:",r),Error(`Failed to activate transaction: ${r.message}`)}static async getDocumentsCount(e){let{count:r,error:t}=await a.from("documentos").select("*",{count:"exact",head:!0}).eq("user_id",e);return t?(console.error("Error getting documents count:",t),0):r||0}static async getUserByEmail(e){try{let{data:{users:r},error:t}=await a.auth.admin.listUsers();if(t)throw console.error("Error getting user by email:",t),Error(`Failed to get user by email: ${t.message}`);if(!r||0===r.length)return null;let s=r.find(r=>r.email===e);if(!s)return null;return{id:s.id,email:s.email,email_confirmed_at:s.email_confirmed_at}}catch(e){throw console.error("Error in getUserByEmail:",e),e}}}},78335:()=>{},89546:(e,r,t)=>{"use strict";t.d(r,{FreeAccountService:()=>i});var a=t(42049),s=t(8814);class i{static async createFreeAccount(e){try{console.log("\uD83C\uDD93 Iniciando creaci\xf3n de cuenta gratuita (flujo de invitaci\xf3n):",e.email);try{if(await a.SupabaseAdminService.getUserByEmail(e.email))return{success:!1,error:"El email ya est\xe1 registrado en el sistema."}}catch(e){e instanceof Error&&e.message.toLowerCase().includes("user not found")?console.log("Usuario no existe en Auth, continuando con la invitaci\xf3n."):console.warn("Advertencia al verificar usuario existente, se continuar\xe1 con el intento de invitaci\xf3n:",e)}let r=new Date;r.setDate(r.getDate()+5);let t={name:e.name||e.email.split("@")[0],plan:"free",free_account:!0,expires_at:r.toISOString(),created_via:"free_invitation_flow",registration_type:"automatic_free_invitation",requires_password_setup:!0};console.log("\uD83D\uDCE7 Invitando nuevo usuario con datos:",{email:e.email,userData:t,redirectTo:"http://localhost:3000/auth/confirm-invitation"});let{data:{user:i},error:o}=await a.E.auth.admin.inviteUserByEmail(e.email,{data:t,redirectTo:"http://localhost:3000/auth/confirm-invitation"});if(o){if(console.error("❌ Error invitando al usuario:",o),o.message.includes("User already registered"))return{success:!1,error:"Ya existe una cuenta con este email."};throw Error(`Error invitando al usuario: ${o.message}`)}if(!i)throw Error("Usuario no devuelto despu\xe9s de la invitaci\xf3n.");console.log("✅ Usuario invitado exitosamente a Supabase Auth:",i.id);let n=(0,s.IE)("free");if(!n)throw await a.E.auth.admin.deleteUser(i.id),Error("Configuraci\xf3n de plan gratuito no encontrada");let c=new Date().toISOString().slice(0,7)+"-01",l={subscription_plan:"free",monthly_token_limit:(0,s.t4)("free"),current_month_tokens:0,current_month:c,payment_verified:!0,stripe_customer_id:null,stripe_subscription_id:null,last_payment_date:null,auto_renew:!1,plan_expires_at:r.toISOString(),plan_features:n.features,security_flags:{created_via_free_invitation_flow:!0,free_account:!0,expires_at:r.toISOString(),activation_date:new Date().toISOString(),usage_count:{documents:0,tests:0,flashcards:0,mindMaps:0,tokens:0}}},{data:u,error:d}=await a.E.rpc("create_user_profile_and_history",{p_user_id:i.id,p_transaction_id:null,p_profile_data:l}).single();if(d)throw console.error("❌ Error al ejecutar la funci\xf3n RPC create_user_profile_and_history:",d),await a.E.auth.admin.deleteUser(i.id),Error(`Error en la creaci\xf3n at\xf3mica del perfil: ${d.message}`);let m=u.created_profile_id;return console.log("✅ Perfil gratuito y historial creados at\xf3micamente. Profile ID:",m),console.log("\uD83C\uDF89 Cuenta gratuita creada exitosamente con flujo de invitaci\xf3n."),{success:!0,userId:i.id,profileId:m,expiresAt:r.toISOString()}}catch(e){return console.error("❌ Error cr\xedtico en la creaci\xf3n de cuenta gratuita:",e),{success:!1,error:e instanceof Error?e.message:"Error desconocido al crear cuenta gratuita"}}}static async getFreeAccountStatus(e){try{let r=await a.SupabaseAdminService.getUserProfile(e);if(!r||"free"!==r.subscription_plan)return null;let t=new Date,i=r.plan_expires_at?new Date(r.plan_expires_at):null;if(!i)return null;let o=t<i,n=i.getTime()-t.getTime(),c=Math.max(0,Math.ceil(n/864e5)),l=Math.max(0,Math.ceil(n/36e5)),u=r.security_flags?.usage_count||{documents:0,tests:0,flashcards:0,mindMaps:0,tokens:0};u.tokens=r.current_month_tokens||0;let d=(0,s.IE)("free"),m={documents:d?.limits.documents||1,tests:d?.limits.testsForTrial||10,flashcards:d?.limits.flashcardsForTrial||10,mindMaps:d?.limits.mindMapsForTrial||2,tokens:d?.limits.tokensForTrial||5e4},_=new Date(r.created_at||Date.now()),f=r.security_flags?.activation_date?new Date(r.security_flags.activation_date):_,g=t.getTime()-f.getTime(),p=Math.min(100,Math.max(0,g/432e6*100));return{isActive:o,expiresAt:i.toISOString(),daysRemaining:c,hoursRemaining:l,usageCount:u,limits:m,progressPercentage:Math.round(p)}}catch(e){return console.error("Error obteniendo estado de cuenta gratuita:",e),null}}static async incrementUsageCount(e,r,t=1){try{let s=await a.SupabaseAdminService.getUserProfile(e);if(!s||"free"!==s.subscription_plan)return!1;let i=s.security_flags?.usage_count||{documents:0,tests:0,flashcards:0,mindMaps:0,tokens:0};i[r]=(i[r]||0)+t;let o={security_flags:{...s.security_flags,usage_count:i},updated_at:new Date().toISOString()};return await a.E.from("user_profiles").update(o).eq("user_id",e),!0}catch(e){return console.error("Error incrementando contador de uso:",e),!1}}static async canPerformAction(e,r,t=1){try{let a=await this.getFreeAccountStatus(e);if(!a)return{allowed:!1,reason:"Cuenta no encontrada o no es gratuita"};if(!a.isActive)return{allowed:!1,reason:"Cuenta gratuita expirada"};let s=a.usageCount[r]||0,i=a.limits[r],o=i-s;if(s+t>i)return{allowed:!1,reason:`L\xedmite de ${r} alcanzado (${s}/${i})`,remaining:Math.max(0,o)};return{allowed:!0,remaining:o-t}}catch(e){return console.error("Error verificando acci\xf3n:",e),{allowed:!1,reason:"Error interno"}}}static async cleanupExpiredAccounts(){try{console.log("\uD83E\uDDF9 Iniciando limpieza de cuentas gratuitas expiradas");let e=new Date().toISOString(),{data:r,error:t}=await a.E.from("user_profiles").select("user_id, id").eq("subscription_plan","free").lt("plan_expires_at",e).neq("security_flags ->> account_disabled","true");if(t)throw Error(`Error buscando cuentas expiradas: ${t.message}`);if(!r||0===r.length)return console.log("✅ No hay cuentas expiradas para limpiar"),{cleaned:0,errors:[]};console.log(`🗑️ Encontradas ${r.length} cuentas expiradas para procesar`);let s=[],i=0;for(let e of r)try{await a.E.auth.admin.updateUserById(e.user_id,{user_metadata:{account_disabled:!0,disabled_reason:"free_account_expired"}});let{data:r}=await a.E.from("user_profiles").select("security_flags").eq("user_id",e.user_id).single(),t=r?.security_flags||{};await a.E.from("user_profiles").update({payment_verified:!1,security_flags:{...t,account_disabled:!0,disabled_at:new Date().toISOString(),disabled_reason:"free_account_expired"}}).eq("user_id",e.user_id),i++}catch(t){let r=`Error limpiando usuario ${e.user_id}: ${t instanceof Error?t.message:String(t)}`;console.error(r),s.push(r)}return console.log(`✅ Limpieza completada: ${i} cuentas procesadas, ${s.length} errores`),{cleaned:i,errors:s}}catch(e){return console.error("❌ Error en limpieza de cuentas:",e),{cleaned:0,errors:[e instanceof Error?e.message:"Error desconocido"]}}}}},96487:()=>{}};