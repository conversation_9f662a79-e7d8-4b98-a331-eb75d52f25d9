exports.id=1191,exports.ids=[1191],exports.modules={5322:(e,s,t)=>{Promise.resolve().then(t.bind(t,52883))},34705:(e,s,t)=>{"use strict";t.d(s,{F3:()=>a,iF:()=>i,n4:()=>n});var r=t(48363);async function a(e,s){try{if(!e||!s)return{user:null,session:null,error:"Por favor, ingresa tu email y contrase\xf1a"};let{data:t,error:a}=await r.N.auth.signInWithPassword({email:e.trim(),password:s});if(a){if(a.message.includes("issued in the future")||a.message.includes("clock for skew"))return{user:null,session:null,error:"Error de sincronizaci\xf3n de tiempo. Por favor, verifica que la hora de tu dispositivo est\xe9 correctamente configurada."};if(a.message.includes("Invalid login credentials"))return{user:null,session:null,error:"Email o contrase\xf1a incorrectos. Por favor, verifica tus credenciales."};return{user:null,session:null,error:a.message}}if(t&&t.user&&t.session)return await new Promise(e=>setTimeout(e,800)),await r.N.auth.getSession(),{user:t.user,session:t.session,error:null};return{user:null,session:null,error:"Respuesta inesperada del servidor al iniciar sesi\xf3n."}}catch(e){return{user:null,session:null,error:e instanceof Error&&e.message?e.message:"Ha ocurrido un error inesperado al iniciar sesi\xf3n"}}}async function n(){try{console.log("\uD83D\uDD13 Iniciando proceso de logout...");let{error:e}=await r.N.auth.signOut({scope:"global"});if(e)return console.error("❌ Error en signOut:",e.message),{error:e.message};return console.log("✅ SignOut exitoso"),{error:null}}catch(e){return console.error("❌ Error inesperado en logout:",e),{error:"Ha ocurrido un error inesperado al cerrar sesi\xf3n"}}}async function i(){try{let{data:{user:e},error:s}=await r.N.auth.getUser();if(s){if("Auth session missing!"===s.message)return{user:null,error:null};return{user:null,error:s.message}}return{user:e,error:null}}catch(e){return{user:null,error:"Ha ocurrido un error inesperado al obtener el usuario actual"}}}},48363:(e,s,t)=>{"use strict";t.d(s,{N:()=>r.N,U:()=>r.U});var r=t(79481)},52883:(e,s,t)=>{"use strict";t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\shared\\\\components\\\\ClientLayout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\shared\\components\\ClientLayout.tsx","default")},61135:()=>{},65660:(e,s,t)=>{"use strict";t.d(s,{O:()=>h,A:()=>p});var r=t(60687),a=t(43210),n=t(16189),i=t(48363),o=t(34705),l=t(93667);let u=["mousedown","mousemove","keypress","scroll","touchstart","click"],c=({timeout:e,onTimeout:s,enabled:t=!0})=>{let r=(0,a.useRef)(null),n=(0,a.useRef)(Date.now()),{tasks:i}=(0,l.M)();i.some(e=>"pending"===e.status||"processing"===e.status);let o=(0,a.useCallback)(()=>{t&&(r.current&&clearTimeout(r.current),n.current=Date.now(),r.current=setTimeout(()=>{0===i.filter(e=>"pending"===e.status||"processing"===e.status).length?s():(console.log("\uD83D\uDD04 Logout diferido: hay tareas de IA en progreso, reintentando en 1 minuto"),setTimeout(()=>o(),6e4))},e))},[e,s,t,i]),c=(0,a.useCallback)(()=>{r.current&&(clearTimeout(r.current),r.current=null)},[]),d=(0,a.useCallback)(()=>t&&r.current?Math.max(0,e-(Date.now()-n.current)):0,[e,t]);return(0,a.useEffect)(()=>{if(!t)return void c();let e=()=>{o()};return u.forEach(s=>{document.addEventListener(s,e,!0)}),o(),()=>{u.forEach(s=>{document.removeEventListener(s,e,!0)}),c()}},[t,o,c]),{resetTimer:o,clearTimer:c,getTimeRemaining:d}},d=(e=5,s,t=!0)=>c({timeout:60*e*1e3,onTimeout:s,enabled:t}),m=(0,a.createContext)(void 0),h=({children:e})=>{let[s,t]=(0,a.useState)(null),[l,u]=(0,a.useState)(null),[c,h]=(0,a.useState)(!0),[p,x]=(0,a.useState)(null),[g,f]=(0,a.useState)(!1),[v,b]=(0,a.useState)(60),j=(0,n.useRouter)(),N=(0,n.usePathname)();(0,a.useEffect)(()=>{h(!0);let{data:e}=i.N.auth.onAuthStateChange((e,s)=>{u(s),t(s?.user??null),x(null),("INITIAL_SESSION"===e||"SIGNED_IN"===e||"SIGNED_OUT"===e||"TOKEN_REFRESHED"===e||"USER_UPDATED"===e||"PASSWORD_RECOVERY"===e)&&h(!1)});return i.N.auth.getSession().then(({data:{session:e},error:s})=>{s&&(x(s.message),h(!1))}).catch(e=>{x(e.message),h(!1)}),()=>{e?.subscription.unsubscribe()}},[]),(0,a.useEffect)(()=>c||N.startsWith("/api")||N.startsWith("/_next")?void 0:l&&"/login"===N?void j.replace("/app"):l||["/","/login","/payment","/thank-you","/auth/callback","/auth/confirmed","/auth/unauthorized","/auth/reset-password","/auth/confirm-reset","/auth/confirm-invitation"].includes(N)||N.startsWith("/api")||N.startsWith("/_next")?void 0:void j.replace("/login"),[l,c,N,j]);let w=(0,a.useCallback)(async(e,s)=>{h(!0),x(null);try{let{user:t,session:r,error:a}=await (0,o.F3)(e,s);if(a)return x(a),h(!1),{user:null,session:null,error:a};return r&&(await new Promise(e=>setTimeout(e,300)),j.replace("/app")),{user:t,session:r,error:null}}catch(s){let e=s instanceof Error&&s.message?s.message:"Error desconocido durante el inicio de sesi\xf3n.";return x(e),h(!1),{user:null,session:null,error:e}}},[j]),y=(0,a.useCallback)(async()=>{h(!0),x(null);let{error:e}=await (0,o.n4)();e&&(x(e),h(!1))},[]),k=(0,a.useCallback)(()=>!!s&&!!l&&!c,[s,l,c]),C=(0,a.useCallback)(async()=>{await y()},[y]);(0,a.useCallback)(()=>{f(!1)},[]),(0,a.useCallback)(async()=>{f(!1),await y()},[y]);let{resetTimer:A}=d(5,C,k());return(0,r.jsxs)(m.Provider,{value:{user:s,session:l,isLoading:c,error:p,iniciarSesion:w,cerrarSesion:y,estaAutenticado:k},children:[e,!1]})},p=()=>{let e=(0,a.useContext)(m);if(void 0===e)throw Error("useAuth debe ser utilizado dentro de un AuthProvider");return e}},65833:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,86346,23)),Promise.resolve().then(t.t.bind(t,27924,23)),Promise.resolve().then(t.t.bind(t,35656,23)),Promise.resolve().then(t.t.bind(t,40099,23)),Promise.resolve().then(t.t.bind(t,38243,23)),Promise.resolve().then(t.t.bind(t,28827,23)),Promise.resolve().then(t.t.bind(t,62763,23)),Promise.resolve().then(t.t.bind(t,97173,23))},68370:(e,s,t)=>{Promise.resolve().then(t.bind(t,68849))},68849:(e,s,t)=>{"use strict";t.d(s,{default:()=>f});var r=t(60687),a=t(43210),n=t(65660),i=t(93667);function o(){return null}t(48363);var l=t(50515),u=t(51426),c=t(58089),d=t(54672),m=t(27010),h=t(27691),p=t(81836);let x=()=>{let{activeTasks:e,completedTasks:s,removeTask:t,clearCompletedTasks:n}=(0,i.M)(),[o,x]=(0,a.useState)(!1),[g,f]=(0,a.useState)(!1);if(0===e.length+s.length)return null;let v=e=>{switch(e.status){case"pending":return(0,r.jsx)(l.A,{className:"h-4 w-4 text-yellow-500"});case"processing":return(0,r.jsx)(u.A,{className:"h-4 w-4 text-blue-500 animate-spin"});case"completed":return(0,r.jsx)(c.A,{className:"h-4 w-4 text-green-500"});case"error":return(0,r.jsx)(d.A,{className:"h-4 w-4 text-red-500"});default:return(0,r.jsx)(l.A,{className:"h-4 w-4 text-gray-500"})}},b=e=>{switch(e){case"mapa-mental":return"Mapa Mental";case"test":return"Test";case"flashcards":return"Flashcards";default:return"Tarea"}},j=e=>e.toLocaleTimeString("es-ES",{hour:"2-digit",minute:"2-digit"});return(0,r.jsx)("div",{className:"fixed bottom-4 right-4 z-50 max-w-sm",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-lg border border-gray-200 overflow-hidden",children:[(0,r.jsxs)("div",{className:"bg-gradient-to-r from-blue-500 to-purple-600 text-white p-3 cursor-pointer flex items-center justify-between",onClick:()=>x(!o),children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(u.A,{className:"h-5 w-5"}),(0,r.jsxs)("span",{className:"font-medium",children:["Tareas (",e.length," activas)"]})]}),o?(0,r.jsx)(m.A,{className:"h-5 w-5"}):(0,r.jsx)(h.A,{className:"h-5 w-5"})]}),o&&(0,r.jsxs)("div",{className:"max-h-96 overflow-y-auto",children:[e.length>0&&(0,r.jsxs)("div",{className:"p-3 border-b border-gray-100",children:[(0,r.jsx)("h4",{className:"text-sm font-semibold text-gray-700 mb-2",children:"Tareas Activas"}),(0,r.jsx)("div",{className:"space-y-2",children:e.map(e=>(0,r.jsxs)("div",{className:"flex items-center space-x-3 p-2 bg-gray-50 rounded-md",children:[v(e),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900 truncate",children:b(e.type)}),(0,r.jsx)("p",{className:"text-xs text-gray-500 truncate",children:e.title}),void 0!==e.progress&&(0,r.jsx)("div",{className:"mt-1",children:(0,r.jsx)("div",{className:"bg-gray-200 rounded-full h-1.5",children:(0,r.jsx)("div",{className:"bg-blue-500 h-1.5 rounded-full transition-all duration-300",style:{width:`${e.progress}%`}})})})]}),(0,r.jsx)("span",{className:"text-xs text-gray-400",children:j(e.createdAt)})]},e.id))})]}),s.length>0&&(0,r.jsxs)("div",{className:"p-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,r.jsxs)("button",{onClick:()=>f(!g),className:"text-sm font-semibold text-gray-700 hover:text-gray-900 flex items-center space-x-1",children:[(0,r.jsxs)("span",{children:["Completadas (",s.length,")"]}),g?(0,r.jsx)(h.A,{className:"h-4 w-4"}):(0,r.jsx)(m.A,{className:"h-4 w-4"})]}),s.length>0&&(0,r.jsx)("button",{onClick:n,className:"text-xs text-gray-500 hover:text-red-600 transition-colors",children:"Limpiar"})]}),g&&(0,r.jsx)("div",{className:"space-y-2",children:s.map(e=>(0,r.jsxs)("div",{className:"flex items-center space-x-3 p-2 bg-gray-50 rounded-md",children:[v(e),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900 truncate",children:b(e.type)}),(0,r.jsx)("p",{className:"text-xs text-gray-500 truncate",children:e.title}),e.error&&(0,r.jsx)("p",{className:"text-xs text-red-500 truncate",children:e.error})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{className:"text-xs text-gray-400",children:e.completedAt?j(e.completedAt):j(e.createdAt)}),(0,r.jsx)("button",{onClick:()=>t(e.id),className:"text-gray-400 hover:text-red-500 transition-colors",children:(0,r.jsx)(p.A,{className:"h-4 w-4"})})]})]},e.id))})]})]})]})})};var g=t(37590);function f({children:e}){return(0,r.jsx)(i.W,{children:(0,r.jsxs)(n.O,{children:[(0,r.jsx)(o,{}),(0,r.jsx)(g.l$,{position:"top-right",toastOptions:{duration:5e3,style:{background:"#363636",color:"#fff"},success:{duration:3e3,style:{background:"#10b981",color:"#fff"}},error:{duration:5e3,style:{background:"#ef4444",color:"#fff"}}}}),(0,r.jsx)(x,{}),e]})})}},75561:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,16444,23)),Promise.resolve().then(t.t.bind(t,16042,23)),Promise.resolve().then(t.t.bind(t,88170,23)),Promise.resolve().then(t.t.bind(t,49477,23)),Promise.resolve().then(t.t.bind(t,29345,23)),Promise.resolve().then(t.t.bind(t,12089,23)),Promise.resolve().then(t.t.bind(t,46577,23)),Promise.resolve().then(t.t.bind(t,31307,23))},79481:(e,s,t)=>{"use strict";t.d(s,{N:()=>n,U:()=>a});var r=t(29605);function a(){return(0,r.createBrowserClient)("https://fxnhpxjijinfuxxxplzj.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4",{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0}})}let n=a()},93667:(e,s,t)=>{"use strict";t.d(s,{M:()=>l,W:()=>o});var r=t(60687),a=t(43210),n=t(37590);let i=(0,a.createContext)(void 0),o=({children:e})=>{let[s,t]=(0,a.useState)([]),o=(0,a.useCallback)(e=>{let s=`task_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,r={...e,id:s,status:"pending",createdAt:new Date};return t(e=>[...e,r]),n.oR.loading(`Iniciando: ${r.title}`,{id:`task_start_${s}`,duration:2e3}),s},[]),l=(0,a.useCallback)((e,s)=>{t(t=>t.map(t=>{if(t.id===e){let r={...t,...s};return"processing"===s.status&&"pending"===t.status?(n.oR.dismiss(`task_start_${e}`),n.oR.loading(`Procesando: ${t.title}`,{id:`task_processing_${e}`})):"completed"===s.status&&"completed"!==t.status?(n.oR.dismiss(`task_processing_${e}`),n.oR.success(`Completado: ${t.title}`,{id:`task_completed_${e}`,duration:4e3}),r.completedAt=new Date):"error"===s.status&&"error"!==t.status&&(n.oR.dismiss(`task_processing_${e}`),n.oR.error(`Error: ${t.title}`,{id:`task_error_${e}`,duration:5e3})),r}return t}))},[]),u=(0,a.useCallback)(e=>{t(s=>s.filter(s=>s.id!==e)),n.oR.dismiss(`task_start_${e}`),n.oR.dismiss(`task_processing_${e}`),n.oR.dismiss(`task_completed_${e}`),n.oR.dismiss(`task_error_${e}`)},[]),c=(0,a.useCallback)(e=>s.find(s=>s.id===e),[s]),d=(0,a.useCallback)(e=>s.filter(s=>s.type===e),[s]),m=(0,a.useCallback)(()=>{t(e=>e.filter(e=>"completed"!==e.status&&"error"!==e.status))},[]),h=(0,a.useMemo)(()=>s.filter(e=>"pending"===e.status||"processing"===e.status),[s]),p=(0,a.useMemo)(()=>s.filter(e=>"completed"===e.status||"error"===e.status),[s]),x=(0,a.useMemo)(()=>({tasks:s,addTask:o,updateTask:l,removeTask:u,getTask:c,getTasksByType:d,clearCompletedTasks:m,activeTasks:h,completedTasks:p}),[s,o,l,u,c,d,m,h,p]);return(0,r.jsx)(i.Provider,{value:x,children:e})},l=()=>{let e=(0,a.useContext)(i);if(void 0===e)throw Error("useBackgroundTasks must be used within a BackgroundTasksProvider");return e}},94431:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i,metadata:()=>n});var r=t(37413);t(61135);var a=t(52883);let n={title:"OposiAI - Asistente IA para Oposiciones",description:"Aplicaci\xf3n de preguntas y respuestas con IA para temarios de oposiciones",icons:{icon:[{url:"/favicon.ico",sizes:"any"},{url:"/logo.png",type:"image/png"}],apple:[{url:"/icon-192.png",sizes:"192x192",type:"image/png"}]},manifest:"/manifest.json"};function i({children:e}){return(0,r.jsx)("html",{lang:"es",children:(0,r.jsx)("body",{className:"font-sans bg-gray-100",children:(0,r.jsx)(a.default,{children:e})})})}}};