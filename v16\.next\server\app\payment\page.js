/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/payment/page";
exports.ids = ["app/payment/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpayment%2Fpage&page=%2Fpayment%2Fpage&appPaths=%2Fpayment%2Fpage&pagePath=private-next-app-dir%2Fpayment%2Fpage.tsx&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpayment%2Fpage&page=%2Fpayment%2Fpage&appPaths=%2Fpayment%2Fpage&pagePath=private-next-app-dir%2Fpayment%2Fpage.tsx&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/payment/page.tsx */ \"(rsc)/./src/app/payment/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'payment',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/payment/page\",\n        pathname: \"/payment\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpayment%2Fpage&page=%2Fpayment%2Fpage&appPaths=%2Fpayment%2Fpage&pagePath=private-next-app-dir%2Fpayment%2Fpage.tsx&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q25hYXRhJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q09wb3NJJTVDJTVDdjE2JTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDY2xpZW50LXBhZ2UuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDbmFhdGElNUMlNUNEb2N1bWVudHMlNUMlNUNhdWdtZW50LXByb2plY3RzJTVDJTVDT3Bvc0klNUMlNUN2MTYlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNjbGllbnQtc2VnbWVudC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNuYWF0YSU1QyU1Q0RvY3VtZW50cyU1QyU1Q2F1Z21lbnQtcHJvamVjdHMlNUMlNUNPcG9zSSU1QyU1Q3YxNiU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q25hYXRhJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q09wb3NJJTVDJTVDdjE2JTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDaHR0cC1hY2Nlc3MtZmFsbGJhY2slNUMlNUNlcnJvci1ib3VuZGFyeS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNuYWF0YSU1QyU1Q0RvY3VtZW50cyU1QyU1Q2F1Z21lbnQtcHJvamVjdHMlNUMlNUNPcG9zSSU1QyU1Q3YxNiU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2xheW91dC1yb3V0ZXIuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDbmFhdGElNUMlNUNEb2N1bWVudHMlNUMlNUNhdWdtZW50LXByb2plY3RzJTVDJTVDT3Bvc0klNUMlNUN2MTYlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNtZXRhZGF0YSU1QyU1Q2FzeW5jLW1ldGFkYXRhLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q25hYXRhJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q09wb3NJJTVDJTVDdjE2JTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDbWV0YWRhdGElNUMlNUNtZXRhZGF0YS1ib3VuZGFyeS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNuYWF0YSU1QyU1Q0RvY3VtZW50cyU1QyU1Q2F1Z21lbnQtcHJvamVjdHMlNUMlNUNPcG9zSSU1QyU1Q3YxNiU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q3JlbmRlci1mcm9tLXRlbXBsYXRlLWNvbnRleHQuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9PQUE0SjtBQUM1SjtBQUNBLDBPQUErSjtBQUMvSjtBQUNBLDBPQUErSjtBQUMvSjtBQUNBLG9SQUFxTDtBQUNyTDtBQUNBLHdPQUE4SjtBQUM5SjtBQUNBLDRQQUF5SztBQUN6SztBQUNBLGtRQUE0SztBQUM1SztBQUNBLHNRQUE2SyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcbmFhdGFcXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcT3Bvc0lcXFxcdjE2XFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcY2xpZW50LXBhZ2UuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXG5hYXRhXFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXE9wb3NJXFxcXHYxNlxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGNsaWVudC1zZWdtZW50LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxuYWF0YVxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxPcG9zSVxcXFx2MTZcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxlcnJvci1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcbmFhdGFcXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcT3Bvc0lcXFxcdjE2XFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcaHR0cC1hY2Nlc3MtZmFsbGJhY2tcXFxcZXJyb3ItYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXG5hYXRhXFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXE9wb3NJXFxcXHYxNlxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGxheW91dC1yb3V0ZXIuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXG5hYXRhXFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXE9wb3NJXFxcXHYxNlxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXG1ldGFkYXRhXFxcXGFzeW5jLW1ldGFkYXRhLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxuYWF0YVxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxPcG9zSVxcXFx2MTZcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxtZXRhZGF0YVxcXFxtZXRhZGF0YS1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcbmFhdGFcXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcT3Bvc0lcXFxcdjE2XFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxccmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qc1wiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Csrc%5C%5Cfeatures%5C%5Cshared%5C%5Ccomponents%5C%5CClientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Csrc%5C%5Cfeatures%5C%5Cshared%5C%5Ccomponents%5C%5CClientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/features/shared/components/ClientLayout.tsx */ \"(rsc)/./src/features/shared/components/ClientLayout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q25hYXRhJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q09wb3NJJTVDJTVDdjE2JTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDbmFhdGElNUMlNUNEb2N1bWVudHMlNUMlNUNhdWdtZW50LXByb2plY3RzJTVDJTVDT3Bvc0klNUMlNUN2MTYlNUMlNUNzcmMlNUMlNUNmZWF0dXJlcyU1QyU1Q3NoYXJlZCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNDbGllbnRMYXlvdXQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsOE1BQWdMIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiQzpcXFxcVXNlcnNcXFxcbmFhdGFcXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcT3Bvc0lcXFxcdjE2XFxcXHNyY1xcXFxmZWF0dXJlc1xcXFxzaGFyZWRcXFxcY29tcG9uZW50c1xcXFxDbGllbnRMYXlvdXQudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Csrc%5C%5Cfeatures%5C%5Cshared%5C%5Ccomponents%5C%5CClientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Csrc%5C%5Capp%5C%5Cpayment%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Csrc%5C%5Capp%5C%5Cpayment%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/payment/page.tsx */ \"(rsc)/./src/app/payment/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q25hYXRhJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q09wb3NJJTVDJTVDdjE2JTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDcGF5bWVudCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnS0FBMkgiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXG5hYXRhXFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXE9wb3NJXFxcXHYxNlxcXFxzcmNcXFxcYXBwXFxcXHBheW1lbnRcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Csrc%5C%5Capp%5C%5Cpayment%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"fbfb08cc9359\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG5hYXRhXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXE9wb3NJXFx2MTZcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImZiZmIwOGNjOTM1OVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _features_shared_components_ClientLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/features/shared/components/ClientLayout */ \"(rsc)/./src/features/shared/components/ClientLayout.tsx\");\n\n\n\nconst metadata = {\n    title: 'OposiAI - Asistente IA para Oposiciones',\n    description: 'Aplicación de preguntas y respuestas con IA para temarios de oposiciones',\n    icons: {\n        icon: [\n            {\n                url: '/favicon.ico',\n                sizes: 'any'\n            },\n            {\n                url: '/logo.png',\n                type: 'image/png'\n            }\n        ],\n        apple: [\n            {\n                url: '/icon-192.png',\n                sizes: '192x192',\n                type: 'image/png'\n            }\n        ]\n    },\n    manifest: '/manifest.json'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"es\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"font-sans bg-gray-100\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_shared_components_ClientLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 28,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/payment/page.tsx":
/*!**********************************!*\
  !*** ./src/app/payment/page.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js\");\n/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(\nfunction() { throw new Error(\"Attempted to call the default export of \\\"C:\\\\\\\\Users\\\\\\\\<USER>\\\\\\\\Documents\\\\\\\\augment-projects\\\\\\\\OposI\\\\\\\\v16\\\\\\\\src\\\\\\\\app\\\\\\\\payment\\\\\\\\page.tsx\\\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n\"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n\"default\",\n));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/payment/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/features/shared/components/ClientLayout.tsx":
/*!*********************************************************!*\
  !*** ./src/features/shared/components/ClientLayout.tsx ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\shared\\\\components\\\\ClientLayout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\shared\\components\\ClientLayout.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Csrc%5C%5Cfeatures%5C%5Cshared%5C%5Ccomponents%5C%5CClientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Csrc%5C%5Cfeatures%5C%5Cshared%5C%5Ccomponents%5C%5CClientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/features/shared/components/ClientLayout.tsx */ \"(ssr)/./src/features/shared/components/ClientLayout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q25hYXRhJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q09wb3NJJTVDJTVDdjE2JTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDbmFhdGElNUMlNUNEb2N1bWVudHMlNUMlNUNhdWdtZW50LXByb2plY3RzJTVDJTVDT3Bvc0klNUMlNUN2MTYlNUMlNUNzcmMlNUMlNUNmZWF0dXJlcyU1QyU1Q3NoYXJlZCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNDbGllbnRMYXlvdXQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsOE1BQWdMIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiQzpcXFxcVXNlcnNcXFxcbmFhdGFcXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcT3Bvc0lcXFxcdjE2XFxcXHNyY1xcXFxmZWF0dXJlc1xcXFxzaGFyZWRcXFxcY29tcG9uZW50c1xcXFxDbGllbnRMYXlvdXQudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Csrc%5C%5Cfeatures%5C%5Cshared%5C%5Ccomponents%5C%5CClientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Csrc%5C%5Capp%5C%5Cpayment%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Csrc%5C%5Capp%5C%5Cpayment%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/payment/page.tsx */ \"(ssr)/./src/app/payment/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q25hYXRhJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q09wb3NJJTVDJTVDdjE2JTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDcGF5bWVudCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnS0FBMkgiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXG5hYXRhXFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXE9wb3NJXFxcXHYxNlxcXFxzcmNcXFxcYXBwXFxcXHBheW1lbnRcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Csrc%5C%5Capp%5C%5Cpayment%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/payment/page.tsx":
/*!**********************************!*\
  !*** ./src/app/payment/page.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PaymentPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_stripe_plans__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/stripe/plans */ \"(ssr)/./src/lib/stripe/plans.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n// ===== Archivo: src\\app\\payment\\page.tsx =====\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction PaymentContent() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const planId = searchParams.get('plan') || 'free';\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [confirmPassword, setConfirmPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [customerName, setCustomerName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const plan = (0,_lib_stripe_plans__WEBPACK_IMPORTED_MODULE_3__.getPlanById)(planId);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PaymentContent.useEffect\": ()=>{\n            if (!plan) {\n                router.push('/'); // Redirigir si el plan no es válido\n            }\n        }\n    }[\"PaymentContent.useEffect\"], [\n        plan,\n        router\n    ]);\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        // Validaciones básicas\n        if (!email.trim()) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error('Por favor, ingresa tu email');\n            return;\n        }\n        if (!password.trim()) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error('Por favor, ingresa una contraseña');\n            return;\n        }\n        if (password.length < 6) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error('La contraseña debe tener al menos 6 caracteres');\n            return;\n        }\n        if (password !== confirmPassword) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error('Las contraseñas no coinciden');\n            return;\n        }\n        setIsLoading(true);\n        try {\n            // Para el plan gratuito\n            if (planId === 'free') {\n                // Llamar al endpoint de registro gratuito\n                const registerResponse = await fetch('/api/auth/register-free', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        email,\n                        password,\n                        customerName\n                    })\n                });\n                const registerData = await registerResponse.json();\n                if (registerResponse.ok && registerData.success) {\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].success('Registro exitoso. Revisa tu email para confirmar tu cuenta.');\n                    router.push(`/thank-you?plan=${planId}&email_sent=true`);\n                } else {\n                    // Manejo de errores del endpoint de registro\n                    if (registerResponse.status === 429) {\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error('Demasiados intentos. Inténtalo en 15 minutos.');\n                    } else {\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error(registerData.error || 'Error al crear la cuenta gratuita');\n                    }\n                }\n            } else {\n                // NUEVO FLUJO: Para planes de pago, crear usuario primero y luego ir a Stripe\n                console.log('🔄 Iniciando nuevo flujo de pre-registro para plan de pago');\n                // Paso 1: Pre-registrar usuario\n                const preRegisterResponse = await fetch('/api/auth/pre-register-paid', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        email,\n                        password,\n                        customerName: customerName || email.split('@')[0],\n                        planId\n                    })\n                });\n                const preRegisterData = await preRegisterResponse.json();\n                if (!preRegisterResponse.ok) {\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error(preRegisterData.error || 'Error al crear la cuenta');\n                    return;\n                }\n                console.log('✅ Usuario pre-registrado exitosamente:', preRegisterData.userId);\n                // Paso 2: Crear sesión de Stripe con el userId\n                const stripeResponse = await fetch('/api/stripe/create-checkout-session', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        planId,\n                        email,\n                        customerName,\n                        userId: preRegisterData.userId\n                    })\n                });\n                const stripeData = await stripeResponse.json();\n                if (stripeResponse.ok && stripeData.url) {\n                    console.log('🔄 Redirigiendo a Stripe Checkout...');\n                    window.location.href = stripeData.url; // Redirigir a Stripe Checkout\n                } else {\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error(stripeData.error || 'Error al crear la sesión de pago');\n                }\n            }\n        } catch (error) {\n            console.error('Error en handleSubmit:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error('Error al procesar la solicitud. Por favor, intenta de nuevo.');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    if (!plan) {\n        // Este return se activará si el useEffect redirige, o si el plan es inválido inicialmente\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: \"Cargando detalles del plan o redirigiendo...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n            lineNumber: 144,\n            columnNumber: 9\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900\",\n                            children: plan.name\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-2xl font-semibold text-blue-600 mt-2\",\n                            children: [\n                                plan.price === 0 ? 'Gratis' : `€${(plan.price / 100).toFixed(2)}`,\n                                (planId === 'pro' || planId === 'usuario') && plan.price > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-500\",\n                                    children: \"/mes\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 78\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                    lineNumber: 153,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white shadow rounded-lg p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-gray-900 mb-3\",\n                                    children: \"Caracter\\xedsticas del plan:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: plan.features.map((feature, index)=>{\n                                        // Si es un encabezado (Incluye: o No incluye:) - SIN ICONO\n                                        if (feature === 'Incluye:' || feature === 'No incluye:') {\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"mt-4 first:mt-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-semibold text-gray-900\",\n                                                    children: feature\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                                    lineNumber: 174,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 21\n                                            }, this);\n                                        }\n                                        // Lógica mejorada para determinar si un ítem está bajo \"No incluye:\"\n                                        const isNotIncludedItem = (()=>{\n                                            if (!feature.startsWith('• ')) return false;\n                                            // Buscar hacia atrás el encabezado más cercano\n                                            for(let i = index - 1; i >= 0; i--){\n                                                if (plan.features[i] === 'Incluye:') return false;\n                                                if (plan.features[i] === 'No incluye:') return true;\n                                            }\n                                            return false;\n                                        })();\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-start ml-2\",\n                                            children: [\n                                                feature.startsWith('• ') ? isNotIncludedItem ? // Icono de Cruz Roja para características no incluidas\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-4 w-4 mt-0.5 mr-3 flex-shrink-0 text-red-500\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 20 20\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fillRule: \"evenodd\",\n                                                        d: \"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\",\n                                                        clipRule: \"evenodd\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                                        lineNumber: 201,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                                    lineNumber: 196,\n                                                    columnNumber: 25\n                                                }, this) : // Icono de Check Verde para características incluidas\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-4 w-4 mt-0.5 mr-3 flex-shrink-0 text-green-500\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 20 20\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fillRule: \"evenodd\",\n                                                        d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                        clipRule: \"evenodd\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                                        lineNumber: 214,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                                    lineNumber: 209,\n                                                    columnNumber: 25\n                                                }, this) : null,\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: feature.startsWith('• ') ? feature.substring(2) : feature\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                                    lineNumber: 222,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"email\",\n                                            className: \"block text-sm font-medium text-gray-700\",\n                                            children: \"Email *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            id: \"email\",\n                                            required: true,\n                                            value: email,\n                                            onChange: (e)=>setEmail(e.target.value),\n                                            className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                            placeholder: \"<EMAIL>\",\n                                            disabled: isLoading\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"password\",\n                                            className: \"block text-sm font-medium text-gray-700\",\n                                            children: \"Contrase\\xf1a *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"password\",\n                                            id: \"password\",\n                                            required: true,\n                                            value: password,\n                                            onChange: (e)=>setPassword(e.target.value),\n                                            className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                            placeholder: \"M\\xednimo 6 caracteres\",\n                                            disabled: isLoading,\n                                            minLength: 6\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                    lineNumber: 248,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"confirmPassword\",\n                                            className: \"block text-sm font-medium text-gray-700\",\n                                            children: \"Confirmar Contrase\\xf1a *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                            lineNumber: 266,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"password\",\n                                            id: \"confirmPassword\",\n                                            required: true,\n                                            value: confirmPassword,\n                                            onChange: (e)=>setConfirmPassword(e.target.value),\n                                            className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                            placeholder: \"Repite tu contrase\\xf1a\",\n                                            disabled: isLoading,\n                                            minLength: 6\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                            lineNumber: 269,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                    lineNumber: 265,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"customerName\",\n                                            className: \"block text-sm font-medium text-gray-700\",\n                                            children: \"Nombre (opcional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            id: \"customerName\",\n                                            value: customerName,\n                                            onChange: (e)=>setCustomerName(e.target.value),\n                                            className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                            placeholder: \"Tu nombre\",\n                                            disabled: isLoading\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                            lineNumber: 286,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: isLoading,\n                                    className: \"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: isLoading ? 'Procesando...' : planId === 'free' ? 'Solicitar Acceso Gratuito' : 'Proceder al Pago'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                    lineNumber: 163,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n            lineNumber: 152,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n        lineNumber: 151,\n        columnNumber: 5\n    }, this);\n}\nfunction PaymentPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-md mx-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white shadow rounded-lg p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                lineNumber: 319,\n                                columnNumber: 15\n                            }, void 0),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-4 text-gray-600\",\n                                children: \"Cargando...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                lineNumber: 320,\n                                columnNumber: 15\n                            }, void 0)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                        lineNumber: 318,\n                        columnNumber: 13\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                    lineNumber: 317,\n                    columnNumber: 11\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                lineNumber: 316,\n                columnNumber: 9\n            }, void 0)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n            lineNumber: 315,\n            columnNumber: 7\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PaymentContent, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n            lineNumber: 326,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\payment\\\\page.tsx\",\n        lineNumber: 314,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/payment/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase/supabaseClient */ \"(ssr)/./src/lib/supabase/supabaseClient.ts\");\n/* harmony import */ var _lib_supabase_authService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/supabase/authService */ \"(ssr)/./src/lib/supabase/authService.ts\");\n/* harmony import */ var _features_auth_hooks_useInactivityTimer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../features/auth/hooks/useInactivityTimer */ \"(ssr)/./src/features/auth/hooks/useInactivityTimer.ts\");\n/* harmony import */ var _features_auth_components_InactivityWarning__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../features/auth/components/InactivityWarning */ \"(ssr)/./src/features/auth/components/InactivityWarning.tsx\");\n\n\n\n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true); // Start true: loading initial auth state\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showInactivityWarning, setShowInactivityWarning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [warningTimeRemaining, setWarningTimeRemaining] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(60); // 60 segundos de advertencia\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    // Effect for auth state listener and initial session check\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            setIsLoading(true); // Explicitly set loading true at the start of auth setup\n            const { data: authListener } = _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.onAuthStateChange({\n                \"AuthProvider.useEffect\": (event, currentSession)=>{\n                    setSession(currentSession);\n                    setUser(currentSession?.user ?? null);\n                    setError(null); // Clear previous errors on any auth state change\n                    // Centralize setIsLoading(false) after processing the event.\n                    if (event === 'INITIAL_SESSION' || event === 'SIGNED_IN' || event === 'SIGNED_OUT' || event === 'TOKEN_REFRESHED' || event === 'USER_UPDATED' || event === 'PASSWORD_RECOVERY') {\n                        setIsLoading(false);\n                    }\n                }\n            }[\"AuthProvider.useEffect\"]);\n            // Initial session fetch. onAuthStateChange with INITIAL_SESSION will also fire.\n            _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.getSession().then({\n                \"AuthProvider.useEffect\": ({ data: { session: initialSessionCheck }, error: getSessionError })=>{\n                    if (getSessionError) {\n                        setError(getSessionError.message);\n                        setIsLoading(false); // Ensure loading is false if initial getSession fails\n                    }\n                    // Para dispositivos móviles, verificar también localStorage si no hay sesión\n                    if (!initialSessionCheck && \"undefined\" !== 'undefined') {}\n                // If INITIAL_SESSION hasn't fired and set loading to false, and this fails, we ensure it's false.\n                // Note: if getSession is successful, `setIsLoading(false)` is primarily handled by INITIAL_SESSION event.\n                }\n            }[\"AuthProvider.useEffect\"]).catch({\n                \"AuthProvider.useEffect\": (error)=>{\n                    setError(error.message);\n                    setIsLoading(false); // Ensure loading is false if initial getSession throws\n                }\n            }[\"AuthProvider.useEffect\"]);\n            return ({\n                \"AuthProvider.useEffect\": ()=>{\n                    authListener?.subscription.unsubscribe();\n                }\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], []); // Runs once on mount\n    // Effect for handling redirections based on auth state and pathname\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // No realizar redirecciones mientras se está cargando\n            if (isLoading) {\n                return;\n            }\n            // No aplicar redirecciones a rutas de API o recursos estáticos\n            if (pathname.startsWith('/api') || pathname.startsWith('/_next')) {\n                return;\n            }\n            // Definir rutas públicas (mantener sincronizado con middleware)\n            // src/contexts/AuthContext.tsx (CORREGIDO)\n            const publicPaths = [\n                '/',\n                '/login',\n                '/payment',\n                '/thank-you',\n                '/auth/callback',\n                '/auth/confirmed',\n                '/auth/unauthorized',\n                '/auth/reset-password',\n                '/auth/confirm-reset',\n                '/auth/confirm-invitation' // <-- **AÑADIR ESTA LÍNEA**\n            ];\n            // Si hay sesión y estamos en /login, redirigir a la aplicación\n            // Esta es una salvaguarda del lado del cliente.\n            if (session && pathname === '/login') {\n                router.replace('/app'); // router.replace es mejor aquí para evitar entradas en el historial\n                return; // Importante retornar para no evaluar la siguiente condición\n            }\n            // Si NO hay sesión y NO estamos en una ruta pública (y no es una ruta API/interna)\n            // Esta lógica es para cuando el estado cambia en el cliente (ej. logout)\n            if (!session && !publicPaths.includes(pathname) && !pathname.startsWith('/api') && !pathname.startsWith('/_next')) {\n                router.replace('/login');\n                return;\n            }\n        }\n    }[\"AuthProvider.useEffect\"], [\n        session,\n        isLoading,\n        pathname,\n        router\n    ]);\n    const iniciarSesion = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[iniciarSesion]\": async (email, password_provided)=>{\n            setIsLoading(true);\n            setError(null);\n            try {\n                const { user: loggedInUser, session: currentAuthSession, error: loginError } = await (0,_lib_supabase_authService__WEBPACK_IMPORTED_MODULE_4__.iniciarSesion)(email, password_provided);\n                if (loginError) {\n                    setError(loginError);\n                    setIsLoading(false); // Ensure loading is false on error\n                    return {\n                        user: null,\n                        session: null,\n                        error: loginError\n                    };\n                }\n                // Verificar que la sesión se haya establecido correctamente antes de redirigir\n                if (currentAuthSession) {\n                    // Esperar un momento adicional para asegurar que las cookies se propaguen\n                    // antes de la redirección\n                    await new Promise({\n                        \"AuthProvider.useCallback[iniciarSesion]\": (resolve)=>setTimeout(resolve, 300)\n                    }[\"AuthProvider.useCallback[iniciarSesion]\"]);\n                    // Redirigir a la aplicación usando replace para evitar entradas en el historial\n                    router.replace('/app');\n                }\n                // If successful, onAuthStateChange (SIGNED_IN) will set user, session, and isLoading to false.\n                return {\n                    user: loggedInUser,\n                    session: currentAuthSession,\n                    error: null\n                };\n            } catch (e) {\n                const errorMessage = e instanceof Error && e.message ? e.message : 'Error desconocido durante el inicio de sesión.';\n                setError(errorMessage);\n                setIsLoading(false); // Ensure loading is false on exception\n                return {\n                    user: null,\n                    session: null,\n                    error: errorMessage\n                };\n            }\n        }\n    }[\"AuthProvider.useCallback[iniciarSesion]\"], [\n        router\n    ]); // Added router dependency\n    const cerrarSesion = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[cerrarSesion]\": async ()=>{\n            setIsLoading(true);\n            setError(null);\n            const { error: logoutError } = await (0,_lib_supabase_authService__WEBPACK_IMPORTED_MODULE_4__.cerrarSesion)();\n            if (logoutError) {\n                setError(logoutError);\n                setIsLoading(false); // Ensure loading is false on error\n            }\n        // If successful, onAuthStateChange (SIGNED_OUT) handles state updates and isLoading.\n        // The redirection useEffect will then handle redirecting to /login.\n        }\n    }[\"AuthProvider.useCallback[cerrarSesion]\"], []); // Assuming cerrarSesionService is a stable import\n    const estaAutenticado = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[estaAutenticado]\": ()=>!!user && !!session && !isLoading\n    }[\"AuthProvider.useCallback[estaAutenticado]\"], [\n        user,\n        session,\n        isLoading\n    ]);\n    // Función para manejar el logout por inactividad\n    const handleInactivityLogout = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[handleInactivityLogout]\": async ()=>{\n            // Cerrar sesión directamente sin mostrar advertencia\n            await cerrarSesion();\n        }\n    }[\"AuthProvider.useCallback[handleInactivityLogout]\"], [\n        cerrarSesion\n    ]);\n    // Función para extender la sesión\n    const handleExtendSession = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[handleExtendSession]\": ()=>{\n            setShowInactivityWarning(false);\n        // El hook useAutoLogout se reiniciará automáticamente con la actividad\n        }\n    }[\"AuthProvider.useCallback[handleExtendSession]\"], []);\n    // Función para cerrar sesión desde la advertencia\n    const handleLogoutFromWarning = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[handleLogoutFromWarning]\": async ()=>{\n            setShowInactivityWarning(false);\n            await cerrarSesion();\n        }\n    }[\"AuthProvider.useCallback[handleLogoutFromWarning]\"], [\n        cerrarSesion\n    ]);\n    // Hook para manejar la inactividad (solo si el usuario está autenticado)\n    const { resetTimer } = (0,_features_auth_hooks_useInactivityTimer__WEBPACK_IMPORTED_MODULE_5__.useAutoLogout)(5, handleInactivityLogout, estaAutenticado() // Solo activo si está autenticado\n    );\n    const value = {\n        user,\n        session,\n        isLoading,\n        error,\n        iniciarSesion,\n        cerrarSesion,\n        estaAutenticado\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: [\n            children,\n             false && /*#__PURE__*/ 0\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 223,\n        columnNumber: 5\n    }, undefined);\n};\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth debe ser utilizado dentro de un AuthProvider');\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/BackgroundTasksContext.tsx":
/*!*************************************************!*\
  !*** ./src/contexts/BackgroundTasksContext.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BackgroundTasksProvider: () => (/* binding */ BackgroundTasksProvider),\n/* harmony export */   useBackgroundTasks: () => (/* binding */ useBackgroundTasks)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ BackgroundTasksProvider,useBackgroundTasks auto */ \n\n\nconst BackgroundTasksContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst BackgroundTasksProvider = ({ children })=>{\n    const [tasks, setTasks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const addTask = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BackgroundTasksProvider.useCallback[addTask]\": (taskData)=>{\n            const id = `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n            const newTask = {\n                ...taskData,\n                id,\n                status: 'pending',\n                createdAt: new Date()\n            };\n            setTasks({\n                \"BackgroundTasksProvider.useCallback[addTask]\": (prev)=>[\n                        ...prev,\n                        newTask\n                    ]\n            }[\"BackgroundTasksProvider.useCallback[addTask]\"]);\n            // Mostrar notificación de inicio\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.loading(`Iniciando: ${newTask.title}`, {\n                id: `task_start_${id}`,\n                duration: 2000\n            });\n            return id;\n        }\n    }[\"BackgroundTasksProvider.useCallback[addTask]\"], []);\n    const updateTask = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BackgroundTasksProvider.useCallback[updateTask]\": (id, updates)=>{\n            setTasks({\n                \"BackgroundTasksProvider.useCallback[updateTask]\": (prev)=>prev.map({\n                        \"BackgroundTasksProvider.useCallback[updateTask]\": (task)=>{\n                            if (task.id === id) {\n                                const updatedTask = {\n                                    ...task,\n                                    ...updates\n                                };\n                                // Manejar notificaciones según el estado\n                                if (updates.status === 'processing' && task.status === 'pending') {\n                                    react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.dismiss(`task_start_${id}`);\n                                    react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.loading(`Procesando: ${task.title}`, {\n                                        id: `task_processing_${id}`\n                                    });\n                                } else if (updates.status === 'completed' && task.status !== 'completed') {\n                                    react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.dismiss(`task_processing_${id}`);\n                                    react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success(`Completado: ${task.title}`, {\n                                        id: `task_completed_${id}`,\n                                        duration: 4000\n                                    });\n                                    updatedTask.completedAt = new Date();\n                                } else if (updates.status === 'error' && task.status !== 'error') {\n                                    react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.dismiss(`task_processing_${id}`);\n                                    react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(`Error: ${task.title}`, {\n                                        id: `task_error_${id}`,\n                                        duration: 5000\n                                    });\n                                }\n                                return updatedTask;\n                            }\n                            return task;\n                        }\n                    }[\"BackgroundTasksProvider.useCallback[updateTask]\"])\n            }[\"BackgroundTasksProvider.useCallback[updateTask]\"]);\n        }\n    }[\"BackgroundTasksProvider.useCallback[updateTask]\"], []);\n    const removeTask = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BackgroundTasksProvider.useCallback[removeTask]\": (id)=>{\n            setTasks({\n                \"BackgroundTasksProvider.useCallback[removeTask]\": (prev)=>prev.filter({\n                        \"BackgroundTasksProvider.useCallback[removeTask]\": (task)=>task.id !== id\n                    }[\"BackgroundTasksProvider.useCallback[removeTask]\"])\n            }[\"BackgroundTasksProvider.useCallback[removeTask]\"]);\n            // Limpiar notificaciones relacionadas\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.dismiss(`task_start_${id}`);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.dismiss(`task_processing_${id}`);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.dismiss(`task_completed_${id}`);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.dismiss(`task_error_${id}`);\n        }\n    }[\"BackgroundTasksProvider.useCallback[removeTask]\"], []);\n    const getTask = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BackgroundTasksProvider.useCallback[getTask]\": (id)=>{\n            return tasks.find({\n                \"BackgroundTasksProvider.useCallback[getTask]\": (task)=>task.id === id\n            }[\"BackgroundTasksProvider.useCallback[getTask]\"]);\n        }\n    }[\"BackgroundTasksProvider.useCallback[getTask]\"], [\n        tasks\n    ]);\n    const getTasksByType = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BackgroundTasksProvider.useCallback[getTasksByType]\": (type)=>{\n            return tasks.filter({\n                \"BackgroundTasksProvider.useCallback[getTasksByType]\": (task)=>task.type === type\n            }[\"BackgroundTasksProvider.useCallback[getTasksByType]\"]);\n        }\n    }[\"BackgroundTasksProvider.useCallback[getTasksByType]\"], [\n        tasks\n    ]);\n    const clearCompletedTasks = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BackgroundTasksProvider.useCallback[clearCompletedTasks]\": ()=>{\n            setTasks({\n                \"BackgroundTasksProvider.useCallback[clearCompletedTasks]\": (prev)=>prev.filter({\n                        \"BackgroundTasksProvider.useCallback[clearCompletedTasks]\": (task)=>task.status !== 'completed' && task.status !== 'error'\n                    }[\"BackgroundTasksProvider.useCallback[clearCompletedTasks]\"])\n            }[\"BackgroundTasksProvider.useCallback[clearCompletedTasks]\"]);\n        }\n    }[\"BackgroundTasksProvider.useCallback[clearCompletedTasks]\"], []);\n    const activeTasks = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"BackgroundTasksProvider.useMemo[activeTasks]\": ()=>tasks.filter({\n                \"BackgroundTasksProvider.useMemo[activeTasks]\": (task)=>task.status === 'pending' || task.status === 'processing'\n            }[\"BackgroundTasksProvider.useMemo[activeTasks]\"])\n    }[\"BackgroundTasksProvider.useMemo[activeTasks]\"], [\n        tasks\n    ]);\n    const completedTasks = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"BackgroundTasksProvider.useMemo[completedTasks]\": ()=>tasks.filter({\n                \"BackgroundTasksProvider.useMemo[completedTasks]\": (task)=>task.status === 'completed' || task.status === 'error'\n            }[\"BackgroundTasksProvider.useMemo[completedTasks]\"])\n    }[\"BackgroundTasksProvider.useMemo[completedTasks]\"], [\n        tasks\n    ]);\n    const value = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"BackgroundTasksProvider.useMemo[value]\": ()=>({\n                tasks,\n                addTask,\n                updateTask,\n                removeTask,\n                getTask,\n                getTasksByType,\n                clearCompletedTasks,\n                activeTasks,\n                completedTasks\n            })\n    }[\"BackgroundTasksProvider.useMemo[value]\"], [\n        tasks,\n        addTask,\n        updateTask,\n        removeTask,\n        getTask,\n        getTasksByType,\n        clearCompletedTasks,\n        activeTasks,\n        completedTasks\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BackgroundTasksContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\contexts\\\\BackgroundTasksContext.tsx\",\n        lineNumber: 133,\n        columnNumber: 5\n    }, undefined);\n};\nconst useBackgroundTasks = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(BackgroundTasksContext);\n    if (context === undefined) {\n        throw new Error('useBackgroundTasks must be used within a BackgroundTasksProvider');\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/BackgroundTasksContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/features/auth/components/AuthManager.tsx":
/*!******************************************************!*\
  !*** ./src/features/auth/components/AuthManager.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AuthManager)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/supabaseClient */ \"(ssr)/./src/lib/supabase/supabaseClient.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n/**\n * Componente para manejar errores comunes de autenticación\n * y sincronización de tiempo en Supabase\n */ function AuthManager() {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"AuthManager.useEffect\": ()=>{\n            // Verificar si hay problemas de sincronización de tiempo\n            const checkTimeSync = {\n                \"AuthManager.useEffect.checkTimeSync\": async ()=>{\n                    try {\n                        // Usar variables de entorno para la configuración de Supabase\n                        const supabaseUrl = \"https://fxnhpxjijinfuxxxplzj.supabase.co\";\n                        const supabaseKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4\";\n                        if (!supabaseUrl || !supabaseKey) {\n                            console.warn('Variables de entorno de Supabase no configuradas');\n                            return;\n                        }\n                        // Obtener la hora del servidor de Supabase\n                        const response = await fetch(`${supabaseUrl}/rest/v1/`, {\n                            method: 'GET',\n                            headers: {\n                                'Content-Type': 'application/json',\n                                'apikey': supabaseKey\n                            }\n                        });\n                        // Obtener la fecha del servidor desde las cabeceras\n                        const serverDate = new Date(response.headers.get('date') || '');\n                        const clientDate = new Date();\n                        // Calcular la diferencia en segundos\n                        const timeDiff = Math.abs((serverDate.getTime() - clientDate.getTime()) / 1000);\n                        // Si la diferencia es mayor a 60 segundos, mostrar una advertencia\n                        if (timeDiff > 60) {\n                            console.warn(`Posible problema de sincronización de tiempo detectado. ` + `La diferencia entre tu hora local y el servidor es de ${Math.round(timeDiff)} segundos. ` + `Esto puede causar problemas de autenticación.`);\n                        }\n                    } catch (error) {\n                        console.error('Error al verificar sincronización de tiempo:', error);\n                    }\n                }\n            }[\"AuthManager.useEffect.checkTimeSync\"];\n            // Ejecutar la verificación\n            checkTimeSync();\n            // Configurar un listener para eventos de autenticación\n            const { data: authListener } = _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_1__.supabase.auth.onAuthStateChange({\n                \"AuthManager.useEffect\": (event, session)=>{\n                    if (event === 'SIGNED_OUT') {\n                    // Supabase ya maneja la limpieza de tokens internamente\n                    } else if (event === 'SIGNED_IN') {}\n                }\n            }[\"AuthManager.useEffect\"]);\n            return ({\n                \"AuthManager.useEffect\": ()=>{\n                    authListener.subscription.unsubscribe();\n                }\n            })[\"AuthManager.useEffect\"];\n        }\n    }[\"AuthManager.useEffect\"], []);\n    // Este componente no renderiza nada visible\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/features/auth/components/AuthManager.tsx\n");

/***/ }),

/***/ "(ssr)/./src/features/auth/components/InactivityWarning.tsx":
/*!************************************************************!*\
  !*** ./src/features/auth/components/InactivityWarning.tsx ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FiAlertTriangle_FiClock_FiRefreshCw_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=FiAlertTriangle,FiClock,FiRefreshCw!=!react-icons/fi */ \"(ssr)/./node_modules/react-icons/fi/index.mjs\");\n\n\n\nconst InactivityWarning = ({ isVisible, timeRemaining, onExtendSession, onLogout })=>{\n    const [countdown, setCountdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(timeRemaining);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"InactivityWarning.useEffect\": ()=>{\n            setCountdown(timeRemaining);\n        }\n    }[\"InactivityWarning.useEffect\"], [\n        timeRemaining\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"InactivityWarning.useEffect\": ()=>{\n            if (!isVisible || countdown <= 0) return;\n            const interval = setInterval({\n                \"InactivityWarning.useEffect.interval\": ()=>{\n                    setCountdown({\n                        \"InactivityWarning.useEffect.interval\": (prev)=>{\n                            if (prev <= 1) {\n                                onLogout();\n                                return 0;\n                            }\n                            return prev - 1;\n                        }\n                    }[\"InactivityWarning.useEffect.interval\"]);\n                }\n            }[\"InactivityWarning.useEffect.interval\"], 1000);\n            return ({\n                \"InactivityWarning.useEffect\": ()=>clearInterval(interval)\n            })[\"InactivityWarning.useEffect\"];\n        }\n    }[\"InactivityWarning.useEffect\"], [\n        isVisible,\n        countdown,\n        onLogout\n    ]);\n    if (!isVisible) return null;\n    const formatTime = (seconds)=>{\n        const mins = Math.floor(seconds / 60);\n        const secs = seconds % 60;\n        return `${mins}:${secs.toString().padStart(2, '0')}`;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-xl p-6 max-w-md w-full mx-4 animate-pulse\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-yellow-100 rounded-full p-3 mr-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiClock_FiRefreshCw_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiAlertTriangle, {\n                                className: \"text-yellow-600 text-xl\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900\",\n                                    children: \"Sesi\\xf3n por expirar\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"Tu sesi\\xf3n expirar\\xe1 por inactividad\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-50 rounded-lg p-4 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiClock_FiRefreshCw_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiClock, {\n                                    className: \"text-gray-500 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-2xl font-mono font-bold text-gray-900\",\n                                    children: formatTime(countdown)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-center text-sm text-gray-600 mt-2\",\n                            children: \"Tiempo restante antes del cierre autom\\xe1tico\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onExtendSession,\n                            className: \"flex-1 bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-lg flex items-center justify-center transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiClock_FiRefreshCw_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiRefreshCw, {\n                                    className: \"mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"Continuar sesi\\xf3n\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onLogout,\n                            className: \"flex-1 bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded-lg transition-colors\",\n                            children: \"Cerrar sesi\\xf3n\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-4 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-gray-500\",\n                        children: \"Por seguridad, tu sesi\\xf3n se cerrar\\xe1 autom\\xe1ticamente tras 10 minutos de inactividad\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n            lineNumber: 49,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (InactivityWarning);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/features/auth/components/InactivityWarning.tsx\n");

/***/ }),

/***/ "(ssr)/./src/features/auth/hooks/useInactivityTimer.ts":
/*!*******************************************************!*\
  !*** ./src/features/auth/hooks/useInactivityTimer.ts ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAutoLogout: () => (/* binding */ useAutoLogout),\n/* harmony export */   useInactivityTimer: () => (/* binding */ useInactivityTimer)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _contexts_BackgroundTasksContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/BackgroundTasksContext */ \"(ssr)/./src/contexts/BackgroundTasksContext.tsx\");\n\n\n// Eventos que consideramos como actividad del usuario (definido fuera del hook para estabilidad)\nconst ACTIVITY_EVENTS = [\n    'mousedown',\n    'mousemove',\n    'keypress',\n    'scroll',\n    'touchstart',\n    'click'\n];\n/**\n * Hook para manejar la desconexión automática por inactividad\n */ const useInactivityTimer = ({ timeout, onTimeout, enabled = true })=>{\n    const timeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const lastActivityRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(Date.now());\n    const { tasks } = (0,_contexts_BackgroundTasksContext__WEBPACK_IMPORTED_MODULE_1__.useBackgroundTasks)();\n    // Verificar si hay tareas de IA activas que deberían pausar el timer\n    const hasActiveAITasks = tasks.some((task)=>task.status === 'pending' || task.status === 'processing');\n    // Función para resetear el timer\n    const resetTimer = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useInactivityTimer.useCallback[resetTimer]\": ()=>{\n            if (!enabled) return;\n            // Limpiar el timer anterior si existe\n            if (timeoutRef.current) {\n                clearTimeout(timeoutRef.current);\n            }\n            // Actualizar la última actividad\n            lastActivityRef.current = Date.now();\n            // Siempre crear un nuevo timer, independientemente de las tareas de IA\n            // La actividad del usuario siempre reinicia el timer\n            timeoutRef.current = setTimeout({\n                \"useInactivityTimer.useCallback[resetTimer]\": ()=>{\n                    // Solo ejecutar logout si no hay tareas de IA activas en el momento del timeout\n                    const currentTasks = tasks.filter({\n                        \"useInactivityTimer.useCallback[resetTimer].currentTasks\": (task)=>task.status === 'pending' || task.status === 'processing'\n                    }[\"useInactivityTimer.useCallback[resetTimer].currentTasks\"]);\n                    if (currentTasks.length === 0) {\n                        onTimeout();\n                    } else {\n                        console.log('🔄 Logout diferido: hay tareas de IA en progreso, reintentando en 1 minuto');\n                        // Reintentar en 1 minuto si hay tareas activas\n                        setTimeout({\n                            \"useInactivityTimer.useCallback[resetTimer]\": ()=>resetTimer()\n                        }[\"useInactivityTimer.useCallback[resetTimer]\"], 60000);\n                    }\n                }\n            }[\"useInactivityTimer.useCallback[resetTimer]\"], timeout);\n        }\n    }[\"useInactivityTimer.useCallback[resetTimer]\"], [\n        timeout,\n        onTimeout,\n        enabled,\n        tasks\n    ]);\n    // Función para limpiar el timer\n    const clearTimer = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useInactivityTimer.useCallback[clearTimer]\": ()=>{\n            if (timeoutRef.current) {\n                clearTimeout(timeoutRef.current);\n                timeoutRef.current = null;\n            }\n        }\n    }[\"useInactivityTimer.useCallback[clearTimer]\"], []);\n    // Función para obtener el tiempo restante\n    const getTimeRemaining = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useInactivityTimer.useCallback[getTimeRemaining]\": ()=>{\n            if (!enabled || !timeoutRef.current) return 0;\n            const elapsed = Date.now() - lastActivityRef.current;\n            const remaining = Math.max(0, timeout - elapsed);\n            return remaining;\n        }\n    }[\"useInactivityTimer.useCallback[getTimeRemaining]\"], [\n        timeout,\n        enabled\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useInactivityTimer.useEffect\": ()=>{\n            if (!enabled) {\n                clearTimer();\n                return;\n            }\n            // Función que maneja los eventos de actividad\n            const handleActivity = {\n                \"useInactivityTimer.useEffect.handleActivity\": ()=>{\n                    resetTimer();\n                }\n            }[\"useInactivityTimer.useEffect.handleActivity\"];\n            // Agregar event listeners\n            ACTIVITY_EVENTS.forEach({\n                \"useInactivityTimer.useEffect\": (event)=>{\n                    document.addEventListener(event, handleActivity, true);\n                }\n            }[\"useInactivityTimer.useEffect\"]);\n            // Iniciar el timer\n            resetTimer();\n            // Cleanup\n            return ({\n                \"useInactivityTimer.useEffect\": ()=>{\n                    ACTIVITY_EVENTS.forEach({\n                        \"useInactivityTimer.useEffect\": (event)=>{\n                            document.removeEventListener(event, handleActivity, true);\n                        }\n                    }[\"useInactivityTimer.useEffect\"]);\n                    clearTimer();\n                }\n            })[\"useInactivityTimer.useEffect\"];\n        }\n    }[\"useInactivityTimer.useEffect\"], [\n        enabled,\n        resetTimer,\n        clearTimer\n    ]);\n    return {\n        resetTimer,\n        clearTimer,\n        getTimeRemaining\n    };\n};\n/**\n * Hook simplificado para desconexión automática\n */ const useAutoLogout = (timeoutMinutes = 5, onLogout, enabled = true)=>{\n    const timeoutMs = timeoutMinutes * 60 * 1000; // Convertir minutos a milisegundos\n    return useInactivityTimer({\n        timeout: timeoutMs,\n        onTimeout: onLogout,\n        enabled\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/features/auth/hooks/useInactivityTimer.ts\n");

/***/ }),

/***/ "(ssr)/./src/features/shared/components/BackgroundTasksPanel.tsx":
/*!*****************************************************************!*\
  !*** ./src/features/shared/components/BackgroundTasksPanel.tsx ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_BackgroundTasksContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/BackgroundTasksContext */ \"(ssr)/./src/contexts/BackgroundTasksContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ChevronDownIcon,ChevronUpIcon,ClockIcon,Cog6ToothIcon,ExclamationCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ChevronDownIcon,ChevronUpIcon,ClockIcon,Cog6ToothIcon,ExclamationCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ChevronDownIcon,ChevronUpIcon,ClockIcon,Cog6ToothIcon,ExclamationCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ChevronDownIcon,ChevronUpIcon,ClockIcon,Cog6ToothIcon,ExclamationCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ExclamationCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ChevronDownIcon,ChevronUpIcon,ClockIcon,Cog6ToothIcon,ExclamationCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ChevronDownIcon,ChevronUpIcon,ClockIcon,Cog6ToothIcon,ExclamationCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChevronUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ChevronDownIcon,ChevronUpIcon,ClockIcon,Cog6ToothIcon,ExclamationCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst BackgroundTasksPanel = ()=>{\n    const { activeTasks, completedTasks, removeTask, clearCompletedTasks } = (0,_contexts_BackgroundTasksContext__WEBPACK_IMPORTED_MODULE_2__.useBackgroundTasks)();\n    const [isExpanded, setIsExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCompleted, setShowCompleted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const totalTasks = activeTasks.length + completedTasks.length;\n    if (totalTasks === 0) {\n        return null;\n    }\n    const getTaskIcon = (task)=>{\n        switch(task.status){\n            case 'pending':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"h-4 w-4 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                    lineNumber: 29,\n                    columnNumber: 16\n                }, undefined);\n            case 'processing':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-4 w-4 text-blue-500 animate-spin\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 16\n                }, undefined);\n            case 'completed':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-4 w-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 16\n                }, undefined);\n            case 'error':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-4 w-4 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"h-4 w-4 text-gray-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    const getTaskTypeLabel = (type)=>{\n        switch(type){\n            case 'mapa-mental':\n                return 'Mapa Mental';\n            case 'test':\n                return 'Test';\n            case 'flashcards':\n                return 'Flashcards';\n            default:\n                return 'Tarea';\n        }\n    };\n    const formatTime = (date)=>{\n        return date.toLocaleTimeString('es-ES', {\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed bottom-4 right-4 z-50 max-w-sm\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg border border-gray-200 overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-blue-500 to-purple-600 text-white p-3 cursor-pointer flex items-center justify-between\",\n                    onClick: ()=>setIsExpanded(!isExpanded),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: [\n                                        \"Tareas (\",\n                                        activeTasks.length,\n                                        \" activas)\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 11\n                        }, undefined),\n                        isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"h-5 w-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"h-5 w-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 9\n                }, undefined),\n                isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-h-96 overflow-y-auto\",\n                    children: [\n                        activeTasks.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-3 border-b border-gray-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-semibold text-gray-700 mb-2\",\n                                    children: \"Tareas Activas\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: activeTasks.map((task)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 p-2 bg-gray-50 rounded-md\",\n                                            children: [\n                                                getTaskIcon(task),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 min-w-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-900 truncate\",\n                                                            children: getTaskTypeLabel(task.type)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                                            lineNumber: 100,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500 truncate\",\n                                                            children: task.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                                            lineNumber: 103,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        task.progress !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-1\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-gray-200 rounded-full h-1.5\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-blue-500 h-1.5 rounded-full transition-all duration-300\",\n                                                                    style: {\n                                                                        width: `${task.progress}%`\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                                                    lineNumber: 109,\n                                                                    columnNumber: 31\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                                                lineNumber: 108,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                                            lineNumber: 107,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                                    lineNumber: 99,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-400\",\n                                                    children: formatTime(task.createdAt)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, task.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 21\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 15\n                        }, undefined),\n                        completedTasks.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowCompleted(!showCompleted),\n                                            className: \"text-sm font-semibold text-gray-700 hover:text-gray-900 flex items-center space-x-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        \"Completadas (\",\n                                                        completedTasks.length,\n                                                        \")\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                                    lineNumber: 134,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                showCompleted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                                    lineNumber: 136,\n                                                    columnNumber: 23\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                                    lineNumber: 138,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        completedTasks.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: clearCompletedTasks,\n                                            className: \"text-xs text-gray-500 hover:text-red-600 transition-colors\",\n                                            children: \"Limpiar\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 17\n                                }, undefined),\n                                showCompleted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: completedTasks.map((task)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 p-2 bg-gray-50 rounded-md\",\n                                            children: [\n                                                getTaskIcon(task),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 min-w-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-900 truncate\",\n                                                            children: getTaskTypeLabel(task.type)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                                            lineNumber: 160,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500 truncate\",\n                                                            children: task.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                                            lineNumber: 163,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        task.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-red-500 truncate\",\n                                                            children: task.error\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                                            lineNumber: 167,\n                                                            columnNumber: 29\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-gray-400\",\n                                                            children: task.completedAt ? formatTime(task.completedAt) : formatTime(task.createdAt)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                                            lineNumber: 173,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>removeTask(task.id),\n                                                            className: \"text-gray-400 hover:text-red-500 transition-colors\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                                                lineNumber: 180,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                                            lineNumber: 176,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            ]\n                                        }, task.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 23\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 19\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n            lineNumber: 64,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n        lineNumber: 62,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BackgroundTasksPanel);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/features/shared/components/BackgroundTasksPanel.tsx\n");

/***/ }),

/***/ "(ssr)/./src/features/shared/components/ClientLayout.tsx":
/*!*********************************************************!*\
  !*** ./src/features/shared/components/ClientLayout.tsx ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ClientLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_BackgroundTasksContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/BackgroundTasksContext */ \"(ssr)/./src/contexts/BackgroundTasksContext.tsx\");\n/* harmony import */ var _auth_components_AuthManager__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../auth/components/AuthManager */ \"(ssr)/./src/features/auth/components/AuthManager.tsx\");\n/* harmony import */ var _BackgroundTasksPanel__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./BackgroundTasksPanel */ \"(ssr)/./src/features/shared/components/BackgroundTasksPanel.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n // Importar Toaster\nfunction ClientLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_BackgroundTasksContext__WEBPACK_IMPORTED_MODULE_3__.BackgroundTasksProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_auth_components_AuthManager__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\shared\\\\components\\\\ClientLayout.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.Toaster, {\n                    position: \"top-right\" // Posición de los toasts\n                    ,\n                    toastOptions: {\n                        // Opciones por defecto para los toasts\n                        duration: 5000,\n                        style: {\n                            background: '#363636',\n                            color: '#fff'\n                        },\n                        success: {\n                            duration: 3000,\n                            style: {\n                                background: '#10b981',\n                                color: '#fff'\n                            }\n                        },\n                        error: {\n                            duration: 5000,\n                            style: {\n                                background: '#ef4444',\n                                color: '#fff'\n                            }\n                        }\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\shared\\\\components\\\\ClientLayout.tsx\",\n                    lineNumber: 19,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BackgroundTasksPanel__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\shared\\\\components\\\\ClientLayout.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 9\n                }, this),\n                children\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\shared\\\\components\\\\ClientLayout.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\shared\\\\components\\\\ClientLayout.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/features/shared/components/ClientLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/stripe/plans.ts":
/*!*********************************!*\
  !*** ./src/lib/stripe/plans.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ADDITIONAL_PRODUCTS: () => (/* binding */ ADDITIONAL_PRODUCTS),\n/* harmony export */   APP_URLS: () => (/* binding */ APP_URLS),\n/* harmony export */   PLANS: () => (/* binding */ PLANS),\n/* harmony export */   getFullPlanConfig: () => (/* binding */ getFullPlanConfig),\n/* harmony export */   getPlanById: () => (/* binding */ getPlanById),\n/* harmony export */   isValidPlan: () => (/* binding */ isValidPlan)\n/* harmony export */ });\n/* harmony import */ var _lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/utils/planLimits */ \"(ssr)/./src/lib/utils/planLimits.ts\");\n// src/lib/stripe/plans.ts\n// Configuración de planes integrada con sistema de límites\n\nconst PLANS = {\n    free: {\n        id: 'free',\n        name: 'Plan Gratis',\n        price: 0,\n        stripeProductId: null,\n        stripePriceId: null,\n        features: [\n            'Incluye:',\n            '• Uso de la plataforma solo durante 5 días',\n            '• Subida de documentos: máximo 1 documento',\n            '• Generador de test: máximo 10 preguntas test',\n            '• Generador de flashcards: máximo 10 tarjetas flashcard',\n            '• Generador de mapas mentales: máximo 2 mapas mentales',\n            'No incluye:',\n            '• Planificación de estudios',\n            '• Habla con tu preparador IA',\n            '• Resúmenes para el ejercicio de desarrollo para cuerpos superiores A2 y A1'\n        ],\n        limits: _lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_0__.PLAN_CONFIGURATIONS.free.limits,\n        planConfig: _lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_0__.PLAN_CONFIGURATIONS.free\n    },\n    usuario: {\n        id: 'usuario',\n        name: 'Plan Usuario',\n        price: 1000,\n        stripeProductId: 'prod_SR65BdKdek1OXd',\n        // IMPORTANTE: Este precio debe ser recurrente (suscripción mensual) en Stripe\n        // Si actualmente es un pago único, crear un nuevo precio recurrente en Stripe Dashboard\n        stripePriceId: 'price_1Rae5807kFn3sIXhRf3adX1n',\n        features: [\n            'Incluye:',\n            '• Uso de la plataforma durante el mes (una vez finalizado no podrá volver a acceder hasta renovación)',\n            '• Subida de documentos',\n            '• Habla con tu preparador IA *',\n            '• Generador de test *',\n            '• Generador de flashcards *',\n            '• Generador de mapas mentales *',\n            '• * Para las tareas en las que se haga uso de IA, el límite mensual será de 1.000.000 de tokens, podrán ampliarse durante el mes mediante pago',\n            'No incluye:',\n            '• Planificación de estudios',\n            '• Resúmenes para el ejercicio de desarrollo para cuerpos superiores A2 y A1'\n        ],\n        limits: _lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_0__.PLAN_CONFIGURATIONS.usuario.limits,\n        planConfig: _lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_0__.PLAN_CONFIGURATIONS.usuario\n    },\n    pro: {\n        id: 'pro',\n        name: 'Plan Pro',\n        price: 1500,\n        stripeProductId: 'prod_SR66U2G7bVJqu3',\n        stripePriceId: 'price_1Rae3U07kFn3sIXhkvSuJco1',\n        features: [\n            'Incluye:',\n            '• Uso de la plataforma durante el mes (una vez finalizado no podrá volver a acceder hasta renovación)',\n            '• Subida de documentos',\n            '• Planificación de estudios mediante IA*',\n            '• Habla con tu preparador IA *',\n            '• Generador de test *',\n            '• Generador de flashcards *',\n            '• Generador de mapas mentales *',\n            '• Generación de resúmenes para el ejercicio de desarrollo para cuerpos superiores A2 y A1',\n            '• * Para las tareas en las que se haga uso de IA, el límite mensual será de 1.000.000 de tokens, podrán ampliarse durante el mes mediante pago'\n        ],\n        limits: _lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_0__.PLAN_CONFIGURATIONS.pro.limits,\n        planConfig: _lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_0__.PLAN_CONFIGURATIONS.pro\n    }\n};\n// Función para obtener plan por ID\nfunction getPlanById(planId) {\n    return PLANS[planId] || null;\n}\n// Función para validar si un plan es válido\nfunction isValidPlan(planId) {\n    return planId in PLANS;\n}\n// Función para obtener configuración completa del plan\nfunction getFullPlanConfig(planId) {\n    const plan = getPlanById(planId);\n    return plan?.planConfig || null;\n}\n// Configuración de productos adicionales\nconst ADDITIONAL_PRODUCTS = {\n    tokens: {\n        id: 'tokens',\n        name: 'Tokens Adicionales',\n        description: '1,000,000 tokens adicionales para tu cuenta',\n        price: 1000,\n        tokenAmount: 1000000,\n        // Estos IDs se deben crear en Stripe Dashboard\n        stripeProductId: 'prod_tokens_additional',\n        stripePriceId: 'price_tokens_additional'\n    }\n};\n// URLs de la aplicación\nconst APP_URLS = {\n    success: `${\"http://localhost:3000\"}/thank-you`,\n    cancel: `${\"http://localhost:3000\"}/upgrade-plan`,\n    webhook: `${\"http://localhost:3000\"}/api/stripe/webhook`\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/stripe/plans.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase/authService.ts":
/*!*****************************************!*\
  !*** ./src/lib/supabase/authService.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cerrarSesion: () => (/* binding */ cerrarSesion),\n/* harmony export */   estaAutenticado: () => (/* binding */ estaAutenticado),\n/* harmony export */   iniciarSesion: () => (/* binding */ iniciarSesion),\n/* harmony export */   obtenerSesion: () => (/* binding */ obtenerSesion),\n/* harmony export */   obtenerUsuarioActual: () => (/* binding */ obtenerUsuarioActual)\n/* harmony export */ });\n/* harmony import */ var _supabaseClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabaseClient */ \"(ssr)/./src/lib/supabase/supabaseClient.ts\");\n\n/**\r\n * Inicia sesión con email y contraseña\r\n */ async function iniciarSesion(email, password) {\n    try {\n        // Verificar que el email y la contraseña no estén vacíos\n        if (!email || !password) {\n            return {\n                user: null,\n                session: null,\n                error: 'Por favor, ingresa tu email y contraseña'\n            };\n        }\n        // No cerramos la sesión antes de iniciar una nueva, esto causa un ciclo\n        const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.signInWithPassword({\n            email: email.trim(),\n            password: password\n        });\n        if (error) {\n            // Manejar específicamente el error de sincronización de tiempo\n            if (error.message.includes('issued in the future') || error.message.includes('clock for skew')) {\n                return {\n                    user: null,\n                    session: null,\n                    error: 'Error de sincronización de tiempo. Por favor, verifica que la hora de tu dispositivo esté correctamente configurada.'\n                };\n            }\n            // Manejar error de credenciales inválidas de forma más amigable\n            if (error.message.includes('Invalid login credentials')) {\n                return {\n                    user: null,\n                    session: null,\n                    error: 'Email o contraseña incorrectos. Por favor, verifica tus credenciales.'\n                };\n            }\n            return {\n                user: null,\n                session: null,\n                error: error.message\n            }; // Added session\n        }\n        // Ensure data.user and data.session exist before returning\n        if (data && data.user && data.session) {\n            // Esperar un momento para asegurar que las cookies se establezcan\n            // Esto es importante para que el middleware pueda detectar la sesión\n            await new Promise((resolve)=>setTimeout(resolve, 800));\n            // Verificar que la sesión esté disponible después de establecer las cookies\n            await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getSession();\n            return {\n                user: data.user,\n                session: data.session,\n                error: null\n            }; // Added session\n        } else {\n            // This case should ideally not be reached if Supabase call is successful\n            // but provides a fallback if data or its properties are unexpectedly null/undefined.\n            return {\n                user: null,\n                session: null,\n                error: 'Respuesta inesperada del servidor al iniciar sesión.'\n            };\n        }\n    } catch (e) {\n        // Check if 'e' is an Error object and has a message property\n        const errorMessage = e instanceof Error && e.message ? e.message : 'Ha ocurrido un error inesperado al iniciar sesión';\n        return {\n            user: null,\n            session: null,\n            error: errorMessage\n        };\n    }\n}\n/**\r\n * Cierra la sesión del usuario actual\r\n */ async function cerrarSesion() {\n    try {\n        console.log('🔓 Iniciando proceso de logout...');\n        // Cerrar sesión con scope 'global' para limpiar tanto local como servidor\n        const { error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.signOut({\n            scope: 'global'\n        });\n        if (error) {\n            console.error('❌ Error en signOut:', error.message);\n            return {\n                error: error.message\n            };\n        }\n        console.log('✅ SignOut exitoso');\n        // Limpiar cualquier dato de sesión residual del localStorage/sessionStorage\n        if (false) {}\n        return {\n            error: null\n        };\n    } catch (error) {\n        console.error('❌ Error inesperado en logout:', error);\n        return {\n            error: 'Ha ocurrido un error inesperado al cerrar sesión'\n        };\n    }\n}\n/**\r\n * Obtiene la sesión actual del usuario\r\n */ async function obtenerSesion() {\n    try {\n        const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getSession();\n        if (error) {\n            // Si el error es \"Auth session missing\", es un caso esperado cuando no hay sesión\n            if (error.message === 'Auth session missing!') {\n                return {\n                    session: null,\n                    error: null\n                };\n            }\n            return {\n                session: null,\n                error: error.message\n            };\n        }\n        return {\n            session: data.session,\n            error: null\n        };\n    } catch (error) {\n        return {\n            session: null,\n            error: 'Ha ocurrido un error inesperado al obtener la sesión'\n        };\n    }\n}\n/**\r\n * Obtiene el usuario actual\r\n */ async function obtenerUsuarioActual() {\n    try {\n        const { data: { user }, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (error) {\n            // Si el error es \"Auth session missing\", es un caso esperado cuando no hay sesión\n            if (error.message === 'Auth session missing!') {\n                return {\n                    user: null,\n                    error: null\n                };\n            }\n            return {\n                user: null,\n                error: error.message\n            };\n        }\n        return {\n            user,\n            error: null\n        };\n    } catch (error) {\n        return {\n            user: null,\n            error: 'Ha ocurrido un error inesperado al obtener el usuario actual'\n        };\n    }\n}\n/**\r\n * Verifica si el usuario está autenticado\r\n */ async function estaAutenticado() {\n    const { session } = await obtenerSesion();\n    return session !== null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase/authService.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase/client.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/client.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClient: () => (/* binding */ createClient),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(ssr)/./node_modules/@supabase/ssr/dist/module/index.js\");\n\n// Cliente para el navegador (componentes del cliente)\nfunction createClient() {\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createBrowserClient)(\"https://fxnhpxjijinfuxxxplzj.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4\", {\n        auth: {\n            persistSession: true,\n            autoRefreshToken: true,\n            detectSessionInUrl: true // ESENCIAL: Detectar y procesar tokens de URL\n        }\n    });\n}\n// Mantener compatibilidad con código existente\nconst supabase = createClient();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3N1cGFiYXNlL2NsaWVudC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBb0Q7QUFFcEQsc0RBQXNEO0FBQy9DLFNBQVNDO0lBQ2QsT0FBT0Qsa0VBQW1CQSxDQUN4QkUsMENBQW9DLEVBQ3BDQSxrTkFBeUMsRUFDekM7UUFDRUksTUFBTTtZQUNKQyxnQkFBZ0I7WUFDaEJDLGtCQUFrQjtZQUNsQkMsb0JBQW9CLEtBQVEsOENBQThDO1FBQzVFO0lBQ0Y7QUFFSjtBQUVBLCtDQUErQztBQUN4QyxNQUFNQyxXQUFXVCxlQUFlIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG5hYXRhXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXE9wb3NJXFx2MTZcXHNyY1xcbGliXFxzdXBhYmFzZVxcY2xpZW50LnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZUJyb3dzZXJDbGllbnQgfSBmcm9tICdAc3VwYWJhc2Uvc3NyJztcblxuLy8gQ2xpZW50ZSBwYXJhIGVsIG5hdmVnYWRvciAoY29tcG9uZW50ZXMgZGVsIGNsaWVudGUpXG5leHBvcnQgZnVuY3Rpb24gY3JlYXRlQ2xpZW50KCkge1xuICByZXR1cm4gY3JlYXRlQnJvd3NlckNsaWVudChcbiAgICBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkwhLFxuICAgIHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX0FOT05fS0VZISxcbiAgICB7XG4gICAgICBhdXRoOiB7XG4gICAgICAgIHBlcnNpc3RTZXNzaW9uOiB0cnVlLCAgICAgICAvLyBQZXJzaXN0aXIgc2VzacOzbiBlbiBlbCBuYXZlZ2Fkb3JcbiAgICAgICAgYXV0b1JlZnJlc2hUb2tlbjogdHJ1ZSwgICAgIC8vIFJlZnJlc2NhciB0b2tlbiBhdXRvbcOhdGljYW1lbnRlXG4gICAgICAgIGRldGVjdFNlc3Npb25JblVybDogdHJ1ZSAgICAvLyBFU0VOQ0lBTDogRGV0ZWN0YXIgeSBwcm9jZXNhciB0b2tlbnMgZGUgVVJMXG4gICAgICB9XG4gICAgfVxuICApO1xufVxuXG4vLyBNYW50ZW5lciBjb21wYXRpYmlsaWRhZCBjb24gY8OzZGlnbyBleGlzdGVudGVcbmV4cG9ydCBjb25zdCBzdXBhYmFzZSA9IGNyZWF0ZUNsaWVudCgpO1xuIl0sIm5hbWVzIjpbImNyZWF0ZUJyb3dzZXJDbGllbnQiLCJjcmVhdGVDbGllbnQiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMIiwiTkVYVF9QVUJMSUNfU1VQQUJBU0VfQU5PTl9LRVkiLCJhdXRoIiwicGVyc2lzdFNlc3Npb24iLCJhdXRvUmVmcmVzaFRva2VuIiwiZGV0ZWN0U2Vzc2lvbkluVXJsIiwic3VwYWJhc2UiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase/client.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase/supabaseClient.ts":
/*!********************************************!*\
  !*** ./src/lib/supabase/supabaseClient.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClient: () => (/* reexport safe */ _client__WEBPACK_IMPORTED_MODULE_0__.createClient),\n/* harmony export */   supabase: () => (/* reexport safe */ _client__WEBPACK_IMPORTED_MODULE_0__.supabase)\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client */ \"(ssr)/./src/lib/supabase/client.ts\");\n// Solo re-exportar el cliente del navegador para mantener compatibilidad\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase/supabaseClient.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils/planLimits.ts":
/*!*************************************!*\
  !*** ./src/lib/utils/planLimits.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PLAN_CONFIGURATIONS: () => (/* binding */ PLAN_CONFIGURATIONS),\n/* harmony export */   canPerformAction: () => (/* binding */ canPerformAction),\n/* harmony export */   checkUserFeatureAccess: () => (/* binding */ checkUserFeatureAccess),\n/* harmony export */   getPlanConfiguration: () => (/* binding */ getPlanConfiguration),\n/* harmony export */   getTokenLimitForPlan: () => (/* binding */ getTokenLimitForPlan),\n/* harmony export */   getTrialLimit: () => (/* binding */ getTrialLimit),\n/* harmony export */   getWeeklyLimit: () => (/* binding */ getWeeklyLimit),\n/* harmony export */   hasFeatureAccess: () => (/* binding */ hasFeatureAccess),\n/* harmony export */   isUnlimited: () => (/* binding */ isUnlimited)\n/* harmony export */ });\n// src/lib/utils/planLimits.ts\n// Configuración centralizada de límites y características por plan\n// Configuración completa de planes con límites y características\nconst PLAN_CONFIGURATIONS = {\n    free: {\n        id: 'free',\n        name: 'Plan Gratis',\n        price: 0,\n        limits: {\n            documents: 1,\n            mindMapsForTrial: 2,\n            testsForTrial: 10,\n            flashcardsForTrial: 10,\n            tokensForTrial: 50000,\n            features: [\n                'document_upload',\n                'test_generation',\n                'flashcard_generation',\n                'mind_map_generation'\n            ]\n        },\n        features: [\n            'document_upload',\n            'test_generation',\n            'flashcard_generation',\n            'mind_map_generation'\n        ],\n        restrictedFeatures: [\n            'study_planning',\n            'ai_tutor_chat',\n            'summary_a1_a2'\n        ]\n    },\n    usuario: {\n        id: 'usuario',\n        name: 'Plan Usuario',\n        price: 1000,\n        limits: {\n            documents: -1,\n            mindMapsPerWeek: -1,\n            testsPerWeek: -1,\n            flashcardsPerWeek: -1,\n            monthlyTokens: 1000000,\n            features: [\n                'document_upload',\n                'ai_tutor_chat',\n                'test_generation',\n                'flashcard_generation',\n                'mind_map_generation'\n            ]\n        },\n        features: [\n            'document_upload',\n            'ai_tutor_chat',\n            'test_generation',\n            'flashcard_generation',\n            'mind_map_generation'\n        ],\n        restrictedFeatures: [\n            'study_planning',\n            'summary_a1_a2'\n        ]\n    },\n    pro: {\n        id: 'pro',\n        name: 'Plan Pro',\n        price: 1500,\n        limits: {\n            documents: -1,\n            mindMapsPerWeek: -1,\n            testsPerWeek: -1,\n            flashcardsPerWeek: -1,\n            monthlyTokens: 1000000,\n            features: [\n                'document_upload',\n                'study_planning',\n                'ai_tutor_chat',\n                'test_generation',\n                'flashcard_generation',\n                'mind_map_generation',\n                'summary_a1_a2'\n            ]\n        },\n        features: [\n            'document_upload',\n            'study_planning',\n            'ai_tutor_chat',\n            'test_generation',\n            'flashcard_generation',\n            'mind_map_generation',\n            'summary_a1_a2'\n        ],\n        restrictedFeatures: []\n    }\n};\n// Funciones de utilidad\nfunction getPlanConfiguration(planId) {\n    return PLAN_CONFIGURATIONS[planId] || null;\n}\nfunction getTokenLimitForPlan(planId) {\n    const config = getPlanConfiguration(planId);\n    if (!config) return 50000;\n    // Para plan gratuito, usar tokensForTrial\n    if (planId === 'free') {\n        return config.limits.tokensForTrial || 50000;\n    }\n    // Para planes de pago, usar monthlyTokens\n    return config.limits.monthlyTokens || 1000000;\n}\nfunction hasFeatureAccess(planId, feature) {\n    const config = getPlanConfiguration(planId);\n    if (!config) return false;\n    // Si la característica está en la lista de restringidas, no tiene acceso\n    if (config.restrictedFeatures.includes(feature)) {\n        return false;\n    }\n    // Si no está restringida, verificar si está en las características permitidas\n    return config.features.includes(feature);\n}\nfunction getWeeklyLimit(planId, limitType) {\n    const config = getPlanConfiguration(planId);\n    if (!config) return 0;\n    switch(limitType){\n        case 'mindMaps':\n            return config.limits.mindMapsPerWeek || 0;\n        case 'tests':\n            return config.limits.testsPerWeek || 0;\n        case 'flashcards':\n            return config.limits.flashcardsPerWeek || 0;\n        default:\n            return 0;\n    }\n}\n// Nueva función para obtener límites de trial (para plan gratuito)\nfunction getTrialLimit(planId, limitType) {\n    const config = getPlanConfiguration(planId);\n    if (!config || planId !== 'free') return -1; // -1 para ilimitado o no aplica\n    switch(limitType){\n        case 'mindMaps':\n            return config.limits.mindMapsForTrial || 0;\n        case 'tests':\n            return config.limits.testsForTrial || 0;\n        case 'flashcards':\n            return config.limits.flashcardsForTrial || 0;\n        case 'tokens':\n            return config.limits.tokensForTrial || 0;\n        default:\n            return 0;\n    }\n}\nfunction isUnlimited(limit) {\n    return limit === -1;\n}\n// Validar si un usuario puede realizar una acción específica\nfunction canPerformAction(planId, feature, currentUsage, limitType) {\n    const config = getPlanConfiguration(planId);\n    if (!config) {\n        return {\n            allowed: false,\n            reason: 'Plan no válido'\n        };\n    }\n    // Verificar si tiene acceso a la característica\n    if (!hasFeatureAccess(planId, feature)) {\n        return {\n            allowed: false,\n            reason: `Característica ${feature} no disponible en ${config.name}`\n        };\n    }\n    // Verificar límites semanales si aplica\n    if (limitType) {\n        const weeklyLimit = getWeeklyLimit(planId, limitType);\n        if (!isUnlimited(weeklyLimit) && currentUsage >= weeklyLimit) {\n            return {\n                allowed: false,\n                reason: `Límite semanal de ${limitType} alcanzado (${weeklyLimit})`\n            };\n        }\n    }\n    return {\n        allowed: true\n    };\n}\n// Función para verificar acceso de usuario (para uso en frontend)\nasync function checkUserFeatureAccess(feature) {\n    try {\n        // Obtener el plan del usuario desde la API\n        const response = await fetch('/api/user/plan');\n        if (!response.ok) {\n            console.error('Error obteniendo plan del usuario');\n            // Si hay error, asumir plan gratuito\n            return hasFeatureAccess('free', feature);\n        }\n        const { plan } = await response.json();\n        const userPlan = plan || 'free';\n        // Usar la misma lógica que la función hasFeatureAccess\n        return hasFeatureAccess(userPlan, feature);\n    } catch (error) {\n        console.error('Error verificando acceso a característica:', error);\n        // En caso de error, asumir plan gratuito\n        return hasFeatureAccess('free', feature);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils/planLimits.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/react-icons","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/react-hot-toast","vendor-chunks/cookie","vendor-chunks/@heroicons","vendor-chunks/webidl-conversions","vendor-chunks/goober"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpayment%2Fpage&page=%2Fpayment%2Fpage&appPaths=%2Fpayment%2Fpage&pagePath=private-next-app-dir%2Fpayment%2Fpage.tsx&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();