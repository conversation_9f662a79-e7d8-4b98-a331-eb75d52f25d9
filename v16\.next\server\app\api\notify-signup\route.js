(()=>{var e={};e.id=4566,e.ids=[4566],e.modules={2502:e=>{"use strict";e.exports=import("prettier/plugins/html")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},83505:e=>{"use strict";e.exports=import("prettier/standalone")},84297:e=>{"use strict";e.exports=require("async_hooks")},90446:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>q,routeModule:()=>O,serverHooks:()=>P,workAsyncStorage:()=>R,workUnitAsyncStorage:()=>S});var s={};r.r(s),r.d(s,{POST:()=>j});var n=r(96559),i=r(48088),o=r(37719),a=r(32190),d=Object.defineProperty,l=Object.defineProperties,c=Object.getOwnPropertyDescriptors,u=Object.getOwnPropertySymbols,p=Object.prototype.hasOwnProperty,h=Object.prototype.propertyIsEnumerable,m=(e,t,r)=>t in e?d(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,f=(e,t)=>{for(var r in t||(t={}))p.call(t,r)&&m(e,r,t[r]);if(u)for(var r of u(t))h.call(t,r)&&m(e,r,t[r]);return e},y=(e,t)=>l(e,c(t)),g=(e,t,r)=>new Promise((s,n)=>{var i=e=>{try{a(r.next(e))}catch(e){n(e)}},o=e=>{try{a(r.throw(e))}catch(e){n(e)}},a=e=>e.done?s(e.value):Promise.resolve(e.value).then(i,o);a((r=r.apply(e,t)).next())}),b=class{constructor(e){this.resend=e}create(e){return g(this,arguments,function*(e,t={}){return yield this.resend.post("/api-keys",e,t)})}list(){return g(this,null,function*(){return yield this.resend.get("/api-keys")})}remove(e){return g(this,null,function*(){return yield this.resend.delete(`/api-keys/${e}`)})}},x=class{constructor(e){this.resend=e}create(e){return g(this,arguments,function*(e,t={}){return yield this.resend.post("/audiences",e,t)})}list(){return g(this,null,function*(){return yield this.resend.get("/audiences")})}get(e){return g(this,null,function*(){return yield this.resend.get(`/audiences/${e}`)})}remove(e){return g(this,null,function*(){return yield this.resend.delete(`/audiences/${e}`)})}};function v(e){return{attachments:e.attachments,bcc:e.bcc,cc:e.cc,from:e.from,headers:e.headers,html:e.html,reply_to:e.replyTo,scheduled_at:e.scheduledAt,subject:e.subject,tags:e.tags,text:e.text,to:e.to}}var E=class{constructor(e){this.resend=e}send(e){return g(this,arguments,function*(e,t={}){return this.create(e,t)})}create(e){return g(this,arguments,function*(e,t={}){let s=[];for(let t of e){if(t.react){if(!this.renderAsync)try{let{renderAsync:e}=yield r.e(3794).then(r.bind(r,3794));this.renderAsync=e}catch(e){throw Error("Failed to render React component. Make sure to install `@react-email/render`")}t.html=yield this.renderAsync(t.react),t.react=void 0}s.push(v(t))}return yield this.resend.post("/emails/batch",s,t)})}},_=class{constructor(e){this.resend=e}create(e){return g(this,arguments,function*(e,t={}){if(e.react){if(!this.renderAsync)try{let{renderAsync:e}=yield r.e(3794).then(r.bind(r,3794));this.renderAsync=e}catch(e){throw Error("Failed to render React component. Make sure to install `@react-email/render`")}e.html=yield this.renderAsync(e.react)}return yield this.resend.post("/broadcasts",{name:e.name,audience_id:e.audienceId,preview_text:e.previewText,from:e.from,html:e.html,reply_to:e.replyTo,subject:e.subject,text:e.text},t)})}send(e,t){return g(this,null,function*(){return yield this.resend.post(`/broadcasts/${e}/send`,{scheduled_at:null==t?void 0:t.scheduledAt})})}list(){return g(this,null,function*(){return yield this.resend.get("/broadcasts")})}get(e){return g(this,null,function*(){return yield this.resend.get(`/broadcasts/${e}`)})}remove(e){return g(this,null,function*(){return yield this.resend.delete(`/broadcasts/${e}`)})}update(e,t){return g(this,null,function*(){return yield this.resend.patch(`/broadcasts/${e}`,{name:t.name,audience_id:t.audienceId,from:t.from,html:t.html,text:t.text,subject:t.subject,reply_to:t.replyTo,preview_text:t.previewText})})}},A=class{constructor(e){this.resend=e}create(e){return g(this,arguments,function*(e,t={}){return yield this.resend.post(`/audiences/${e.audienceId}/contacts`,{unsubscribed:e.unsubscribed,email:e.email,first_name:e.firstName,last_name:e.lastName},t)})}list(e){return g(this,null,function*(){return yield this.resend.get(`/audiences/${e.audienceId}/contacts`)})}get(e){return g(this,null,function*(){return e.id||e.email?yield this.resend.get(`/audiences/${e.audienceId}/contacts/${(null==e?void 0:e.email)?null==e?void 0:e.email:null==e?void 0:e.id}`):{data:null,error:{message:"Missing `id` or `email` field.",name:"missing_required_field"}}})}update(e){return g(this,null,function*(){return e.id||e.email?yield this.resend.patch(`/audiences/${e.audienceId}/contacts/${(null==e?void 0:e.email)?null==e?void 0:e.email:null==e?void 0:e.id}`,{unsubscribed:e.unsubscribed,first_name:e.firstName,last_name:e.lastName}):{data:null,error:{message:"Missing `id` or `email` field.",name:"missing_required_field"}}})}remove(e){return g(this,null,function*(){return e.id||e.email?yield this.resend.delete(`/audiences/${e.audienceId}/contacts/${(null==e?void 0:e.email)?null==e?void 0:e.email:null==e?void 0:e.id}`):{data:null,error:{message:"Missing `id` or `email` field.",name:"missing_required_field"}}})}},N=class{constructor(e){this.resend=e}create(e){return g(this,arguments,function*(e,t={}){return yield this.resend.post("/domains",{name:e.name,region:e.region,custom_return_path:e.customReturnPath},t)})}list(){return g(this,null,function*(){return yield this.resend.get("/domains")})}get(e){return g(this,null,function*(){return yield this.resend.get(`/domains/${e}`)})}update(e){return g(this,null,function*(){return yield this.resend.patch(`/domains/${e.id}`,{click_tracking:e.clickTracking,open_tracking:e.openTracking,tls:e.tls})})}remove(e){return g(this,null,function*(){return yield this.resend.delete(`/domains/${e}`)})}verify(e){return g(this,null,function*(){return yield this.resend.post(`/domains/${e}/verify`)})}},w=class{constructor(e){this.resend=e}send(e){return g(this,arguments,function*(e,t={}){return this.create(e,t)})}create(e){return g(this,arguments,function*(e,t={}){if(e.react){if(!this.renderAsync)try{let{renderAsync:e}=yield r.e(3794).then(r.bind(r,3794));this.renderAsync=e}catch(e){throw Error("Failed to render React component. Make sure to install `@react-email/render`")}e.html=yield this.renderAsync(e.react)}return yield this.resend.post("/emails",v(e),t)})}get(e){return g(this,null,function*(){return yield this.resend.get(`/emails/${e}`)})}update(e){return g(this,null,function*(){return yield this.resend.patch(`/emails/${e.id}`,{scheduled_at:e.scheduledAt})})}cancel(e){return g(this,null,function*(){return yield this.resend.post(`/emails/${e}/cancel`)})}},$="undefined"!=typeof process&&process.env&&process.env.RESEND_BASE_URL||"https://api.resend.com",I="undefined"!=typeof process&&process.env&&process.env.RESEND_USER_AGENT||"resend-node:4.5.1";let k=new class{constructor(e){if(this.key=e,this.apiKeys=new b(this),this.audiences=new x(this),this.batch=new E(this),this.broadcasts=new _(this),this.contacts=new A(this),this.domains=new N(this),this.emails=new w(this),!e&&("undefined"!=typeof process&&process.env&&(this.key=process.env.RESEND_API_KEY),!this.key))throw Error('Missing API key. Pass it to the constructor `new Resend("re_123")`');this.headers=new Headers({Authorization:`Bearer ${this.key}`,"User-Agent":I,"Content-Type":"application/json"})}fetchRequest(e){return g(this,arguments,function*(e,t={}){try{let r=yield fetch(`${$}${e}`,t);if(!r.ok)try{let e=yield r.text();return{data:null,error:JSON.parse(e)}}catch(t){if(t instanceof SyntaxError)return{data:null,error:{name:"application_error",message:"Internal server error. We are unable to process your request right now, please try again later."}};let e={message:r.statusText,name:"application_error"};if(t instanceof Error)return{data:null,error:y(f({},e),{message:t.message})};return{data:null,error:e}}return{data:yield r.json(),error:null}}catch(e){return{data:null,error:{name:"application_error",message:"Unable to fetch data. The request could not be resolved."}}}})}post(e,t){return g(this,arguments,function*(e,t,r={}){let s=new Headers(this.headers);r.idempotencyKey&&s.set("Idempotency-Key",r.idempotencyKey);let n=f({method:"POST",headers:s,body:JSON.stringify(t)},r);return this.fetchRequest(e,n)})}get(e){return g(this,arguments,function*(e,t={}){let r=f({method:"GET",headers:this.headers},t);return this.fetchRequest(e,r)})}put(e,t){return g(this,arguments,function*(e,t,r={}){let s=f({method:"PUT",headers:this.headers,body:JSON.stringify(t)},r);return this.fetchRequest(e,s)})}patch(e,t){return g(this,arguments,function*(e,t,r={}){let s=f({method:"PATCH",headers:this.headers,body:JSON.stringify(t)},r);return this.fetchRequest(e,s)})}delete(e,t){return g(this,null,function*(){let r={method:"DELETE",headers:this.headers,body:JSON.stringify(t)};return this.fetchRequest(e,r)})}}(process.env.RESEND_API_KEY);async function j(e){try{let t,r=await e.json();if(console.log("Using Resend API key starting with:",process.env.RESEND_API_KEY?.substring(0,10)+"..."),console.log("Sending notification to:",process.env.NOTIFICATION_EMAIL),"subscription_request"===r.type){if(!r.email||!r.planName)return a.NextResponse.json({error:"Email y planName son requeridos para subscription_request"},{status:400})}else if("subscription_cancelled"!==r.type)return a.NextResponse.json({error:"Tipo de notificaci\xf3n no v\xe1lido"},{status:400});else if(!r.userEmail||!r.subscriptionPlan||!r.periodEnd)return a.NextResponse.json({error:"userEmail, subscriptionPlan y periodEnd son requeridos para subscription_cancelled"},{status:400});t="subscription_cancelled"===r.type?{from:"OposiAI Notificaciones <<EMAIL>>",to:[process.env.NOTIFICATION_EMAIL],subject:`❌ Suscripci\xf3n Cancelada OposiAI: ${r.userName||"Usuario"}`,html:`
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #dc2626;">Suscripci\xf3n Cancelada</h2>

            <div style="background-color: #fef2f2; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3 style="margin-top: 0; color: #991b1b;">Detalles de la Cancelaci\xf3n</h3>
              <p><strong>Email:</strong> ${r.userEmail}</p>
              <p><strong>Nombre:</strong> ${r.userName||"No proporcionado"}</p>
              <p><strong>Plan Cancelado:</strong> ${r.subscriptionPlan}</p>
              <p><strong>Acceso hasta:</strong> ${r.periodEnd}</p>
              <p><strong>Fecha de Cancelaci\xf3n:</strong> ${new Date().toLocaleString("es-ES")}</p>
            </div>

            <div style="background-color: #fef3c7; padding: 15px; border-radius: 8px; border-left: 4px solid #f59e0b;">
              <h4 style="margin-top: 0; color: #92400e;">Informaci\xf3n</h4>
              <p style="margin-bottom: 0;">El usuario mantendr\xe1 acceso a las funciones premium hasta el final de su per\xedodo de facturaci\xf3n.</p>
            </div>

            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
              <p style="color: #6b7280; font-size: 14px;">
                Este email fue generado autom\xe1ticamente por el sistema de suscripciones de OposiAI.
              </p>
            </div>
          </div>
        `}:{from:"OposiAI Notificaciones <<EMAIL>>",to:[process.env.NOTIFICATION_EMAIL],subject:`🚀 Nueva Solicitud de Suscripci\xf3n OposiAI: Plan ${r.planName}`,html:`
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #2563eb;">Nueva Solicitud de Suscripci\xf3n</h2>

            <div style="background-color: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3 style="margin-top: 0; color: #1e40af;">Detalles del Cliente</h3>
              <p><strong>Email:</strong> ${r.email}</p>
              <p><strong>Nombre:</strong> ${r.customerName||"No proporcionado"}</p>
              <p><strong>Plan Seleccionado:</strong> ${r.planName}</p>
              <p><strong>Fecha:</strong> ${new Date().toLocaleString("es-ES")}</p>
            </div>

            <div style="background-color: #fef3c7; padding: 15px; border-radius: 8px; border-left: 4px solid #f59e0b;">
              <h4 style="margin-top: 0; color: #92400e;">Acci\xf3n Requerida</h4>
              <p style="margin-bottom: 0;">Por favor, a\xf1ade manualmente este usuario a Supabase con el plan correspondiente.</p>
            </div>

            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
              <p style="color: #6b7280; font-size: 14px;">
                Este email fue generado autom\xe1ticamente por el sistema de suscripciones de OposiAI.
              </p>
            </div>
          </div>
        `},console.log("Attempting to send email with Resend..."),console.log("Email payload:",{from:t.from,to:t.to,subject:t.subject});let s=await k.emails.send(t);return console.log("Email enviado exitosamente:",s),a.NextResponse.json({success:!0,message:"Notificaci\xf3n enviada correctamente",emailData:s})}catch(e){return console.error("Error enviando email con Resend:",e),a.NextResponse.json({error:"Error al enviar la notificaci\xf3n",details:e instanceof Error?e.message:"Error desconocido"},{status:500})}}let O=new n.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/notify-signup/route",pathname:"/api/notify-signup",filename:"route",bundlePath:"app/api/notify-signup/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\notify-signup\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:R,workUnitAsyncStorage:S,serverHooks:P}=O;function q(){return(0,o.patchFetch)({workAsyncStorage:R,workUnitAsyncStorage:S})}},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580],()=>r(90446));module.exports=s})();