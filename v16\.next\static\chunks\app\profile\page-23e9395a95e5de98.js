(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6636],{3248:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>h});var t=a(5155),r=a(2115),l=a(5695),i=a(351),n=a(4092);function c(e){let{userProfile:s,onUpdate:a}=e,[l,n]=(0,r.useState)(!1),[c,d]=(0,r.useState)(s.user.name),[o,m]=(0,r.useState)(!1),[x,u]=(0,r.useState)(null),[h,p]=(0,r.useState)(!1),g=async()=>{try{if(m(!0),u(null),!(await fetch("/api/user/profile",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:c})})).ok)throw Error("Error actualizando perfil");p(!0),n(!1),a(),setTimeout(()=>p(!1),3e3)}catch(e){console.error("Error updating profile:",e),u(e instanceof Error?e.message:"Error desconocido")}finally{m(!1)}},b=e=>new Date(e).toLocaleDateString("es-ES",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"});return(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Informaci\xf3n de la Cuenta"}),!l&&(0,t.jsxs)("button",{onClick:()=>n(!0),className:"flex items-center text-blue-600 hover:text-blue-700 transition-colors",children:[(0,t.jsx)(i.WXf,{className:"w-4 h-4 mr-1"}),"Editar"]})]}),x&&(0,t.jsxs)("div",{className:"mb-4 p-3 bg-red-50 border border-red-200 rounded-md flex items-center",children:[(0,t.jsx)(i.y3G,{className:"w-5 h-5 text-red-500 mr-2"}),(0,t.jsx)("span",{className:"text-red-700",children:x})]}),h&&(0,t.jsxs)("div",{className:"mb-4 p-3 bg-green-50 border border-green-200 rounded-md flex items-center",children:[(0,t.jsx)(i.YrT,{className:"w-5 h-5 text-green-500 mr-2"}),(0,t.jsx)("span",{className:"text-green-700",children:"Perfil actualizado correctamente"})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Informaci\xf3n Personal"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[(0,t.jsx)(i.JXP,{className:"w-4 h-4 inline mr-1"}),"Nombre"]}),l?(0,t.jsx)("input",{type:"text",value:c,onChange:e=>d(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Tu nombre"}):(0,t.jsx)("p",{className:"text-gray-900",children:s.user.name})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[(0,t.jsx)(i.pHD,{className:"w-4 h-4 inline mr-1"}),"Email"]}),(0,t.jsx)("p",{className:"text-gray-900",children:s.user.email}),(0,t.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"El email no se puede modificar"})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[(0,t.jsx)(i.wIk,{className:"w-4 h-4 inline mr-1"}),"Miembro desde"]}),(0,t.jsx)("p",{className:"text-gray-900",children:b(s.user.created_at)})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"ID de Usuario"}),(0,t.jsx)("p",{className:"text-gray-600 font-mono text-sm",children:s.user.id})]})]}),l&&(0,t.jsxs)("div",{className:"flex items-center gap-3 mt-4 pt-4 border-t border-gray-200",children:[(0,t.jsxs)("button",{onClick:g,disabled:o,className:"flex items-center bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50",children:[o?(0,t.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}):(0,t.jsx)(i.Bc_,{className:"w-4 h-4 mr-2"}),o?"Guardando...":"Guardar"]}),(0,t.jsxs)("button",{onClick:()=>{d(s.user.name),n(!1),u(null)},disabled:o,className:"flex items-center bg-gray-200 text-gray-800 px-4 py-2 rounded-md hover:bg-gray-300 transition-colors disabled:opacity-50",children:[(0,t.jsx)(i.yGN,{className:"w-4 h-4 mr-2"}),"Cancelar"]})]})]}),(0,t.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Informaci\xf3n de Suscripci\xf3n"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[(0,t.jsx)(i.lZI,{className:"w-4 h-4 inline mr-1"}),"Plan Actual"]}),(0,t.jsx)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ".concat((e=>{switch(e){case"free":default:return"bg-gray-100 text-gray-800";case"usuario":return"bg-blue-100 text-blue-800";case"pro":return"bg-purple-100 text-purple-800"}})(s.profile.subscription_plan)),children:(e=>{switch(e){case"free":return"Plan Gratuito";case"usuario":return"Plan Usuario";case"pro":return"Plan Pro";default:return e}})(s.profile.subscription_plan)})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Estado de Pago"}),(0,t.jsx)("div",{className:"flex items-center",children:s.profile.payment_verified?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(i.YrT,{className:"w-4 h-4 text-green-500 mr-1"}),(0,t.jsx)("span",{className:"text-green-700",children:"Verificado"})]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(i.Ohp,{className:"w-4 h-4 text-yellow-500 mr-1"}),(0,t.jsx)("span",{className:"text-yellow-700",children:"Pendiente"})]})})]}),"free"!==s.profile.subscription_plan&&(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Renovaci\xf3n Autom\xe1tica"}),(0,t.jsx)("div",{className:"flex items-center",children:s.profile.auto_renew?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(i.YrT,{className:"w-4 h-4 text-green-500 mr-1"}),(0,t.jsx)("span",{className:"text-green-700",children:"Activa"})]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(i.yGN,{className:"w-4 h-4 text-red-500 mr-1"}),(0,t.jsx)("span",{className:"text-red-700",children:"Inactiva"})]})})]}),s.profile.plan_expires_at&&(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:s.profile.auto_renew?"Pr\xf3xima Renovaci\xf3n":"Expira el"}),(0,t.jsx)("p",{className:"text-gray-900",children:b(s.profile.plan_expires_at)})]}),s.profile.last_payment_date&&(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\xdaltimo Pago"}),(0,t.jsx)("p",{className:"text-gray-900",children:b(s.profile.last_payment_date)})]})]}),"free"===s.profile.subscription_plan&&(0,t.jsx)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:(0,t.jsxs)("a",{href:"/upgrade-plan",className:"inline-flex items-center bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors",children:[(0,t.jsx)(i.lZI,{className:"w-4 h-4 mr-2"}),"Actualizar Plan"]})})]})]})]})}function d(e){var s;let{userId:a}=e,[l,n]=(0,r.useState)([]),[c,d]=(0,r.useState)(!0),[o,m]=(0,r.useState)(null),[x,u]=(0,r.useState)("all"),[h,p]=(0,r.useState)(0);(0,r.useEffect)(()=>{g()},[a,x]);let g=async()=>{try{d(!0),m(null);let e=new URLSearchParams({limit:"20"});"all"!==x&&e.append("type",x);let s=await fetch("/api/user/notifications?".concat(e));if(!s.ok)throw Error("Error cargando notificaciones");let a=await s.json();if(a.success)n(a.data.notifications),p(a.data.total);else throw Error(a.error||"Error desconocido")}catch(e){console.error("Error loading notifications:",e),m(e instanceof Error?e.message:"Error desconocido")}finally{d(!1)}},b=e=>{switch(e){case"subscription_cancelled":case"payment_failed":return(0,t.jsx)(i.yGN,{className:"w-5 h-5 text-red-500"});case"grace_period_ending":return(0,t.jsx)(i.Ohp,{className:"w-5 h-5 text-yellow-500"});case"plan_expired":return(0,t.jsx)(i.y3G,{className:"w-5 h-5 text-red-500"});case"welcome":return(0,t.jsx)(i.YrT,{className:"w-5 h-5 text-green-500"});default:return(0,t.jsx)(i.zd,{className:"w-5 h-5 text-blue-500"})}},f=e=>{switch(e){case"subscription_cancelled":return"Suscripci\xf3n Cancelada";case"grace_period_ending":return"Per\xedodo de Gracia";case"plan_expired":return"Plan Expirado";case"payment_failed":return"Pago Fallido";case"welcome":return"Bienvenida";default:return"Notificaci\xf3n"}},j=e=>{switch(e){case"sent":return(0,t.jsx)(i.YrT,{className:"w-4 h-4 text-green-500"});case"failed":return(0,t.jsx)(i.yGN,{className:"w-4 h-4 text-red-500"});case"pending":return(0,t.jsx)(i.Ohp,{className:"w-4 h-4 text-yellow-500"});default:return(0,t.jsx)(i.pHD,{className:"w-4 h-4 text-gray-500"})}},N=e=>{let s=new Date(e),a=Math.floor((new Date().getTime()-s.getTime())/36e5);return a<1?"Hace unos minutos":a<24?"Hace ".concat(a," hora").concat(a>1?"s":""):s.toLocaleDateString("es-ES",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"})},y=[{value:"all",label:"Todas las notificaciones"},{value:"subscription_cancelled",label:"Suscripciones canceladas"},{value:"grace_period_ending",label:"Per\xedodos de gracia"},{value:"plan_expired",label:"Planes expirados"},{value:"payment_failed",label:"Pagos fallidos"},{value:"welcome",label:"Bienvenida"}];return(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Historial de Notificaciones"}),(0,t.jsxs)("button",{onClick:g,disabled:c,className:"flex items-center text-blue-600 hover:text-blue-700 transition-colors disabled:opacity-50",children:[(0,t.jsx)(i.jTZ,{className:"w-4 h-4 mr-1 ".concat(c?"animate-spin":"")}),"Actualizar"]})]}),(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,t.jsx)(i.K7R,{className:"w-4 h-4 text-gray-500"}),(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Filtrar por tipo:"})]}),(0,t.jsx)("select",{value:x,onChange:e=>u(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:y.map(e=>(0,t.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,t.jsx)("div",{className:"bg-gray-50 rounded-lg p-4 mb-6",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:h}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Total de notificaciones"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-green-600",children:l.filter(e=>"sent"===e.status).length}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Enviadas exitosamente"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-red-600",children:l.filter(e=>"failed"===e.status).length}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Fallos de env\xedo"})]})]})}),c?(0,t.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):o?(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)(i.y3G,{className:"w-12 h-12 text-red-500 mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Error cargando notificaciones"}),(0,t.jsx)("p",{className:"text-gray-600 mb-4",children:o}),(0,t.jsx)("button",{onClick:g,className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors",children:"Reintentar"})]}):0===l.length?(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)(i.zd,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No hay notificaciones"}),(0,t.jsx)("p",{className:"text-gray-600",children:"all"===x?"A\xfan no has recibido ninguna notificaci\xf3n por email.":'No hay notificaciones del tipo "'.concat(null==(s=y.find(e=>e.value===x))?void 0:s.label,'".')})]}):(0,t.jsx)("div",{className:"space-y-4",children:l.map(e=>(0,t.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow",children:(0,t.jsx)("div",{className:"flex items-start justify-between",children:(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)("div",{className:"flex-shrink-0 mt-1",children:b(e.type)}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,t.jsx)("span",{className:"text-sm font-medium text-blue-600",children:f(e.type)}),(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[j(e.status),(0,t.jsx)("span",{className:"text-xs text-gray-500 capitalize",children:e.status})]})]}),(0,t.jsx)("h4",{className:"text-sm font-medium text-gray-900 mb-1",children:e.subject}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:N(e.sentAt)}),e.metadata&&(0,t.jsxs)("div",{className:"mt-2 text-xs text-gray-600",children:[e.metadata.planName&&(0,t.jsxs)("span",{className:"inline-block bg-gray-100 px-2 py-1 rounded mr-2",children:["Plan: ",e.metadata.planName]}),void 0!==e.metadata.daysRemaining&&(0,t.jsxs)("span",{className:"inline-block bg-yellow-100 px-2 py-1 rounded mr-2",children:[e.metadata.daysRemaining," d\xedas restantes"]}),void 0!==e.metadata.hoursRemaining&&(0,t.jsxs)("span",{className:"inline-block bg-red-100 px-2 py-1 rounded mr-2",children:[e.metadata.hoursRemaining," horas restantes"]})]})]})]})})},e.id))})]})}function o(e){var s,a,r,l,n,c,d,o,m,x,u,h,p,g,b,f,j;let{userProfile:N}=e,{access:y,tokenUsage:v,profile:w}=N;if(!y||!v||!w)return(0,t.jsx)("div",{className:"p-6",children:(0,t.jsx)("div",{className:"text-center",children:(0,t.jsx)("p",{className:"text-gray-500",children:"Cargando informaci\xf3n del plan..."})})});let k=(e,s)=>"string"==typeof s||0===s?0:Math.min(e/s*100,100),E=e=>e>=90?"bg-red-500":e>=75?"bg-yellow-500":e>=50?"bg-blue-500":"bg-green-500",_=e=>-1===e||"Ilimitado"===e||"string"==typeof e,P=(e,s)=>s?e.replace("este mes","(sin l\xedmites)"):e,C=[{icon:i.jH2,label:"Documentos",current:(null==(s=y.currentUsage)?void 0:s.documents)||0,limit:_(null==(a=y.limits)?void 0:a.documents)?"Ilimitado":(null==(r=y.limits)?void 0:r.documents)||0,color:"blue",description:P("Documentos PDF/TXT subidos",_(null==(l=y.limits)?void 0:l.documents))},{icon:i.NLe,label:"Tests",current:(null==(n=y.currentUsage)?void 0:n.tests)||0,limit:_(null==(c=y.limits)?void 0:c.tests)?"Ilimitado":(null==(d=y.limits)?void 0:d.tests)||0,color:"green",description:P("Tests generados este mes",_(null==(o=y.limits)?void 0:o.tests))},{icon:i.s_k,label:"Flashcards",current:(null==(m=y.currentUsage)?void 0:m.flashcards)||0,limit:_(null==(x=y.limits)?void 0:x.flashcards)?"Ilimitado":(null==(u=y.limits)?void 0:u.flashcards)||0,color:"purple",description:P("Flashcards creadas este mes",_(null==(h=y.limits)?void 0:h.flashcards))},{icon:i.x_j,label:"Mapas Mentales",current:(null==(p=y.currentUsage)?void 0:p.mindMaps)||0,limit:_(null==(g=y.limits)?void 0:g.mindMaps)?"Ilimitado":(null==(b=y.limits)?void 0:b.mindMaps)||0,color:"indigo",description:P("Mapas mentales generados este mes",_(null==(f=y.limits)?void 0:f.mindMaps))}],S=e=>e>=1e6?(e/1e6).toFixed(1)+"M":e>=1e3?(e/1e3).toFixed(1)+"K":e.toString();return(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Uso y L\xedmites del Plan"}),(0,t.jsx)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ".concat("free"===w.subscription_plan?"bg-gray-100 text-gray-800":"usuario"===w.subscription_plan?"bg-blue-100 text-blue-800":"bg-purple-100 text-purple-800"),children:(e=>{switch(e){case"free":return"Plan Gratuito";case"usuario":return"Plan Usuario";case"pro":return"Plan Pro";default:return e}})(w.subscription_plan)})]}),(0,t.jsxs)("div",{className:"bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 mb-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(i.Ojn,{className:"w-6 h-6 text-blue-600 mr-3"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Tokens de IA"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Uso mensual de procesamiento de IA"})]})]}),(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:S(v.current||0)}),(0,t.jsxs)("div",{className:"text-sm text-gray-600",children:["de ",S(v.limit||0)]})]})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-3 mb-2",children:(0,t.jsx)("div",{className:"h-3 rounded-full transition-all duration-300 ".concat(E(v.percentage||0)),style:{width:"".concat(v.percentage||0,"%")}})}),(0,t.jsxs)("div",{className:"flex justify-between items-center text-sm",children:[(0,t.jsxs)("span",{className:(j=v.percentage||0)>=90?"text-red-600":j>=75?"text-yellow-600":"text-gray-600",children:[(v.percentage||0).toFixed(1),"% utilizado"]}),(0,t.jsxs)("span",{className:"text-gray-600",children:[S(v.remaining||0)," restantes"]})]}),(v.percentage||0)>=90&&(0,t.jsxs)("div",{className:"mt-3 p-3 bg-red-50 border border-red-200 rounded-md flex items-center",children:[(0,t.jsx)(i.eHT,{className:"w-5 h-5 text-red-500 mr-2"}),(0,t.jsx)("span",{className:"text-red-700 text-sm",children:"Te est\xe1s acercando al l\xedmite de tokens. Considera actualizar tu plan."})]})]}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:C.map(e=>{let s=e.icon,a=k(e.current,e.limit),r="string"==typeof e.limit;return(0,t.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(s,{className:"w-5 h-5 text-".concat(e.color,"-600 mr-2")}),(0,t.jsx)("span",{className:"font-medium text-gray-900",children:e.label})]}),(0,t.jsx)("div",{className:"text-right",children:r?(0,t.jsx)("div",{className:"flex items-center",children:(0,t.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800",children:[(0,t.jsx)(i.YrT,{className:"w-3 h-3 mr-1"}),"Ilimitado"]})}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"font-semibold text-gray-900",children:e.current}),(0,t.jsxs)("div",{className:"text-xs text-gray-500",children:["de ",e.limit]})]})})]}),!r&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2 mb-2",children:(0,t.jsx)("div",{className:"h-2 rounded-full transition-all duration-300 ".concat(E(a)),style:{width:"".concat(a,"%")}})}),(0,t.jsxs)("div",{className:"text-xs text-gray-600",children:[a.toFixed(1),"% utilizado"]})]}),r&&(0,t.jsx)("div",{className:"text-xs text-green-600 font-medium",children:"✓ Acceso completo sin restricciones"}),(0,t.jsx)("p",{className:"text-xs text-gray-500 mt-2",children:e.description})]},e.label)})}),(0,t.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Funciones Disponibles"}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:(y.features||[]).map(e=>(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(i.YrT,{className:"w-4 h-4 text-green-500 mr-2"}),(0,t.jsx)("span",{className:"text-sm text-gray-700",children:{document_upload:"Subida de documentos",test_generation:"Generaci\xf3n de tests",flashcard_generation:"Creaci\xf3n de flashcards",mind_map_generation:"Mapas mentales",study_planning:"Planificaci\xf3n de estudios",ai_tutor:"Tutor de IA",summaries:"Res\xfamenes autom\xe1ticos",advanced_analytics:"An\xe1lisis avanzado"}[e]||e})]},e))}),"free"===w.subscription_plan&&(0,t.jsxs)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:[(0,t.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:"\xbfNecesitas m\xe1s funciones y l\xedmites m\xe1s altos?"}),(0,t.jsxs)("a",{href:"/upgrade-plan",className:"inline-flex items-center bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors text-sm",children:[(0,t.jsx)(i.ARf,{className:"w-4 h-4 mr-2"}),"Actualizar Plan"]})]})]})]})}var m=a(6317),x=a(8260);function u(e){let{userProfile:s,onUpdate:a}=e,n=(0,l.useRouter)(),[c,d]=(0,r.useState)(!1),[o,u]=(0,r.useState)(null),[h,p]=(0,r.useState)(!1),[g,b]=(0,r.useState)(!1),[f,j]=(0,r.useState)(!0),[N,y]=(0,r.useState)(!1),[v,w]=(0,r.useState)(!1),[k,E]=(0,r.useState)(!1);(0,r.useEffect)(()=>{var e,a,t;if(null==s||null==(e=s.profile)?void 0:e.security_flags){let e=s.profile.security_flags;j(null==(a=e.email_notifications)||a),y(null!=(t=e.marketing_emails)&&t)}},[s]);let _=async()=>{let e;E(!0);try{e=x.oR.loading("Enviando enlace de recuperaci\xf3n...");let a=(0,m.U)(),{error:t}=await a.auth.resetPasswordForEmail(s.user.email,{redirectTo:"".concat(window.location.origin,"/auth/reset-password")});if(t)throw t;x.oR.success("Se ha enviado un enlace a tu email para restablecer la contrase\xf1a.",{id:e,duration:6e3}),w(!1)}catch(s){console.error("Error al solicitar reseteo de contrase\xf1a:",s),x.oR.error("No se pudo enviar el enlace de recuperaci\xf3n. Int\xe9ntalo de nuevo m\xe1s tarde.",{id:e})}finally{E(!1)}},P=async()=>{try{if(d(!0),u(null),!(await fetch("/api/user/profile",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({preferences:{email_notifications:f,marketing_emails:N}})})).ok)throw Error("Error actualizando preferencias");p(!0),a(),setTimeout(()=>p(!1),3e3)}catch(e){console.error("Error updating preferences:",e),u(e instanceof Error?e.message:"Error desconocido")}finally{d(!1)}},C=async()=>{try{let e=(0,m.U)();await e.auth.signOut(),n.push("/")}catch(e){console.error("Error logging out:",e)}},S=async()=>{try{d(!0),u(null);let e=await fetch("/api/user/cancel-subscription",{method:"POST",headers:{"Content-Type":"application/json"}}),s=await e.json();if(!e.ok)throw Error(s.error||"Error cancelando suscripci\xf3n");p(!0),b(!1),a(),x.oR.success("Suscripci\xf3n cancelada exitosamente. Mantendr\xe1s acceso hasta: ".concat(new Date(s.details.periodEnd).toLocaleDateString("es-ES")),{duration:6e3}),setTimeout(()=>p(!1),5e3)}catch(e){console.error("Error canceling subscription:",e),u(e instanceof Error?e.message:"Error desconocido"),b(!1)}finally{d(!1)}};return(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsx)("div",{className:"flex items-center justify-between mb-6",children:(0,t.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Configuraci\xf3n de la Cuenta"})}),o&&(0,t.jsx)("div",{className:"mb-4 p-3 bg-red-50 border border-red-200 rounded-md",children:(0,t.jsx)("span",{className:"text-red-700",children:o})}),h&&(0,t.jsxs)("div",{className:"mb-4 p-3 bg-green-50 border border-green-200 rounded-md flex items-center",children:[(0,t.jsx)(i.YrT,{className:"w-5 h-5 text-green-500 mr-2"}),(0,t.jsx)("span",{className:"text-green-700",children:"Configuraci\xf3n actualizada correctamente"})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,t.jsxs)("div",{className:"flex items-center mb-4",children:[(0,t.jsx)(i.zd,{className:"w-5 h-5 text-blue-600 mr-2"}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Preferencias de Notificaciones"})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-900",children:"Notificaciones por Email"}),(0,t.jsx)("p",{className:"text-xs text-gray-600",children:"Recibir notificaciones importantes sobre tu cuenta y suscripci\xf3n"})]}),(0,t.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,t.jsx)("input",{type:"checkbox",checked:f,onChange:e=>j(e.target.checked),className:"sr-only peer"}),(0,t.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-900",children:"Emails de Marketing"}),(0,t.jsx)("p",{className:"text-xs text-gray-600",children:"Recibir informaci\xf3n sobre nuevas funciones y ofertas especiales"})]}),(0,t.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,t.jsx)("input",{type:"checkbox",checked:N,onChange:e=>y(e.target.checked),className:"sr-only peer"}),(0,t.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]})]}),(0,t.jsx)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:(0,t.jsxs)("button",{onClick:P,disabled:c,className:"flex items-center bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50",children:[c?(0,t.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}):(0,t.jsx)(i.Bc_,{className:"w-4 h-4 mr-2"}),c?"Guardando...":"Guardar Preferencias"]})})]}),(0,t.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,t.jsxs)("div",{className:"flex items-center mb-4",children:[(0,t.jsx)(i.pcC,{className:"w-5 h-5 text-green-600 mr-2"}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Seguridad"})]}),(0,t.jsx)("div",{className:"space-y-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-900",children:"Cambiar Contrase\xf1a"}),(0,t.jsx)("p",{className:"text-xs text-gray-600",children:"Recibir\xe1s un enlace en tu email para establecer una nueva contrase\xf1a."})]}),(0,t.jsx)("button",{onClick:()=>w(!0),className:"bg-gray-200 text-gray-800 px-4 py-2 rounded-md hover:bg-gray-300 transition-colors text-sm",children:"Cambiar"})]})})]}),(0,t.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,t.jsxs)("div",{className:"flex items-center mb-4",children:[(0,t.jsx)(i.VSk,{className:"w-5 h-5 text-gray-600 mr-2"}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Acciones de Cuenta"})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-900",children:"Cerrar Sesi\xf3n"}),(0,t.jsx)("p",{className:"text-xs text-gray-600",children:"Cerrar sesi\xf3n en este dispositivo"})]}),(0,t.jsxs)("button",{onClick:C,className:"flex items-center bg-gray-200 text-gray-800 px-4 py-2 rounded-md hover:bg-gray-300 transition-colors text-sm",children:[(0,t.jsx)(i.QeK,{className:"w-4 h-4 mr-2"}),"Cerrar Sesi\xf3n"]})]}),"free"!==s.profile.subscription_plan&&(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-900",children:"Cancelar Suscripci\xf3n"}),(0,t.jsx)("p",{className:"text-xs text-gray-600",children:"Cancelar tu suscripci\xf3n actual (mantendr\xe1s acceso hasta el final del per\xedodo)"})]}),(0,t.jsxs)("button",{onClick:()=>b(!0),className:"flex items-center bg-red-100 text-red-700 px-4 py-2 rounded-md hover:bg-red-200 transition-colors text-sm",children:[(0,t.jsx)(i.lZI,{className:"w-4 h-4 mr-2"}),"Cancelar"]})]})]})]})]}),v&&(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-md w-full mx-4",children:[(0,t.jsxs)("div",{className:"flex items-center mb-4",children:[(0,t.jsx)(i.pHD,{className:"w-6 h-6 text-blue-500 mr-2"}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Restablecer Contrase\xf1a"})]}),(0,t.jsxs)("p",{className:"text-gray-600 mb-6",children:["Se enviar\xe1 un enlace seguro a tu direcci\xf3n de correo electr\xf3nico ",(0,t.jsxs)("strong",{children:["(",s.user.email,")"]})," para que puedas establecer una nueva contrase\xf1a."]}),(0,t.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,t.jsx)("button",{onClick:()=>w(!1),disabled:k,className:"px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors",children:"Cancelar"}),(0,t.jsxs)("button",{onClick:_,disabled:k,className:"px-4 py-2 bg-blue-600 text-white hover:bg-blue-700 rounded-lg transition-colors disabled:opacity-50 flex items-center",children:[k?(0,t.jsx)(i.TwU,{className:"animate-spin mr-2"}):(0,t.jsx)(i.pHD,{className:"mr-2"}),k?"Enviando...":"Enviar Enlace"]})]})]})}),g&&(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-md w-full mx-4",children:[(0,t.jsxs)("div",{className:"flex items-center mb-4",children:[(0,t.jsx)(i.lZI,{className:"w-6 h-6 text-red-500 mr-2"}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Confirmar Cancelaci\xf3n"})]}),(0,t.jsx)("p",{className:"text-gray-600 mb-6",children:"\xbfEst\xe1s seguro de que quieres cancelar tu suscripci\xf3n? Mantendr\xe1s acceso a las funciones premium hasta el final de tu per\xedodo de facturaci\xf3n actual."}),(0,t.jsxs)("div",{className:"flex gap-3",children:[(0,t.jsx)("button",{onClick:S,disabled:c,className:"flex-1 bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 transition-colors disabled:opacity-50 flex items-center justify-center",children:c?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Cancelando..."]}):"S\xed, cancelar suscripci\xf3n"}),(0,t.jsx)("button",{onClick:()=>b(!1),className:"flex-1 bg-gray-200 text-gray-800 px-4 py-2 rounded-md hover:bg-gray-300 transition-colors",children:"Mantener suscripci\xf3n"})]})]})})]})}function h(){let{user:e}=(0,n.A)(),s=(0,l.useRouter)(),[a,m]=(0,r.useState)("account"),[x,h]=(0,r.useState)(null),[p,g]=(0,r.useState)(!0),[b,f]=(0,r.useState)(null);(0,r.useEffect)(()=>{if(!e)return void s.push("/login");j()},[e,s]);let j=async()=>{try{g(!0),f(null);let e=await fetch("/api/user/profile");if(!e.ok)throw Error("Error cargando perfil de usuario");let s=await e.json();h(s)}catch(e){console.error("Error loading user profile:",e),f(e instanceof Error?e.message:"Error desconocido")}finally{g(!1)}},N=[{id:"account",label:"Informaci\xf3n de Cuenta",icon:i.JXP},{id:"notifications",label:"Notificaciones",icon:i.zd},{id:"usage",label:"Uso y L\xedmites",icon:i.lZI},{id:"settings",label:"Configuraci\xf3n",icon:i.VSk}];return p?(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):b||!x?(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Error cargando perfil"}),(0,t.jsx)("p",{className:"text-gray-600 mb-4",children:b||"No se pudo cargar la informaci\xf3n del perfil"}),(0,t.jsx)("button",{onClick:()=>s.push("/app"),className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors",children:"Volver al Dashboard"})]})}):(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,t.jsx)("div",{className:"bg-white shadow-sm border-b",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,t.jsx)("div",{className:"flex items-center",children:(0,t.jsxs)("button",{onClick:()=>s.push("/app"),className:"flex items-center text-gray-600 hover:text-gray-900 transition-colors",children:[(0,t.jsx)(i.kRp,{className:"w-5 h-5 mr-2"}),"Volver al Dashboard"]})}),(0,t.jsx)("h1",{className:"text-xl font-semibold text-gray-900",children:"Mi Perfil"}),(0,t.jsx)("div",{className:"w-24"})," "]})})}),(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,t.jsxs)("div",{className:"lg:grid lg:grid-cols-12 lg:gap-x-8",children:[(0,t.jsx)("div",{className:"lg:col-span-3",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6 mb-6",children:[(0,t.jsxs)("div",{className:"text-center mb-6",children:[(0,t.jsx)("div",{className:"w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,t.jsx)(i.JXP,{className:"w-10 h-10 text-blue-600"})}),(0,t.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:x.user.name}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:x.user.email}),(0,t.jsx)("div",{className:"mt-2",children:(0,t.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat("free"===x.profile.subscription_plan?"bg-gray-100 text-gray-800":"usuario"===x.profile.subscription_plan?"bg-blue-100 text-blue-800":"bg-purple-100 text-purple-800"),children:["Plan ","free"===x.profile.subscription_plan?"Gratuito":"usuario"===x.profile.subscription_plan?"Usuario":"Pro"]})})]}),(0,t.jsx)("nav",{className:"space-y-1",children:N.map(e=>{let s=e.icon;return(0,t.jsxs)("button",{onClick:()=>m(e.id),className:"w-full flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ".concat(a===e.id?"bg-blue-50 text-blue-700 border-r-2 border-blue-700":"text-gray-600 hover:text-gray-900 hover:bg-gray-50"),children:[(0,t.jsx)(s,{className:"w-4 h-4 mr-3"}),e.label]},e.id)})})]})}),(0,t.jsx)("div",{className:"lg:col-span-9",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm",children:["account"===a&&(0,t.jsx)(c,{userProfile:x,onUpdate:j}),"notifications"===a&&(0,t.jsx)(d,{userId:x.user.id}),"usage"===a&&(0,t.jsx)(o,{userProfile:x}),"settings"===a&&(0,t.jsx)(u,{userProfile:x,onUpdate:j})]})})]})})]})}},4436:(e,s,a)=>{"use strict";a.d(s,{k5:()=>o});var t=a(2115),r={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},l=t.createContext&&t.createContext(r),i=["attr","size","title"];function n(){return(n=Object.assign?Object.assign.bind():function(e){for(var s=1;s<arguments.length;s++){var a=arguments[s];for(var t in a)Object.prototype.hasOwnProperty.call(a,t)&&(e[t]=a[t])}return e}).apply(this,arguments)}function c(e,s){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);s&&(t=t.filter(function(s){return Object.getOwnPropertyDescriptor(e,s).enumerable})),a.push.apply(a,t)}return a}function d(e){for(var s=1;s<arguments.length;s++){var a=null!=arguments[s]?arguments[s]:{};s%2?c(Object(a),!0).forEach(function(s){var t,r,l;t=e,r=s,l=a[s],(r=function(e){var s=function(e,s){if("object"!=typeof e||!e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var t=a.call(e,s||"default");if("object"!=typeof t)return t;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===s?String:Number)(e)}(e,"string");return"symbol"==typeof s?s:s+""}(r))in t?Object.defineProperty(t,r,{value:l,enumerable:!0,configurable:!0,writable:!0}):t[r]=l}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):c(Object(a)).forEach(function(s){Object.defineProperty(e,s,Object.getOwnPropertyDescriptor(a,s))})}return e}function o(e){return s=>t.createElement(m,n({attr:d({},e.attr)},s),function e(s){return s&&s.map((s,a)=>t.createElement(s.tag,d({key:a},s.attr),e(s.child)))}(e.child))}function m(e){var s=s=>{var a,{attr:r,size:l,title:c}=e,o=function(e,s){if(null==e)return{};var a,t,r=function(e,s){if(null==e)return{};var a={};for(var t in e)if(Object.prototype.hasOwnProperty.call(e,t)){if(s.indexOf(t)>=0)continue;a[t]=e[t]}return a}(e,s);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);for(t=0;t<l.length;t++)a=l[t],!(s.indexOf(a)>=0)&&Object.prototype.propertyIsEnumerable.call(e,a)&&(r[a]=e[a])}return r}(e,i),m=l||s.size||"1em";return s.className&&(a=s.className),e.className&&(a=(a?a+" ":"")+e.className),t.createElement("svg",n({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},s.attr,r,o,{className:a,style:d(d({color:e.color||s.color},s.style),e.style),height:m,width:m,xmlns:"http://www.w3.org/2000/svg"}),c&&t.createElement("title",null,c),e.children)};return void 0!==l?t.createElement(l.Consumer,null,e=>s(e)):s(r)}},4828:(e,s,a)=>{Promise.resolve().then(a.bind(a,3248))}},e=>{var s=s=>e(e.s=s);e.O(0,[844,7361,8260,4092,8441,1684,7358],()=>s(4828)),_N_E=e.O()}]);