(()=>{var e={};e.id=9755,e.ids=[9755],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},15763:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var a=t(65239),s=t(48088),n=t(88170),i=t.n(n),o=t(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(r,l);let d={children:["",{children:["thank-you",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,47529)),"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\thank-you\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\thank-you\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/thank-you/page",pathname:"/thank-you",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20509:(e,r,t)=>{Promise.resolve().then(t.bind(t,82531))},27910:e=>{"use strict";e.exports=require("stream")},28611:(e,r,t)=>{"use strict";t.d(r,{Md:()=>n,NB:()=>s});var a=t(78956);let s={free:{id:"free",name:"Plan Gratis",price:0,stripeProductId:null,stripePriceId:null,features:["Incluye:","• Uso de la plataforma solo durante 5 d\xedas","• Subida de documentos: m\xe1ximo 1 documento","• Generador de test: m\xe1ximo 10 preguntas test","• Generador de flashcards: m\xe1ximo 10 tarjetas flashcard","• Generador de mapas mentales: m\xe1ximo 2 mapas mentales","No incluye:","• Planificaci\xf3n de estudios","• Habla con tu preparador IA","• Res\xfamenes A2 y A1"],limits:a.qo.free.limits,planConfig:a.qo.free},usuario:{id:"usuario",name:"Plan Usuario",price:1e3,stripeProductId:"prod_SR65BdKdek1OXd",stripePriceId:"price_1Rae5807kFn3sIXhRf3adX1n",features:["Incluye:","• Uso de la plataforma durante el mes (una vez finalizado no podr\xe1 volver a acceder hasta renovaci\xf3n)","• Subida de documentos","• Habla con tu preparador IA *","• Generador de test *","• Generador de flashcards *","• Generador de mapas mentales *","• * Para las tareas en las que se haga uso de IA, el l\xedmite mensual ser\xe1 de 1.000.000 de tokens, podr\xe1n ampliarse durante el mes mediante pago","No incluye:","• Planificaci\xf3n de estudios","• Res\xfamenes A2 y A1"],limits:a.qo.usuario.limits,planConfig:a.qo.usuario},pro:{id:"pro",name:"Plan Pro",price:1500,stripeProductId:"prod_SR66U2G7bVJqu3",stripePriceId:"price_1Rae3U07kFn3sIXhkvSuJco1",features:["Incluye:","• Uso de la plataforma durante el mes (una vez finalizado no podr\xe1 volver a acceder hasta renovaci\xf3n)","• Subida de documentos","• Planificaci\xf3n de estudios mediante IA*","• Habla con tu preparador IA *","• Generador de test *","• Generador de flashcards *","• Generador de mapas mentales *","• Generaci\xf3n de Res\xfamenes para A2 y A1","• * Para las tareas en las que se haga uso de IA, el l\xedmite mensual ser\xe1 de 1.000.000 de tokens, podr\xe1n ampliarse durante el mes mediante pago"],limits:a.qo.pro.limits,planConfig:a.qo.pro}};function n(e){return s[e]||null}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},47529:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\thank-you\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\thank-you\\page.tsx","default")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78956:(e,r,t)=>{"use strict";t.d(r,{IE:()=>s,qk:()=>i,qo:()=>a});let a={free:{id:"free",name:"Plan Gratis",price:0,limits:{documents:1,mindMapsForTrial:2,testsForTrial:10,flashcardsForTrial:10,tokensForTrial:5e4,features:["document_upload","test_generation","flashcard_generation","mind_map_generation"]},features:["document_upload","test_generation","flashcard_generation","mind_map_generation"],restrictedFeatures:["study_planning","ai_tutor_chat","summary_a1_a2"]},usuario:{id:"usuario",name:"Plan Usuario",price:1e3,limits:{documents:-1,mindMapsPerWeek:-1,testsPerWeek:-1,flashcardsPerWeek:-1,monthlyTokens:1e6,features:["document_upload","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation"]},features:["document_upload","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation"],restrictedFeatures:["study_planning","summary_a1_a2"]},pro:{id:"pro",name:"Plan Pro",price:1500,limits:{documents:-1,mindMapsPerWeek:-1,testsPerWeek:-1,flashcardsPerWeek:-1,monthlyTokens:1e6,features:["document_upload","study_planning","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation","summary_a1_a2"]},features:["document_upload","study_planning","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation","summary_a1_a2"],restrictedFeatures:[]}};function s(e){return a[e]||null}function n(e,r){let t=s(e);return!(!t||t.restrictedFeatures.includes(r))&&t.features.includes(r)}async function i(e){try{let r=await fetch("/api/user/plan");if(!r.ok)return console.error("Error obteniendo plan del usuario"),n("free",e);let{plan:t}=await r.json();return n(t||"free",e)}catch(r){return console.error("Error verificando acceso a caracter\xedstica:",r),n("free",e)}}},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},82531:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>u});var a=t(60687),s=t(43210),n=t(16189),i=t(28611),o=t(85814),l=t.n(o),d=t(17019);function c(){let e=(0,n.useSearchParams)();(0,n.useRouter)();let r=e.get("plan")||"free",t=e.get("session_id"),s="true"===e.get("email_sent");e.get("payment");let o=(0,i.Md)(r);return s&&"free"===r?(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:(0,a.jsx)("div",{className:"sm:mx-auto sm:w-full sm:max-w-xl",children:(0,a.jsx)("div",{className:"bg-white py-8 px-6 shadow-xl sm:rounded-lg sm:px-10",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(d.pHD,{className:"mx-auto h-12 w-12 text-blue-600 mb-4"}),(0,a.jsx)("h2",{className:"text-2xl md:text-3xl font-bold text-gray-800 mb-3",children:"\xa1Registro Exitoso!"}),(0,a.jsxs)("p",{className:"text-md text-gray-600 mb-6",children:["Tu cuenta gratuita de ",(0,a.jsx)("strong",{children:o?.name||"OposicionesIA"})," ha sido creada exitosamente."]}),(0,a.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6",children:[(0,a.jsx)("h3",{className:"font-semibold text-blue-800 mb-2",children:"\uD83D\uDCE7 Confirma tu Email"}),(0,a.jsx)("p",{className:"text-blue-700 text-sm mb-3",children:"Hemos enviado un email de confirmaci\xf3n a tu direcci\xf3n de correo."}),(0,a.jsxs)("p",{className:"text-blue-700 text-sm",children:[(0,a.jsx)("strong",{children:"Haz clic en el enlace del email"})," para activar tu cuenta y poder iniciar sesi\xf3n."]})]}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-6",children:"Si no recibes el email en unos minutos, revisa tu carpeta de spam."}),(0,a.jsx)(l(),{href:"/auth/login",className:"inline-flex justify-center py-3 px-6 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-blue-600 hover:bg-blue-700 transition-colors",children:"Ir a Iniciar Sesi\xf3n"})]})})})}):t&&"undefined"!==t?(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:(0,a.jsx)("div",{className:"sm:mx-auto sm:w-full sm:max-w-xl",children:(0,a.jsx)("div",{className:"bg-white py-10 px-6 shadow-xl sm:rounded-lg sm:px-10",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(d.A3x,{className:"mx-auto h-16 w-16 text-green-500 mb-5"}),(0,a.jsx)("h2",{className:"text-2xl md:text-3xl font-bold text-gray-800 mb-4",children:"\xa1Pago Confirmado!"}),(0,a.jsxs)("p",{className:"text-md text-gray-700 mb-6",children:["Tu pago para el plan ",(0,a.jsx)("strong",{children:o?.name||"seleccionado"})," ha sido procesado exitosamente."]}),(0,a.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4 mb-6",children:(0,a.jsxs)("p",{className:"text-green-800 text-sm",children:[(0,a.jsx)("strong",{children:"✅ Tu cuenta se est\xe1 activando"}),(0,a.jsx)("br",{}),"En unos momentos, tu cuenta estar\xe1 completamente activada. Mientras tanto, puedes intentar iniciar sesi\xf3n."]})}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-8",children:"Si tienes alg\xfan problema para iniciar sesi\xf3n, espera unos minutos y vuelve a intentarlo."}),(0,a.jsx)(l(),{href:"/auth/login",className:"inline-flex justify-center py-3 px-8 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-blue-600 hover:bg-blue-700 transition-colors",children:"Ir a Iniciar Sesi\xf3n"})]})})})}):(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center items-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(d.eHT,{className:"mx-auto h-12 w-12 text-orange-500 mb-4"}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-4",children:"P\xe1gina de Agradecimiento"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"No se encontraron par\xe1metros v\xe1lidos para mostrar el contenido apropiado."}),(0,a.jsx)(l(),{href:"/",className:"inline-flex justify-center py-3 px-6 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-blue-600 hover:bg-blue-700 transition-colors",children:"Volver al Inicio"})]})})}function u(){return(0,a.jsx)(s.Suspense,{fallback:(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center items-center",children:[(0,a.jsx)(d.TwU,{className:"animate-spin h-12 w-12 text-blue-600"}),(0,a.jsx)("p",{className:"mt-4 text-gray-600",children:"Cargando..."})]}),children:(0,a.jsx)(c,{})})}},91645:e=>{"use strict";e.exports=require("net")},92893:(e,r,t)=>{Promise.resolve().then(t.bind(t,47529))},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4447,6126,7019,5814,1191],()=>t(15763));module.exports=a})();