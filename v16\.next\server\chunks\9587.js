"use strict";exports.id=9587,exports.ids=[9587],exports.modules={19:(e,r,a)=>{a.d(r,{vW:()=>l.vW,fW:()=>l.fW,xq:()=>c.xq,qJ:()=>c.qJ,Yp:()=>l.Yp,_4:()=>u._4,CM:()=>l.CM,sq:()=>l.sq,Q3:()=>i,hE:()=>n,yK:()=>c.yK,QE:()=>l.QE,OA:()=>u.OA,oE:()=>c.oE,Sl:()=>l.Sl,sj:()=>l.sj,R1:()=>s,yV:()=>d.yV,wU:()=>d.wU,oC:()=>u.oC,dd:()=>u.dd,C9:()=>l.C9,hg:()=>u.hg,Kj:()=>u.Kj,Lx:()=>u.Lx,Gl:()=>u.Gl});var t=a(48363),o=a(34705);async function s(){try{let{user:e}=await (0,o.iF)();if(!e)return console.error("No hay usuario autenticado"),[];let{data:r,error:a}=await t.N.from("documentos").select("*").eq("user_id",e.id).order("numero_tema",{ascending:!0});if(a)return console.error("Error al obtener documentos:",a),[];return r||[]}catch(e){return console.error("Error al obtener documentos:",e),[]}}async function n(e){try{let{user:r}=await (0,o.iF)();if(!r)return console.error("No hay usuario autenticado"),null;let a={...e,user_id:r.id,tipo_original:e.tipo_original},{data:s,error:n}=await t.N.from("documentos").insert([a]).select();if(n)return console.error("Error al guardar documento:",n),null;return s?.[0]?.id||null}catch(e){return console.error("Error al guardar documento:",e),null}}async function i(e){try{console.log("\uD83D\uDDD1️ Iniciando eliminaci\xf3n de documento:",e);let{user:r}=await (0,o.iF)();if(!r)return console.error("❌ No hay usuario autenticado para eliminar documento"),!1;console.log("\uD83D\uDC64 Usuario autenticado:",r.id),console.log("\uD83D\uDCC4 Eliminando documento ID:",e);let{error:a,count:s}=await t.N.from("documentos").delete({count:"exact"}).eq("id",e).eq("user_id",r.id);if(a)return console.error("❌ Error al eliminar documento de Supabase:",a),!1;if(console.log("✅ Documento eliminado exitosamente. Filas afectadas:",s),0===s)return console.warn("⚠️ No se elimin\xf3 ning\xfan documento. Posibles causas: documento no existe o no pertenece al usuario"),!1;return!0}catch(e){return console.error("\uD83D\uDCA5 Error inesperado al eliminar documento:",e),!1}}var l=a(72971),c=a(24932),d=a(49318),u=a(78286);a(97697)},3930:(e,r,a)=>{a.d(r,{A:()=>S});var t=a(60687),o=a(43210),s=a.n(o),n=a(17019),i=a(81552),l=a(37590);let c={firstDayOfWeek:1,locale:"es-ES"},d=["Lunes","Martes","Mi\xe9rcoles","Jueves","Viernes","S\xe1bado","Domingo"],u=["LU","MA","MI","JU","VI","SA","DO"],m=["Enero","Febrero","Marzo","Abril","Mayo","Junio","Julio","Agosto","Septiembre","Octubre","Noviembre","Diciembre"];function f(e){if(!e||"string"!=typeof e||!/^\d{4}-\d{2}-\d{2}$/.test(e))return null;let r=new Date(e+"T00:00:00.000Z");if(isNaN(r.getTime()))return null;let a=r.getUTCFullYear(),t=String(r.getUTCMonth()+1).padStart(2,"0"),o=String(r.getUTCDate()).padStart(2,"0");return`${a}-${t}-${o}`!==e?null:r}function h(e){if(!e||isNaN(e.getTime()))return"";let r=e.getFullYear(),a=String(e.getMonth()+1).padStart(2,"0"),t=String(e.getDate()).padStart(2,"0");return`${r}-${a}-${t}`}function p(e,r){let a=f(e);if(!a)return null;let t=function(e){let r=d.findIndex(r=>r.toLowerCase()===e.toLowerCase());return r>=0?r:-1}(r);if(-1===t)return null;let o=new Date(a);return o.setDate(a.getDate()+t),o}function g(e,r){return new Date(e,r,1)}function x(e){var r;return r=new Date,!!e&&!!r&&e.getFullYear()===r.getFullYear()&&e.getMonth()===r.getMonth()&&e.getDate()===r.getDate()}function y(e,r,a){return e.getFullYear()===r&&e.getMonth()===a}function b(e,r){let a=h(e),t=r.mapaDias.get(a);return t?.tareas||[]}function N(e){}function v(){let e=new Date;return{mesActual:e.getMonth(),yearActual:e.getFullYear(),fechaSeleccionada:void 0,calendarioExpandido:!0,primerDiaSemana:1,vistaTamaño:"normal"}}function w(e){e&&e.toISOString()}let j=({plan:e,progresoPlan:r,fechaSeleccionada:a,onFechaSeleccionada:s,onMesChanged:i,className:l=""})=>{let{estadoCalendario:d,isLoading:j,error:_,navegarMes:E,irAHoy:S,tituloMes:D,esFechaSeleccionable:A}=function(e,r,a){let{preferences:t,updatePreference:s}=function(){let[e,r]=(0,o.useState)(v),a=(0,o.useCallback)(e=>{r(r=>{let a={...r,...e};return N(e),a})},[]),t=(0,o.useCallback)((e,r)=>{a({[e]:r})},[a]);return{preferences:e,updatePreferences:a,updatePreference:t,clearPreferences:()=>{r(v())}}}(),[n,i]=(0,o.useState)(()=>{void 0!==t.primerDiaSemana&&(c.firstDayOfWeek=t.primerDiaSemana);let e=new Date,r=function(){let e=v();if(!e.fechaSeleccionada)return null;try{let r=new Date(e.fechaSeleccionada);return isNaN(r.getTime())?null:r}catch{return null}}();return{yearActual:t.yearActual??e.getFullYear(),mesActual:t.mesActual??e.getMonth(),fechaSeleccionada:a||r||null,fechasCalendario:[],diasCalendario:[]}}),[l,d]=(0,o.useState)(!1),[u,j]=(0,o.useState)(null),_=(0,o.useMemo)(()=>{if(!e)return null;try{d(!0),j(null);let a=function(e,r,a={}){let t=[],o=new Map,s={incluirDiasSinTareas:!0,calcularEstadisticas:!0,validarFechas:!0,ordenarTareasPorTipo:!1,...a};if(!e||!e.semanas||!Array.isArray(e.semanas)){var n;return t.push("Plan de estudios inv\xe1lido o sin semanas"),n=t,{datosPlan:{fechaInicio:new Date,fechaFin:new Date,totalSemanas:0,mapaDias:new Map,rangoFechas:{minYear:new Date().getFullYear(),maxYear:new Date().getFullYear(),minMonth:new Date().getMonth(),maxMonth:new Date().getMonth()}},estadisticas:{totalTareas:0,tareasCompletadas:0,porcentajeGeneral:0,diasConTareas:0,diasCompletados:0},errores:n}}if(s.validarFechas){let r=function(e){let r=[],a=[],t=[];if(!e.semanas||!Array.isArray(e.semanas))return r.push("El plan no contiene semanas v\xe1lidas"),{esValido:!1,errores:r,advertencias:a,fechasProblematicas:t};let o=null;for(let s of e.semanas){if(!s||"number"!=typeof s.numero){r.push(`Semana inv\xe1lida encontrada`);continue}let e=f(s.fechaInicio),n=f(s.fechaFin);if(!e){r.push(`Fecha de inicio inv\xe1lida en semana ${s.numero}: ${s.fechaInicio}`),t.push({semana:s.numero,fecha:s.fechaInicio,problema:"Fecha de inicio inv\xe1lida"});continue}if(!n){r.push(`Fecha de fin inv\xe1lida en semana ${s.numero}: ${s.fechaFin}`),t.push({semana:s.numero,fecha:s.fechaFin,problema:"Fecha de fin inv\xe1lida"});continue}n<=e&&r.push(`La fecha de fin debe ser posterior a la de inicio en semana ${s.numero}`),o&&e<o&&a.push(`La semana ${s.numero} comienza antes de que termine la anterior`),o=n}return{esValido:0===r.length,errores:r,advertencias:a,fechasProblematicas:t}}(e);r.esValido||t.push(...r.errores)}let i=null,l=null;for(let a of e.semanas){if(!a||"number"!=typeof a.numero){t.push(`Semana inv\xe1lida encontrada`);continue}let e=f(a.fechaInicio),n=f(a.fechaFin);if(!e||!n){t.push(`Fechas inv\xe1lidas en semana ${a.numero}`);continue}if((!i||e<i)&&(i=e),(!l||n>l)&&(l=n),a.dias&&Array.isArray(a.dias))for(let e of a.dias){let n=function(e,r,a,t){var o,s,n;if(!e||!e.dia)return{error:`D\xeda inv\xe1lido en semana ${r.numero}`};let i=p(r.fechaInicio,e.dia);if(!i)return{error:`No se pudo calcular la fecha para ${e.dia} en semana ${r.numero}`};let l=[];if(e.tareas&&Array.isArray(e.tareas))for(let t of e.tareas){if(!t||!t.titulo)continue;let o=a.find(a=>a.semana_numero===r.numero&&a.dia_nombre===e.dia&&a.tarea_titulo===t.titulo);l.push({tarea:t,semanaNumero:r.numero,diaNombre:e.dia,completada:o?.completado||!1,fechaCompletado:o?.fecha_completado})}t.ordenarTareasPorTipo&&l.sort((e,r)=>{let a={estudio:0,repaso:1,practica:2,evaluacion:3};return(a[e.tarea.tipo]||99)-(a[r.tarea.tipo]||99)});let c=l.length,d=l.filter(e=>e.completada).length,u=c>0?d/c*100:0,m=(o=i,s=c,n=d,x(o)?"hoy":0===s?"normal":0===n?"con-tareas":n===s?"completado":"parcial");return{diaCalendario:{fecha:i,dia:i.getDate(),estaEnMesActual:!0,esHoy:x(i),estado:m,tareas:l,totalTareas:c,tareasCompletadas:d,porcentajeCompletado:u}}}(e,a,r,s);if(n.error){t.push(n.error);continue}if(n.diaCalendario){let e=h(n.diaCalendario.fecha);o.set(e,n.diaCalendario),n.diaCalendario.totalTareas}}}return{datosPlan:{fechaInicio:i||new Date,fechaFin:l||new Date,totalSemanas:e.semanas.length,mapaDias:o,rangoFechas:function(e,r){if(!e||!r){let e=new Date;return{minYear:e.getFullYear(),maxYear:e.getFullYear(),minMonth:e.getMonth(),maxMonth:e.getMonth()}}return{minYear:e.getFullYear(),maxYear:r.getFullYear(),minMonth:e.getMonth(),maxMonth:r.getMonth()}}(i,l)},estadisticas:s.calcularEstadisticas?function(e,r){let a=0,t=0,o=0,s=0;for(let r of Array.from(e.values()))r.totalTareas>0&&(o++,a+=r.totalTareas,t+=r.tareasCompletadas,r.tareasCompletadas===r.totalTareas&&s++);return{totalTareas:a,tareasCompletadas:t,porcentajeGeneral:a>0?t/a*100:0,diasConTareas:o,diasCompletados:s}}(o,0):{totalTareas:0,tareasCompletadas:0,porcentajeGeneral:0,diasConTareas:0,diasCompletados:0},errores:t}}(e,r,{incluirDiasSinTareas:!0,calcularEstadisticas:!0,validarFechas:!0,ordenarTareasPorTipo:!0});return a.errores.length>0&&(console.warn("Errores al procesar el plan:",a.errores),j(a.errores[0])),a.datosPlan}catch(e){return j(e instanceof Error?e.message:"Error desconocido al procesar el plan"),console.error("Error al procesar plan para calendario:",e),null}finally{d(!1)}},[e,r]),E=(0,o.useMemo)(()=>(function(e,r){let a=[],t=g(e,r),o=new Date(e,r+1,0),s=function(e,r){let a=g(e,r).getDay();return 1===c.firstDayOfWeek?0===a?6:a-1:a}(e,r);for(let e=s-1;e>=0;e--){let r=new Date(t);r.setDate(r.getDate()-(e+1)),a.push(r)}for(let t=1;t<=o.getDate();t++)a.push(new Date(e,r,t));let n=42-a.length;for(let e=1;e<=n;e++){let r=new Date(o);r.setDate(r.getDate()+e),a.push(r)}return a})(n.yearActual,n.mesActual),[n.yearActual,n.mesActual]);(0,o.useMemo)(()=>_?E.map(e=>{let r=h(e),a=_.mapaDias.get(r);return a?{...a,estaEnMesActual:y(e,n.yearActual,n.mesActual),esHoy:x(e)}:{fecha:e,dia:e.getDate(),estaEnMesActual:y(e,n.yearActual,n.mesActual),esHoy:x(e),estado:x(e)?"hoy":y(e,n.yearActual,n.mesActual)?"normal":"fuera-mes",tareas:[],totalTareas:0,tareasCompletadas:0,porcentajeCompletado:0}}):[],[E,_,n.yearActual,n.mesActual]);let S=(0,o.useCallback)(e=>{i(r=>{var a,t,o,n;let{year:i,month:l}="anterior"===e?(a=r.yearActual,0===(t=r.mesActual)?{year:a-1,month:11}:{year:a,month:t-1}):(o=r.yearActual,11===(n=r.mesActual)?{year:o+1,month:0}:{year:o,month:n+1});return s("yearActual",i),s("mesActual",l),{...r,yearActual:i,mesActual:l}})},[s]),D=(0,o.useCallback)((e,r)=>{s("yearActual",e),s("mesActual",r),i(a=>({...a,yearActual:e,mesActual:r}))},[s]),A=(0,o.useCallback)(e=>{w(e),i(r=>({...r,fechaSeleccionada:e}))},[]),C=(0,o.useCallback)(()=>{let e=new Date;s("yearActual",e.getFullYear()),s("mesActual",e.getMonth()),w(e),i(r=>({...r,yearActual:e.getFullYear(),mesActual:e.getMonth(),fechaSeleccionada:e}))},[s]),T=(0,o.useCallback)(e=>_?b(e,_):[],[_]),F=(0,o.useCallback)(e=>{if(!_)return"normal";let r=h(e),a=_.mapaDias.get(r);return a?.estado||"normal"},[_]),q=(0,o.useCallback)(e=>{if(!_)return!1;let r=h(e);return void 0!==_.mapaDias.get(r)||e>=_.fechaInicio&&e<=_.fechaFin},[_]),I=(0,o.useMemo)(()=>{let e=new Date(n.yearActual,n.mesActual);return`${m[e.getMonth()]} ${n.yearActual}`},[n.yearActual,n.mesActual]),M=(0,o.useMemo)(()=>n.fechaSeleccionada&&_?b(n.fechaSeleccionada,_):[],[n.fechaSeleccionada,_]),k=(0,o.useMemo)(()=>{if(!n.fechaSeleccionada||!_)return null;let e=h(n.fechaSeleccionada),r=_.mapaDias.get(e);return r?{total:r.totalTareas,completadas:r.tareasCompletadas,porcentaje:r.porcentajeCompletado}:null},[n.fechaSeleccionada,_]);return{estadoCalendario:n,datosPlan:_,isLoading:l,error:u,navegarMes:S,irAMes:D,seleccionarFecha:A,irAHoy:C,obtenerTareasDelDia:T,obtenerEstadoDia:F,esFechaSeleccionable:q,tituloMes:I,tareasDelDiaSeleccionado:M,estadisticasDelDia:k}}(e,r,a),C=e=>{A(e.fecha)&&s(e.fecha)},T=e=>{E(e),i&&i(d.yearActual,d.mesActual)},F=(e,r)=>{("Enter"===e.key||" "===e.key)&&(e.preventDefault(),C(r))},q=(e,r)=>{("Enter"===e.key||" "===e.key)&&(e.preventDefault(),r())},I=e=>{let r=["relative","aspect-square","flex","items-center","justify-center","text-xs","sm:text-sm","font-medium","cursor-pointer","calendario-day-hover","calendario-estado-transition","rounded-none","sm:rounded-lg","border","border-transparent","min-h-[2.5rem]","sm:min-h-[3rem]"];switch(e.estaEnMesActual?r.push("text-gray-700","hover:text-gray-900"):r.push("text-gray-300","hover:text-gray-400"),e.estado){case"hoy":r.push("bg-blue-100","text-blue-900","border-blue-300","font-bold","ring-2","ring-blue-400","ring-opacity-50","calendario-pulso");break;case"con-tareas":r.push("bg-orange-50","text-orange-800","border-orange-200","hover:bg-orange-100","hover:border-orange-300");break;case"completado":r.push("bg-green-50","text-green-800","border-green-200","hover:bg-green-100","hover:border-green-300");break;case"parcial":r.push("bg-yellow-50","text-yellow-800","border-yellow-200","hover:bg-yellow-100","hover:border-yellow-300");break;case"normal":e.estaEnMesActual&&r.push("hover:bg-gray-50","hover:border-gray-200")}return a&&e.fecha.getTime()===a.getTime()&&r.push("ring-2","ring-blue-500","ring-opacity-75","bg-blue-50","border-blue-300"),A(e.fecha)||r.push("cursor-not-allowed","opacity-50"),r.join(" ")},M=e=>{if(0===e.totalTareas)return null;let r=e.porcentajeCompletado,a="bg-orange-400";return 100===r?a="bg-green-400":r>0&&(a="bg-yellow-400"),(0,t.jsx)("div",{className:"absolute bottom-1 right-1",children:(0,t.jsx)("div",{className:`w-2 h-2 rounded-full ${a}`,title:`${e.tareasCompletadas}/${e.totalTareas} tareas completadas`})})};return _?(0,t.jsxs)("div",{className:`bg-red-50 border border-red-200 rounded-lg p-4 ${l}`,children:[(0,t.jsxs)("div",{className:"flex items-center text-red-800",children:[(0,t.jsx)(n.wIk,{className:"w-5 h-5 mr-2"}),(0,t.jsx)("span",{className:"font-medium",children:"Error en el calendario"})]}),(0,t.jsx)("p",{className:"text-red-600 text-sm mt-1",children:_})]}):(0,t.jsxs)("div",{className:`bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm calendario-fade-in ${l}`,children:[(0,t.jsx)("div",{className:"bg-gray-50 px-3 sm:px-4 py-2 sm:py-3 border-b border-gray-200",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("button",{onClick:()=>T("anterior"),onKeyDown:e=>q(e,()=>T("anterior")),className:"p-1 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors","aria-label":"Mes anterior",tabIndex:0,children:(0,t.jsx)(n.irw,{className:"w-5 h-5 text-gray-600"})}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(n.wIk,{className:"w-4 h-4 text-gray-600 hidden sm:block"}),(0,t.jsx)("h3",{className:"font-semibold text-gray-900 text-sm sm:text-base",children:D})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)("button",{onClick:S,onKeyDown:e=>q(e,S),className:"p-1 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors",title:"Ir a hoy","aria-label":"Ir a hoy",tabIndex:0,children:(0,t.jsx)(n.V5Y,{className:"w-4 h-4 text-gray-600"})}),(0,t.jsx)("button",{onClick:()=>T("siguiente"),onKeyDown:e=>q(e,()=>T("siguiente")),className:"p-1 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors","aria-label":"Mes siguiente",tabIndex:0,children:(0,t.jsx)(n.fOo,{className:"w-5 h-5 text-gray-600"})})]})]})}),(0,t.jsx)("div",{className:"grid grid-cols-7 bg-gray-100 border-b border-gray-200",children:u.map(e=>(0,t.jsx)("div",{className:"py-1 sm:py-2 text-center text-xs font-medium text-gray-600 uppercase tracking-wide",children:e},e))}),(0,t.jsx)("div",{className:"grid grid-cols-7 gap-0",children:j?Array.from({length:42},(e,r)=>(0,t.jsx)("div",{className:"aspect-square flex items-center justify-center border-r border-b border-gray-100 last:border-r-0",children:(0,t.jsx)("div",{className:"w-6 h-6 bg-gray-200 rounded animate-pulse"})},r)):d.diasCalendario.map((e,r)=>(0,t.jsx)("div",{className:`border-r border-b border-gray-100 last:border-r-0 ${5===Math.floor(r/7)?"border-b-0":""}`,children:(0,t.jsxs)("button",{onClick:()=>C(e),onKeyDown:r=>F(r,e),className:`${I(e)} focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50`,disabled:!A(e.fecha),tabIndex:A(e.fecha)?0:-1,"aria-label":`${e.dia} de ${D}${e.totalTareas>0?`, ${e.totalTareas} tareas`:""}${e.esHoy?", hoy":""}${"completado"===e.estado?", completado":"parcial"===e.estado?", parcialmente completado":"con-tareas"===e.estado?", con tareas pendientes":""}`,"aria-pressed":a&&e.fecha.getTime()===a.getTime()?"true":"false",children:[e.dia,M(e)]})},r))}),(0,t.jsx)("div",{className:"bg-gray-50 px-3 sm:px-4 py-2 border-t border-gray-200",children:(0,t.jsxs)("div",{className:"flex items-center justify-center space-x-2 sm:space-x-4 text-xs text-gray-600",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-orange-400 rounded-full"}),(0,t.jsx)("span",{className:"hidden sm:inline",children:"Pendientes"}),(0,t.jsx)("span",{className:"sm:hidden",children:"Pend."})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-yellow-400 rounded-full"}),(0,t.jsx)("span",{className:"hidden sm:inline",children:"Parcial"}),(0,t.jsx)("span",{className:"sm:hidden",children:"Parc."})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-green-400 rounded-full"}),(0,t.jsx)("span",{className:"hidden sm:inline",children:"Completado"}),(0,t.jsx)("span",{className:"sm:hidden",children:"Comp."})]})]})})]})},_=({fecha:e,tareas:r,isLoading:a=!1,onTareaClick:i,onTareaCompletadaChange:l,className:d=""})=>{let[u,m]=(0,o.useState)(new Set),f=e=>{switch(e){case"estudio":return(0,t.jsx)(n.H9b,{className:"w-4 h-4"});case"repaso":return(0,t.jsx)(n.jTZ,{className:"w-4 h-4"});case"practica":return(0,t.jsx)(n.aze,{className:"w-4 h-4"});case"evaluacion":return(0,t.jsx)(n.x_j,{className:"w-4 h-4"});default:return(0,t.jsx)(n.Ohp,{className:"w-4 h-4"})}},h=e=>{switch(e){case"estudio":return"text-blue-600 bg-blue-50 border-blue-200";case"repaso":return"text-green-600 bg-green-50 border-green-200";case"practica":return"text-purple-600 bg-purple-50 border-purple-200";case"evaluacion":return"text-red-600 bg-red-50 border-red-200";default:return"text-gray-600 bg-gray-50 border-gray-200"}},p=e=>{switch(e){case"estudio":return"Estudio";case"repaso":return"Repaso";case"practica":return"Pr\xe1ctica";case"evaluacion":return"Evaluaci\xf3n";default:return"Tarea"}},g=s().useMemo(()=>{if(!r.length)return null;let e=r.filter(e=>e.completada).length,a=r.length,t=Math.round(e/a*100);return{completadas:e,total:a,porcentaje:t}},[r]),x=e=>{i&&i(e)},y=async(e,r)=>{if(!l)return;let a=`${e.semanaNumero}-${e.diaNombre}-${e.tarea.titulo}`;try{m(e=>new Set(e).add(a)),await l(e,r)}catch(e){console.error("Error al actualizar tarea:",e)}finally{m(e=>{let r=new Set(e);return r.delete(a),r})}},b=e=>{let r=`${e.semanaNumero}-${e.diaNombre}-${e.tarea.titulo}`;return u.has(r)};return e?(0,t.jsxs)("div",{className:`bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm calendario-slide-in ${d}`,children:[(0,t.jsx)("div",{className:"bg-gray-50 px-3 sm:px-4 py-2 sm:py-3 border-b border-gray-200",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold text-gray-900 text-sm sm:text-base",children:function(e){return!e||isNaN(e.getTime())?"":e.toLocaleDateString(c.locale,{day:"2-digit",month:"2-digit",year:"numeric"})}(e)}),g&&(0,t.jsxs)("p",{className:"text-xs sm:text-sm text-gray-600",children:[g.completadas," de ",g.total," tareas",(0,t.jsx)("span",{className:"hidden sm:inline",children:" completadas"})]})]}),g&&(0,t.jsx)("div",{className:"text-right",children:(0,t.jsxs)("div",{className:`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${100===g.porcentaje?"bg-green-100 text-green-800":g.porcentaje>0?"bg-yellow-100 text-yellow-800":"bg-gray-100 text-gray-800"}`,children:[g.porcentaje,"%"]})})]})}),(0,t.jsx)("div",{className:"p-3 sm:p-4",children:a?(0,t.jsx)("div",{className:"space-y-3",children:Array.from({length:3},(e,r)=>(0,t.jsx)("div",{className:"animate-pulse",children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-4 h-4 bg-gray-200 rounded"}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4 mb-2"}),(0,t.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/2"})]})]})},r))}):0===r.length?(0,t.jsxs)("div",{className:"text-center py-6",children:[(0,t.jsx)(n.y3G,{className:"w-8 h-8 mx-auto mb-2 text-gray-400"}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"No hay tareas programadas"}),(0,t.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"para este d\xeda"})]}):(0,t.jsx)("div",{className:"space-y-3",children:r.map((e,r)=>{let a=b(e);return(0,t.jsx)("div",{className:`border rounded-lg p-3 calendario-estado-transition ${e.completada?"bg-green-50 border-green-200":"bg-white border-gray-200"} ${a?"opacity-75":""}`,children:(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[l?(0,t.jsx)("button",{onClick:r=>{r.stopPropagation(),y(e,!e.completada)},disabled:a,className:`flex-shrink-0 w-5 h-5 rounded border-2 mt-0.5 flex items-center justify-center transition-all hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 ${e.completada?"bg-green-500 border-green-500 hover:bg-green-600":"border-gray-300 hover:border-gray-400"} ${a?"opacity-50 cursor-not-allowed":"cursor-pointer"}`,"aria-label":`${e.completada?"Desmarcar":"Marcar"} como completada: ${e.tarea.titulo}`,title:`${e.completada?"Desmarcar":"Marcar"} como completada`,children:a?(0,t.jsx)(n.TwU,{className:"w-3 h-3 text-gray-400 animate-spin"}):e.completada?(0,t.jsx)(n.YrT,{className:"w-3 h-3 text-white"}):null}):(0,t.jsx)("div",{className:`flex-shrink-0 w-5 h-5 rounded border-2 mt-0.5 flex items-center justify-center ${e.completada?"bg-green-500 border-green-500":"border-gray-300"}`,children:e.completada&&(0,t.jsx)(n.YrT,{className:"w-3 h-3 text-white"})}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,t.jsxs)("div",{className:`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${h(e.tarea.tipo)}`,children:[f(e.tarea.tipo),(0,t.jsx)("span",{className:"ml-1",children:p(e.tarea.tipo)})]}),e.tarea.duracionEstimada&&(0,t.jsxs)("div",{className:"flex items-center text-xs text-gray-500",children:[(0,t.jsx)(n.Ohp,{className:"w-3 h-3 mr-1"}),e.tarea.duracionEstimada]})]}),(0,t.jsx)("h5",{className:`font-medium text-sm ${e.completada?"text-green-800 line-through":"text-gray-900"}`,children:e.tarea.titulo}),e.tarea.descripcion&&(0,t.jsx)("p",{className:`text-xs mt-1 ${e.completada?"text-green-600":"text-gray-600"}`,children:e.tarea.descripcion}),(0,t.jsxs)("div",{className:"flex items-center justify-between mt-2",children:[(0,t.jsxs)("div",{className:"text-xs text-gray-500",children:["Semana ",e.semanaNumero," • ",e.diaNombre]}),e.completada&&e.fechaCompletado&&(0,t.jsx)("div",{className:"text-xs text-green-600",children:"✓ Completada"})]})]}),i&&(0,t.jsx)("button",{onClick:r=>{r.stopPropagation(),x(e)},className:"flex-shrink-0 p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50","aria-label":`Ir a esta tarea en el plan: ${e.tarea.titulo}`,title:"Ir al plan de estudios",children:(0,t.jsx)(n.HaR,{className:"w-4 h-4"})})]})},r)})})}),r.length>0&&!a&&(0,t.jsx)("div",{className:"bg-gray-50 px-4 py-2 border-t border-gray-200",children:(0,t.jsxs)("div",{className:"flex items-center justify-between text-xs text-gray-600",children:[(0,t.jsxs)("span",{children:[r.filter(e=>e.completada).length," completadas"]}),(0,t.jsxs)("span",{children:[r.filter(e=>!e.completada).length," pendientes"]})]})})]}):(0,t.jsx)("div",{className:`bg-gray-50 border border-gray-200 rounded-lg p-3 sm:p-4 ${d}`,children:(0,t.jsxs)("div",{className:"text-center text-gray-500",children:[(0,t.jsx)(n.wIk,{className:"w-6 h-6 sm:w-8 sm:h-8 mx-auto mb-2 text-gray-400"}),(0,t.jsx)("p",{className:"text-xs sm:text-sm",children:"Selecciona un d\xeda en el calendario"}),(0,t.jsx)("p",{className:"text-xs text-gray-400 mt-1 hidden sm:block",children:"para ver las tareas programadas"})]})})},E=({isOpen:e,onClose:r,plan:a,progresoPlan:s,fechaSeleccionada:i,onFechaSeleccionada:l,tareasDelDia:c,onTareaClick:d,onTareaCompletadaChange:u})=>((0,o.useEffect)(()=>{let a=e=>{"Escape"===e.key&&r()};return e&&(document.addEventListener("keydown",a),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",a),document.body.style.overflow="unset"}},[e,r]),e)?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden calendario-modal-overlay",onClick:r,"aria-hidden":"true"}),(0,t.jsx)("div",{className:"fixed inset-0 z-50 lg:hidden",children:(0,t.jsx)("div",{className:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:(0,t.jsxs)("div",{className:"inline-block align-bottom bg-white rounded-t-lg sm:rounded-lg text-left overflow-hidden shadow-xl calendario-modal-content sm:my-8 sm:align-middle sm:max-w-lg sm:w-full",children:[(0,t.jsxs)("div",{className:"bg-gray-50 px-4 py-3 border-b border-gray-200 flex items-center justify-between",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Calendario del Plan"}),(0,t.jsx)("button",{onClick:r,className:"p-2 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors","aria-label":"Cerrar calendario",children:(0,t.jsx)(n.yGN,{className:"w-5 h-5 text-gray-600"})})]}),(0,t.jsxs)("div",{className:"bg-white px-4 py-4 space-y-4 max-h-[70vh] overflow-y-auto",children:[(0,t.jsx)(j,{plan:a,progresoPlan:s,fechaSeleccionada:i,onFechaSeleccionada:l}),i&&(0,t.jsx)(_,{fecha:i,tareas:c,onTareaClick:e=>{d&&d(e),r()},onTareaCompletadaChange:u})]}),(0,t.jsx)("div",{className:"bg-gray-50 px-4 py-3 border-t border-gray-200",children:(0,t.jsx)("button",{onClick:r,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors",children:"Cerrar"})})]})})})]}):null,S=({plan:e,temarioId:r})=>{let[a,s]=(0,o.useState)([]),[c,d]=(0,o.useState)(null),[u,m]=(0,o.useState)(!0),[f,g]=(0,o.useState)(null),[x,y]=(0,o.useState)(!1),b=(0,o.useRef)({});(0,o.useEffect)(()=>{N()},[r]);let N=async()=>{try{let e=await (0,i.fF)(r);if(!e){console.warn("No se encontr\xf3 plan activo para el temario:",r),d(null),s([]),m(!1);return}d(e.id);let a=await (0,i.$S)(e.id);s(a)}catch(e){console.error("Error al cargar progreso:",e),d(null),s([])}finally{m(!1)}},v=async(e,r,t)=>{if(!c)return void l.oR.error("No se pudo identificar el plan de estudios");try{let o=a.find(a=>a.semana_numero===r&&a.dia_nombre===t&&a.tarea_titulo===e.titulo),n=!o?.completado;await (0,i.d7)(c,r,t,e.titulo,e.tipo,n)?(s(a=>{let o=a.findIndex(a=>a.semana_numero===r&&a.dia_nombre===t&&a.tarea_titulo===e.titulo);if(!(o>=0))return[...a,{id:`temp-${Date.now()}`,plan_id:c,user_id:"",semana_numero:r,dia_nombre:t,tarea_titulo:e.titulo,tarea_tipo:e.tipo,completado:n,fecha_completado:n?new Date().toISOString():void 0,creado_en:new Date().toISOString(),actualizado_en:new Date().toISOString()}];{let e=[...a];return e[o]={...e[o],completado:n,fecha_completado:n?new Date().toISOString():void 0},e}}),l.oR.success(n?"Tarea completada":"Tarea marcada como pendiente")):l.oR.error("Error al actualizar el progreso")}catch(e){console.error("Error al actualizar tarea:",e),l.oR.error("Error al actualizar el progreso")}},w=(e,r,t)=>a.some(a=>a.semana_numero===r&&a.dia_nombre===t&&a.tarea_titulo===e.titulo&&a.completado),S=r=>{if(g(r),e&&e.semanas)for(let a of e.semanas)for(let e of a.dias||[]){let t=p(a.fechaInicio,e.dia);if(t&&h(t)===h(r)){let e=b.current[a.numero];e&&e.scrollIntoView({behavior:"smooth",block:"start",inline:"nearest"});return}}},D=()=>{if(!f||!e||!e.semanas)return[];let r=[];for(let t of e.semanas)for(let e of t.dias||[]){let o=p(t.fechaInicio,e.dia);if(o&&h(o)===h(f))for(let o of e.tareas||[]){let s=w(o,t.numero,e.dia);r.push({tarea:o,semanaNumero:t.numero,diaNombre:e.dia,completada:s,fechaCompletado:a.find(r=>r.semana_numero===t.numero&&r.dia_nombre===e.dia&&r.tarea_titulo===o.titulo)?.fecha_completado})}}return r},A=e=>{let r=b.current[e.semanaNumero];r&&r.scrollIntoView({behavior:"smooth",block:"center",inline:"nearest"})},C=async(e,r)=>{try{await v(e.tarea,e.semanaNumero,e.diaNombre),l.oR.success(r?"✅ Tarea marcada como completada":"↩️ Tarea marcada como pendiente")}catch(e){throw console.error("Error al actualizar tarea desde calendario:",e),l.oR.error("Error al actualizar la tarea"),e}},T=(()=>{if(!e||!e.semanas||!Array.isArray(e.semanas))return{completadas:0,total:0,porcentaje:0};let r=e.semanas.reduce((e,r)=>r&&r.dias&&Array.isArray(r.dias)?e+r.dias.reduce((e,r)=>r&&r.tareas&&Array.isArray(r.tareas)?e+r.tareas.length:e,0):e,0),t=a.filter(e=>e.completado).length;return{completadas:t,total:r,porcentaje:r>0?Math.round(t/r*100):0}})();return u?(0,t.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),(0,t.jsx)("span",{className:"ml-3 text-gray-600",children:"Cargando progreso..."})]}):e?r&&""!==r.trim()?(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,t.jsx)("h3",{className:"font-semibold text-blue-900 mb-2",children:"Introducci\xf3n"}),(0,t.jsx)("p",{className:"text-blue-800",children:e.introduccion||"Introducci\xf3n no disponible"})]}),(0,t.jsxs)("div",{className:"bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-lg p-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-900",children:"Progreso General"}),(0,t.jsxs)("span",{className:"text-2xl font-bold text-green-600",children:[T.porcentaje,"%"]})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-3 mb-2",children:(0,t.jsx)("div",{className:"bg-gradient-to-r from-green-500 to-blue-500 h-3 rounded-full transition-all duration-300",style:{width:`${T.porcentaje}%`}})}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:[T.completadas," de ",T.total," tareas completadas"]})]}),e.resumen&&(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,t.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(n.Ohp,{className:"w-5 h-5 text-blue-600 mr-2"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Tiempo Total"}),(0,t.jsx)("p",{className:"font-semibold",children:e.resumen.tiempoTotalEstudio||"No disponible"})]})]})}),(0,t.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(n.H9b,{className:"w-5 h-5 text-green-600 mr-2"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Temas"}),(0,t.jsx)("p",{className:"font-semibold",children:e.resumen.numeroTemas||"No disponible"})]})]})}),(0,t.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(n.x_j,{className:"w-5 h-5 text-purple-600 mr-2"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Estudio Nuevo"}),(0,t.jsx)("p",{className:"font-semibold",children:e.resumen.duracionEstudioNuevo||"No disponible"})]})]})}),(0,t.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(n.jTZ,{className:"w-5 h-5 text-orange-600 mr-2"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Repaso Final"}),(0,t.jsx)("p",{className:"font-semibold",children:e.resumen.duracionRepasoFinal||"No disponible"})]})]})})]}),e.semanas&&e.semanas.length>0&&(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("h3",{className:"text-xl font-semibold text-gray-900 flex items-center",children:[(0,t.jsx)(n.wIk,{className:"w-5 h-5 mr-2"}),"Cronograma Semanal"]}),(0,t.jsxs)("button",{onClick:()=>y(!0),className:"lg:hidden flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors",children:[(0,t.jsx)(n.wIk,{className:"w-4 h-4 mr-2"}),"Ver Calendario"]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-12 gap-6",children:[(0,t.jsx)("div",{className:"lg:col-span-8 space-y-6",children:e.semanas.map((e,r)=>(0,t.jsxs)("div",{ref:r=>{b.current[e.numero]=r},className:"border border-gray-200 rounded-lg overflow-hidden",children:[(0,t.jsxs)("div",{className:"bg-gray-50 px-6 py-4 border-b border-gray-200",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("h4",{className:"text-lg font-semibold text-gray-900",children:["Semana ",e?.numero||"N/A"]}),(0,t.jsxs)("span",{className:"text-sm text-gray-600",children:[e?.fechaInicio||"N/A"," - ",e?.fechaFin||"N/A"]})]}),(0,t.jsx)("p",{className:"text-gray-700 mt-2",children:e?.objetivoPrincipal||"Objetivo no especificado"})]}),(0,t.jsx)("div",{className:"p-6 space-y-4",children:e.dias&&Array.isArray(e.dias)?e.dias.map((r,a)=>(0,t.jsxs)("div",{className:"border border-gray-100 rounded-lg p-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,t.jsx)("h5",{className:"font-semibold text-gray-900",children:r?.dia||"D\xeda no especificado"}),(0,t.jsxs)("span",{className:"text-sm text-gray-600 bg-gray-100 px-2 py-1 rounded",children:[r?.horas||0,"h"]})]}),(0,t.jsx)("div",{className:"space-y-2",children:r.tareas&&Array.isArray(r.tareas)?r.tareas.map((a,o)=>{let s=w(a,e.numero,r.dia);return(0,t.jsxs)("div",{className:`flex items-start p-3 rounded-lg border transition-all cursor-pointer ${s?"bg-green-50 border-green-200":"bg-white border-gray-200 hover:border-blue-300"}`,onClick:()=>v(a,e.numero,r.dia),children:[(0,t.jsx)("div",{className:`flex-shrink-0 w-5 h-5 rounded border-2 mr-3 mt-0.5 flex items-center justify-center ${s?"bg-green-500 border-green-500":"border-gray-300 hover:border-blue-400"}`,children:s&&(0,t.jsx)(n.YrT,{className:"w-3 h-3 text-white"})}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("h6",{className:`font-medium ${s?"text-green-800 line-through":"text-gray-900"}`,children:a?.titulo||"Tarea sin t\xedtulo"}),a?.descripcion&&(0,t.jsx)("p",{className:`text-sm mt-1 ${s?"text-green-700":"text-gray-600"}`,children:a.descripcion}),(0,t.jsxs)("div",{className:"flex items-center mt-2 space-x-3",children:[(0,t.jsx)("span",{className:`text-xs px-2 py-1 rounded ${a?.tipo==="estudio"?"bg-blue-100 text-blue-800":a?.tipo==="repaso"?"bg-yellow-100 text-yellow-800":a?.tipo==="practica"?"bg-purple-100 text-purple-800":"bg-gray-100 text-gray-800"}`,children:a?.tipo||"general"}),(0,t.jsx)("span",{className:"text-xs text-gray-500",children:a?.duracionEstimada||"No especificado"})]})]})]},o)}):(0,t.jsx)("p",{className:"text-gray-500 text-sm",children:"No hay tareas disponibles"})})]},a)):(0,t.jsx)("p",{className:"text-gray-500 text-sm",children:"No hay d\xedas disponibles"})})]},r))}),(0,t.jsxs)("div",{className:"hidden lg:block lg:col-span-4 space-y-4",children:[(0,t.jsx)(j,{plan:e,progresoPlan:a,fechaSeleccionada:f,onFechaSeleccionada:S,className:"sticky top-4"}),(0,t.jsx)(_,{fecha:f,tareas:D(),onTareaClick:A,onTareaCompletadaChange:C,className:"sticky top-4"})]})]})]}),(0,t.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:[(0,t.jsx)("h3",{className:"font-semibold text-yellow-900 mb-2",children:"Estrategia de Repasos"}),(0,t.jsx)("p",{className:"text-yellow-800",children:"string"==typeof e.estrategiaRepasos?e.estrategiaRepasos:e.estrategiaRepasos&&"object"==typeof e.estrategiaRepasos&&e.estrategiaRepasos.descripcion||"Estrategia de repasos no disponible"})]}),(0,t.jsxs)("div",{className:"bg-purple-50 border border-purple-200 rounded-lg p-4",children:[(0,t.jsx)("h3",{className:"font-semibold text-purple-900 mb-2",children:"Pr\xf3ximos Pasos y Consejos"}),(0,t.jsx)("p",{className:"text-purple-800",children:"string"==typeof e.proximosPasos?e.proximosPasos:e.proximosPasos&&"object"==typeof e.proximosPasos&&e.proximosPasos.descripcion||"Pr\xf3ximos pasos no disponibles"})]}),(0,t.jsx)(E,{isOpen:x,onClose:()=>y(!1),plan:e,progresoPlan:a,fechaSeleccionada:f,onFechaSeleccionada:S,tareasDelDia:D(),onTareaClick:A,onTareaCompletadaChange:C})]}):(0,t.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,t.jsx)("div",{className:"text-center",children:(0,t.jsx)("p",{className:"text-gray-600",children:"ID de temario no v\xe1lido"})})}):(0,t.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,t.jsx)("div",{className:"text-center",children:(0,t.jsx)("p",{className:"text-gray-600",children:"No se pudo cargar el plan de estudios"})})})}},24932:(e,r,a)=>{a.d(r,{Iv:()=>f,Og:()=>c,Q1:()=>i,QU:()=>g,_W:()=>m,_p:()=>u,as:()=>x,kO:()=>d,oE:()=>n,qJ:()=>s,xq:()=>p,yK:()=>l,yf:()=>h});var t=a(48363),o=a(34705);async function s(e,r){try{let{user:a}=await (0,o.iF)();if(!a)return console.error("No hay usuario autenticado"),null;let{data:s,error:n}=await t.N.from("colecciones_flashcards").insert([{titulo:e,descripcion:r,user_id:a.id}]).select();if(n)return console.error("Error al crear colecci\xf3n de flashcards:",n),null;return s?.[0]?.id||null}catch(e){return console.error("Error al crear colecci\xf3n de flashcards:",e),null}}async function n(){try{let{user:e,error:r}=await (0,o.iF)();if(r)return console.error("Error al obtener usuario:",r),[];if(!e)return console.error("No hay usuario autenticado"),[];let{data:a,error:s}=await t.N.from("colecciones_flashcards").select("*").eq("user_id",e.id).order("creado_en",{ascending:!1});if(s)return console.error("Error al obtener colecciones de flashcards:",s),[];if(!a||0===a.length)return[];return await Promise.all(a.map(async e=>{try{let{data:r,error:a}=await t.N.from("flashcards").select("id").eq("coleccion_id",e.id);if(a)return console.error("Error al contar flashcards para colecci\xf3n",e.id,":",a),{...e,numero_flashcards:0,pendientes_hoy:0};let{data:o,error:s}=await t.N.from("flashcards").select(`
              id,
              progreso_flashcards!inner(
                proxima_revision,
                estado
              )
            `).eq("coleccion_id",e.id).lte("progreso_flashcards.proxima_revision",new Date().toISOString()),n=s?0:o?.length||0;return{...e,numero_flashcards:r?.length||0,pendientes_hoy:n}}catch(r){return console.error("Error al procesar colecci\xf3n",e.id,":",r),{...e,numero_flashcards:0,pendientes_hoy:0}}}))}catch(e){return console.error("Error general al obtener colecciones de flashcards:",e),[]}}async function i(e){let{data:r,error:a}=await t.N.from("flashcards").select("*").eq("coleccion_id",e).order("creado_en",{ascending:!0});return a?(console.error("Error al obtener flashcards:",a),[]):r||[]}async function l(e){let{data:r,error:a}=await t.N.from("flashcards").insert(e).select();return a?(console.error("Error al guardar flashcards:",a),null):r?.map(e=>e.id)||null}async function c(e){let r=await i(e),{data:a,error:o}=await t.N.from("progreso_flashcards").select("*").in("flashcard_id",r.map(e=>e.id));if(o)return console.error("Error al obtener progreso de flashcards:",o),[];let s=new Date,n=new Date(s.getFullYear(),s.getMonth(),s.getDate());return r.map(e=>{let r=a?.find(r=>r.flashcard_id===e.id);if(!r)return{...e,debeEstudiar:!0};let t=new Date(r.proxima_revision),o=new Date(t.getFullYear(),t.getMonth(),t.getDate());return{...e,debeEstudiar:o<=n,progreso:{factor_facilidad:r.factor_facilidad,intervalo:r.intervalo,repeticiones:r.repeticiones,estado:r.estado,proxima_revision:r.proxima_revision}}})}async function d(e,r=10){try{let a=await c(e),o=a.map(e=>e.id),{data:s,error:n}=await t.N.from("historial_revisiones").select("flashcard_id, dificultad").in("flashcard_id",o);if(n)return console.error("Error al obtener historial de revisiones:",n),a.slice(0,r);let i=new Map;return s?.forEach(e=>{let r=i.get(e.flashcard_id)||{dificil:0,total:0};r.total++,"dificil"===e.dificultad&&r.dificil++,i.set(e.flashcard_id,r)}),a.map(e=>{let r=i.get(e.id),a=r?r.dificil/r.total:0;return{...e,ratioDificultad:a}}).sort((e,r)=>r.ratioDificultad-e.ratioDificultad).slice(0,r)}catch(e){return console.error("Error al obtener flashcards m\xe1s dif\xedciles:",e),[]}}async function u(e,r=10){try{return[...await c(e)].sort(()=>Math.random()-.5).slice(0,r)}catch(e){return console.error("Error al obtener flashcards aleatorias:",e),[]}}async function m(e,r=10){try{let a=await c(e),o=a.map(e=>e.id),{data:s,error:n}=await t.N.from("historial_revisiones").select("flashcard_id, fecha").in("flashcard_id",o).order("fecha",{ascending:!1});if(n)return console.error("Error al obtener \xfaltimas revisiones:",n),a.slice(0,r);let i=new Map;return s?.forEach(e=>{i.has(e.flashcard_id)||i.set(e.flashcard_id,e.fecha)}),a.map(e=>{let r=i.get(e.id);return{...e,ultimaRevision:new Date(r||0)}}).sort((e,r)=>e.ultimaRevision.getTime()-r.ultimaRevision.getTime()).slice(0,r)}catch(e){return console.error("Error al obtener flashcards no recientes:",e),[]}}async function f(e,r,a=10){try{return(await c(e)).filter(e=>e.progreso?e.progreso.estado===r:"nuevo"===r).slice(0,a)}catch(e){return console.error("Error al obtener flashcards por estado:",e),[]}}async function h(e,r){try{let a=2.5,o=1,s=0,n="nuevo",i=!1,{data:l,error:c}=await t.N.from("progreso_flashcards").select("factor_facilidad, intervalo, repeticiones, estado").eq("flashcard_id",e).single();!c&&l&&(a=l.factor_facilidad||2.5,o=l.intervalo||1,s=l.repeticiones||0,n=l.estado||"nuevo",i=!0);let d=a,u=o,m=s,f=n;"dificil"===r?(d=Math.max(1.3,a-.3),m=0,u=1,f="aprendiendo"):(m++,"normal"===r?d=a-.15:"facil"===r&&(d=a+.1),d=Math.max(1.3,Math.min(2.5,d)),1===m?(u=1,f="aprendiendo"):2===m?(u=6,f="repasando"):f=(u=Math.round(o*d))>30?"aprendido":"repasando");let h=new Date,p=new Date(h);p.setDate(p.getDate()+u);let g=null;if(i){let{error:r}=await t.N.from("progreso_flashcards").update({factor_facilidad:d,intervalo:u,repeticiones:m,estado:f,ultima_revision:h.toISOString(),proxima_revision:p.toISOString()}).eq("flashcard_id",e);g=r}else{let{error:r}=await t.N.from("progreso_flashcards").insert({flashcard_id:e,factor_facilidad:d,intervalo:u,repeticiones:m,estado:f,ultima_revision:h.toISOString(),proxima_revision:p.toISOString()});g=r}if(g)return console.error("Error al guardar progreso:",g),!1;let{error:x}=await t.N.from("historial_revisiones").insert({flashcard_id:e,dificultad:r,factor_facilidad:d,intervalo:u,repeticiones:m,fecha:h.toISOString()});return!0}catch(e){return!1}}async function p(e,r,a){try{let{error:o}=await t.N.from("flashcards").update({pregunta:r,respuesta:a,actualizado_en:new Date().toISOString()}).eq("id",e);if(o)return!1;return!0}catch(e){return!1}}async function g(e){try{let{error:r}=await t.N.from("progreso_flashcards").delete().eq("flashcard_id",e);if(r)return!1;let{error:a}=await t.N.from("historial_revisiones").delete().eq("flashcard_id",e);if(a)return!1;let{error:o,count:s}=await t.N.from("flashcards").delete({count:"exact"}).eq("id",e);if(o||0===s)return!1;return!0}catch(e){return!1}}async function x(e){try{let{user:r}=await (0,o.iF)();if(!r)return!1;let{data:a,error:s}=await t.N.from("flashcards").select("id").eq("coleccion_id",e);if(s)return!1;let n=a?.map(e=>e.id)||[];if(n.length>0){let{error:r}=await t.N.from("progreso_flashcards").delete().in("flashcard_id",n);if(r)return!1;let{error:a}=await t.N.from("historial_revisiones").delete().in("flashcard_id",n);if(a)return!1;let{error:o}=await t.N.from("flashcards").delete().eq("coleccion_id",e);if(o)return!1}let{error:i,count:l}=await t.N.from("colecciones_flashcards").delete({count:"exact"}).eq("id",e).eq("user_id",r.id);if(i||0===l)return!1;return!0}catch(e){return!1}}},32332:(e,r,a)=>{a.d(r,{Pk:()=>i,u9:()=>n,vD:()=>s});var t=a(79481),o=a(34705);async function s(e){try{let{user:r,error:a}=await (0,o.iF)();if(!r||a)return console.log("No hay usuario autenticado o error:",a),!1;let{data:s,error:n}=await t.N.from("temarios").select("id").eq("id",e).eq("user_id",r.id).single();if(n){if("PGRST116"===n.code)return!1;return console.error("Error al verificar temario:",n),!1}if(!s)return!1;let{data:i,error:l}=await t.N.from("planificacion_usuario").select("id").eq("user_id",r.id).eq("temario_id",e).eq("completado",!0).limit(1);if(l)return console.error("Error al verificar planificaci\xf3n:",l),!1;return i&&i.length>0}catch(e){return console.error("Error al verificar planificaci\xf3n:",e),!1}}async function n(e){try{let{user:r,error:a}=await (0,o.iF)();if(!r||a)return null;let{data:s,error:n}=await t.N.from("planificacion_usuario").select("*").eq("user_id",r.id).eq("temario_id",e).single();if(n){if("PGRST116"===n.code)return null;return console.error("Error al obtener planificaci\xf3n:",n),null}return s}catch(e){return console.error("Error al obtener planificaci\xf3n:",e),null}}async function i(e,r){try{let{user:a,error:s}=await (0,o.iF)();if(!a||s)return console.error("No hay usuario autenticado"),null;let i=await n(e);if(i){let{data:e,error:a}=await t.N.from("planificacion_usuario").update({...r,completado:!0,actualizado_en:new Date().toISOString()}).eq("id",i.id).select().single();if(a)return console.error("Error al actualizar planificaci\xf3n:",a),null;return e.id}{let{data:o,error:s}=await t.N.from("planificacion_usuario").insert([{user_id:a.id,temario_id:e,...r,completado:!0}]).select().single();if(s)return console.error("Error al crear planificaci\xf3n:",s),null;return o.id}}catch(e){return console.error("Error al guardar planificaci\xf3n:",e),null}}},49318:(e,r,a)=>{a.d(r,{wU:()=>n,yV:()=>s});var t=a(48363),o=a(24932);async function s(e){let r=await (0,o.Q1)(e);if(0===r.length)return{total:0,nuevas:0,aprendiendo:0,repasando:0,aprendidas:0,paraHoy:0};let{data:a,error:s}=await t.N.from("progreso_flashcards").select("*").in("flashcard_id",r.map(e=>e.id));if(s)return console.error("Error al obtener progreso de flashcards:",s),{total:r.length,nuevas:r.length,aprendiendo:0,repasando:0,aprendidas:0,paraHoy:r.length};let n={total:r.length,nuevas:0,aprendiendo:0,repasando:0,aprendidas:0,paraHoy:0},i=new Date;return r.forEach(e=>{let r=a?.find(r=>r.flashcard_id===e.id);if(r){switch(r.estado){case"nuevo":n.nuevas++;break;case"aprendiendo":n.aprendiendo++;break;case"repasando":n.repasando++;break;case"aprendido":n.aprendidas++}let e=new Date(r.proxima_revision);new Date(e.getFullYear(),e.getMonth(),e.getDate())<=new Date(i.getFullYear(),i.getMonth(),i.getDate())&&n.paraHoy++}else n.nuevas++,n.paraHoy++}),n}async function n(e){try{let r=await (0,o.Q1)(e);if(0===r.length)return{totalSesiones:0,totalRevisiones:0,distribucionDificultad:{dificil:0,normal:0,facil:0},progresoTiempo:[],tarjetasMasDificiles:[]};let a=r.map(e=>e.id),{data:s,error:n}=await t.N.from("historial_revisiones").select("*").in("flashcard_id",a).order("fecha",{ascending:!0});if(n)return console.error("Error al obtener revisiones:",n),null;let{data:i,error:l}=await t.N.from("progreso_flashcards").select("*").in("flashcard_id",a);l&&console.error("Error al obtener progreso:",l);let c={totalSesiones:0,totalRevisiones:s?s.length:0,distribucionDificultad:{dificil:0,normal:0,facil:0},progresoTiempo:[],tarjetasMasDificiles:[]};if(s&&s.length>0){s.forEach(e=>{"dificil"===e.dificultad?c.distribucionDificultad.dificil++:"normal"===e.dificultad?c.distribucionDificultad.normal++:"facil"===e.dificultad&&c.distribucionDificultad.facil++});let e=new Set;s.forEach(r=>{let a=new Date(r.fecha).toISOString().split("T")[0];e.add(a)}),c.totalSesiones=e.size;let a=new Map;r.forEach(e=>{a.set(e.id,{dificil:0,normal:0,facil:0,total:0})}),s.forEach(e=>{let r=a.get(e.flashcard_id);r&&("dificil"===e.dificultad?r.dificil++:"normal"===e.dificultad?r.normal++:"facil"===e.dificultad&&r.facil++,r.total++)}),c.tarjetasMasDificiles=r.map(e=>{let r=a.get(e.id)||{dificil:0,normal:0,facil:0,total:0};return{id:e.id,pregunta:e.pregunta,dificil:r.dificil,normal:r.normal,facil:r.facil,totalRevisiones:r.total}}).filter(e=>e.totalRevisiones>0).sort((e,r)=>{let a=e.totalRevisiones>0?e.dificil/e.totalRevisiones:0;return(r.totalRevisiones>0?r.dificil/r.totalRevisiones:0)-a}).slice(0,10)}return c}catch(e){return console.error("Error al calcular estad\xedsticas detalladas:",e),null}}},72971:(e,r,a)=>{a.d(r,{C9:()=>u,CM:()=>l,QE:()=>d,Sl:()=>c,Yp:()=>o,fW:()=>n,sj:()=>s,sq:()=>m,vW:()=>i});var t=a(48363);async function o(e,r=!1){try{let{data:{user:a}}=await t.N.auth.getUser();if(!a)return console.error("No hay usuario autenticado para crear conversaci\xf3n"),null;r&&await l();let{data:o,error:s}=await t.N.from("conversaciones").insert([{titulo:e,activa:r,user_id:a.id}]).select();if(s)return console.error("Error al crear conversaci\xf3n:",s),null;return o?.[0]?.id||null}catch(e){return console.error("Error inesperado al crear conversaci\xf3n:",e),null}}async function s(){try{let{data:{user:e}}=await t.N.auth.getUser();if(!e)return console.error("No hay usuario autenticado para obtener conversaciones"),[];let{data:r,error:a}=await t.N.from("conversaciones").select("*").eq("user_id",e.id).order("actualizado_en",{ascending:!1});if(a)return console.error("Error al obtener conversaciones:",a),[];return r||[]}catch(e){return console.error("Error inesperado al obtener conversaciones:",e),[]}}async function n(e,r){let{error:a}=await t.N.from("conversaciones").update({titulo:r,actualizado_en:new Date().toISOString()}).eq("id",e);return!a||(console.error("Error al actualizar conversaci\xf3n:",a),!1)}async function i(e){try{await l();let{error:r}=await t.N.from("conversaciones").update({activa:!0,actualizado_en:new Date().toISOString()}).eq("id",e);if(r)return console.error("Error al activar conversaci\xf3n:",r),!1;return!0}catch(e){return console.error("Error inesperado al activar conversaci\xf3n:",e),!1}}async function l(){try{let{data:{user:e}}=await t.N.auth.getUser();if(!e)return console.error("No hay usuario autenticado para desactivar conversaciones"),!1;let{error:r}=await t.N.from("conversaciones").update({activa:!1}).eq("user_id",e.id).eq("activa",!0);if(r)return console.error("Error al desactivar todas las conversaciones:",r),!1;return!0}catch(e){return console.error("Error inesperado al desactivar conversaciones:",e),!1}}async function c(){try{let{data:{user:e}}=await t.N.auth.getUser();if(!e)return console.warn("No hay usuario autenticado para obtener conversaci\xf3n activa"),null;let{data:r,error:a}=await t.N.from("conversaciones").select("*").eq("user_id",e.id);a&&console.error("Error al obtener todas las conversaciones:",a);let{data:o,error:s}=await t.N.from("conversaciones").select("*").eq("user_id",e.id).eq("activa",!0).limit(1);if(s){if("406"===s.code||s.message.includes("406"))return null;return console.error("Error al obtener conversaci\xf3n activa:",s),null}return o&&o.length>0?o[0]:null}catch(e){return console.error("Error inesperado al obtener conversaci\xf3n activa:",e),null}}async function d(e){try{let{data:r,error:a}=await t.N.from("conversaciones").select("id").eq("id",e.conversacion_id).single();if(a)return console.error("Error al verificar la conversaci\xf3n:",a),null;let{data:o,error:s}=await t.N.from("mensajes").insert([e]).select();if(s)return console.error("Error al guardar mensaje:",s),null;let{error:n}=await t.N.from("conversaciones").update({actualizado_en:new Date().toISOString()}).eq("id",e.conversacion_id);return n&&console.error("Error al actualizar la fecha de la conversaci\xf3n:",n),o?.[0]?.id||null}catch(e){return console.error("Error inesperado al guardar mensaje:",e),null}}async function u(e){let{data:r,error:a}=await t.N.from("mensajes").select("*").eq("conversacion_id",e).order("timestamp",{ascending:!0});return a?(console.error("Error al obtener mensajes:",a),[]):r||[]}async function m(e){try{let{data:{user:r}}=await t.N.auth.getUser();if(!r)return console.error("No hay usuario autenticado para eliminar conversaci\xf3n"),!1;let{error:a}=await t.N.from("mensajes").delete().eq("conversacion_id",e);if(a)return console.error("Error al eliminar mensajes de la conversaci\xf3n:",a),!1;let{error:o,count:s}=await t.N.from("conversaciones").delete({count:"exact"}).eq("id",e).eq("user_id",r.id);if(o)return console.error("Error al eliminar conversaci\xf3n:",o),!1;if(0===s)return!1;return!0}catch(e){return console.error("Error inesperado al eliminar conversaci\xf3n:",e),!1}}},78286:(e,r,a)=>{a.d(r,{Gl:()=>d,Kj:()=>l,Lx:()=>n,OA:()=>c,_4:()=>s,dd:()=>m,hg:()=>i,oC:()=>u});var t=a(48363),o=a(34705);async function s(e,r,a){try{console.log("\uD83D\uDCDD Creando nuevo test:",e);let{user:s}=await (0,o.iF)();if(!s)return console.error("❌ No hay usuario autenticado para crear test"),null;console.log("\uD83D\uDC64 Usuario autenticado:",s.id);let{data:n,error:i}=await t.N.from("tests").insert([{titulo:e,descripcion:r,documentos_ids:a,user_id:s.id}]).select();if(i)return console.error("❌ Error al crear test:",i),null;return console.log("✅ Test creado exitosamente:",n?.[0]?.id),n?.[0]?.id||null}catch(e){return console.error("\uD83D\uDCA5 Error inesperado al crear test:",e),null}}async function n(){try{let{user:e}=await (0,o.iF)();if(!e)return console.error("No hay usuario autenticado"),[];let{data:r,error:a}=await t.N.from("tests").select("*").eq("user_id",e.id).order("creado_en",{ascending:!1});if(a)return console.error("Error al obtener tests:",a),[];return r||[]}catch(e){return console.error("Error al obtener tests:",e),[]}}async function i(e){let{data:r,error:a}=await t.N.from("preguntas_test").select("*").eq("test_id",e);return a?(console.error("Error al obtener preguntas de test:",a),[]):r||[]}async function l(e){let{count:r,error:a}=await t.N.from("preguntas_test").select("*",{count:"exact",head:!0}).eq("test_id",e);return a?(console.error("Error al obtener conteo de preguntas:",a),0):r||0}async function c(e){let{error:r}=await t.N.from("preguntas_test").insert(e);return!r||(console.error("Error al guardar preguntas de test:",r),!1)}async function d(e,r,a,o){let{error:s}=await t.N.from("estadisticas_test").insert([{test_id:e,pregunta_id:r,respuesta_seleccionada:a,es_correcta:o,fecha_respuesta:new Date().toISOString()}]);return!s||(console.error("Error al registrar respuesta de test:",s),!1)}async function u(){let{data:e,error:r}=await t.N.from("estadisticas_test").select("*");if(r)return console.error("Error al obtener estad\xedsticas de tests:",r),{totalTests:0,totalPreguntas:0,totalRespuestasCorrectas:0,totalRespuestasIncorrectas:0,porcentajeAcierto:0};let a=new Set(e?.map(e=>e.test_id)||[]),o=new Set(e?.map(e=>e.pregunta_id)||[]),s=e?.filter(e=>e.es_correcta).length||0,n=(e?.length||0)-s,i=e&&e.length>0?Math.round(s/e.length*100):0;return{totalTests:a.size,totalPreguntas:o.size,totalRespuestasCorrectas:s,totalRespuestasIncorrectas:n,porcentajeAcierto:i}}async function m(e){let{data:r,error:a}=await t.N.from("estadisticas_test").select("*").eq("test_id",e);if(a)return console.error("Error al obtener estad\xedsticas del test:",a),{testId:e,totalPreguntas:0,totalCorrectas:0,totalIncorrectas:0,porcentajeAcierto:0,fechasRealizacion:[],preguntasMasFalladas:[]};let{data:o}=await t.N.from("preguntas_test").select("*").eq("test_id",e),s=r?.filter(e=>e.es_correcta).length||0,n=(r?.length||0)-s,i=r&&r.length>0?Math.round(s/r.length*100):0,l=new Set;r?.forEach(e=>{let r=new Date(e.fecha_respuesta);l.add(`${r.getDate()}/${r.getMonth()+1}/${r.getFullYear()}`)});let c=Array.from(l),d=new Map;r?.forEach(e=>{let r=d.get(e.pregunta_id)||{fallos:0,aciertos:0};e.es_correcta?r.aciertos++:r.fallos++,d.set(e.pregunta_id,r)});let u=Array.from(d.entries()).map(([e,r])=>({preguntaId:e,totalFallos:r.fallos,totalAciertos:r.aciertos,pregunta:o?.find(r=>r.id===e)?.pregunta||"Desconocida"})).sort((e,r)=>r.totalFallos-e.totalFallos).slice(0,5);return{testId:e,totalPreguntas:o?.length||0,totalCorrectas:s,totalIncorrectas:n,porcentajeAcierto:i,fechasRealizacion:c,preguntasMasFalladas:u}}},78956:(e,r,a)=>{a.d(r,{IE:()=>o,qk:()=>n,qo:()=>t});let t={free:{id:"free",name:"Plan Gratis",price:0,limits:{documents:1,mindMapsForTrial:2,testsForTrial:10,flashcardsForTrial:10,tokensForTrial:5e4,features:["document_upload","test_generation","flashcard_generation","mind_map_generation"]},features:["document_upload","test_generation","flashcard_generation","mind_map_generation"],restrictedFeatures:["study_planning","ai_tutor_chat","summary_a1_a2"]},usuario:{id:"usuario",name:"Plan Usuario",price:1e3,limits:{documents:-1,mindMapsPerWeek:-1,testsPerWeek:-1,flashcardsPerWeek:-1,monthlyTokens:1e6,features:["document_upload","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation"]},features:["document_upload","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation"],restrictedFeatures:["study_planning","summary_a1_a2"]},pro:{id:"pro",name:"Plan Pro",price:1500,limits:{documents:-1,mindMapsPerWeek:-1,testsPerWeek:-1,flashcardsPerWeek:-1,monthlyTokens:1e6,features:["document_upload","study_planning","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation","summary_a1_a2"]},features:["document_upload","study_planning","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation","summary_a1_a2"],restrictedFeatures:[]}};function o(e){return t[e]||null}function s(e,r){let a=o(e);return!(!a||a.restrictedFeatures.includes(r))&&a.features.includes(r)}async function n(e){try{let r=await fetch("/api/user/plan");if(!r.ok)return console.error("Error obteniendo plan del usuario"),s("free",e);let{plan:a}=await r.json();return s(a||"free",e)}catch(r){return console.error("Error verificando acceso a caracter\xedstica:",r),s("free",e)}}},81552:(e,r,a)=>{a.d(r,{$S:()=>n,d7:()=>i,fF:()=>s});var t=a(79481),o=a(34705);async function s(e){try{let{user:r,error:a}=await (0,o.iF)();if(!r||a)return null;let{data:s,error:n}=await t.N.from("temarios").select("id").eq("id",e).eq("user_id",r.id).single();if(n){if("PGRST116"===n.code)return console.log("Temario no encontrado:",e),null;return console.error("Error al verificar temario:",n),null}if(!s)return console.log("Temario no encontrado:",e),null;let{data:i,error:l}=await t.N.from("planes_estudios").select("*").eq("user_id",r.id).eq("temario_id",e).eq("activo",!0).single();if(l){if("PGRST116"===l.code)return null;return console.error("Error al obtener plan activo:",l),null}return i}catch(e){return console.error("Error al obtener plan activo:",e),null}}async function n(e){try{let{user:r,error:a}=await (0,o.iF)();if(!r||a)return[];let{data:s,error:n}=await t.N.from("progreso_plan_estudios").select("*").eq("plan_id",e).eq("user_id",r.id).order("semana_numero",{ascending:!0}).order("creado_en",{ascending:!0});if(n)return console.error("Error al obtener progreso del plan:",n),[];return s||[]}catch(e){return console.error("Error al obtener progreso del plan:",e),[]}}async function i(e,r,a,s,n,i,l,c,d){try{let{user:u,error:m}=await (0,o.iF)();if(!u||m)return!1;let{data:f}=await t.N.from("progreso_plan_estudios").select("id").eq("plan_id",e).eq("user_id",u.id).eq("semana_numero",r).eq("dia_nombre",a).eq("tarea_titulo",s).single();if(f){let{error:e}=await t.N.from("progreso_plan_estudios").update({completado:i,fecha_completado:i?new Date().toISOString():null,tiempo_real_minutos:l,notas_progreso:c,calificacion:d,actualizado_en:new Date().toISOString()}).eq("id",f.id);if(e)return console.error("Error al actualizar progreso:",e),!1}else{let{error:o}=await t.N.from("progreso_plan_estudios").insert([{plan_id:e,user_id:u.id,semana_numero:r,dia_nombre:a,tarea_titulo:s,tarea_tipo:n,completado:i,fecha_completado:i?new Date().toISOString():null,tiempo_real_minutos:l,notas_progreso:c,calificacion:d}]);if(o)return console.error("Error al crear progreso:",o),!1}return!0}catch(e){return console.error("Error al guardar progreso de tarea:",e),!1}}},84567:(e,r,a)=>{a.d(r,{A:()=>l});var t=a(60687),o=a(43210),s=a(8348),n=a(19);let i=(0,o.forwardRef)(({onSelectionChange:e},r)=>{let[a,i]=(0,o.useState)([]),[l,c]=(0,o.useState)([]),[d,u]=(0,o.useState)(!0),m=async()=>{u(!0);try{let e=await (0,n.R1)();i(e)}catch(e){console.error("Error al cargar documentos:",e)}finally{u(!1)}};(0,o.useEffect)(()=>{m()},[]),(0,o.useImperativeHandle)(r,()=>({recargarDocumentos:m}));let f=a.map(e=>({value:e.id,label:`${e.numero_tema?`Tema ${e.numero_tema}: `:""}${e.titulo} ${e.categoria?`(${e.categoria})`:""}`}));return(0,t.jsxs)("div",{className:"mb-3",children:[(0,t.jsx)("label",{className:"block text-gray-700 text-sm font-medium mb-1",children:"Selecciona los documentos para consultar:"}),(0,t.jsx)(s.Ay,{instanceId:"document-selector",isMulti:!0,isLoading:d,options:f,className:"basic-multi-select",classNamePrefix:"select",placeholder:"Selecciona uno o m\xe1s documentos...",noOptionsMessage:()=>"No hay documentos disponibles",onChange:r=>{c(r||[]),e(r.map(e=>a.find(r=>r.id===e.value)).filter(Boolean))},value:l,styles:{control:e=>({...e,minHeight:"36px",fontSize:"14px"}),multiValue:e=>({...e,fontSize:"12px"}),placeholder:e=>({...e,fontSize:"14px"})}}),0===l.length&&(0,t.jsx)("p",{className:"text-red-500 text-xs italic mt-0.5",children:"Debes seleccionar al menos un documento"})]})});i.displayName="DocumentSelector";let l=i},87373:(e,r,a)=>{a.d(r,{A:()=>i});var t=a(60687);a(43210);var o=a(85814),s=a.n(o),n=a(17019);function i({feature:e,featureDescription:r,benefits:a=[],className:o=""}){let i=a.length>0?a:["Acceso ilimitado a todas las funcionalidades","Generaci\xf3n de contenido sin l\xedmites","Soporte prioritario","Nuevas funcionalidades en primicia"],l={ai_tutor_chat:{name:"Chat con IA",description:"Conversa con tu preparador personal de IA para resolver dudas y obtener explicaciones detalladas."},study_planning:{name:"Planificaci\xf3n de Estudios",description:"Crea planes de estudio personalizados con IA que se adaptan a tu ritmo y objetivos."},summary_a1_a2:{name:"Res\xfamenes Avanzados",description:"Genera res\xfamenes inteligentes y estructurados de tus documentos de estudio."}}[e]||{name:e,description:r||"Esta funcionalidad avanzada te ayudar\xe1 a mejorar tu preparaci\xf3n."};return(0,t.jsx)("div",{className:`min-h-screen bg-gray-50 flex items-center justify-center p-4 ${o}`,children:(0,t.jsx)("div",{className:"max-w-2xl w-full",children:(0,t.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl overflow-hidden",children:[(0,t.jsxs)("div",{className:"bg-gradient-to-r from-blue-600 to-purple-600 px-8 py-12 text-center text-white",children:[(0,t.jsx)("div",{className:"w-20 h-20 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,t.jsx)(n.F5$,{className:"w-10 h-10"})}),(0,t.jsx)("h1",{className:"text-3xl font-bold mb-4",children:l.name}),(0,t.jsx)("p",{className:"text-blue-100 text-lg leading-relaxed",children:l.description})]}),(0,t.jsxs)("div",{className:"px-8 py-8",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsxs)("div",{className:"inline-flex items-center px-4 py-2 bg-yellow-100 text-yellow-800 rounded-full text-sm font-medium mb-6",children:[(0,t.jsx)(n.usP,{className:"w-4 h-4 mr-2"}),"Funcionalidad Premium"]}),(0,t.jsx)("h2",{className:"text-2xl font-semibold text-gray-900 mb-4",children:"Actualiza tu plan para acceder"}),(0,t.jsx)("p",{className:"text-gray-600 text-lg",children:"Esta funcionalidad est\xe1 disponible para usuarios con planes de pago. Actualiza ahora y desbloquea todo el potencial de OposiAI."})]}),(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4 text-center",children:"\xbfQu\xe9 obtienes al actualizar?"}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:i.map((e,r)=>(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)("div",{className:"w-6 h-6 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5",children:(0,t.jsx)(n.YrT,{className:"w-4 h-4 text-green-600"})}),(0,t.jsx)("span",{className:"text-gray-700",children:e})]},r))})]}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,t.jsxs)(s(),{href:"/upgrade-plan",className:"inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5",children:[(0,t.jsx)(n.ei4,{className:"w-5 h-5 mr-2"}),"Actualizar Plan"]}),(0,t.jsx)(s(),{href:"/app",className:"inline-flex items-center justify-center px-8 py-4 border-2 border-gray-300 text-gray-700 font-semibold rounded-xl hover:border-gray-400 hover:bg-gray-50 transition-colors",children:"Volver al Dashboard"})]}),(0,t.jsx)("div",{className:"mt-8 text-center",children:(0,t.jsxs)("p",{className:"text-sm text-gray-500",children:["\xbfTienes preguntas? ",(0,t.jsx)("a",{href:"mailto:<EMAIL>",className:"text-blue-600 hover:underline",children:"Cont\xe1ctanos"})]})})]})]})})})}},94854:(e,r,a)=>{a.d(r,{B$:()=>m,Il:()=>h,Se:()=>d,cN:()=>f,cm:()=>l,jg:()=>n,oS:()=>u,r5:()=>i,sW:()=>c,xv:()=>p,yr:()=>s});var t=a(79481),o=a(34705);async function s(){try{let{user:e,error:r}=await (0,o.iF)();if(!e||r)return!1;let{data:a,error:s}=await t.N.from("temarios").select("id").eq("user_id",e.id).limit(1);if(s){if("PGRST116"===s.code||s.message?.includes("relation")||s.message?.includes("does not exist"))return!1;return console.error("Error al verificar temario en Supabase:",s),!1}return a&&a.length>0}catch(e){return console.error("Error general al verificar temario:",e),!1}}async function n(){try{let{user:e,error:r}=await (0,o.iF)();if(!e||r)return null;let{data:a,error:s}=await t.N.from("temarios").select("*").eq("user_id",e.id).single();if(s){if("PGRST116"===s.code)return null;return console.error("Error al obtener temario en Supabase:",s),null}return a}catch(e){return console.error("Error general al obtener temario:",e),null}}async function i(e,r,a){try{let{user:s,error:n}=await (0,o.iF)();if(!s||n)return console.error("No hay usuario autenticado o error:",n),null;let{data:i,error:l}=await t.N.from("temarios").insert([{titulo:e,descripcion:r,tipo:a,user_id:s.id}]).select().single();if(l)return console.error("Error al crear temario:",l),null;return i.id}catch(e){return console.error("Error al crear temario:",e),null}}async function l(e){try{let{data:r,error:a}=await t.N.from("temas").select("*").eq("temario_id",e).order("orden",{ascending:!0});if(a)return console.error("Error al obtener temas:",a),[];return r||[]}catch(e){return console.error("Error al obtener temas:",e),[]}}async function c(e,r){try{let a=r.map(r=>({...r,temario_id:e})),{error:o}=await t.N.from("temas").insert(a);if(o)return console.error("Error al crear temas:",o),!1;return!0}catch(e){return console.error("Error al crear temas:",e),!1}}async function d(e,r,a){try{let{error:o}=await t.N.from("temarios").update({titulo:r,descripcion:a,actualizado_en:new Date().toISOString()}).eq("id",e);if(o)return console.error("Error al actualizar temario:",o),!1;return!0}catch(e){return console.error("Error al actualizar temario:",e),!1}}async function u(e,r,a){try{let{error:o}=await t.N.from("temas").update({titulo:r,descripcion:a,actualizado_en:new Date().toISOString()}).eq("id",e);if(o)return console.error("Error al actualizar tema:",o),!1;return!0}catch(e){return console.error("Error al actualizar tema:",e),!1}}async function m(e){try{let{error:r}=await t.N.from("temas").delete().eq("id",e);if(r)return console.error("Error al eliminar tema:",r),!1;return!0}catch(e){return console.error("Error al eliminar tema:",e),!1}}async function f(e,r){try{let a={completado:r,actualizado_en:new Date().toISOString()};r?a.fecha_completado=new Date().toISOString():a.fecha_completado=null;let{error:o}=await t.N.from("temas").update(a).eq("id",e);if(o)return console.error("Error al actualizar estado del tema:",o),!1;return!0}catch(e){return console.error("Error al actualizar estado del tema:",e),!1}}async function h(e){try{let{data:r,error:a}=await t.N.from("temas").select("completado").eq("temario_id",e);if(a)return console.error("Error al obtener estad\xedsticas del temario:",a),null;let o=r.length,s=r.filter(e=>e.completado).length;return{totalTemas:o,temasCompletados:s,porcentajeCompletado:o>0?s/o*100:0}}catch(e){return console.error("Error al obtener estad\xedsticas del temario:",e),null}}async function p(e){try{let{error:r}=await t.N.from("temarios").delete().eq("id",e);if(r)return console.error("Error al eliminar temario:",r),!1;return!0}catch(e){return console.error("Error al eliminar temario:",e),!1}}},97697:(e,r,a)=>{a.d(r,{w:()=>c,x:()=>l});var t=a(48363),o=a(34705),s=a(24932),n=a(78286),i=a(49318);async function l(){try{let{user:e}=await (0,o.iF)();if(!e)throw Error("Usuario no autenticado");let{data:r}=await t.N.from("documentos").select("id").eq("user_id",e.id),a=await (0,s.oE)(),l=await (0,n.Lx)(),c=await (0,n.oC)(),d=0,u=0,m=0,f=0,h=0,p=(await Promise.all(a.map(async e=>{let r=await (0,i.yV)(e.id);return d+=r.total,u+=r.paraHoy,m+=r.nuevas,f+=r.aprendiendo,h+=r.repasando,{id:e.id,titulo:e.titulo,fechaCreacion:e.creado_en,paraHoy:r.paraHoy}}))).sort((e,r)=>new Date(r.fechaCreacion).getTime()-new Date(e.fechaCreacion).getTime()).slice(0,5),g=l.map(e=>({id:e.id,titulo:e.titulo,fechaCreacion:e.creado_en,numeroPreguntas:e.numero_preguntas||0})).slice(0,5);return{totalDocumentos:r?.length||0,totalColeccionesFlashcards:a.length,totalTests:l.length,totalFlashcards:d,flashcardsParaHoy:u,flashcardsNuevas:m,flashcardsAprendiendo:f,flashcardsRepasando:h,testsRealizados:c.totalTests,porcentajeAcierto:c.porcentajeAcierto,coleccionesRecientes:p,testsRecientes:g}}catch(e){return console.error("Error al obtener estad\xedsticas del dashboard:",e),{totalDocumentos:0,totalColeccionesFlashcards:0,totalTests:0,totalFlashcards:0,flashcardsParaHoy:0,flashcardsNuevas:0,flashcardsAprendiendo:0,flashcardsRepasando:0,testsRealizados:0,porcentajeAcierto:0,coleccionesRecientes:[],testsRecientes:[]}}}async function c(e=10){try{let{user:r}=await (0,o.iF)();if(!r)return[];let a=await (0,s.oE)();if(0===a.length)return[];let n=new Date;n.setHours(23,59,59,999);let{data:i,error:l}=await t.N.from("progreso_flashcards").select("flashcard_id, proxima_revision, estado").lte("proxima_revision",n.toISOString()).order("proxima_revision",{ascending:!0}).limit(e);if(l)return console.error("Error al obtener progreso de flashcards:",l),[];if(!i||0===i.length)return[];let c=i.map(e=>e.flashcard_id),{data:d,error:u}=await t.N.from("flashcards").select("id, pregunta, coleccion_id").in("id",c);if(u)return console.error("Error al obtener flashcards:",u),[];let m=[];for(let e of i){let r=d?.find(r=>r.id===e.flashcard_id);if(r){let t=a.find(e=>e.id===r.coleccion_id);t&&m.push({id:r.id,pregunta:r.pregunta,coleccionTitulo:t.titulo,coleccionId:t.id,proximaRevision:e.proxima_revision,estado:e.estado||"nuevo"})}}return m}catch(e){return console.error("Error al obtener pr\xf3ximas flashcards:",e),[]}}}};