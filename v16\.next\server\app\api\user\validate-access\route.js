"use strict";(()=>{var e={};e.id=3407,e.ids=[3407],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{e.exports=require("punycode")},27910:e=>{e.exports=require("stream")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32571:(e,r,t)=>{t.d(r,{o:()=>i});var a=t(42049),s=t(8814),n=t(89546);class o{static async logWebhookEvent(e){try{let r=e.success?"✅":"❌",t=new Date().toISOString();console.log(`${r} [WEBHOOK] ${t}`,{eventType:e.eventType,eventId:e.eventId,success:e.success,processingTime:`${e.processingTime}ms`,message:e.message,...e.error&&{error:e.error},...e.data&&{data:e.data}}),await this.logToExternalService(e)}catch(e){console.error("Error logging webhook event:",e)}}static async logFeatureAccess(e,r,a,s,n=0,o){try{if(process.env.SUPABASE_SERVICE_ROLE_KEY){let{SupabaseAdminService:i}=await Promise.resolve().then(t.bind(t,42049));await i.logFeatureAccess({user_id:e,feature_name:r,access_granted:a,plan_at_time:s,tokens_used:n,denial_reason:o})}let i=a?"✅":"❌";console.log(`${i} [FEATURE_ACCESS]`,{userId:e,feature:r,granted:a,plan:s,tokens:n,...o&&{reason:o}})}catch(e){console.error("Error logging feature access:",e)}}static async logPlanChange(e,r,a,s,n,o){try{if(process.env.SUPABASE_SERVICE_ROLE_KEY){let{SupabaseAdminService:i}=await Promise.resolve().then(t.bind(t,42049));await i.logPlanChange({user_id:e,old_plan:r||void 0,new_plan:a,changed_by:s,reason:n,transaction_id:o})}console.log("\uD83D\uDD04 [PLAN_CHANGE]",{userId:e,oldPlan:r,newPlan:a,changedBy:s,reason:n,transactionId:o})}catch(e){console.error("Error logging plan change:",e)}}static async logCriticalError(e,r,t){try{let a={context:e,message:r.message,stack:r.stack,timestamp:new Date().toISOString(),additionalData:t};console.error("\uD83D\uDEA8 [CRITICAL_ERROR]",a),await this.sendCriticalAlert(a)}catch(e){console.error("Error logging critical error:",e)}}static logPerformanceMetrics(e,r,t,a){let s={operation:e,duration:`${r}ms`,success:t,timestamp:new Date().toISOString(),...a};console.log("\uD83D\uDCCA [PERFORMANCE]",s),this.sendMetrics(s)}static async logToExternalService(e){}static async sendCriticalAlert(e){}static sendMetrics(e){"true"===process.env.ENABLE_METRICS_LOGGING&&console.log("\uD83D\uDCC8 [METRICS_EXPORT]",e)}static async getWebhookStats(e="day"){return{totalEvents:0,successRate:0,averageProcessingTime:0,errorsByType:{}}}}class i{static async validateFeatureAccess(e,r,t=0){try{let n=await a.SupabaseAdminService.getUserProfile(e);if(!n)return await o.logFeatureAccess(e,r,!1,"unknown",0,"User profile not found"),{allowed:!1,reason:"Perfil de usuario no encontrado"};if("free"!==n.subscription_plan&&!n.payment_verified)return await o.logFeatureAccess(e,r,!1,n.subscription_plan,0,"Payment not verified"),{allowed:!1,reason:"Pago no verificado. Complete el proceso de pago para acceder a esta caracter\xedstica."};if(!(0,s.Nu)(n.subscription_plan,r))return await o.logFeatureAccess(e,r,!1,n.subscription_plan,0,`Feature not available in ${n.subscription_plan} plan`),{allowed:!1,reason:`La caracter\xedstica ${r} no est\xe1 disponible en su plan ${n.subscription_plan}`};if(t>0){let a=await this.validateTokenUsage(n,t);if(!a.allowed)return await o.logFeatureAccess(e,r,!1,n.subscription_plan,t,a.reason),a}return await o.logFeatureAccess(e,r,!0,n.subscription_plan,t),{allowed:!0,remainingUsage:n.monthly_token_limit-n.current_month_tokens,planLimits:{monthlyTokens:n.monthly_token_limit,currentTokens:n.current_month_tokens}}}catch(a){return console.error("Error validating feature access:",a),await o.logFeatureAccess(e,r,!1,"error",t,"Internal validation error"),{allowed:!1,reason:"Error interno de validaci\xf3n"}}}static async validateTokenUsage(e,r){let t=new Date().toISOString().slice(0,7)+"-01";e.current_month!==t&&(await a.SupabaseAdminService.upsertUserProfile({...e,current_month_tokens:0,current_month:t,updated_at:new Date().toISOString()}),e.current_month_tokens=0,e.current_month=t);let s=e.current_month_tokens+r;return s>e.monthly_token_limit?{allowed:!1,reason:`L\xedmite mensual de tokens alcanzado. Usado: ${e.current_month_tokens}/${e.monthly_token_limit}`,remainingUsage:Math.max(0,e.monthly_token_limit-e.current_month_tokens)}:{allowed:!0,remainingUsage:e.monthly_token_limit-s}}static async getUserAccessInfo(e){try{let r=await a.SupabaseAdminService.getUserProfile(e);if(!r)return null;let n=(0,s.IE)(r.subscription_plan);if(!n)return null;let o={tokens:r.current_month_tokens,tokenLimit:r.monthly_token_limit,month:r.current_month,documents:0,tests:0,flashcards:0,mindMaps:0};try{let r=await a.SupabaseAdminService.getDocumentsCount(e);o.documents=r}catch(e){console.error("Error getting documents count:",e)}if("free"===r.subscription_plan)try{let{FreeAccountService:r}=await Promise.resolve().then(t.bind(t,89546)),a=await r.getFreeAccountStatus(e);a&&(o={...o,tests:a.usageCount.tests||0,flashcards:a.usageCount.flashcards||0,mindMaps:a.usageCount.mindMaps||0})}catch(e){console.error("Error getting free account usage:",e)}let i={...n.limits,tests:n.limits.testsPerWeek??0,flashcards:n.limits.flashcardsPerWeek??0,mindMaps:n.limits.mindMapsPerWeek??0};return{userId:e,plan:r.subscription_plan,paymentVerified:r.payment_verified,features:n.features||[],limits:i,currentUsage:o}}catch(e){return console.error("Error getting user access info:",e),null}}static async canUserPerformAction(e,r,t=1){try{let s=await a.SupabaseAdminService.getUserProfile(e);if(!s)return{allowed:!1,reason:"Usuario no encontrado"};let o=await this.validateFeatureAccess(e,{test_generation:"test_generation",flashcard_generation:"flashcard_generation",mind_map_generation:"mind_map_generation",ai_chat:"ai_tutor_chat",study_planning:"study_planning",summary_generation:"summary_a1_a2"}[r]);if(!o.allowed)return o;if("free"===s.subscription_plan){let a={test_generation:"tests",flashcard_generation:"flashcards",mind_map_generation:"mindMaps"}[r];if(a){let s=await n.FreeAccountService.canPerformAction(e,a,t);if(!s.allowed)return{allowed:!1,reason:s.reason||`L\xedmite alcanzado para ${r}`,remainingUsage:s.remaining}}}return{allowed:!0,remainingUsage:o.remainingUsage}}catch(e){return console.error("Error checking user action:",e),{allowed:!1,reason:"Error interno de validaci\xf3n"}}}static async updateTokenUsage(e,r,t){try{let t=await a.SupabaseAdminService.getUserProfile(e);if(!t)return!1;let s=t.current_month_tokens+r;return await a.SupabaseAdminService.upsertUserProfile({...t,current_month_tokens:s,updated_at:new Date().toISOString()}),console.log(`✅ Tokens actualizados para usuario ${e}: +${r} (Total: ${s}/${t.monthly_token_limit})`),!0}catch(e){return console.error("Error updating token usage:",e),!1}}static async checkUpgradeNeeded(e){try{let r=await a.SupabaseAdminService.getUserProfile(e);if(!r)return{needsUpgrade:!1};let t=r.current_month_tokens/r.monthly_token_limit*100;if(t>=90){let e="free"===r.subscription_plan?"usuario":"pro";return{needsUpgrade:!0,reason:`Has usado el ${t.toFixed(1)}% de tus tokens mensuales`,suggestedPlan:e}}return{needsUpgrade:!1}}catch(e){return console.error("Error checking upgrade need:",e),{needsUpgrade:!1}}}}},34631:e=>{e.exports=require("tls")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47780:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>p,serverHooks:()=>_,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>m});var a={};t.r(a),t.d(a,{POST:()=>u,PUT:()=>d});var s=t(96559),n=t(48088),o=t(37719),i=t(32190),c=t(99869),l=t(32571);async function u(e){try{let r=(0,c.U)(),{data:{user:t},error:a}=await r.auth.getUser();if(a||!t)return i.NextResponse.json({error:"No autorizado"},{status:401});let{feature:s,tokensToUse:n=0,action:o,quantity:u=1}=await e.json();if(!s)return i.NextResponse.json({error:"Caracter\xedstica requerida"},{status:400});let d=await l.o.validateFeatureAccess(t.id,s,n);if(!d.allowed)return i.NextResponse.json({allowed:!1,reason:d.reason,needsUpgrade:!0},{status:403});if(o){let e=await l.o.canUserPerformAction(t.id,o,u);if(!e.allowed)return i.NextResponse.json({allowed:!1,reason:e.reason,needsUpgrade:!0},{status:403})}return i.NextResponse.json({allowed:!0,remainingUsage:d.remainingUsage,planLimits:d.planLimits})}catch(e){return console.error("Error validating access:",e),i.NextResponse.json({error:"Error interno del servidor"},{status:500})}}async function d(e){try{let r=(0,c.U)(),{data:{user:t},error:a}=await r.auth.getUser();if(a||!t)return i.NextResponse.json({error:"No autorizado"},{status:401});let{tokensUsed:s,activity:n}=await e.json();if(!s||!n)return i.NextResponse.json({error:"Tokens y actividad requeridos"},{status:400});if(!await l.o.updateTokenUsage(t.id,s,n))return i.NextResponse.json({error:"Error actualizando uso de tokens"},{status:500});let o=await l.o.getUserAccessInfo(t.id);return i.NextResponse.json({success:!0,tokensUsed:s,currentUsage:o?.currentUsage||null})}catch(e){return console.error("Error updating token usage:",e),i.NextResponse.json({error:"Error interno del servidor"},{status:500})}}let p=new s.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/user/validate-access/route",pathname:"/api/user/validate-access",filename:"route",bundlePath:"app/api/user/validate-access/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\user\\validate-access\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:g,workUnitAsyncStorage:m,serverHooks:_}=p;function h(){return(0,o.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:m})}},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63511:(e,r,t)=>{t.d(r,{U:()=>s});var a=t(34386);function s(){return(0,a.createBrowserClient)("https://fxnhpxjijinfuxxxplzj.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4",{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0}})}s()},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")},99869:(e,r,t)=>{t.d(r,{U:()=>a.U});var a=t(63511)}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4447,580,6345,4386,7902],()=>t(47780));module.exports=a})();