[{"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\admin\\reactivate\\page.tsx": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\admin\\cleanup-expired-free\\route.ts": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\admin\\email-failures\\route.ts": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\admin\\notification-stats\\route.ts": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\admin\\process-expired-grace-periods\\route.ts": "5", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\admin\\reactivate-user\\route.ts": "6", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\admin\\send-grace-period-reminders\\route.ts": "7", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\ai\\route.ts": "8", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\auth\\free-account-status\\route.ts": "9", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\auth\\generate-password-reset\\route.ts": "10", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\auth\\pre-register-paid\\route.ts": "11", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\auth\\register-free\\route.ts": "12", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\auth\\resend-confirmation\\route.ts": "13", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\document\\upload\\route.ts": "14", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\health\\route.ts": "15", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\notify-signup\\route.ts": "16", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\payment\\status\\route.ts": "17", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\stripe\\create-checkout-session\\route.ts": "18", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\stripe\\webhook\\route.ts": "19", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\tokens\\purchase\\route.ts": "20", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\user\\cancel-subscription\\route.ts": "21", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\user\\notifications\\route.ts": "22", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\user\\plan\\route.ts": "23", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\user\\profile\\route.ts": "24", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\user\\reactivate\\route.ts": "25", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\user\\status\\route.ts": "26", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\user\\validate-access\\route.ts": "27", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\app\\page.tsx": "28", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\auth\\callback\\page.tsx": "29", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\auth\\confirm-reset\\page.tsx": "30", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\auth\\confirmed\\page.tsx": "31", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\auth\\reset-password\\page.tsx": "32", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\auth\\unauthorized\\page.tsx": "33", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\debug-user\\page.tsx": "34", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\layout.tsx": "35", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\login\\page.tsx": "36", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\page.tsx": "37", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\payment\\page.tsx": "38", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\payment-pending\\page.tsx": "39", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\plan-estudios\\page.tsx": "40", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\profile\\page.tsx": "41", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\thank-you\\page.tsx": "42", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\upgrade-plan\\page.tsx": "43", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\welcome\\page.tsx": "44", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\auth\\PlanValidationWrapper.tsx": "45", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\thank-you\\ProcessingState.tsx": "46", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\DocumentLimitStatus.tsx": "47", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\FreeAccountGuard.tsx": "48", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\FreeAccountStatus.tsx": "49", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\FreeAccountTimer.tsx": "50", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\PlanCard.tsx": "51", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\SessionInfo.tsx": "52", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\TokenProgressBar.tsx": "53", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\TokenStatsModal.tsx": "54", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\TokenUsageChart.tsx": "55", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\UnauthorizedAccess.tsx": "56", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\UpgradePlanMessage.tsx": "57", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\config\\openai.ts": "58", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\config\\prompts.ts": "59", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\contexts\\AuthContext.tsx": "60", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\contexts\\BackgroundTasksContext.tsx": "61", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\auth\\components\\AuthManager.tsx": "62", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\auth\\components\\ForgotPasswordModal.tsx": "63", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\auth\\components\\InactivityWarning.tsx": "64", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\auth\\components\\ProtectedRoute.tsx": "65", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\auth\\components\\SessionIndicator.tsx": "66", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\auth\\hooks\\useInactivityTimer.ts": "67", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\conversations\\components\\ConversationHistory.tsx": "68", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\conversations\\components\\ConversationSidebar.tsx": "69", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\conversations\\components\\QuestionForm.tsx": "70", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\dashboard\\components\\Dashboard.tsx": "71", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\dashboard\\components\\StudyStatistics.tsx": "72", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\dashboard\\services\\estadisticasService.ts": "73", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\documents\\components\\DocumentManager.tsx": "74", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\documents\\components\\DocumentSelector.tsx": "75", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\documents\\components\\DocumentUploader.tsx": "76", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardCollectionCard.tsx": "77", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardCollectionList.test.tsx": "78", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardCollectionList.tsx": "79", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardCollectionView.tsx": "80", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardDetailedStatistics.tsx": "81", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardEditModal.tsx": "82", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardGeneralStatistics.tsx": "83", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardGenerator.tsx": "84", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardStatistics.tsx": "85", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardStudyMode.tsx": "86", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardStudyOptions.tsx": "87", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardViewer.tsx": "88", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\RevisionHistory.tsx": "89", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\types.ts": "90", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\mindmaps\\components\\MindMapGenerator.tsx": "91", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\mindmaps\\components\\MindMapHelp.tsx": "92", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\components\\CalendarioModal.tsx": "93", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\components\\PlanCalendario.tsx": "94", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\components\\PlanEstudiosViewer.tsx": "95", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\components\\PlanificacionAsistente.tsx": "96", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\components\\TareasDelDia.tsx": "97", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\hooks\\usePlanCalendario.ts": "98", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\services\\planEstudiosClientService.ts": "99", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\services\\planEstudiosService.ts": "100", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\services\\planGeneratorService.ts": "101", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\services\\planificacionService.ts": "102", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\types\\calendarTypes.ts": "103", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\utils\\calendarioPreferences.ts": "104", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\utils\\planDateUtils.ts": "105", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\profile\\components\\AccountInfo.tsx": "106", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\profile\\components\\AccountSettings.tsx": "107", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\profile\\components\\NotificationHistory.tsx": "108", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\profile\\components\\PlanUsage.tsx": "109", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\shared\\components\\BackgroundTasksPanel.tsx": "110", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\shared\\components\\ClientLayout.tsx": "111", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\shared\\components\\DiagnosticPanel.tsx": "112", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\shared\\components\\MobileDebugInfo.tsx": "113", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\shared\\components\\SidebarMenu.tsx": "114", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\summaries\\components\\SummaryGenerator.tsx": "115", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\summaries\\components\\SummaryList.tsx": "116", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\temario\\components\\TemaActions.tsx": "117", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\temario\\components\\TemaEditModal.tsx": "118", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\temario\\components\\TemarioEditModal.tsx": "119", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\temario\\components\\TemarioManager.tsx": "120", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\temario\\components\\TemarioSetup.tsx": "121", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\temario\\components\\TemariosPredefinidosSelector.tsx": "122", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\temario\\services\\temarioService.ts": "123", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\temario\\services\\temariosPredefinidosService.ts": "124", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\components\\TestDetailedStatistics.tsx": "125", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\components\\TestEditModal.tsx": "126", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\components\\TestGeneralStatistics.tsx": "127", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\components\\TestGenerator.tsx": "128", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\components\\TestRepasoConfig.tsx": "129", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\components\\TestRepasoViewer.tsx": "130", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\components\\TestViewer.tsx": "131", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\hooks\\useBackgroundGeneration.ts": "132", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\hooks\\useFreeAccount.ts": "133", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\hooks\\useMobileAuth.ts": "134", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\hooks\\usePlanEstudiosResults.ts": "135", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\hooks\\usePlanLimits.ts": "136", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\hooks\\useTaskResults.ts": "137", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\hooks\\useUserPlan.ts": "138", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\ai\\tokenTracker.ts": "139", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\auth\\validateUserAccess.ts": "140", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\formSchemas.ts": "141", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini\\flashcardGenerator.ts": "142", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini\\geminiClient.ts": "143", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini\\index.ts": "144", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini\\mindMapGenerator.ts": "145", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini\\questionService.ts": "146", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini\\resumenEditor.ts": "147", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini\\resumenGenerator.ts": "148", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini\\testGenerator.ts": "149", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\openai\\openaiClient.ts": "150", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\email\\emailAnalytics.ts": "151", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\email\\emailLogger.ts": "152", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\email\\emailNotificationService.ts": "153", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\email\\emailSender.ts": "154", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\email\\emailTemplates.ts": "155", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\email\\index.ts": "156", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\email\\types.ts": "157", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\freeAccountService.ts": "158", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\limitHandler.ts": "159", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\permissionService.ts": "160", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\planValidation.ts": "161", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\stripeWebhookHandlers.ts": "162", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\userManagement.ts": "163", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\stripe\\config.ts": "164", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\stripe\\plans.ts": "165", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\admin.ts": "166", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\authService.ts": "167", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\client.ts": "168", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\conversacionesService.ts": "169", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\dashboardService.ts": "170", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\documentosService.server.ts": "171", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\documentosService.ts": "172", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\estadisticasService.ts": "173", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\flashcardsService.ts": "174", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\index.ts": "175", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\resumenesService.ts": "176", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\server.ts": "177", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\supabaseClient.ts": "178", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\testsService.ts": "179", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\tokenUsageService.server.ts": "180", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\tokenUsageService.ts": "181", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\utils\\dateUtils.ts": "182", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\utils\\emailTemplates.ts": "183", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\utils\\planLimits.ts": "184", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\utils\\securityHelpers.ts": "185", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\utils\\webhookLogger.ts": "186", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\zodSchemas.ts": "187", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\middleware.ts": "188", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\scripts\\validateSystem.ts": "189", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\utils\\markdownToHTML.ts": "190", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\__tests__\\hooks\\usePlanLimits.test.tsx": "191", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\__tests__\\integration\\paymentFlow.test.ts": "192", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\__tests__\\services\\limitHandler.test.ts": "193", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\__tests__\\services\\permissionService.test.ts": "194", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\__tests__\\services\\planValidation.test.ts": "195"}, {"size": 8681, "mtime": 1750549865174, "results": "196", "hashOfConfig": "197"}, {"size": 8713, "mtime": 1749926277358, "results": "198", "hashOfConfig": "197"}, {"size": 5845, "mtime": 1750309142357, "results": "199", "hashOfConfig": "197"}, {"size": 3612, "mtime": 1750086603738, "results": "200", "hashOfConfig": "197"}, {"size": 6683, "mtime": 1750085064464, "results": "201", "hashOfConfig": "197"}, {"size": 5926, "mtime": 1750549829701, "results": "202", "hashOfConfig": "197"}, {"size": 8784, "mtime": 1750086580286, "results": "203", "hashOfConfig": "197"}, {"size": 8497, "mtime": 1750862479835, "results": "204", "hashOfConfig": "197"}, {"size": 7052, "mtime": 1750082610808, "results": "205", "hashOfConfig": "197"}, {"size": 1301, "mtime": 1750287727080, "results": "206", "hashOfConfig": "197"}, {"size": 5091, "mtime": 1750698172366, "results": "207", "hashOfConfig": "197"}, {"size": 4017, "mtime": 1750546019180, "results": "208", "hashOfConfig": "197"}, {"size": 2270, "mtime": 1750549762427, "results": "209", "hashOfConfig": "197"}, {"size": 9194, "mtime": 1750020486326, "results": "210", "hashOfConfig": "197"}, {"size": 271, "mtime": 1750308563029, "results": "211", "hashOfConfig": "197"}, {"size": 5932, "mtime": 1750106816513, "results": "212", "hashOfConfig": "197"}, {"size": 3019, "mtime": 1750549747348, "results": "213", "hashOfConfig": "197"}, {"size": 4781, "mtime": 1750547906770, "results": "214", "hashOfConfig": "197"}, {"size": 4139, "mtime": 1749829295626, "results": "215", "hashOfConfig": "197"}, {"size": 3765, "mtime": 1750309415770, "results": "216", "hashOfConfig": "197"}, {"size": 3987, "mtime": 1750107085364, "results": "217", "hashOfConfig": "197"}, {"size": 2245, "mtime": 1750086615997, "results": "218", "hashOfConfig": "197"}, {"size": 1120, "mtime": 1750110995546, "results": "219", "hashOfConfig": "197"}, {"size": 5431, "mtime": 1750283688975, "results": "220", "hashOfConfig": "197"}, {"size": 2214, "mtime": 1749910333290, "results": "221", "hashOfConfig": "197"}, {"size": 2110, "mtime": 1749913156452, "results": "222", "hashOfConfig": "197"}, {"size": 3432, "mtime": 1749830150148, "results": "223", "hashOfConfig": "197"}, {"size": 29187, "mtime": 1750865191556, "results": "224", "hashOfConfig": "197"}, {"size": 12130, "mtime": 1750519961058, "results": "225", "hashOfConfig": "197"}, {"size": 5086, "mtime": 1750254562388, "results": "226", "hashOfConfig": "197"}, {"size": 5649, "mtime": 1750544723119, "results": "227", "hashOfConfig": "197"}, {"size": 19613, "mtime": 1750866829931, "results": "228", "hashOfConfig": "197"}, {"size": 8147, "mtime": 1750075142671, "results": "229", "hashOfConfig": "197"}, {"size": 6679, "mtime": 1750020180985, "results": "230", "hashOfConfig": "197"}, {"size": 841, "mtime": 1750771975166, "results": "231", "hashOfConfig": "197"}, {"size": 6974, "mtime": 1750867022329, "results": "232", "hashOfConfig": "197"}, {"size": 11112, "mtime": 1750865170358, "results": "233", "hashOfConfig": "197"}, {"size": 9894, "mtime": 1750549596184, "results": "234", "hashOfConfig": "197"}, {"size": 9534, "mtime": 1750865465599, "results": "235", "hashOfConfig": "197"}, {"size": 16407, "mtime": 1750025885793, "results": "236", "hashOfConfig": "197"}, {"size": 7697, "mtime": 1750086750423, "results": "237", "hashOfConfig": "197"}, {"size": 5926, "mtime": 1750690443715, "results": "238", "hashOfConfig": "197"}, {"size": 12876, "mtime": 1750865767620, "results": "239", "hashOfConfig": "197"}, {"size": 8472, "mtime": 1749855884749, "results": "240", "hashOfConfig": "197"}, {"size": 8384, "mtime": 1750865830885, "results": "241", "hashOfConfig": "197"}, {"size": 2673, "mtime": 1749908591667, "results": "242", "hashOfConfig": "197"}, {"size": 6340, "mtime": 1750073060045, "results": "243", "hashOfConfig": "197"}, {"size": 7534, "mtime": 1750075103015, "results": "244", "hashOfConfig": "197"}, {"size": 9495, "mtime": 1750073106534, "results": "245", "hashOfConfig": "197"}, {"size": 8278, "mtime": 1750073147880, "results": "246", "hashOfConfig": "197"}, {"size": 6370, "mtime": 1750949816238, "results": "247", "hashOfConfig": "197"}, {"size": 2643, "mtime": 1749364222822, "results": "248", "hashOfConfig": "197"}, {"size": 3239, "mtime": 1750692677577, "results": "249", "hashOfConfig": "197"}, {"size": 3469, "mtime": 1750693988715, "results": "250", "hashOfConfig": "197"}, {"size": 4720, "mtime": 1750692751611, "results": "251", "hashOfConfig": "197"}, {"size": 8346, "mtime": 1750692897611, "results": "252", "hashOfConfig": "197"}, {"size": 5161, "mtime": 1750073161083, "results": "253", "hashOfConfig": "197"}, {"size": 2571, "mtime": 1750760269966, "results": "254", "hashOfConfig": "197"}, {"size": 56653, "mtime": 1750714886122, "results": "255", "hashOfConfig": "197"}, {"size": 9777, "mtime": 1750546783460, "results": "256", "hashOfConfig": "197"}, {"size": 4662, "mtime": 1750696077841, "results": "257", "hashOfConfig": "197"}, {"size": 2386, "mtime": 1748898729938, "results": "258", "hashOfConfig": "197"}, {"size": 8459, "mtime": 1750761381376, "results": "259", "hashOfConfig": "197"}, {"size": 3162, "mtime": 1748813999472, "results": "260", "hashOfConfig": "197"}, {"size": 1279, "mtime": 1748898005145, "results": "261", "hashOfConfig": "197"}, {"size": 2089, "mtime": 1748784425494, "results": "262", "hashOfConfig": "197"}, {"size": 3675, "mtime": 1750865559572, "results": "263", "hashOfConfig": "197"}, {"size": 3905, "mtime": 1750862535382, "results": "264", "hashOfConfig": "197"}, {"size": 11443, "mtime": 1750862546425, "results": "265", "hashOfConfig": "197"}, {"size": 21493, "mtime": 1750862556972, "results": "266", "hashOfConfig": "197"}, {"size": 14701, "mtime": 1749642914607, "results": "267", "hashOfConfig": "197"}, {"size": 15797, "mtime": 1748789194243, "results": "268", "hashOfConfig": "197"}, {"size": 11388, "mtime": 1749655003060, "results": "269", "hashOfConfig": "197"}, {"size": 6561, "mtime": 1749655003060, "results": "270", "hashOfConfig": "197"}, {"size": 3186, "mtime": 1750697251442, "results": "271", "hashOfConfig": "197"}, {"size": 10544, "mtime": 1750020029412, "results": "272", "hashOfConfig": "197"}, {"size": 2048, "mtime": 1749137653402, "results": "273", "hashOfConfig": "197"}, {"size": 4361, "mtime": 1749655003075, "results": "274", "hashOfConfig": "197"}, {"size": 4261, "mtime": 1748777250685, "results": "275", "hashOfConfig": "197"}, {"size": 6739, "mtime": 1749137786700, "results": "276", "hashOfConfig": "197"}, {"size": 8975, "mtime": 1750865875037, "results": "277", "hashOfConfig": "197"}, {"size": 6275, "mtime": 1748381218646, "results": "278", "hashOfConfig": "197"}, {"size": 11220, "mtime": 1748562362912, "results": "279", "hashOfConfig": "197"}, {"size": 20873, "mtime": 1750862568515, "results": "280", "hashOfConfig": "197"}, {"size": 1500, "mtime": 1748376642824, "results": "281", "hashOfConfig": "197"}, {"size": 10380, "mtime": 1748563534405, "results": "282", "hashOfConfig": "197"}, {"size": 4612, "mtime": 1748778081572, "results": "283", "hashOfConfig": "197"}, {"size": 15473, "mtime": 1749642914618, "results": "284", "hashOfConfig": "197"}, {"size": 4918, "mtime": 1748789236100, "results": "285", "hashOfConfig": "197"}, {"size": 1090, "mtime": 1749138136757, "results": "286", "hashOfConfig": "197"}, {"size": 13256, "mtime": 1750108436905, "results": "287", "hashOfConfig": "197"}, {"size": 3779, "mtime": 1748557840096, "results": "288", "hashOfConfig": "197"}, {"size": 4463, "mtime": 1750719906831, "results": "289", "hashOfConfig": "197"}, {"size": 11231, "mtime": 1750718509926, "results": "290", "hashOfConfig": "197"}, {"size": 20926, "mtime": 1750865923199, "results": "291", "hashOfConfig": "197"}, {"size": 16874, "mtime": 1750866029504, "results": "292", "hashOfConfig": "197"}, {"size": 13033, "mtime": 1750719717802, "results": "293", "hashOfConfig": "197"}, {"size": 8780, "mtime": 1750718935939, "results": "294", "hashOfConfig": "197"}, {"size": 7378, "mtime": 1750862399705, "results": "295", "hashOfConfig": "197"}, {"size": 10458, "mtime": 1750862409883, "results": "296", "hashOfConfig": "197"}, {"size": 20287, "mtime": 1749364863869, "results": "297", "hashOfConfig": "197"}, {"size": 6272, "mtime": 1750862420020, "results": "298", "hashOfConfig": "197"}, {"size": 4912, "mtime": 1750719594922, "results": "299", "hashOfConfig": "197"}, {"size": 7055, "mtime": 1750719029925, "results": "300", "hashOfConfig": "197"}, {"size": 10727, "mtime": 1750718826516, "results": "301", "hashOfConfig": "197"}, {"size": 11016, "mtime": 1750086795224, "results": "302", "hashOfConfig": "197"}, {"size": 16089, "mtime": 1750257232905, "results": "303", "hashOfConfig": "197"}, {"size": 10652, "mtime": 1750866373520, "results": "304", "hashOfConfig": "197"}, {"size": 11515, "mtime": 1750110978404, "results": "305", "hashOfConfig": "197"}, {"size": 7292, "mtime": 1748559970572, "results": "306", "hashOfConfig": "197"}, {"size": 1394, "mtime": 1748815043814, "results": "307", "hashOfConfig": "197"}, {"size": 8677, "mtime": 1750866247630, "results": "308", "hashOfConfig": "197"}, {"size": 5212, "mtime": 1749655003075, "results": "309", "hashOfConfig": "197"}, {"size": 7880, "mtime": 1750697439339, "results": "310", "hashOfConfig": "197"}, {"size": 11098, "mtime": 1750696256433, "results": "311", "hashOfConfig": "197"}, {"size": 21535, "mtime": 1750760269966, "results": "312", "hashOfConfig": "197"}, {"size": 4528, "mtime": 1748810558456, "results": "313", "hashOfConfig": "197"}, {"size": 6444, "mtime": 1748810539098, "results": "314", "hashOfConfig": "197"}, {"size": 6524, "mtime": 1748810280408, "results": "315", "hashOfConfig": "197"}, {"size": 20502, "mtime": 1750862432534, "results": "316", "hashOfConfig": "197"}, {"size": 17180, "mtime": 1748900565639, "results": "317", "hashOfConfig": "197"}, {"size": 9265, "mtime": 1748900735287, "results": "318", "hashOfConfig": "197"}, {"size": 7562, "mtime": 1750862442484, "results": "319", "hashOfConfig": "197"}, {"size": 6378, "mtime": 1748877566234, "results": "320", "hashOfConfig": "197"}, {"size": 4512, "mtime": 1748630532621, "results": "321", "hashOfConfig": "197"}, {"size": 5574, "mtime": 1750862770953, "results": "322", "hashOfConfig": "197"}, {"size": 2905, "mtime": 1748630509095, "results": "323", "hashOfConfig": "197"}, {"size": 22864, "mtime": 1750862581402, "results": "324", "hashOfConfig": "197"}, {"size": 9383, "mtime": 1750862867014, "results": "325", "hashOfConfig": "197"}, {"size": 14034, "mtime": 1750865581974, "results": "326", "hashOfConfig": "197"}, {"size": 30941, "mtime": 1750865609942, "results": "327", "hashOfConfig": "197"}, {"size": 7297, "mtime": 1750696131484, "results": "328", "hashOfConfig": "197"}, {"size": 9239, "mtime": 1750865636993, "results": "329", "hashOfConfig": "197"}, {"size": 3333, "mtime": 1748376643113, "results": "330", "hashOfConfig": "197"}, {"size": 2060, "mtime": 1748814199030, "results": "331", "hashOfConfig": "197"}, {"size": 9266, "mtime": 1750692614862, "results": "332", "hashOfConfig": "197"}, {"size": 2651, "mtime": 1748561592350, "results": "333", "hashOfConfig": "197"}, {"size": 1337, "mtime": 1750107504888, "results": "334", "hashOfConfig": "197"}, {"size": 7023, "mtime": 1749416233417, "results": "335", "hashOfConfig": "197"}, {"size": 7540, "mtime": 1750021679298, "results": "336", "hashOfConfig": "197"}, {"size": 2314, "mtime": 1750761338786, "results": "337", "hashOfConfig": "197"}, {"size": 2407, "mtime": 1749364828132, "results": "338", "hashOfConfig": "197"}, {"size": 1086, "mtime": 1749329759055, "results": "339", "hashOfConfig": "197"}, {"size": 3253, "mtime": 1749655003091, "results": "340", "hashOfConfig": "197"}, {"size": 3527, "mtime": 1749364808623, "results": "341", "hashOfConfig": "197"}, {"size": 2587, "mtime": 1749364782712, "results": "342", "hashOfConfig": "197"}, {"size": 2046, "mtime": 1750695500164, "results": "343", "hashOfConfig": "197"}, {"size": 3077, "mtime": 1749655003091, "results": "344", "hashOfConfig": "197"}, {"size": 3887, "mtime": 1749364846856, "results": "345", "hashOfConfig": "197"}, {"size": 6123, "mtime": 1749364772281, "results": "346", "hashOfConfig": "197"}, {"size": 8426, "mtime": 1750086480511, "results": "347", "hashOfConfig": "197"}, {"size": 6508, "mtime": 1750086442416, "results": "348", "hashOfConfig": "197"}, {"size": 7674, "mtime": 1750086554604, "results": "349", "hashOfConfig": "197"}, {"size": 8509, "mtime": 1750086519480, "results": "350", "hashOfConfig": "197"}, {"size": 8535, "mtime": 1750086411730, "results": "351", "hashOfConfig": "197"}, {"size": 868, "mtime": 1750086640332, "results": "352", "hashOfConfig": "197"}, {"size": 1707, "mtime": 1750086372857, "results": "353", "hashOfConfig": "197"}, {"size": 13923, "mtime": 1750509873388, "results": "354", "hashOfConfig": "197"}, {"size": 17383, "mtime": 1750698297109, "results": "355", "hashOfConfig": "197"}, {"size": 12474, "mtime": 1750692847133, "results": "356", "hashOfConfig": "197"}, {"size": 11782, "mtime": 1750110965854, "results": "357", "hashOfConfig": "197"}, {"size": 31143, "mtime": 1750549685191, "results": "358", "hashOfConfig": "197"}, {"size": 18327, "mtime": 1750547982192, "results": "359", "hashOfConfig": "197"}, {"size": 426, "mtime": 1749138861932, "results": "360", "hashOfConfig": "197"}, {"size": 4105, "mtime": 1750084350458, "results": "361", "hashOfConfig": "197"}, {"size": 13571, "mtime": 1750550713790, "results": "362", "hashOfConfig": "197"}, {"size": 6717, "mtime": 1749655003091, "results": "363", "hashOfConfig": "197"}, {"size": 622, "mtime": 1750254594447, "results": "364", "hashOfConfig": "197"}, {"size": 9246, "mtime": 1749655003091, "results": "365", "hashOfConfig": "197"}, {"size": 6595, "mtime": 1749655003091, "results": "366", "hashOfConfig": "197"}, {"size": 1310, "mtime": 1748378808764, "results": "367", "hashOfConfig": "197"}, {"size": 4038, "mtime": 1749655003108, "results": "368", "hashOfConfig": "197"}, {"size": 11321, "mtime": 1749655003111, "results": "369", "hashOfConfig": "197"}, {"size": 23405, "mtime": 1749642901184, "results": "370", "hashOfConfig": "197"}, {"size": 313, "mtime": 1749655003114, "results": "371", "hashOfConfig": "197"}, {"size": 6748, "mtime": 1749655430336, "results": "372", "hashOfConfig": "197"}, {"size": 1576, "mtime": 1749479562972, "results": "373", "hashOfConfig": "197"}, {"size": 8151, "mtime": 1750084543676, "results": "374", "hashOfConfig": "197"}, {"size": 12117, "mtime": 1750862913333, "results": "375", "hashOfConfig": "197"}, {"size": 8702, "mtime": 1750692817251, "results": "376", "hashOfConfig": "197"}, {"size": 16305, "mtime": 1750692885638, "results": "377", "hashOfConfig": "197"}, {"size": 7381, "mtime": 1750719054362, "results": "378", "hashOfConfig": "197"}, {"size": 11110, "mtime": 1749830202167, "results": "379", "hashOfConfig": "197"}, {"size": 7188, "mtime": 1750078415710, "results": "380", "hashOfConfig": "197"}, {"size": 10079, "mtime": 1750026225814, "results": "381", "hashOfConfig": "197"}, {"size": 7707, "mtime": 1750026605896, "results": "382", "hashOfConfig": "197"}, {"size": 2423, "mtime": 1749655955625, "results": "383", "hashOfConfig": "197"}, {"size": 17529, "mtime": 1750798998148, "results": "384", "hashOfConfig": "197"}, {"size": 13888, "mtime": 1750698368960, "results": "385", "hashOfConfig": "197"}, {"size": 4309, "mtime": 1749504407466, "results": "386", "hashOfConfig": "197"}, {"size": 11509, "mtime": 1749853487198, "results": "387", "hashOfConfig": "197"}, {"size": 12234, "mtime": 1749853536927, "results": "388", "hashOfConfig": "197"}, {"size": 11484, "mtime": 1749853441638, "results": "389", "hashOfConfig": "197"}, {"size": 9864, "mtime": 1749853396810, "results": "390", "hashOfConfig": "197"}, {"size": 8686, "mtime": 1749853357863, "results": "391", "hashOfConfig": "197"}, {"filePath": "392", "messages": "393", "suppressedMessages": "394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "17uv4wy", {"filePath": "395", "messages": "396", "suppressedMessages": "397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "398", "messages": "399", "suppressedMessages": "400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "401", "messages": "402", "suppressedMessages": "403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "404", "messages": "405", "suppressedMessages": "406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "407", "messages": "408", "suppressedMessages": "409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "410", "messages": "411", "suppressedMessages": "412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "413", "messages": "414", "suppressedMessages": "415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "416", "messages": "417", "suppressedMessages": "418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "419", "messages": "420", "suppressedMessages": "421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "422", "messages": "423", "suppressedMessages": "424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "425", "messages": "426", "suppressedMessages": "427", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "428", "messages": "429", "suppressedMessages": "430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "431", "messages": "432", "suppressedMessages": "433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "434", "messages": "435", "suppressedMessages": "436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "437", "messages": "438", "suppressedMessages": "439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "440", "messages": "441", "suppressedMessages": "442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "443", "messages": "444", "suppressedMessages": "445", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "446", "messages": "447", "suppressedMessages": "448", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "449", "messages": "450", "suppressedMessages": "451", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "452", "messages": "453", "suppressedMessages": "454", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "455", "messages": "456", "suppressedMessages": "457", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "458", "messages": "459", "suppressedMessages": "460", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "461", "messages": "462", "suppressedMessages": "463", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "464", "messages": "465", "suppressedMessages": "466", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "467", "messages": "468", "suppressedMessages": "469", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "470", "messages": "471", "suppressedMessages": "472", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "473", "messages": "474", "suppressedMessages": "475", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "476", "messages": "477", "suppressedMessages": "478", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "479", "messages": "480", "suppressedMessages": "481", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "482", "messages": "483", "suppressedMessages": "484", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "485", "messages": "486", "suppressedMessages": "487", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "488", "messages": "489", "suppressedMessages": "490", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "491", "messages": "492", "suppressedMessages": "493", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "494", "messages": "495", "suppressedMessages": "496", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "497", "messages": "498", "suppressedMessages": "499", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "500", "messages": "501", "suppressedMessages": "502", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "503", "messages": "504", "suppressedMessages": "505", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "506", "messages": "507", "suppressedMessages": "508", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "509", "messages": "510", "suppressedMessages": "511", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "512", "messages": "513", "suppressedMessages": "514", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "515", "messages": "516", "suppressedMessages": "517", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "518", "messages": "519", "suppressedMessages": "520", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "521", "messages": "522", "suppressedMessages": "523", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "524", "messages": "525", "suppressedMessages": "526", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "527", "messages": "528", "suppressedMessages": "529", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "530", "messages": "531", "suppressedMessages": "532", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "533", "messages": "534", "suppressedMessages": "535", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "536", "messages": "537", "suppressedMessages": "538", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "539", "messages": "540", "suppressedMessages": "541", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "542", "messages": "543", "suppressedMessages": "544", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "545", "messages": "546", "suppressedMessages": "547", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "548", "messages": "549", "suppressedMessages": "550", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "551", "messages": "552", "suppressedMessages": "553", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "554", "messages": "555", "suppressedMessages": "556", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "557", "messages": "558", "suppressedMessages": "559", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "560", "messages": "561", "suppressedMessages": "562", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "563", "messages": "564", "suppressedMessages": "565", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "566", "messages": "567", "suppressedMessages": "568", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "569", "messages": "570", "suppressedMessages": "571", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "572", "messages": "573", "suppressedMessages": "574", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "575", "messages": "576", "suppressedMessages": "577", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "578", "messages": "579", "suppressedMessages": "580", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "581", "messages": "582", "suppressedMessages": "583", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "584", "messages": "585", "suppressedMessages": "586", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "587", "messages": "588", "suppressedMessages": "589", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "590", "messages": "591", "suppressedMessages": "592", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "593", "messages": "594", "suppressedMessages": "595", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "596", "messages": "597", "suppressedMessages": "598", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "599", "messages": "600", "suppressedMessages": "601", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "602", "messages": "603", "suppressedMessages": "604", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "605", "messages": "606", "suppressedMessages": "607", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "608", "messages": "609", "suppressedMessages": "610", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "611", "messages": "612", "suppressedMessages": "613", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "614", "messages": "615", "suppressedMessages": "616", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "617", "messages": "618", "suppressedMessages": "619", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "620", "messages": "621", "suppressedMessages": "622", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "623", "messages": "624", "suppressedMessages": "625", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "626", "messages": "627", "suppressedMessages": "628", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "629", "messages": "630", "suppressedMessages": "631", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "632", "messages": "633", "suppressedMessages": "634", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "635", "messages": "636", "suppressedMessages": "637", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "638", "messages": "639", "suppressedMessages": "640", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "641", "messages": "642", "suppressedMessages": "643", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "644", "messages": "645", "suppressedMessages": "646", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "647", "messages": "648", "suppressedMessages": "649", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "650", "messages": "651", "suppressedMessages": "652", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "653", "messages": "654", "suppressedMessages": "655", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "656", "messages": "657", "suppressedMessages": "658", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "659", "messages": "660", "suppressedMessages": "661", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "662", "messages": "663", "suppressedMessages": "664", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "665", "messages": "666", "suppressedMessages": "667", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "668", "messages": "669", "suppressedMessages": "670", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "671", "messages": "672", "suppressedMessages": "673", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "674", "messages": "675", "suppressedMessages": "676", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "677", "messages": "678", "suppressedMessages": "679", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "680", "messages": "681", "suppressedMessages": "682", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "683", "messages": "684", "suppressedMessages": "685", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "686", "messages": "687", "suppressedMessages": "688", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "689", "messages": "690", "suppressedMessages": "691", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "692", "messages": "693", "suppressedMessages": "694", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "695", "messages": "696", "suppressedMessages": "697", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "698", "messages": "699", "suppressedMessages": "700", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "701", "messages": "702", "suppressedMessages": "703", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "704", "messages": "705", "suppressedMessages": "706", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "707", "messages": "708", "suppressedMessages": "709", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "710", "messages": "711", "suppressedMessages": "712", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "713", "messages": "714", "suppressedMessages": "715", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "716", "messages": "717", "suppressedMessages": "718", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "719", "messages": "720", "suppressedMessages": "721", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "722", "messages": "723", "suppressedMessages": "724", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "725", "messages": "726", "suppressedMessages": "727", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "728", "messages": "729", "suppressedMessages": "730", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "731", "messages": "732", "suppressedMessages": "733", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "734", "messages": "735", "suppressedMessages": "736", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "737", "messages": "738", "suppressedMessages": "739", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "740", "messages": "741", "suppressedMessages": "742", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "743", "messages": "744", "suppressedMessages": "745", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "746", "messages": "747", "suppressedMessages": "748", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "749", "messages": "750", "suppressedMessages": "751", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "752", "messages": "753", "suppressedMessages": "754", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "755", "messages": "756", "suppressedMessages": "757", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "758", "messages": "759", "suppressedMessages": "760", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "761", "messages": "762", "suppressedMessages": "763", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "764", "messages": "765", "suppressedMessages": "766", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "767", "messages": "768", "suppressedMessages": "769", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "770", "messages": "771", "suppressedMessages": "772", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "773", "messages": "774", "suppressedMessages": "775", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "776", "messages": "777", "suppressedMessages": "778", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "779", "messages": "780", "suppressedMessages": "781", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "782", "messages": "783", "suppressedMessages": "784", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "785", "messages": "786", "suppressedMessages": "787", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "788", "messages": "789", "suppressedMessages": "790", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "791", "messages": "792", "suppressedMessages": "793", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "794", "messages": "795", "suppressedMessages": "796", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "797", "messages": "798", "suppressedMessages": "799", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "800", "messages": "801", "suppressedMessages": "802", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "803", "messages": "804", "suppressedMessages": "805", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "806", "messages": "807", "suppressedMessages": "808", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "809", "messages": "810", "suppressedMessages": "811", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "812", "messages": "813", "suppressedMessages": "814", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "815", "messages": "816", "suppressedMessages": "817", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "818", "messages": "819", "suppressedMessages": "820", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "821", "messages": "822", "suppressedMessages": "823", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "824", "messages": "825", "suppressedMessages": "826", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "827", "messages": "828", "suppressedMessages": "829", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "830", "messages": "831", "suppressedMessages": "832", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "833", "messages": "834", "suppressedMessages": "835", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "836", "messages": "837", "suppressedMessages": "838", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "839", "messages": "840", "suppressedMessages": "841", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "842", "messages": "843", "suppressedMessages": "844", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "845", "messages": "846", "suppressedMessages": "847", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "848", "messages": "849", "suppressedMessages": "850", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "851", "messages": "852", "suppressedMessages": "853", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "854", "messages": "855", "suppressedMessages": "856", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "857", "messages": "858", "suppressedMessages": "859", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "860", "messages": "861", "suppressedMessages": "862", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "863", "messages": "864", "suppressedMessages": "865", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "866", "messages": "867", "suppressedMessages": "868", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "869", "messages": "870", "suppressedMessages": "871", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "872", "messages": "873", "suppressedMessages": "874", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "875", "messages": "876", "suppressedMessages": "877", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "878", "messages": "879", "suppressedMessages": "880", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "881", "messages": "882", "suppressedMessages": "883", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "884", "messages": "885", "suppressedMessages": "886", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "887", "messages": "888", "suppressedMessages": "889", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "890", "messages": "891", "suppressedMessages": "892", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "893", "messages": "894", "suppressedMessages": "895", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "896", "messages": "897", "suppressedMessages": "898", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "899", "messages": "900", "suppressedMessages": "901", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "902", "messages": "903", "suppressedMessages": "904", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "905", "messages": "906", "suppressedMessages": "907", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "908", "messages": "909", "suppressedMessages": "910", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "911", "messages": "912", "suppressedMessages": "913", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "914", "messages": "915", "suppressedMessages": "916", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "917", "messages": "918", "suppressedMessages": "919", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "920", "messages": "921", "suppressedMessages": "922", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "923", "messages": "924", "suppressedMessages": "925", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "926", "messages": "927", "suppressedMessages": "928", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "929", "messages": "930", "suppressedMessages": "931", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "932", "messages": "933", "suppressedMessages": "934", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "935", "messages": "936", "suppressedMessages": "937", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "938", "messages": "939", "suppressedMessages": "940", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "941", "messages": "942", "suppressedMessages": "943", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "944", "messages": "945", "suppressedMessages": "946", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "947", "messages": "948", "suppressedMessages": "949", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "950", "messages": "951", "suppressedMessages": "952", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "953", "messages": "954", "suppressedMessages": "955", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "956", "messages": "957", "suppressedMessages": "958", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "959", "messages": "960", "suppressedMessages": "961", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "962", "messages": "963", "suppressedMessages": "964", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "965", "messages": "966", "suppressedMessages": "967", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "968", "messages": "969", "suppressedMessages": "970", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "971", "messages": "972", "suppressedMessages": "973", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "974", "messages": "975", "suppressedMessages": "976", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\admin\\reactivate\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\admin\\cleanup-expired-free\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\admin\\email-failures\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\admin\\notification-stats\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\admin\\process-expired-grace-periods\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\admin\\reactivate-user\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\admin\\send-grace-period-reminders\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\ai\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\auth\\free-account-status\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\auth\\generate-password-reset\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\auth\\pre-register-paid\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\auth\\register-free\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\auth\\resend-confirmation\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\document\\upload\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\health\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\notify-signup\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\payment\\status\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\stripe\\create-checkout-session\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\stripe\\webhook\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\tokens\\purchase\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\user\\cancel-subscription\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\user\\notifications\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\user\\plan\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\user\\profile\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\user\\reactivate\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\user\\status\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\user\\validate-access\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\auth\\callback\\page.tsx", [], ["977"], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\auth\\confirm-reset\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\auth\\confirmed\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\auth\\reset-password\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\auth\\unauthorized\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\debug-user\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\login\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\payment\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\payment-pending\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\plan-estudios\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\profile\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\thank-you\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\upgrade-plan\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\welcome\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\auth\\PlanValidationWrapper.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\thank-you\\ProcessingState.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\DocumentLimitStatus.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\FreeAccountGuard.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\FreeAccountStatus.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\FreeAccountTimer.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\PlanCard.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\SessionInfo.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\TokenProgressBar.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\TokenStatsModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\TokenUsageChart.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\UnauthorizedAccess.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\UpgradePlanMessage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\config\\openai.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\config\\prompts.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\contexts\\AuthContext.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\contexts\\BackgroundTasksContext.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\auth\\components\\AuthManager.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\auth\\components\\ForgotPasswordModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\auth\\components\\InactivityWarning.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\auth\\components\\ProtectedRoute.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\auth\\components\\SessionIndicator.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\auth\\hooks\\useInactivityTimer.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\conversations\\components\\ConversationHistory.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\conversations\\components\\ConversationSidebar.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\conversations\\components\\QuestionForm.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\dashboard\\components\\Dashboard.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\dashboard\\components\\StudyStatistics.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\dashboard\\services\\estadisticasService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\documents\\components\\DocumentManager.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\documents\\components\\DocumentSelector.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\documents\\components\\DocumentUploader.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardCollectionCard.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardCollectionList.test.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardCollectionList.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardCollectionView.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardDetailedStatistics.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardEditModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardGeneralStatistics.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardGenerator.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardStatistics.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardStudyMode.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardStudyOptions.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardViewer.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\RevisionHistory.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\types.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\mindmaps\\components\\MindMapGenerator.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\mindmaps\\components\\MindMapHelp.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\components\\CalendarioModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\components\\PlanCalendario.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\components\\PlanEstudiosViewer.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\components\\PlanificacionAsistente.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\components\\TareasDelDia.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\hooks\\usePlanCalendario.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\services\\planEstudiosClientService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\services\\planEstudiosService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\services\\planGeneratorService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\services\\planificacionService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\types\\calendarTypes.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\utils\\calendarioPreferences.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\utils\\planDateUtils.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\profile\\components\\AccountInfo.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\profile\\components\\AccountSettings.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\profile\\components\\NotificationHistory.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\profile\\components\\PlanUsage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\shared\\components\\BackgroundTasksPanel.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\shared\\components\\ClientLayout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\shared\\components\\DiagnosticPanel.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\shared\\components\\MobileDebugInfo.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\shared\\components\\SidebarMenu.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\summaries\\components\\SummaryGenerator.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\summaries\\components\\SummaryList.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\temario\\components\\TemaActions.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\temario\\components\\TemaEditModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\temario\\components\\TemarioEditModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\temario\\components\\TemarioManager.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\temario\\components\\TemarioSetup.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\temario\\components\\TemariosPredefinidosSelector.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\temario\\services\\temarioService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\temario\\services\\temariosPredefinidosService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\components\\TestDetailedStatistics.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\components\\TestEditModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\components\\TestGeneralStatistics.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\components\\TestGenerator.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\components\\TestRepasoConfig.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\components\\TestRepasoViewer.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\components\\TestViewer.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\hooks\\useBackgroundGeneration.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\hooks\\useFreeAccount.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\hooks\\useMobileAuth.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\hooks\\usePlanEstudiosResults.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\hooks\\usePlanLimits.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\hooks\\useTaskResults.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\hooks\\useUserPlan.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\ai\\tokenTracker.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\auth\\validateUserAccess.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\formSchemas.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini\\flashcardGenerator.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini\\geminiClient.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini\\mindMapGenerator.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini\\questionService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini\\resumenEditor.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini\\resumenGenerator.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini\\testGenerator.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\openai\\openaiClient.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\email\\emailAnalytics.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\email\\emailLogger.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\email\\emailNotificationService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\email\\emailSender.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\email\\emailTemplates.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\email\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\email\\types.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\freeAccountService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\limitHandler.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\permissionService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\planValidation.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\stripeWebhookHandlers.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\userManagement.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\stripe\\config.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\stripe\\plans.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\admin.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\authService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\client.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\conversacionesService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\dashboardService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\documentosService.server.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\documentosService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\estadisticasService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\flashcardsService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\resumenesService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\server.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\supabaseClient.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\testsService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\tokenUsageService.server.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\tokenUsageService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\utils\\dateUtils.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\utils\\emailTemplates.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\utils\\planLimits.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\utils\\securityHelpers.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\utils\\webhookLogger.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\zodSchemas.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\middleware.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\scripts\\validateSystem.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\utils\\markdownToHTML.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\__tests__\\hooks\\usePlanLimits.test.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\__tests__\\integration\\paymentFlow.test.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\__tests__\\services\\limitHandler.test.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\__tests__\\services\\permissionService.test.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\__tests__\\services\\planValidation.test.ts", [], [], {"ruleId": "978", "severity": 1, "message": "979", "line": 169, "column": 6, "nodeType": "980", "endLine": 169, "endColumn": 28, "suggestions": "981", "suppressions": "982"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'status'. Either include it or remove the dependency array.", "ArrayExpression", ["983"], ["984"], {"desc": "985", "fix": "986"}, {"kind": "987", "justification": "988"}, "Update the dependencies array to be: [router, searchParams, status]", {"range": "989", "text": "990"}, "directive", "", [8806, 8828], "[router, searchParams, status]"]