[{"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\admin\\reactivate\\page.tsx": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\admin\\cleanup-expired-free\\route.ts": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\admin\\email-failures\\route.ts": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\admin\\notification-stats\\route.ts": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\admin\\process-expired-grace-periods\\route.ts": "5", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\admin\\reactivate-user\\route.ts": "6", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\admin\\send-grace-period-reminders\\route.ts": "7", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\ai\\route.ts": "8", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\auth\\free-account-status\\route.ts": "9", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\auth\\generate-password-reset\\route.ts": "10", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\auth\\pre-register-paid\\route.ts": "11", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\auth\\register-free\\route.ts": "12", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\auth\\resend-confirmation\\route.ts": "13", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\document\\upload\\route.ts": "14", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\document\\upload\\__tests__\\route.test.ts": "15", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\health\\route.ts": "16", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\notify-signup\\route.ts": "17", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\payment\\status\\route.ts": "18", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\stripe\\create-checkout-session\\route.ts": "19", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\stripe\\webhook\\route.ts": "20", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\tokens\\purchase\\route.ts": "21", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\user\\cancel-subscription\\route.ts": "22", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\user\\notifications\\route.ts": "23", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\user\\plan\\route.ts": "24", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\user\\profile\\route.ts": "25", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\user\\reactivate\\route.ts": "26", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\user\\status\\route.ts": "27", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\user\\validate-access\\route.ts": "28", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\app\\page.tsx": "29", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\auth\\callback\\page.tsx": "30", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\auth\\confirm-reset\\page.tsx": "31", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\auth\\confirmed\\page.tsx": "32", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\auth\\reset-password\\page.tsx": "33", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\auth\\unauthorized\\page.tsx": "34", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\debug-user\\page.tsx": "35", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\layout.tsx": "36", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\login\\page.tsx": "37", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\page.tsx": "38", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\payment\\page.tsx": "39", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\payment-pending\\page.tsx": "40", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\plan-estudios\\page.tsx": "41", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\profile\\page.tsx": "42", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\thank-you\\page.tsx": "43", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\upgrade-plan\\page.tsx": "44", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\welcome\\page.tsx": "45", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\auth\\PlanValidationWrapper.tsx": "46", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\thank-you\\ProcessingState.tsx": "47", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\DocumentLimitStatus.tsx": "48", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\FreeAccountGuard.tsx": "49", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\FreeAccountStatus.tsx": "50", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\FreeAccountTimer.tsx": "51", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\PlanCard.tsx": "52", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\SessionInfo.tsx": "53", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\TokenProgressBar.tsx": "54", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\TokenStatsModal.tsx": "55", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\TokenUsageChart.tsx": "56", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\UnauthorizedAccess.tsx": "57", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\UpgradePlanMessage.tsx": "58", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\config\\openai.ts": "59", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\config\\prompts.ts": "60", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\contexts\\AuthContext.tsx": "61", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\contexts\\BackgroundTasksContext.tsx": "62", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\auth\\components\\AuthManager.tsx": "63", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\auth\\components\\InactivityWarning.tsx": "64", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\auth\\components\\ProtectedRoute.tsx": "65", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\auth\\components\\SessionIndicator.tsx": "66", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\auth\\hooks\\useInactivityTimer.ts": "67", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\auth\\services\\authService.ts": "68", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\conversations\\components\\ConversationHistory.tsx": "69", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\conversations\\components\\ConversationSidebar.tsx": "70", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\conversations\\components\\QuestionForm.tsx": "71", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\conversations\\services\\conversacionesService.ts": "72", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\conversations\\services\\questionService.ts": "73", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\dashboard\\components\\Dashboard.tsx": "74", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\dashboard\\components\\StudyStatistics.tsx": "75", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\dashboard\\services\\dashboardService.ts": "76", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\dashboard\\services\\estadisticasService.ts": "77", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\documents\\components\\DocumentManager.tsx": "78", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\documents\\components\\DocumentSelector.tsx": "79", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\documents\\components\\DocumentUploader.tsx": "80", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\documents\\services\\documentosService.ts": "81", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardCollectionCard.tsx": "82", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardCollectionList.test.tsx": "83", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardCollectionList.tsx": "84", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardCollectionView.tsx": "85", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardDetailedStatistics.tsx": "86", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardEditModal.tsx": "87", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardGeneralStatistics.tsx": "88", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardGenerator.tsx": "89", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardStatistics.tsx": "90", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardStudyMode.tsx": "91", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardStudyOptions.tsx": "92", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardViewer.tsx": "93", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\RevisionHistory.tsx": "94", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\types.ts": "95", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\services\\flashcardGenerator.ts": "96", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\services\\flashcardsService.ts": "97", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\mindmaps\\components\\MindMapGenerator.tsx": "98", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\mindmaps\\components\\MindMapHelp.tsx": "99", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\components\\CalendarioModal.tsx": "100", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\components\\PlanCalendario.tsx": "101", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\components\\PlanEstudiosViewer.tsx": "102", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\components\\PlanificacionAsistente.tsx": "103", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\components\\TareasDelDia.tsx": "104", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\hooks\\usePlanCalendario.ts": "105", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\services\\planEstudiosClientService.ts": "106", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\services\\planEstudiosService.ts": "107", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\services\\planGeneratorService.ts": "108", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\services\\planificacionService.ts": "109", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\types\\calendarTypes.ts": "110", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\utils\\calendarioPreferences.ts": "111", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\utils\\planDateUtils.ts": "112", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\profile\\components\\AccountInfo.tsx": "113", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\profile\\components\\AccountSettings.tsx": "114", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\profile\\components\\NotificationHistory.tsx": "115", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\profile\\components\\PlanUsage.tsx": "116", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\shared\\components\\BackgroundTasksPanel.tsx": "117", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\shared\\components\\ClientLayout.tsx": "118", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\shared\\components\\DiagnosticPanel.tsx": "119", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\shared\\components\\MobileDebugInfo.tsx": "120", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\shared\\components\\SidebarMenu.tsx": "121", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\summaries\\components\\SummaryGenerator.tsx": "122", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\summaries\\components\\SummaryList.tsx": "123", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\temario\\components\\TemaActions.tsx": "124", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\temario\\components\\TemaEditModal.tsx": "125", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\temario\\components\\TemarioEditModal.tsx": "126", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\temario\\components\\TemarioManager.tsx": "127", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\temario\\components\\TemarioSetup.tsx": "128", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\temario\\components\\TemariosPredefinidosSelector.tsx": "129", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\temario\\services\\temarioService.ts": "130", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\temario\\services\\temariosPredefinidosService.ts": "131", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\components\\TestDetailedStatistics.tsx": "132", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\components\\TestGeneralStatistics.tsx": "133", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\components\\TestGenerator.tsx": "134", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\components\\TestRepasoConfig.tsx": "135", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\components\\TestRepasoViewer.tsx": "136", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\components\\TestViewer.tsx": "137", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\services\\testGenerator.ts": "138", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\services\\testsService.ts": "139", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\hooks\\useBackgroundGeneration.ts": "140", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\hooks\\useFreeAccount.ts": "141", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\hooks\\useMobileAuth.ts": "142", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\hooks\\usePlanEstudiosResults.ts": "143", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\hooks\\usePlanLimits.ts": "144", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\hooks\\useTaskResults.ts": "145", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\hooks\\useUserPlan.ts": "146", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\ai\\tokenTracker.ts": "147", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\auth\\validateUserAccess.ts": "148", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\formSchemas.ts": "149", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini\\flashcardGenerator.ts": "150", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini\\geminiClient.ts": "151", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini\\index.ts": "152", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini\\mindMapGenerator.ts": "153", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini\\questionService.ts": "154", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini\\resumenEditor.ts": "155", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini\\resumenGenerator.ts": "156", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini\\testGenerator.ts": "157", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\openai\\openaiClient.ts": "158", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\email\\emailAnalytics.ts": "159", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\email\\emailLogger.ts": "160", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\email\\emailNotificationService.ts": "161", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\email\\emailSender.ts": "162", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\email\\emailTemplates.ts": "163", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\email\\index.ts": "164", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\email\\types.ts": "165", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\freeAccountService.ts": "166", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\limitHandler.ts": "167", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\permissionService.ts": "168", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\planValidation.ts": "169", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\stripeWebhookHandlers.ts": "170", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\userManagement.ts": "171", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\stripe\\config.ts": "172", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\stripe\\plans.ts": "173", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\admin.ts": "174", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\authService.ts": "175", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\client.ts": "176", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\conversacionesService.ts": "177", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\dashboardService.ts": "178", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\documentosService.server.ts": "179", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\documentosService.ts": "180", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\estadisticasService.ts": "181", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\flashcardsService.ts": "182", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\index.ts": "183", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\resumenesService.ts": "184", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\server.ts": "185", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\supabaseClient.ts": "186", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\testsService.ts": "187", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\tokenUsageService.server.ts": "188", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\tokenUsageService.ts": "189", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\utils\\dateUtils.ts": "190", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\utils\\emailTemplates.ts": "191", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\utils\\planLimits.ts": "192", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\utils\\securityHelpers.ts": "193", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\utils\\webhookLogger.ts": "194", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\zodSchemas.ts": "195", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\middleware.ts": "196", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\scripts\\validateSystem.ts": "197", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\utils\\markdownToHTML.ts": "198", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\__tests__\\hooks\\usePlanLimits.test.tsx": "199", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\__tests__\\integration\\paymentFlow.test.ts": "200", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\__tests__\\services\\limitHandler.test.ts": "201", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\__tests__\\services\\permissionService.test.ts": "202", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\__tests__\\services\\planValidation.test.ts": "203", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\auth\\components\\ForgotPasswordModal.tsx": "204", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\components\\TestEditModal.tsx": "205"}, {"size": 8681, "mtime": 1750549865174, "results": "206", "hashOfConfig": "207"}, {"size": 8713, "mtime": 1749926277358, "results": "208", "hashOfConfig": "207"}, {"size": 5845, "mtime": 1750309142357, "results": "209", "hashOfConfig": "207"}, {"size": 3612, "mtime": 1750086603738, "results": "210", "hashOfConfig": "207"}, {"size": 6683, "mtime": 1750085064464, "results": "211", "hashOfConfig": "207"}, {"size": 5926, "mtime": 1750549829701, "results": "212", "hashOfConfig": "207"}, {"size": 8784, "mtime": 1750086580286, "results": "213", "hashOfConfig": "207"}, {"size": 8497, "mtime": 1750862479835, "results": "214", "hashOfConfig": "207"}, {"size": 7052, "mtime": 1750082610808, "results": "215", "hashOfConfig": "207"}, {"size": 1301, "mtime": 1750287727080, "results": "216", "hashOfConfig": "207"}, {"size": 5091, "mtime": 1750698172366, "results": "217", "hashOfConfig": "207"}, {"size": 4017, "mtime": 1750546019180, "results": "218", "hashOfConfig": "207"}, {"size": 2270, "mtime": 1750549762427, "results": "219", "hashOfConfig": "207"}, {"size": 9194, "mtime": 1750020486326, "results": "220", "hashOfConfig": "207"}, {"size": 11683, "mtime": 1749644921713, "results": "221", "hashOfConfig": "207"}, {"size": 271, "mtime": 1750308563029, "results": "222", "hashOfConfig": "207"}, {"size": 5932, "mtime": 1750106816513, "results": "223", "hashOfConfig": "207"}, {"size": 3019, "mtime": 1750549747348, "results": "224", "hashOfConfig": "207"}, {"size": 4781, "mtime": 1750547906770, "results": "225", "hashOfConfig": "207"}, {"size": 4139, "mtime": 1749829295626, "results": "226", "hashOfConfig": "207"}, {"size": 3765, "mtime": 1750309415770, "results": "227", "hashOfConfig": "207"}, {"size": 3987, "mtime": 1750107085364, "results": "228", "hashOfConfig": "207"}, {"size": 2245, "mtime": 1750086615997, "results": "229", "hashOfConfig": "207"}, {"size": 1120, "mtime": 1750110995546, "results": "230", "hashOfConfig": "207"}, {"size": 5431, "mtime": 1750283688975, "results": "231", "hashOfConfig": "207"}, {"size": 2214, "mtime": 1749910333290, "results": "232", "hashOfConfig": "207"}, {"size": 2110, "mtime": 1749913156452, "results": "233", "hashOfConfig": "207"}, {"size": 3432, "mtime": 1749830150148, "results": "234", "hashOfConfig": "207"}, {"size": 29091, "mtime": 1750862525244, "results": "235", "hashOfConfig": "207"}, {"size": 12130, "mtime": 1750519961058, "results": "236", "hashOfConfig": "207"}, {"size": 5086, "mtime": 1750254562388, "results": "237", "hashOfConfig": "207"}, {"size": 5649, "mtime": 1750544723119, "results": "238", "hashOfConfig": "207"}, {"size": 18524, "mtime": 1750764452691, "results": "239", "hashOfConfig": "207"}, {"size": 8147, "mtime": 1750075142671, "results": "240", "hashOfConfig": "207"}, {"size": 6679, "mtime": 1750020180985, "results": "241", "hashOfConfig": "207"}, {"size": 841, "mtime": 1750771975166, "results": "242", "hashOfConfig": "207"}, {"size": 7195, "mtime": 1750761434317, "results": "243", "hashOfConfig": "207"}, {"size": 11029, "mtime": 1750798740268, "results": "244", "hashOfConfig": "207"}, {"size": 9894, "mtime": 1750549596184, "results": "245", "hashOfConfig": "207"}, {"size": 9527, "mtime": 1750698572231, "results": "246", "hashOfConfig": "207"}, {"size": 16407, "mtime": 1750025885793, "results": "247", "hashOfConfig": "207"}, {"size": 7697, "mtime": 1750086750423, "results": "248", "hashOfConfig": "207"}, {"size": 5926, "mtime": 1750690443715, "results": "249", "hashOfConfig": "207"}, {"size": 12825, "mtime": 1750074337834, "results": "250", "hashOfConfig": "207"}, {"size": 8472, "mtime": 1749855884749, "results": "251", "hashOfConfig": "207"}, {"size": 8352, "mtime": 1749830601307, "results": "252", "hashOfConfig": "207"}, {"size": 2673, "mtime": 1749908591667, "results": "253", "hashOfConfig": "207"}, {"size": 6340, "mtime": 1750073060045, "results": "254", "hashOfConfig": "207"}, {"size": 7534, "mtime": 1750075103015, "results": "255", "hashOfConfig": "207"}, {"size": 9495, "mtime": 1750073106534, "results": "256", "hashOfConfig": "207"}, {"size": 8278, "mtime": 1750073147880, "results": "257", "hashOfConfig": "207"}, {"size": 6047, "mtime": 1749826972699, "results": "258", "hashOfConfig": "207"}, {"size": 2643, "mtime": 1749364222822, "results": "259", "hashOfConfig": "207"}, {"size": 3239, "mtime": 1750692677577, "results": "260", "hashOfConfig": "207"}, {"size": 3469, "mtime": 1750693988715, "results": "261", "hashOfConfig": "207"}, {"size": 4720, "mtime": 1750692751611, "results": "262", "hashOfConfig": "207"}, {"size": 8346, "mtime": 1750692897611, "results": "263", "hashOfConfig": "207"}, {"size": 5161, "mtime": 1750073161083, "results": "264", "hashOfConfig": "207"}, {"size": 2571, "mtime": 1750760269966, "results": "265", "hashOfConfig": "207"}, {"size": 56653, "mtime": 1750714886122, "results": "266", "hashOfConfig": "207"}, {"size": 9777, "mtime": 1750546783460, "results": "267", "hashOfConfig": "207"}, {"size": 4662, "mtime": 1750696077841, "results": "268", "hashOfConfig": "207"}, {"size": 2386, "mtime": 1748898729938, "results": "269", "hashOfConfig": "207"}, {"size": 3162, "mtime": 1748813999472, "results": "270", "hashOfConfig": "207"}, {"size": 1279, "mtime": 1748898005145, "results": "271", "hashOfConfig": "207"}, {"size": 2089, "mtime": 1748784425494, "results": "272", "hashOfConfig": "207"}, {"size": 3631, "mtime": 1748814882360, "results": "273", "hashOfConfig": "207"}, {"size": 8228, "mtime": 1750761465791, "results": "274", "hashOfConfig": "207"}, {"size": 3905, "mtime": 1750862535382, "results": "275", "hashOfConfig": "207"}, {"size": 11443, "mtime": 1750862546425, "results": "276", "hashOfConfig": "207"}, {"size": 21493, "mtime": 1750862556972, "results": "277", "hashOfConfig": "207"}, {"size": 10017, "mtime": 1749655003060, "results": "278", "hashOfConfig": "207"}, {"size": 2310, "mtime": 1749329923920, "results": "279", "hashOfConfig": "207"}, {"size": 14701, "mtime": 1749642914607, "results": "280", "hashOfConfig": "207"}, {"size": 15797, "mtime": 1748789194243, "results": "281", "hashOfConfig": "207"}, {"size": 6652, "mtime": 1750862368434, "results": "282", "hashOfConfig": "207"}, {"size": 11388, "mtime": 1749655003060, "results": "283", "hashOfConfig": "207"}, {"size": 6561, "mtime": 1749655003060, "results": "284", "hashOfConfig": "207"}, {"size": 3186, "mtime": 1750697251442, "results": "285", "hashOfConfig": "207"}, {"size": 10544, "mtime": 1750020029412, "results": "286", "hashOfConfig": "207"}, {"size": 4297, "mtime": 1750862378564, "results": "287", "hashOfConfig": "207"}, {"size": 2048, "mtime": 1749137653402, "results": "288", "hashOfConfig": "207"}, {"size": 4361, "mtime": 1749655003075, "results": "289", "hashOfConfig": "207"}, {"size": 4261, "mtime": 1748777250685, "results": "290", "hashOfConfig": "207"}, {"size": 6739, "mtime": 1749137786700, "results": "291", "hashOfConfig": "207"}, {"size": 8927, "mtime": 1748562315253, "results": "292", "hashOfConfig": "207"}, {"size": 6275, "mtime": 1748381218646, "results": "293", "hashOfConfig": "207"}, {"size": 11220, "mtime": 1748562362912, "results": "294", "hashOfConfig": "207"}, {"size": 20873, "mtime": 1750862568515, "results": "295", "hashOfConfig": "207"}, {"size": 1500, "mtime": 1748376642824, "results": "296", "hashOfConfig": "207"}, {"size": 10380, "mtime": 1748563534405, "results": "297", "hashOfConfig": "207"}, {"size": 4612, "mtime": 1748778081572, "results": "298", "hashOfConfig": "207"}, {"size": 15473, "mtime": 1749642914618, "results": "299", "hashOfConfig": "207"}, {"size": 4918, "mtime": 1748789236100, "results": "300", "hashOfConfig": "207"}, {"size": 1090, "mtime": 1749138136757, "results": "301", "hashOfConfig": "207"}, {"size": 2319, "mtime": 1749329859812, "results": "302", "hashOfConfig": "207"}, {"size": 23631, "mtime": 1750862389053, "results": "303", "hashOfConfig": "207"}, {"size": 13256, "mtime": 1750108436905, "results": "304", "hashOfConfig": "207"}, {"size": 3779, "mtime": 1748557840096, "results": "305", "hashOfConfig": "207"}, {"size": 4463, "mtime": 1750719906831, "results": "306", "hashOfConfig": "207"}, {"size": 11231, "mtime": 1750718509926, "results": "307", "hashOfConfig": "207"}, {"size": 20882, "mtime": 1750719869257, "results": "308", "hashOfConfig": "207"}, {"size": 16833, "mtime": 1749045203181, "results": "309", "hashOfConfig": "207"}, {"size": 13033, "mtime": 1750719717802, "results": "310", "hashOfConfig": "207"}, {"size": 8780, "mtime": 1750718935939, "results": "311", "hashOfConfig": "207"}, {"size": 7378, "mtime": 1750862399705, "results": "312", "hashOfConfig": "207"}, {"size": 10458, "mtime": 1750862409883, "results": "313", "hashOfConfig": "207"}, {"size": 20287, "mtime": 1749364863869, "results": "314", "hashOfConfig": "207"}, {"size": 6272, "mtime": 1750862420020, "results": "315", "hashOfConfig": "207"}, {"size": 4912, "mtime": 1750719594922, "results": "316", "hashOfConfig": "207"}, {"size": 7055, "mtime": 1750719029925, "results": "317", "hashOfConfig": "207"}, {"size": 10727, "mtime": 1750718826516, "results": "318", "hashOfConfig": "207"}, {"size": 11016, "mtime": 1750086795224, "results": "319", "hashOfConfig": "207"}, {"size": 16089, "mtime": 1750257232905, "results": "320", "hashOfConfig": "207"}, {"size": 10625, "mtime": 1750086841859, "results": "321", "hashOfConfig": "207"}, {"size": 11515, "mtime": 1750110978404, "results": "322", "hashOfConfig": "207"}, {"size": 7292, "mtime": 1748559970572, "results": "323", "hashOfConfig": "207"}, {"size": 1394, "mtime": 1748815043814, "results": "324", "hashOfConfig": "207"}, {"size": 8618, "mtime": 1749655003075, "results": "325", "hashOfConfig": "207"}, {"size": 5212, "mtime": 1749655003075, "results": "326", "hashOfConfig": "207"}, {"size": 7880, "mtime": 1750697439339, "results": "327", "hashOfConfig": "207"}, {"size": 11098, "mtime": 1750696256433, "results": "328", "hashOfConfig": "207"}, {"size": 21535, "mtime": 1750760269966, "results": "329", "hashOfConfig": "207"}, {"size": 4528, "mtime": 1748810558456, "results": "330", "hashOfConfig": "207"}, {"size": 6444, "mtime": 1748810539098, "results": "331", "hashOfConfig": "207"}, {"size": 6524, "mtime": 1748810280408, "results": "332", "hashOfConfig": "207"}, {"size": 20502, "mtime": 1750862432534, "results": "333", "hashOfConfig": "207"}, {"size": 17180, "mtime": 1748900565639, "results": "334", "hashOfConfig": "207"}, {"size": 9265, "mtime": 1748900735287, "results": "335", "hashOfConfig": "207"}, {"size": 7562, "mtime": 1750862442484, "results": "336", "hashOfConfig": "207"}, {"size": 6378, "mtime": 1748877566234, "results": "337", "hashOfConfig": "207"}, {"size": 4512, "mtime": 1748630532621, "results": "338", "hashOfConfig": "207"}, {"size": 2905, "mtime": 1748630509095, "results": "339", "hashOfConfig": "207"}, {"size": 22864, "mtime": 1750862581402, "results": "340", "hashOfConfig": "207"}, {"size": 9380, "mtime": 1749644921767, "results": "341", "hashOfConfig": "207"}, {"size": 14023, "mtime": 1749309504678, "results": "342", "hashOfConfig": "207"}, {"size": 30922, "mtime": 1750862592085, "results": "343", "hashOfConfig": "207"}, {"size": 3017, "mtime": 1749329950394, "results": "344", "hashOfConfig": "207"}, {"size": 13928, "mtime": 1750862453988, "results": "345", "hashOfConfig": "207"}, {"size": 7297, "mtime": 1750696131484, "results": "346", "hashOfConfig": "207"}, {"size": 9266, "mtime": 1750082632460, "results": "347", "hashOfConfig": "207"}, {"size": 3333, "mtime": 1748376643113, "results": "348", "hashOfConfig": "207"}, {"size": 2060, "mtime": 1748814199030, "results": "349", "hashOfConfig": "207"}, {"size": 9266, "mtime": 1750692614862, "results": "350", "hashOfConfig": "207"}, {"size": 2651, "mtime": 1748561592350, "results": "351", "hashOfConfig": "207"}, {"size": 1337, "mtime": 1750107504888, "results": "352", "hashOfConfig": "207"}, {"size": 7023, "mtime": 1749416233417, "results": "353", "hashOfConfig": "207"}, {"size": 7540, "mtime": 1750021679298, "results": "354", "hashOfConfig": "207"}, {"size": 2314, "mtime": 1750761338786, "results": "355", "hashOfConfig": "207"}, {"size": 2407, "mtime": 1749364828132, "results": "356", "hashOfConfig": "207"}, {"size": 1086, "mtime": 1749329759055, "results": "357", "hashOfConfig": "207"}, {"size": 3253, "mtime": 1749655003091, "results": "358", "hashOfConfig": "207"}, {"size": 3527, "mtime": 1749364808623, "results": "359", "hashOfConfig": "207"}, {"size": 2587, "mtime": 1749364782712, "results": "360", "hashOfConfig": "207"}, {"size": 2046, "mtime": 1750695500164, "results": "361", "hashOfConfig": "207"}, {"size": 3077, "mtime": 1749655003091, "results": "362", "hashOfConfig": "207"}, {"size": 3887, "mtime": 1749364846856, "results": "363", "hashOfConfig": "207"}, {"size": 6123, "mtime": 1749364772281, "results": "364", "hashOfConfig": "207"}, {"size": 8426, "mtime": 1750086480511, "results": "365", "hashOfConfig": "207"}, {"size": 6508, "mtime": 1750086442416, "results": "366", "hashOfConfig": "207"}, {"size": 7674, "mtime": 1750086554604, "results": "367", "hashOfConfig": "207"}, {"size": 8509, "mtime": 1750086519480, "results": "368", "hashOfConfig": "207"}, {"size": 8535, "mtime": 1750086411730, "results": "369", "hashOfConfig": "207"}, {"size": 868, "mtime": 1750086640332, "results": "370", "hashOfConfig": "207"}, {"size": 1707, "mtime": 1750086372857, "results": "371", "hashOfConfig": "207"}, {"size": 13923, "mtime": 1750509873388, "results": "372", "hashOfConfig": "207"}, {"size": 17383, "mtime": 1750698297109, "results": "373", "hashOfConfig": "207"}, {"size": 12474, "mtime": 1750692847133, "results": "374", "hashOfConfig": "207"}, {"size": 11782, "mtime": 1750110965854, "results": "375", "hashOfConfig": "207"}, {"size": 31143, "mtime": 1750549685191, "results": "376", "hashOfConfig": "207"}, {"size": 18327, "mtime": 1750547982192, "results": "377", "hashOfConfig": "207"}, {"size": 426, "mtime": 1749138861932, "results": "378", "hashOfConfig": "207"}, {"size": 4105, "mtime": 1750084350458, "results": "379", "hashOfConfig": "207"}, {"size": 13571, "mtime": 1750550713790, "results": "380", "hashOfConfig": "207"}, {"size": 6717, "mtime": 1749655003091, "results": "381", "hashOfConfig": "207"}, {"size": 622, "mtime": 1750254594447, "results": "382", "hashOfConfig": "207"}, {"size": 9246, "mtime": 1749655003091, "results": "383", "hashOfConfig": "207"}, {"size": 6595, "mtime": 1749655003091, "results": "384", "hashOfConfig": "207"}, {"size": 1310, "mtime": 1748378808764, "results": "385", "hashOfConfig": "207"}, {"size": 4038, "mtime": 1749655003108, "results": "386", "hashOfConfig": "207"}, {"size": 11321, "mtime": 1749655003111, "results": "387", "hashOfConfig": "207"}, {"size": 23405, "mtime": 1749642901184, "results": "388", "hashOfConfig": "207"}, {"size": 313, "mtime": 1749655003114, "results": "389", "hashOfConfig": "207"}, {"size": 6748, "mtime": 1749655430336, "results": "390", "hashOfConfig": "207"}, {"size": 1576, "mtime": 1749479562972, "results": "391", "hashOfConfig": "207"}, {"size": 8151, "mtime": 1750084543676, "results": "392", "hashOfConfig": "207"}, {"size": 9301, "mtime": 1749655003118, "results": "393", "hashOfConfig": "207"}, {"size": 8702, "mtime": 1750692817251, "results": "394", "hashOfConfig": "207"}, {"size": 16305, "mtime": 1750692885638, "results": "395", "hashOfConfig": "207"}, {"size": 7381, "mtime": 1750719054362, "results": "396", "hashOfConfig": "207"}, {"size": 11110, "mtime": 1749830202167, "results": "397", "hashOfConfig": "207"}, {"size": 7188, "mtime": 1750078415710, "results": "398", "hashOfConfig": "207"}, {"size": 10079, "mtime": 1750026225814, "results": "399", "hashOfConfig": "207"}, {"size": 7707, "mtime": 1750026605896, "results": "400", "hashOfConfig": "207"}, {"size": 2423, "mtime": 1749655955625, "results": "401", "hashOfConfig": "207"}, {"size": 17529, "mtime": 1750798998148, "results": "402", "hashOfConfig": "207"}, {"size": 13888, "mtime": 1750698368960, "results": "403", "hashOfConfig": "207"}, {"size": 4309, "mtime": 1749504407466, "results": "404", "hashOfConfig": "207"}, {"size": 11509, "mtime": 1749853487198, "results": "405", "hashOfConfig": "207"}, {"size": 12234, "mtime": 1749853536927, "results": "406", "hashOfConfig": "207"}, {"size": 11484, "mtime": 1749853441638, "results": "407", "hashOfConfig": "207"}, {"size": 9864, "mtime": 1749853396810, "results": "408", "hashOfConfig": "207"}, {"size": 8686, "mtime": 1749853357863, "results": "409", "hashOfConfig": "207"}, {"size": 8459, "mtime": 1750761381376, "results": "410", "hashOfConfig": "207"}, {"size": 5571, "mtime": 1750766638542, "results": "411", "hashOfConfig": "207"}, {"filePath": "412", "messages": "413", "suppressedMessages": "414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "mbgsrr", {"filePath": "415", "messages": "416", "suppressedMessages": "417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "418", "messages": "419", "suppressedMessages": "420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "421", "messages": "422", "suppressedMessages": "423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "424", "messages": "425", "suppressedMessages": "426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "427", "messages": "428", "suppressedMessages": "429", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "430", "messages": "431", "suppressedMessages": "432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "433", "messages": "434", "suppressedMessages": "435", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "436", "messages": "437", "suppressedMessages": "438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "439", "messages": "440", "suppressedMessages": "441", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "442", "messages": "443", "suppressedMessages": "444", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "445", "messages": "446", "suppressedMessages": "447", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "448", "messages": "449", "suppressedMessages": "450", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "451", "messages": "452", "suppressedMessages": "453", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "454", "messages": "455", "suppressedMessages": "456", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "457", "messages": "458", "suppressedMessages": "459", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "460", "messages": "461", "suppressedMessages": "462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "463", "messages": "464", "suppressedMessages": "465", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "466", "messages": "467", "suppressedMessages": "468", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "469", "messages": "470", "suppressedMessages": "471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "472", "messages": "473", "suppressedMessages": "474", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "475", "messages": "476", "suppressedMessages": "477", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "478", "messages": "479", "suppressedMessages": "480", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "481", "messages": "482", "suppressedMessages": "483", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "484", "messages": "485", "suppressedMessages": "486", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "487", "messages": "488", "suppressedMessages": "489", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "490", "messages": "491", "suppressedMessages": "492", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "493", "messages": "494", "suppressedMessages": "495", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "496", "messages": "497", "suppressedMessages": "498", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "499", "messages": "500", "suppressedMessages": "501", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "502", "messages": "503", "suppressedMessages": "504", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "505", "messages": "506", "suppressedMessages": "507", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "508", "messages": "509", "suppressedMessages": "510", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "511", "messages": "512", "suppressedMessages": "513", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "514", "messages": "515", "suppressedMessages": "516", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "517", "messages": "518", "suppressedMessages": "519", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "520", "messages": "521", "suppressedMessages": "522", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "523", "messages": "524", "suppressedMessages": "525", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "526", "messages": "527", "suppressedMessages": "528", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "529", "messages": "530", "suppressedMessages": "531", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "532", "messages": "533", "suppressedMessages": "534", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "535", "messages": "536", "suppressedMessages": "537", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "538", "messages": "539", "suppressedMessages": "540", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "541", "messages": "542", "suppressedMessages": "543", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "544", "messages": "545", "suppressedMessages": "546", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "547", "messages": "548", "suppressedMessages": "549", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "550", "messages": "551", "suppressedMessages": "552", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "553", "messages": "554", "suppressedMessages": "555", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "556", "messages": "557", "suppressedMessages": "558", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "559", "messages": "560", "suppressedMessages": "561", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "562", "messages": "563", "suppressedMessages": "564", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "565", "messages": "566", "suppressedMessages": "567", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "568", "messages": "569", "suppressedMessages": "570", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "571", "messages": "572", "suppressedMessages": "573", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "574", "messages": "575", "suppressedMessages": "576", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "577", "messages": "578", "suppressedMessages": "579", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "580", "messages": "581", "suppressedMessages": "582", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "583", "messages": "584", "suppressedMessages": "585", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "586", "messages": "587", "suppressedMessages": "588", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "589", "messages": "590", "suppressedMessages": "591", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "592", "messages": "593", "suppressedMessages": "594", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "595", "messages": "596", "suppressedMessages": "597", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "598", "messages": "599", "suppressedMessages": "600", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "601", "messages": "602", "suppressedMessages": "603", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "604", "messages": "605", "suppressedMessages": "606", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "607", "messages": "608", "suppressedMessages": "609", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "610", "messages": "611", "suppressedMessages": "612", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "613", "messages": "614", "suppressedMessages": "615", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "616", "messages": "617", "suppressedMessages": "618", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "619", "messages": "620", "suppressedMessages": "621", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "622", "messages": "623", "suppressedMessages": "624", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "625", "messages": "626", "suppressedMessages": "627", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "628", "messages": "629", "suppressedMessages": "630", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "631", "messages": "632", "suppressedMessages": "633", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "634", "messages": "635", "suppressedMessages": "636", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "637", "messages": "638", "suppressedMessages": "639", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "640", "messages": "641", "suppressedMessages": "642", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "643", "messages": "644", "suppressedMessages": "645", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "646", "messages": "647", "suppressedMessages": "648", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "649", "messages": "650", "suppressedMessages": "651", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "652", "messages": "653", "suppressedMessages": "654", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "655", "messages": "656", "suppressedMessages": "657", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "658", "messages": "659", "suppressedMessages": "660", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "661", "messages": "662", "suppressedMessages": "663", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "664", "messages": "665", "suppressedMessages": "666", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "667", "messages": "668", "suppressedMessages": "669", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "670", "messages": "671", "suppressedMessages": "672", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "673", "messages": "674", "suppressedMessages": "675", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "676", "messages": "677", "suppressedMessages": "678", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "679", "messages": "680", "suppressedMessages": "681", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "682", "messages": "683", "suppressedMessages": "684", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "685", "messages": "686", "suppressedMessages": "687", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "688", "messages": "689", "suppressedMessages": "690", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "691", "messages": "692", "suppressedMessages": "693", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "694", "messages": "695", "suppressedMessages": "696", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "697", "messages": "698", "suppressedMessages": "699", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "700", "messages": "701", "suppressedMessages": "702", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "703", "messages": "704", "suppressedMessages": "705", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "706", "messages": "707", "suppressedMessages": "708", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "709", "messages": "710", "suppressedMessages": "711", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "712", "messages": "713", "suppressedMessages": "714", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "715", "messages": "716", "suppressedMessages": "717", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "718", "messages": "719", "suppressedMessages": "720", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "721", "messages": "722", "suppressedMessages": "723", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "724", "messages": "725", "suppressedMessages": "726", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "727", "messages": "728", "suppressedMessages": "729", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "730", "messages": "731", "suppressedMessages": "732", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "733", "messages": "734", "suppressedMessages": "735", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "736", "messages": "737", "suppressedMessages": "738", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "739", "messages": "740", "suppressedMessages": "741", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "742", "messages": "743", "suppressedMessages": "744", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "745", "messages": "746", "suppressedMessages": "747", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "748", "messages": "749", "suppressedMessages": "750", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "751", "messages": "752", "suppressedMessages": "753", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "754", "messages": "755", "suppressedMessages": "756", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "757", "messages": "758", "suppressedMessages": "759", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "760", "messages": "761", "suppressedMessages": "762", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "763", "messages": "764", "suppressedMessages": "765", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "766", "messages": "767", "suppressedMessages": "768", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "769", "messages": "770", "suppressedMessages": "771", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "772", "messages": "773", "suppressedMessages": "774", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "775", "messages": "776", "suppressedMessages": "777", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "778", "messages": "779", "suppressedMessages": "780", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "781", "messages": "782", "suppressedMessages": "783", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "784", "messages": "785", "suppressedMessages": "786", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "787", "messages": "788", "suppressedMessages": "789", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "790", "messages": "791", "suppressedMessages": "792", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "793", "messages": "794", "suppressedMessages": "795", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "796", "messages": "797", "suppressedMessages": "798", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "799", "messages": "800", "suppressedMessages": "801", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "802", "messages": "803", "suppressedMessages": "804", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "805", "messages": "806", "suppressedMessages": "807", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "808", "messages": "809", "suppressedMessages": "810", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "811", "messages": "812", "suppressedMessages": "813", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "814", "messages": "815", "suppressedMessages": "816", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "817", "messages": "818", "suppressedMessages": "819", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "820", "messages": "821", "suppressedMessages": "822", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "823", "messages": "824", "suppressedMessages": "825", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "826", "messages": "827", "suppressedMessages": "828", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "829", "messages": "830", "suppressedMessages": "831", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "832", "messages": "833", "suppressedMessages": "834", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "835", "messages": "836", "suppressedMessages": "837", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "838", "messages": "839", "suppressedMessages": "840", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "841", "messages": "842", "suppressedMessages": "843", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "844", "messages": "845", "suppressedMessages": "846", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "847", "messages": "848", "suppressedMessages": "849", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "850", "messages": "851", "suppressedMessages": "852", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "853", "messages": "854", "suppressedMessages": "855", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "856", "messages": "857", "suppressedMessages": "858", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "859", "messages": "860", "suppressedMessages": "861", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "862", "messages": "863", "suppressedMessages": "864", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "865", "messages": "866", "suppressedMessages": "867", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "868", "messages": "869", "suppressedMessages": "870", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "871", "messages": "872", "suppressedMessages": "873", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "874", "messages": "875", "suppressedMessages": "876", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "877", "messages": "878", "suppressedMessages": "879", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "880", "messages": "881", "suppressedMessages": "882", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "883", "messages": "884", "suppressedMessages": "885", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "886", "messages": "887", "suppressedMessages": "888", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "889", "messages": "890", "suppressedMessages": "891", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "892", "messages": "893", "suppressedMessages": "894", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "895", "messages": "896", "suppressedMessages": "897", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "898", "messages": "899", "suppressedMessages": "900", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "901", "messages": "902", "suppressedMessages": "903", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "904", "messages": "905", "suppressedMessages": "906", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "907", "messages": "908", "suppressedMessages": "909", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "910", "messages": "911", "suppressedMessages": "912", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "913", "messages": "914", "suppressedMessages": "915", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "916", "messages": "917", "suppressedMessages": "918", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "919", "messages": "920", "suppressedMessages": "921", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "922", "messages": "923", "suppressedMessages": "924", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "925", "messages": "926", "suppressedMessages": "927", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "928", "messages": "929", "suppressedMessages": "930", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "931", "messages": "932", "suppressedMessages": "933", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "934", "messages": "935", "suppressedMessages": "936", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "937", "messages": "938", "suppressedMessages": "939", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "940", "messages": "941", "suppressedMessages": "942", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "943", "messages": "944", "suppressedMessages": "945", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "946", "messages": "947", "suppressedMessages": "948", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "949", "messages": "950", "suppressedMessages": "951", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "952", "messages": "953", "suppressedMessages": "954", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "955", "messages": "956", "suppressedMessages": "957", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "958", "messages": "959", "suppressedMessages": "960", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "961", "messages": "962", "suppressedMessages": "963", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "964", "messages": "965", "suppressedMessages": "966", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "967", "messages": "968", "suppressedMessages": "969", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "970", "messages": "971", "suppressedMessages": "972", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "973", "messages": "974", "suppressedMessages": "975", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "976", "messages": "977", "suppressedMessages": "978", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "979", "messages": "980", "suppressedMessages": "981", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "982", "messages": "983", "suppressedMessages": "984", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "985", "messages": "986", "suppressedMessages": "987", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "988", "messages": "989", "suppressedMessages": "990", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "991", "messages": "992", "suppressedMessages": "993", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "994", "messages": "995", "suppressedMessages": "996", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "997", "messages": "998", "suppressedMessages": "999", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1000", "messages": "1001", "suppressedMessages": "1002", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1003", "messages": "1004", "suppressedMessages": "1005", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1006", "messages": "1007", "suppressedMessages": "1008", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1009", "messages": "1010", "suppressedMessages": "1011", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1012", "messages": "1013", "suppressedMessages": "1014", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1015", "messages": "1016", "suppressedMessages": "1017", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1018", "messages": "1019", "suppressedMessages": "1020", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1021", "messages": "1022", "suppressedMessages": "1023", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1024", "messages": "1025", "suppressedMessages": "1026", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\admin\\reactivate\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\admin\\cleanup-expired-free\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\admin\\email-failures\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\admin\\notification-stats\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\admin\\process-expired-grace-periods\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\admin\\reactivate-user\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\admin\\send-grace-period-reminders\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\ai\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\auth\\free-account-status\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\auth\\generate-password-reset\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\auth\\pre-register-paid\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\auth\\register-free\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\auth\\resend-confirmation\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\document\\upload\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\document\\upload\\__tests__\\route.test.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\health\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\notify-signup\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\payment\\status\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\stripe\\create-checkout-session\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\stripe\\webhook\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\tokens\\purchase\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\user\\cancel-subscription\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\user\\notifications\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\user\\plan\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\user\\profile\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\user\\reactivate\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\user\\status\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\user\\validate-access\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\app\\page.tsx", ["1027"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\auth\\callback\\page.tsx", [], ["1028"], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\auth\\confirm-reset\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\auth\\confirmed\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\auth\\reset-password\\page.tsx", ["1029"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\auth\\unauthorized\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\debug-user\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\login\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\page.tsx", ["1030"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\payment\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\payment-pending\\page.tsx", ["1031"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\plan-estudios\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\profile\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\thank-you\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\upgrade-plan\\page.tsx", ["1032"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\welcome\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\auth\\PlanValidationWrapper.tsx", ["1033"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\thank-you\\ProcessingState.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\DocumentLimitStatus.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\FreeAccountGuard.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\FreeAccountStatus.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\FreeAccountTimer.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\PlanCard.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\SessionInfo.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\TokenProgressBar.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\TokenStatsModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\TokenUsageChart.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\UnauthorizedAccess.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\UpgradePlanMessage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\config\\openai.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\config\\prompts.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\contexts\\AuthContext.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\contexts\\BackgroundTasksContext.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\auth\\components\\AuthManager.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\auth\\components\\InactivityWarning.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\auth\\components\\ProtectedRoute.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\auth\\components\\SessionIndicator.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\auth\\hooks\\useInactivityTimer.ts", ["1034"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\auth\\services\\authService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\conversations\\components\\ConversationHistory.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\conversations\\components\\ConversationSidebar.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\conversations\\components\\QuestionForm.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\conversations\\services\\conversacionesService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\conversations\\services\\questionService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\dashboard\\components\\Dashboard.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\dashboard\\components\\StudyStatistics.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\dashboard\\services\\dashboardService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\dashboard\\services\\estadisticasService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\documents\\components\\DocumentManager.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\documents\\components\\DocumentSelector.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\documents\\components\\DocumentUploader.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\documents\\services\\documentosService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardCollectionCard.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardCollectionList.test.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardCollectionList.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardCollectionView.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardDetailedStatistics.tsx", ["1035"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardEditModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardGeneralStatistics.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardGenerator.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardStatistics.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardStudyMode.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardStudyOptions.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardViewer.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\RevisionHistory.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\types.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\services\\flashcardGenerator.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\services\\flashcardsService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\mindmaps\\components\\MindMapGenerator.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\mindmaps\\components\\MindMapHelp.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\components\\CalendarioModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\components\\PlanCalendario.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\components\\PlanEstudiosViewer.tsx", ["1036"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\components\\PlanificacionAsistente.tsx", ["1037"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\components\\TareasDelDia.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\hooks\\usePlanCalendario.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\services\\planEstudiosClientService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\services\\planEstudiosService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\services\\planGeneratorService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\services\\planificacionService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\types\\calendarTypes.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\utils\\calendarioPreferences.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\utils\\planDateUtils.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\profile\\components\\AccountInfo.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\profile\\components\\AccountSettings.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\profile\\components\\NotificationHistory.tsx", ["1038"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\profile\\components\\PlanUsage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\shared\\components\\BackgroundTasksPanel.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\shared\\components\\ClientLayout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\shared\\components\\DiagnosticPanel.tsx", ["1039"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\shared\\components\\MobileDebugInfo.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\shared\\components\\SidebarMenu.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\summaries\\components\\SummaryGenerator.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\summaries\\components\\SummaryList.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\temario\\components\\TemaActions.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\temario\\components\\TemaEditModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\temario\\components\\TemarioEditModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\temario\\components\\TemarioManager.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\temario\\components\\TemarioSetup.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\temario\\components\\TemariosPredefinidosSelector.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\temario\\services\\temarioService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\temario\\services\\temariosPredefinidosService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\components\\TestDetailedStatistics.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\components\\TestGeneralStatistics.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\components\\TestGenerator.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\components\\TestRepasoConfig.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\components\\TestRepasoViewer.tsx", ["1040"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\components\\TestViewer.tsx", ["1041"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\services\\testGenerator.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\services\\testsService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\hooks\\useBackgroundGeneration.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\hooks\\useFreeAccount.ts", ["1042"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\hooks\\useMobileAuth.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\hooks\\usePlanEstudiosResults.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\hooks\\usePlanLimits.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\hooks\\useTaskResults.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\hooks\\useUserPlan.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\ai\\tokenTracker.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\auth\\validateUserAccess.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\formSchemas.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini\\flashcardGenerator.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini\\geminiClient.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini\\mindMapGenerator.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini\\questionService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini\\resumenEditor.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini\\resumenGenerator.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini\\testGenerator.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\openai\\openaiClient.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\email\\emailAnalytics.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\email\\emailLogger.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\email\\emailNotificationService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\email\\emailSender.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\email\\emailTemplates.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\email\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\email\\types.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\freeAccountService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\limitHandler.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\permissionService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\planValidation.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\stripeWebhookHandlers.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\userManagement.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\stripe\\config.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\stripe\\plans.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\admin.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\authService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\client.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\conversacionesService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\dashboardService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\documentosService.server.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\documentosService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\estadisticasService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\flashcardsService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\resumenesService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\server.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\supabaseClient.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\testsService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\tokenUsageService.server.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\tokenUsageService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\utils\\dateUtils.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\utils\\emailTemplates.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\utils\\planLimits.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\utils\\securityHelpers.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\utils\\webhookLogger.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\zodSchemas.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\middleware.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\scripts\\validateSystem.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\utils\\markdownToHTML.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\__tests__\\hooks\\usePlanLimits.test.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\__tests__\\integration\\paymentFlow.test.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\__tests__\\services\\limitHandler.test.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\__tests__\\services\\permissionService.test.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\__tests__\\services\\planValidation.test.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\auth\\components\\ForgotPasswordModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\components\\TestEditModal.tsx", [], [], {"ruleId": "1043", "severity": 1, "message": "1044", "line": 325, "column": 17, "nodeType": "1045", "endLine": 329, "endColumn": 19}, {"ruleId": "1046", "severity": 1, "message": "1047", "line": 169, "column": 6, "nodeType": "1048", "endLine": 169, "endColumn": 28, "suggestions": "1049", "suppressions": "1050"}, {"ruleId": "1046", "severity": 1, "message": "1051", "line": 226, "column": 6, "nodeType": "1048", "endLine": 226, "endColumn": 20, "suggestions": "1052"}, {"ruleId": "1043", "severity": 1, "message": "1044", "line": 53, "column": 11, "nodeType": "1045", "endLine": 57, "endColumn": 13}, {"ruleId": "1046", "severity": 1, "message": "1053", "line": 62, "column": 6, "nodeType": "1048", "endLine": 62, "endColumn": 33, "suggestions": "1054"}, {"ruleId": "1046", "severity": 1, "message": "1055", "line": 84, "column": 6, "nodeType": "1048", "endLine": 84, "endColumn": 8, "suggestions": "1056"}, {"ruleId": "1046", "severity": 1, "message": "1057", "line": 45, "column": 6, "nodeType": "1048", "endLine": 45, "endColumn": 50, "suggestions": "1058"}, {"ruleId": "1046", "severity": 1, "message": "1059", "line": 113, "column": 6, "nodeType": "1048", "endLine": 113, "endColumn": 39, "suggestions": "1060"}, {"ruleId": "1046", "severity": 1, "message": "1061", "line": 22, "column": 6, "nodeType": "1048", "endLine": 22, "endColumn": 19, "suggestions": "1062"}, {"ruleId": "1046", "severity": 1, "message": "1063", "line": 35, "column": 6, "nodeType": "1048", "endLine": 35, "endColumn": 17, "suggestions": "1064"}, {"ruleId": "1046", "severity": 1, "message": "1065", "line": 78, "column": 6, "nodeType": "1048", "endLine": 78, "endColumn": 29, "suggestions": "1066"}, {"ruleId": "1046", "severity": 1, "message": "1067", "line": 45, "column": 6, "nodeType": "1048", "endLine": 45, "endColumn": 22, "suggestions": "1068"}, {"ruleId": "1046", "severity": 1, "message": "1069", "line": 127, "column": 6, "nodeType": "1048", "endLine": 127, "endColumn": 14, "suggestions": "1070"}, {"ruleId": "1046", "severity": 1, "message": "1071", "line": 58, "column": 6, "nodeType": "1048", "endLine": 58, "endColumn": 41, "suggestions": "1072"}, {"ruleId": "1046", "severity": 1, "message": "1073", "line": 86, "column": 6, "nodeType": "1048", "endLine": 86, "endColumn": 17, "suggestions": "1074"}, {"ruleId": "1046", "severity": 1, "message": "1075", "line": 142, "column": 6, "nodeType": "1048", "endLine": 142, "endColumn": 39, "suggestions": "1076"}, "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'status'. Either include it or remove the dependency array.", "ArrayExpression", ["1077"], ["1078"], "React Hook useEffect has missing dependencies: 'checkUserMetadata', 'hasRecoverySession', 'isRecoveryUrl', 'isVerifyingToken', and 'linkError'. Either include them or remove the dependency array.", ["1079"], "React Hook useEffect has a missing dependency: 'email'. Either include it or remove the dependency array.", ["1080"], "React Hook useEffect has a missing dependency: 'loadUserProfile'. Either include it or remove the dependency array.", ["1081"], "React Hook useEffect has a missing dependency: 'validateAccess'. Either include it or remove the dependency array.", ["1082"], "React Hook useEffect has a missing dependency: 'events'. Either include it or remove the dependency array.", ["1083"], "React Hook useEffect has a missing dependency: 'cargarEstadisticas'. Either include it or remove the dependency array.", ["1084"], "React Hook useEffect has a missing dependency: 'cargarProgreso'. Either include it or remove the dependency array.", ["1085"], "React Hook useEffect has a missing dependency: 'cargarDatos'. Either include it or remove the dependency array.", ["1086"], "React Hook useEffect has a missing dependency: 'loadNotifications'. Either include it or remove the dependency array.", ["1087"], "React Hook useEffect has a missing dependency: 'runDiagnostics'. Either include it or remove the dependency array.", ["1088"], "React Hook useEffect has a missing dependency: 'preguntas'. Either include it or remove the dependency array.", ["1089"], "React Hook useEffect has a missing dependency: 'testCompletado'. Either include it or remove the dependency array.", ["1090"], "React Hook useCallback has a missing dependency: 'data'. Either include it or remove the dependency array.", ["1091"], {"desc": "1092", "fix": "1093"}, {"kind": "1094", "justification": "1095"}, {"desc": "1096", "fix": "1097"}, {"desc": "1098", "fix": "1099"}, {"desc": "1100", "fix": "1101"}, {"desc": "1102", "fix": "1103"}, {"desc": "1104", "fix": "1105"}, {"desc": "1106", "fix": "1107"}, {"desc": "1108", "fix": "1109"}, {"desc": "1110", "fix": "1111"}, {"desc": "1112", "fix": "1113"}, {"desc": "1114", "fix": "1115"}, {"desc": "1116", "fix": "1117"}, {"desc": "1118", "fix": "1119"}, {"desc": "1120", "fix": "1121"}, "Update the dependencies array to be: [router, searchParams, status]", {"range": "1122", "text": "1123"}, "directive", "", "Update the dependencies array to be: [checkUserMetadata, hasRecoverySession, isRecoveryUrl, isVerifyingToken, linkError, searchParams]", {"range": "1124", "text": "1125"}, "Update the dependencies array to be: [sessionId, planId, router, email]", {"range": "1126", "text": "1127"}, "Update the dependencies array to be: [loadUserProfile]", {"range": "1128", "text": "1129"}, "Update the dependencies array to be: [requiredFeature, requiredPlan, tokensToUse, validateAccess]", {"range": "1130", "text": "1131"}, "Update the dependencies array to be: [enabled, resetTimer, clearTimer, events]", {"range": "1132", "text": "1133"}, "Update the dependencies array to be: [cargarEstadisticas, coleccionId]", {"range": "1134", "text": "1135"}, "Update the dependencies array to be: [cargarProgreso, temarioId]", {"range": "1136", "text": "1137"}, "Update the dependencies array to be: [temario.id, isEditing, cargarDatos]", {"range": "1138", "text": "1139"}, "Update the dependencies array to be: [userId, filter, loadNotifications]", {"range": "1140", "text": "1141"}, "Update the dependencies array to be: [isOpen, runDiagnostics]", {"range": "1142", "text": "1143"}, "Update the dependencies array to be: [preguntaActual, preguntas, respuestasUsuario]", {"range": "1144", "text": "1145"}, "Update the dependencies array to be: [preguntas, testCompletado]", {"range": "1146", "text": "1147"}, "Update the dependencies array to be: [data]", {"range": "1148", "text": "1149"}, [8806, 8828], "[router, searchParams, status]", [9932, 9946], "[checkUserMetadata, hasRecoverySession, isRecoveryUrl, isVerifyingToken, linkError, searchParams]", [2104, 2131], "[sessionId, planId, router, email]", [3095, 3097], "[loadUserProfile]", [1127, 1171], "[requiredFeature, requiredPlan, tokensToUse, validateAccess]", [3145, 3178], "[enabled, resetTimer, clearTimer, events]", [719, 732], "[cargarEstadisticas, coleccionId]", [1450, 1461], "[cargar<PERSON><PERSON><PERSON><PERSON>, temarioId]", [2229, 2252], "[temario.id, isEditing, cargarDatos]", [1042, 1058], "[userId, filter, loadNotifications]", [4364, 4372], "[isOpen, runDiagnostics]", [2002, 2037], "[preguntaActual, preguntas, respuestasUsuario]", [3511, 3522], "[pregun<PERSON>, testCompletado]", [4018, 4051], "[data]"]